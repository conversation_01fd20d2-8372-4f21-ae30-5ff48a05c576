# Row-Level Security (RLS) 実装ガイド

## 概要 (Overview)

行単位セキュリティ（Row-Level Security, RLS）は、特定のデータベースユーザーがテーブル内のどの行にアクセス（表示、更新、削除）できるかを制御するための、PostgreSQLの強力な機能です。

このドキュメントでは、マルチテナントSaaSアプリケーションにおけるRLSの実装、テスト、および運用について説明します。

## 新しいアーキテクチャ (New Architecture)

### クエリ単位RLS実装
- **必須テナントID**: 全てのクエリでテナントIDが第一パラメータとして必須
- **クエリ毎のRLS設定**: 各クエリが独自の接続でRLSコンテキストを設定
- **100%強制**: RLSを忘れることが不可能な設計
- **マイクロサービス対応**: ステートレスで接続プール対応

### セキュリティレベル
- ✅ **データベースレベル**: PostgreSQL RLSによる強制的な行レベル分離
- ✅ **アプリケーションレベル**: クエリ毎の自動テナント設定
- ✅ **認証レベル**: Cognito claimsからのテナントID抽出
- ✅ **100%強制**: 全てのクエリでRLS必須

## クエリメソッド (Query Methods)

### 1. 標準クエリ（RLS適用）
```javascript
const result = await pool.rlsQuery(tenantId, sql, params);
```
- `app.current_tenant = tenantId` を設定
- RLSポリシーを適用
- 全てのアプリケーションクエリで使用

### 2. 管理者クエリ（RLSバイパス）
```javascript
const result = await pool.byPassQuery(tenantId, sql, params);
```
- `app.current_tenant = tenantId` AND `app.bypass_rls = 'on'` を設定
- RLSポリシーをバイパス
- 管理者操作やバッチジョブで使用

### 3. ヘルパーメソッド
```javascript
const tenantId = Base.extractTenantId(event);
```
- LambdaイベントからテナントIDを抽出
- テナントIDが取得できない場合はエラー

## 内部動作フロー (Internal Flow)

```javascript
pool.rlsQuery(tenantId, sql, params)
  ↓
1. テナントIDバリデーション（無効な場合はエラー）
2. プールから接続を取得
3. 実行: SELECT set_config('app.current_tenant', tenantId, TRUE)
4. 実行: SQLクエリ
5. 接続をプールに返却
```

## 新しいテーブル作成時の手順 (Steps for Creating New Tables)

### 1. テーブル作成
```sql
-- 新しいテーブルを作成（tenant_noカラムを含める）
CREATE TABLE t_new_table (
  tenant_no bigint NOT NULL,
  new_table_no bigint NOT NULL,
  name character varying NOT NULL,
  status integer DEFAULT 0 NOT NULL,
  create_datetime timestamp with time zone DEFAULT now() NOT NULL,
  update_datetime timestamp with time zone DEFAULT now() NOT NULL
);

-- コメント追加
COMMENT ON TABLE t_new_table IS '新しいテーブル';
COMMENT ON COLUMN t_new_table.tenant_no IS 'テナント番号';
COMMENT ON COLUMN t_new_table.new_table_no IS '新しいテーブル番号';

-- 主キー設定
ALTER TABLE ONLY t_new_table ADD CONSTRAINT t_new_table_pkey PRIMARY KEY (new_table_no);

-- 外部キー制約（テナント）
ALTER TABLE ONLY t_new_table ADD CONSTRAINT t_new_table_tenant_no_fkey
  FOREIGN KEY (tenant_no) REFERENCES m_tenant(tenant_no);

-- インデックス作成（tenant_no用）
CREATE INDEX t_new_table_tenant_no_idx ON t_new_table USING btree (tenant_no);
```

### 2. RLS有効化
```sql
-- RLSを有効化
ALTER TABLE t_new_table ENABLE ROW LEVEL SECURITY;

-- 強制RLS（スーパーユーザーもRLSに従う）
ALTER TABLE t_new_table FORCE ROW LEVEL SECURITY;
```

### 3. RLSポリシー作成
```sql
-- テナント分離ポリシー
CREATE POLICY tenant_isolation_policy ON t_new_table
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

-- バイパスポリシー（管理者用）
CREATE POLICY bypass_rls_policy ON t_new_table
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');
```

## Node.jsバックエンド実装手順 (Node.js Backend Implementation)

### 1. 標準的なアプリケーションクエリ

```javascript
// Lambda関数での実装例
exports.handle = async (event, context, callback) => {
  try {
    // イベントからテナントIDを取得
    const tenantId = Base.extractTenantId(event);

    // 標準クエリ（RLS適用）
    const users = await pool.rlsQuery(
      tenantId,
      'SELECT * FROM m_user WHERE status = $1',
      ['active']
    );

    return Base.createSuccessResponse(callback, { users });
  } catch (error) {
    return Base.createErrorResponse(callback, error);
  }
};
```

### 2. 管理者操作（RLSバイパス）

```javascript
// 管理者機能での実装例
exports.adminHandle = async (event, context, callback) => {
  try {
    // 管理者権限チェック
    const roleId = Base.extractRoleId(event);
    if (roleId !== '10') { // 管理者ロールのみ
      throw new Error('管理者権限が必要です');
    }

    const tenantId = Base.extractTenantId(event);

    // RLSバイパスクエリ（全テナントのデータを取得）
    const allUsers = await pool.byPassQuery(
      tenantId,
      'SELECT * FROM m_user ORDER BY create_datetime DESC',
      []
    );

    return Base.createSuccessResponse(callback, { allUsers });
  } catch (error) {
    return Base.createErrorResponse(callback, error);
  }
};
```

### 3. バッチジョブ（システム操作）

```javascript
// バッチ処理での実装例
const SYSTEM_TENANT_ID = 'SYSTEM_BATCH';

exports.batchHandle = async (event, context, callback) => {
  try {
    // システム全体のデータを取得（RLSバイパス）
    const alerts = await pool.byPassQuery(
      SYSTEM_TENANT_ID,
      'SELECT * FROM "f_batch_auction_start_alert"()',
      []
    );

    // 各テナント毎の処理
    for (const alert of alerts) {
      // テナント固有のデータ取得（RLS適用）
      const tenantData = await pool.rlsQuery(
        alert.tenant_no,
        'SELECT * FROM m_constant WHERE key_string = $1',
        ['EMAIL_SETTINGS']
      );

      // 処理実行...
    }

    return Base.createSuccessResponse(callback, { processed: alerts.length });
  } catch (error) {
    return Base.createErrorResponse(callback, error);
  }
};
```

## 従来方式との比較 (Comparison with Previous Approach)

### 従来方式
```javascript
// セットアップが必要
const tenantId = Base.setupTenantContext(event, pool);
const result = await pool.rlsQuery(sql, params);
pool.clearTenantContext();
```

### 新方式
```javascript
// セットアップ不要、クエリ毎にテナントID必須
const tenantId = Base.extractTenantId(event);
const result = await pool.rlsQuery(tenantId, sql, params);
```
## 本番環境へリリースタイミングに確認

1. **RLSポリシーの有効性確認**
```sql
-- RLS有効テーブル数の確認
SELECT COUNT(*) FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE c.relrowsecurity = true AND n.nspname = 'public';

-- RLSポリシー数の確認
SELECT COUNT(*) FROM pg_policies
WHERE policyname IN ('tenant_isolation_policy', 'bypass_rls_policy');
```

2. **テーブルにtenant_noカラムが含まれているか確認**
```sql
scripts/check-tenant-columns.sql
```

## 参考資料 (References)

- [PostgreSQL Row Level Security Documentation](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [AWS Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
- [Multi-Tenant SaaS Security](https://aws.amazon.com/blogs/apn/isolating-saas-tenants-with-dynamically-generated-iam-policies/)
- [RLSを使ったマルチテナントSaaSの構築(実装例含む)](https://zenn.dev/swy/articles/bc544f1d89e942)
