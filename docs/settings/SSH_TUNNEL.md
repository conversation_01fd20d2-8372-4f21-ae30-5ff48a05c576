# 🔧 Connect to PostgreSQL via SSH Tunnel



This step guide helps configure macOS environment to connect to PostgreSQL through an SSH tunnel and query by macOS cli


## 📚 Table of Contents

1. [Setup Guide](#-connect-to-postgresql-via-ssh-tunnel)
2. [Usage Instruction](#-summary-of-usage-after-successful-setup)




## Step 1: Install `psql` via Homebrew

PostgreSQL client (`psql`) is part of the `libpq` formula and not installed globally by default.

### 1.1 Install `libpq`:

```bash
brew install libpq
```

### 1.2 Add `psql` to PATH:

If you're on Intel-based Mac:

```bash
echo 'export PATH="/usr/local/opt/libpq/bin:$PATH"' >> ~/.zshrc
```

If you're on Apple Silicon (M1/M2/M3):

```bash
echo 'export PATH="/opt/homebrew/opt/libpq/bin:$PATH"' >> ~/.zshrc
```

Then reload shell:

```bash
source ~/.zshrc
```

### 1.3 Test `psql` command:

```bash
psql --version
```

should see something like: `psql (PostgreSQL) 17.x`

---

## Step 2: Create `.pgpass` on macOS

Run the following in terminal:

```bash
echo 'localhost:5433:postgres:postgres:IvJM7Vpr6Y0oMO2s' > ~/.pgpass
chmod 600 ~/.pgpass
```

> 🔒 Explanation:

* `localhost`: because you're connecting via local tunnel
* `5433`: the local port forwarded from Mac to EC2 → RDS
* `postgres`: database name
* `postgres`: username
* `IvJM7Vpr6Y0oMO2s`: the password (retrieved from `PGPASSWORD`)

---

## Step 3: Set up SSH Tunnel to EC2

Assuming `~/.ssh/config` contains:

```bash
Host rds-tunnel
  HostName <EC2_PUBLIC_IP>
  User ubuntu
  IdentityFile ~/.ssh/<your-key>
  LocalForward 5433 demo2-saas-db-proxy.proxy-cswooo0b2t5e.ap-northeast-1.rds.amazonaws.com:5432
  ServerAliveInterval 60
  ServerAliveCountMax 3
```

To open the tunnel:

```bash
ssh rds-tunnel
```

> ⚠️ Keep this terminal open to keep the tunnel alive.

---

## Step 4: Connect to PostgreSQL from Mac Terminal

Now can connect without typing a password:

```bash
psql -h localhost -p 5433 -U postgres -d postgres
```

---

## Bonus: Create an Alias for Quick Access

Add this to `~/.zshrc` (or `~/.bashrc`):

```bash
alias psql-rds="psql -h localhost -p 5433 -U postgres -d postgres"
```

Then reload shell:

```bash
source ~/.zshrc
```

And simply connect with:

```bash
psql-rds
```

---

## Debug `.pgpass` if It Doesn't Work

To explicitly point to `.pgpass` file:

```bash
PGPASSFILE=~/.pgpass psql -h localhost -p 5433 -U postgres -d postgres
```

Other: If still asked for a password, check:

* `.pgpass` **must have `chmod 600`**


## ✅ Summary of Usage After Successful Setup

Once everything is set up:

1. **Open SSH Tunnel**

   ```bash
   ssh rds-tunnel
   ```

   > Keep this terminal open to maintain the tunnel.

2. **Connect to PostgreSQL**

   ```bash
   psql -h localhost -p 5433 -U postgres -d postgres
   ```

   > No password input needed (uses `.pgpass`).

3. **(Optional) Use Alias for Quick Access**
   After adding alias to `.zshrc`:

   ```bash
   psql-rds
   ```

That's it — you're now connected to the remote PostgreSQL via SSH tunnel.
