# 🔐 Connect from Local to EC2 & Query Aurora PostgreSQL

## 📚 Table of Contents

1. [Setup Guide (by <PERSON><PERSON> or <PERSON><PERSON><PERSON>)](#-1-prerequisites-ssh-config-on-local)
2. [Usage Instruction (for team members)](#-part-2-usage-instruction-for-team-members)

## 🧱 Part 1: Setup Guide (by <PERSON><PERSON> or <PERSON><PERSON><PERSON>)

### ✅ 1. Prerequisites: SSH Config on Local

```bash
nano ~/.ssh/config
````

Add:

```bash
Host ec2-jump
  HostName <EC2_PUBLIC_IP>
  User ubuntu
  IdentityFile infrastructure/demo2/key-pair/demo2-saas-ec2-access-key.id_rsa
```

> 🔒 Ensure the private key file (`*.id_rsa`) has permission `600` : `chmod 600 demo2-saas-ec2-access-key.id_rsa`.

---

### ✅ 2. SSH to EC2 (Initial Test)

```bash
ssh ec2-jump
```

---

### ✅ 3. Install PostgreSQL Client on EC2

```bash
sudo apt update
sudo apt install -y postgresql-client
psql --version
```

---

### ✅ 4. Create `.pgpass` file on EC2

```bash
echo 'demo2-saas-db-proxy.{PROXY_URL}.ap-northeast-1.rds.amazonaws.com:5432:postgres:postgres:{PASSWORD}' > ~/.pgpass
chmod 600 ~/.pgpass
```
#### ※ Get above information from a lambda function environment

| Field    | Value                                                                                                       |
| -------- | ----------------------------------------------------------------------------------------------------------- |
| Host     | `demo2-saas-db-proxy.{PROXY_URL}.ap-northeast-1.rds.amazonaws.com`                                                |
| Port     | `5432`                                                                                                      |
| Database | `postgres`                                                                                                  |
| User     | `postgres`                                                                                                  |
| Password |　`{PASSWORD}`                                                                                             |

---


> 🔐 This stores password securely and avoids typing it again.

---

### ✅ 5. (Optional) Add Alias for Reuse

Edit:

```bash
nano ~/.bashrc   # or ~/.zshrc
```

Add:

```bash
alias psql-saas="psql -h demo2-saas-db-proxy.{PROXY_URL}.ap-northeast-1.rds.amazonaws.com -U postgres -d postgres"
```

Reload:

```bash
source ~/.bashrc
```

---

## 📦 Part 2: Usage Instruction (for team members)

### ✅ Step 1: SSH into EC2

Ensure your `~/.ssh/config` is set up (admin will provide it), then:

```bash
ssh ec2-jump
```

---

### ✅ Step 2: Connect to PostgreSQL

If alias is registered:

```bash
psql-saas
```

Or manually:

```bash
psql -h demo2-saas-db-proxy.{PROXY_URL}.ap-northeast-1.rds.amazonaws.com -U postgres -d postgres
```

No password input required.

---

### ✅ Step 3: Query Example

```sql
SELECT * FROM public.m_admin;
```

Or edit function:

```sql
CREATE OR REPLACE FUNCTION public.sample_func()
RETURNS void AS $$
BEGIN
  RAISE NOTICE 'Hello World';
END;
$$ LANGUAGE plpgsql;
```

---

## 🎯 Summary

| Task                                  | Status |
| ------------------------------------- | ------ |
| SSH config to EC2                     | ✅      |
| PostgreSQL client installed           | ✅      |
| `.pgpass` used for passwordless login | ✅      |
| Alias setup for fast command          | ✅      |
| Query / Function edit from EC2        | ✅      |
