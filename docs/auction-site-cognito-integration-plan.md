# Auction Site Cognito Authentication Integration Plan

## Executive Summary

This document outlines the comprehensive implementation plan for integrating AWS Cognito authentication into the auction site of our multi-tenant SPA application. The plan implements a **separate Cognito User Pool architecture** for better service isolation between admin and auction systems, ensuring independent authentication management and enhanced security.

## Key Architectural Changes

### 🔄 **Separate User Pool Strategy**

- **Admin User Pool**: `saas-demo2` (existing) - remains unchanged
- **Auction User Pool**: `saas-demo2-auction` (new) - dedicated for auction users
- **Independent Authorization**: Each API Gateway uses its respective User Pool
- **Service Isolation**: Complete separation between admin and auction authentication

### 🔐 **Authentication Flow**

- **Public Endpoints**: Login/registration remain public (no authorizer)
- **Private Endpoints**: All other auction endpoints use auction User Pool authorizer
- **Multi-tenant Isolation**: Maintained within auction User Pool using tenant groups
- **Token Management**: Auction-specific JWT tokens with custom claims

### 🏗️ **Infrastructure Benefits**

- **Scalability**: Independent scaling per service
- **Security**: Better isolation and access control
- **Maintenance**: Easier to manage and update each system
- **Compliance**: Separate audit trails per service

## Current Architecture Analysis

### Admin Site (Current Cognito Implementation)

- **User Pool**: `saas-demo2` with custom attributes (admin_no, member_no, admin_language_code, role_id, admin_name)
- **Custom Claims**: `tenant-id` for multi-tenant isolation
- **Authorization**: Cognito User Pool Authorizer on API Gateway
- **Frontend**: Vue 3 + AWS Amplify with Pinia store for auth state
- **Token Handling**: idToken used in Authorization headers

### Auction Site (Current State)

- **Authentication**: Database-based with bcrypt password hashing
- **Database Tables**: `m_member`, `m_user`, `t_login` for user management
- **JWT Tokens**: Custom JWT with auction-specific claims
- **API Structure**: Public endpoints (no auth) and Private endpoints (custom auth)
- **Frontend**: Vue 3 with cookie-based session management

### Infrastructure Status

- ✅ Admin Cognito User Pool (`saas-demo2`) already configured
- ✅ Cognito authorizer infrastructure exists in auction-side Terraform
- ✅ Multi-tenant database structure with RLS ready
- ✅ Lambda layers and common infrastructure in place
- 🔄 **NEW**: Separate auction Cognito User Pool (`saas-demo2-auction`) to be created

## Implementation Strategy

### Architectural Decision: Separate User Pools

**Rationale for Separate User Pools:**

- **Service Isolation**: Admin and auction systems operate independently
- **Security Boundaries**: Different user types with different access patterns
- **Scalability**: Independent scaling and configuration per service
- **Maintenance**: Easier to manage and update each system separately
- **Compliance**: Better audit trails and access control per service

**Architecture Overview:**

- **Admin User Pool**: `saas-demo2` (existing) - for admin users
- **Auction User Pool**: `saas-demo2-auction` (new) - for auction members
- **Independent Authorization**: Each API Gateway uses its respective User Pool
- **Shared Infrastructure**: Database, Lambda layers, and VPC remain shared

### Phase 1: Create Separate Auction Cognito User Pool

#### 1.1 New Cognito User Pool Configuration

**Path**: `infrastructure/common/auction-cognito/main.tf`

```hcl
resource "aws_cognito_user_pool" "auction_user_pool" {
  name = "${var.project_name}-${var.environment}-auction"

  # Allow users to sign in with email instead of username
  username_attributes = ["email"]
  auto_verified_attributes = ["email"]

  # Password policy
  password_policy {
    minimum_length                    = var.password_policy.minimum_length
    require_lowercase                 = var.password_policy.require_lowercase
    require_numbers                   = var.password_policy.require_numbers
    require_symbols                   = var.password_policy.require_symbols
    require_uppercase                 = var.password_policy.require_uppercase
    temporary_password_validity_days  = var.password_policy.temporary_password_validity_days
  }

  # Auction-specific custom attributes
  schema {
    name                     = "member_no"
    attribute_data_type      = "Number"
    mutable                  = true
    required                 = false
    number_attribute_constraints {
      min_value = "0"
    }
  }
  schema {
    name                     = "user_no"
    attribute_data_type      = "Number"
    mutable                  = true
    required                 = false
    number_attribute_constraints {
      min_value = "0"
    }
  }
  schema {
    name                     = "member_name"
    attribute_data_type      = "String"
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      max_length = "100"
    }
  }
  schema {
    name                     = "language_code"
    attribute_data_type      = "String"
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      max_length = "2"
    }
  }

  # Email verification configuration
  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
    email_subject = "[${var.project_name}] 検証コード"
    email_message = "検証コードは「{####}」です。"
  }

  # MFA is set to OFF - no email verification on login
  mfa_configuration = "OFF"

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }
}

# Create tenant groups for auction users
resource "aws_cognito_user_group" "auction_tenant_groups" {
  for_each = toset(var.tenant_ids)
  name        = "tenant-id:${each.value}"
  user_pool_id = aws_cognito_user_pool.auction_user_pool.id
  description = each.value == "0" || each.value == 0 ? "システム管理者用グループ" : "テナント${each.value}用グループ"
}

# Create app client for auction site
resource "aws_cognito_user_pool_client" "auction_client" {
  name                   = "auction-client"
  user_pool_id           = aws_cognito_user_pool.auction_user_pool.id
  generate_secret        = false
  refresh_token_validity = 30
  access_token_validity  = 1
  id_token_validity      = 1

  callback_urls          = var.callback_urls
  logout_urls            = var.logout_urls

  allowed_oauth_flows    = ["code", "implicit"]
  allowed_oauth_scopes   = ["email", "openid", "profile"]

  # Security setting to prevent user enumeration attacks
  prevent_user_existence_errors = "ENABLED"

  # Authentication flows allowed for this client application
  explicit_auth_flows = [
    "ALLOW_REFRESH_TOKEN_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_ADMIN_USER_PASSWORD_AUTH"
  ]
}

# Create domain for auction user pool
resource "aws_cognito_user_pool_domain" "auction_cognito_domain" {
  domain       = "${var.domain_prefix}-auction"
  user_pool_id = aws_cognito_user_pool.auction_user_pool.id
}
```

#### 1.2 Update Common Module to Include Auction Cognito

**Path**: `infrastructure/common/modules.tf`

```hcl
# Add after existing cognito module
module "auction-cognito" {
  source           = "./auction-cognito"
  project_name     = var.project_name
  environment      = var.environment
  domain_prefix    = "${var.project_name}-${var.environment}"

  # Password policy (same as admin)
  password_policy  = var.password_policy
  tenant_ids       = [0, 1, 2, 3, 4, 5] # 0: system-admin, 1: tenant 1, 2: tenant 2, 3: tenant 3 ...

  callback_urls = [
    "https://${var.auction_domain_name}/callback",
    "https://${var.auction_domain_name}/login/callback"
  ]
  logout_urls = [
    "https://${var.auction_domain_name}/logout",
    "https://${var.auction_domain_name}/login"
  ]
}
```

### Phase 2: Database Integration Strategy

#### 2.1 Utilize Existing Database Schema

The implementation will work with the existing database schema without modifications:

- **m_member table**: Use existing structure from `dll/table/08.m_member.sql`
- **m_user table**: Use existing structure for user authentication data
- **t_login table**: Use existing structure for login tracking
- **Cognito Integration**: Map Cognito user attributes to existing database fields

#### 2.2 Cognito-to-Database Mapping Strategy

```javascript
// Mapping strategy for Cognito attributes to existing database fields
const cognitoToDbMapping = {
  // Cognito User Pool attributes -> Database fields
  email: 'm_user.user_id', // Email as user_id
  'custom:member_name': 'm_member.free_field.memberName', // Store in JSON field
  'custom:language_code': 'm_member.free_field.language', // Store in JSON field
  'custom:member_no': 'm_member.member_no', // Set after member creation
  'custom:user_no': 'm_user.user_no', // Set after user creation
  'tenant-id': 'm_member.tenant_no', // Multi-tenant isolation TODO, change tenant-id to tenant_no
}
```

#### 2.3 User Registration Flow

```mermaid
graph TD
    A[User Registration Request] --> B[Create Cognito User]
    B --> C[Add to Tenant Group]
    C --> D[Create m_member Record]
    D --> E[Create m_user Record]
    E --> F[Update Cognito Custom Attributes]
    F --> G[Return Success Response]
```

#### 2.1 Update Auction-Side Module Configuration

**Path**: `infrastructure/common/modules.tf` (auction-side module section)

```hcl
module "auction-side" {
  source                           = "./auction-side"
  project_name                     = var.project_name
  environment                      = var.environment
  external_domain_name             = var.external_domain_name
  record_name                      = var.record_name
  domain_name                      = var.auction_domain_name
  mail_from_domain                 = var.mail_from_domain
  s3-bucket-arn                    = var.s3-bucket-arn
  s3-bucket-id                     = var.s3-bucket-id
  s3-bucket-bucket                 = var.s3-bucket-bucket
  bucket_regional_domain_name      = var.bucket_regional_domain_name
  slack-notification-sns-topic-arn = module.sns-event.system_error_sns_topic_arn
  lambda_subnet_ids                = module.vpc.lambda_with_ngw_subnets_ids
  lambda_security_group_id         = module.vpc.lambda_security_group_id
  slack-notification-lambda-arn    = ""
  convert_xlsx_to_pdf_endpoint_lambda_arn  = module.batch.convert_xlsx_to_pdf_endpoint_lambda_arn

  # Use NEW auction-specific Cognito User Pool
  cognito_user_pool_arn            = module.auction-cognito.user_pool_arn
  cognito_user_pool_id             = module.auction-cognito.user_pool_id
  cognito_client_id                = module.auction-cognito.app_client_id

  lambda_global_environment_variables = merge(
    var.auction_lambda_global_environment_variables,
    {
      "PGUSER" : module.rds-aurora.master_username,
      "PGHOST" : module.rds-aurora.proxy_endpoint,
      "READ_ONLY_PGHOST" : module.rds-aurora.proxy_read_only_endpoint,
      "PGDATABASE" : module.rds-aurora.database_name,
      "PGPASSWORD" : module.rds-aurora.master_password,
      "PGPORT" : module.rds-aurora.port,
      "S3_BUCKET" : var.s3-bucket-id,
      "NOTICE_SLACK_FUNCTION" : "",
      "WEB_SOCKET_ENDPOINT" : var.web_socket_domain,
      # Auction-specific Cognito configuration
      "AUCTION_COGNITO_USER_POOL_ID" : module.auction-cognito.user_pool_id,
      "AUCTION_COGNITO_CLIENT_ID" : module.auction-cognito.app_client_id,
    }
  )
  common_lambda_layer       = module.nodejs-lib.nodejs_lib_layer_arn
  libreoffice_lib_layer     = module.libreoffice.layer_arn
  profile_name              = var.profile_name
  basic_auth_enable         = var.basic_auth_enable
  auction_tenants          = var.auction_tenants
  auction_custom_domains   = var.auction_custom_domains
  auction_acm_domain_name  = var.auction_acm_domain_name
}
```

### Phase 3: Backend Lambda Functions

#### 3.1 Create Cognito User Registration Lambda

**Path**: `infrastructure/common/auction-side/gateway-resource/cognito-register-member/source/index.js`

```javascript
const AWS = require('aws-sdk')
const cognito = new AWS.CognitoIdentityServiceProvider()
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)

exports.handle = async (event, context, callback) => {
  const pool = new PgPool()
  const base = new Base(pool, event.body.languageCode)

  try {
    await base.startRequest(event)
    const {email, password, memberData, tenantNo} = event.body

    // 1. Create Cognito user in AUCTION-SPECIFIC User Pool
    const cognitoUser = await cognito
      .adminCreateUser({
        UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
        Username: email,
        TemporaryPassword: password,
        MessageAction: 'SUPPRESS',
        UserAttributes: [
          {Name: 'email', Value: email},
          {Name: 'email_verified', Value: 'true'},
          {Name: 'custom:member_name', Value: memberData.memberName || ''},
          {Name: 'custom:language_code', Value: memberData.language || 'ja'},
        ],
      })
      .promise()

    // 2. Add user to tenant group in auction User Pool
    await cognito
      .adminAddUserToGroup({
        UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
        Username: email,
        GroupName: `tenant-id:${tenantNo}`,
      })
      .promise()

    // 3. Set permanent password
    await cognito
      .adminSetUserPassword({
        UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
        Username: email,
        Password: password,
        Permanent: true,
      })
      .promise()

    // 4. Create member record in database (using existing schema)
    const memberResult = await pool.query(
      'SELECT * FROM f_create_member_with_cognito($1, $2, $3)',
      [tenantNo, JSON.stringify(memberData), email]
    )

    // 5. Update Cognito user with member_no and user_no
    await cognito
      .adminUpdateUserAttributes({
        UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
        Username: email,
        UserAttributes: [
          {
            Name: 'custom:member_no',
            Value: memberResult[0].member_no.toString(),
          },
          {Name: 'custom:user_no', Value: memberResult[0].user_no.toString()},
        ],
      })
      .promise()

    return base.createSuccessResponse(callback, {
      message: 'Member registered successfully',
      memberNo: memberResult[0].member_no,
    })
  } catch (error) {
    console.error('Registration error:', error)
    return base.createErrorResponse(callback, error)
  }
}
```

#### 3.2 Create Cognito Login Lambda

**Path**: `infrastructure/common/auction-side/gateway-resource/cognito-login/source/index.js`

```javascript
const AWS = require('aws-sdk')
const cognito = new AWS.CognitoIdentityServiceProvider()
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)

exports.handle = async (event, context, callback) => {
  const pool = new PgPool()
  const base = new Base(pool, event.body.languageCode)

  try {
    await base.startRequest(event)
    const {email, password} = event.body

    // 1. Authenticate with Cognito
    const authResult = await cognito
      .adminInitiateAuth({
        UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
        ClientId: process.env.AUCTION_COGNITO_CLIENT_ID,
        AuthFlow: 'ADMIN_NO_SRP_AUTH',
        AuthParameters: {
          USERNAME: email,
          PASSWORD: password,
        },
      })
      .promise()

    // 2. Get user details from Cognito
    const userDetails = await cognito
      .adminGetUser({
        UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
        Username: email,
      })
      .promise()

    // 3. Extract tenant info from groups
    const groups =
      userDetails.UserAttributes.find(attr => attr.Name === 'cognito:groups')
        ?.Value || ''
    const tenantMatch = groups.match(/tenant-id:(\d+)/)
    const tenantNo = tenantMatch ? parseInt(tenantMatch[1]) : null

    if (!tenantNo) {
      throw new Error('User not assigned to any tenant')
    }

    // 4. Get member info from database using email
    const memberResult = await pool.query(
      'SELECT * FROM f_get_member_by_email($1, $2)',
      [tenantNo, email]
    )

    if (memberResult.length === 0) {
      throw new Error('Member not found in database')
    }

    const member = memberResult[0]

    // 5. Log the login
    await pool.query(
      'SELECT * FROM f_insert_login($1, $2, $3, $4, $5, $6, $7)',
      [
        tenantNo,
        member.member_no,
        member.user_no,
        email,
        email,
        event.requestContext.identity.sourceIp,
        event.requestContext.identity.userAgent,
      ]
    )

    return base.createSuccessResponse(callback, {
      idToken: authResult.AuthenticationResult.IdToken,
      accessToken: authResult.AuthenticationResult.AccessToken,
      refreshToken: authResult.AuthenticationResult.RefreshToken,
      memberInfo: {
        memberNo: member.member_no,
        memberName: member.free_field?.memberName || '',
        language: member.free_field?.language || 'ja',
      },
    })
  } catch (error) {
    console.error('Login error:', error)
    return base.createErrorResponse(callback, {
      status: 401,
      message: 'ログインIDもしくはパスワードが間違っています。',
    })
  }
}
```

### Phase 4: Frontend Integration

#### 4.1 AWS Amplify Configuration

**Path**: `auction-side/src/aws-config.js`

```javascript
import {Amplify} from 'aws-amplify'

// Use auction-specific Cognito User Pool environment variables
const userPoolId = import.meta.env.VITE_AUCTION_USER_POOL_ID
const clientId = import.meta.env.VITE_AUCTION_CLIENT_ID

if (userPoolId && clientId) {
  Amplify.configure({
    Auth: {
      Cognito: {
        userPoolClientId: clientId,
        userPoolId: userPoolId,
        region: 'ap-northeast-1',
        mandatorySignIn: true,
        loginWith: {
          username: true,
          email: true,
          phone: false,
        },
      },
    },
  })
} else {
  console.error('❌ Missing required auction Cognito configuration:', {
    userPoolId,
    clientId,
  })
}

export default Amplify
```

#### 4.2 Environment Variables Configuration

**Path**: `auction-side/.env.demo2`

```bash
# Auction-specific Cognito User Pool configuration
VITE_AUCTION_USER_POOL_ID=ap-northeast-1_XXXXXXXXX  # From terraform output
VITE_AUCTION_CLIENT_ID=XXXXXXXXXXXXXXXXXXXXXXXXXX   # From terraform output
VITE_API_ENDPOINT=https://api-demo2.example.com
VITE_APP_TIMEOUT=30000
```

#### 4.3 Authentication Store (Pinia)

**Path**: `auction-side/src/stores/cognitoAuth.js`

```javascript
import {defineStore} from 'pinia'
import {ref, computed} from 'vue'
import {useRouter} from 'vue-router'
import {
  signIn,
  signOut,
  confirmSignIn,
  fetchAuthSession as amplifyFetchAuthSession,
} from '@aws-amplify/auth'
import {Hub} from 'aws-amplify/utils'
import {jwtDecode} from 'jwt-decode'

export const useCognitoAuthStore = defineStore('cognitoAuth', () => {
  const router = useRouter()

  // State
  const user = ref(null)
  const idToken = ref(null)
  const accessToken = ref(null)
  const isAuthenticated = computed(() => !!user.value && !!idToken.value)

  // Extract user info from token
  function _setUserFromToken(token) {
    if (!token) {
      user.value = null
      idToken.value = null
      accessToken.value = null
      return
    }

    const decodedToken = jwtDecode(token)
    user.value = {
      email: decodedToken.email,
      memberNo: decodedToken['custom:member_no'],
      userNo: decodedToken['custom:user_no'],
      memberName: decodedToken['custom:member_name'],
      languageCode: decodedToken['custom:language_code'],
      tenantId: decodedToken['tenant-id'],
    }
    idToken.value = token
  }

  // Fetch auth session
  async function fetchAuthSession(forceRefresh = false) {
    try {
      const session = await amplifyFetchAuthSession({forceRefresh})
      const token = session.tokens?.idToken?.toString()
      if (!token) {
        throw new Error('No token found in session')
      }
      _setUserFromToken(token)
      accessToken.value = session.tokens?.accessToken?.toString()
      return true
    } catch (e) {
      user.value = null
      idToken.value = null
      accessToken.value = null
      return false
    }
  }

  // Login function
  async function login(email, password) {
    try {
      const signInResponse = await signIn({
        username: email,
        password,
        options: {
          authFlowType: 'USER_PASSWORD_AUTH',
        },
      })

      if (signInResponse.isSignedIn) {
        await fetchAuthSession()
        return {type: 'SUCCESS'}
      }

      const {signInStep} = signInResponse.nextStep
      switch (signInStep) {
        case 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED':
          return {
            type: 'NEW_PASSWORD_REQUIRED',
            message: '初回ログイン時はパスワードの変更が必要です。',
          }
        default:
          return {
            type: 'ERROR',
            message: `未対応の認証ステップです: ${signInStep}`,
          }
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        type: 'ERROR',
        message: error.message || 'ログインに失敗しました。',
      }
    }
  }

  // Logout function
  async function logout() {
    try {
      await signOut()
      user.value = null
      idToken.value = null
      accessToken.value = null
      router.push('/login')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  // Hub listener for auth events
  Hub.listen('auth', ({payload: {event}}) => {
    switch (event) {
      case 'signedIn':
      case 'cognitoHostedUI':
        fetchAuthSession()
        break
      case 'signedOut':
      case 'session_fail':
      case 'tokenRefresh_failure':
        user.value = null
        idToken.value = null
        accessToken.value = null
        break
    }
  })

  return {
    user,
    idToken,
    accessToken,
    isAuthenticated,
    fetchAuthSession,
    login,
    logout,
  }
})
```

## Implementation Strategy

### Greenfield Implementation Approach

This is a **new Cognito implementation** for the auction site, not a migration from an existing authentication system:

1. **Clean Implementation**: Build Cognito authentication from scratch
2. **Existing Schema Utilization**: Work with current database structure without modifications
3. **No Migration Required**: New users will register directly with Cognito
4. **Parallel Development**: Can be developed alongside existing admin authentication

## Security Considerations

### Multi-Tenant Isolation

- **Cognito Groups**: Use `tenant-id:X` groups for tenant isolation
- **Custom Claims**: Include tenant-id in JWT tokens
- **API Gateway**: Validate tenant access in Lambda authorizer
- **Database RLS**: Ensure Row-Level Security policies are enforced

### Token Management

- **Short-lived tokens**: 1-hour expiry for ID/Access tokens
- **Refresh tokens**: 30-day expiry with automatic rotation
- **Secure storage**: Use httpOnly cookies for sensitive tokens
- **CSRF protection**: Implement proper CSRF tokens

## Testing Strategy

### Unit Tests

- **Lambda functions**: Test authentication flows
- **Frontend components**: Test login/logout functionality
- **Database functions**: Test user creation and migration

### Integration Tests

- **End-to-end login flow**: From frontend to database
- **Multi-tenant isolation**: Verify tenant separation
- **Token validation**: Test JWT token handling

### Load Testing

- **Concurrent logins**: Test Cognito limits
- **Database performance**: Test with migrated users
- **API Gateway**: Test authorizer performance

## Deployment Plan

### Environment-Specific Rollout

1. **Development**: Full implementation and testing
2. **Demo2**: Staged rollout with feature flags
3. **Production**: Gradual migration with monitoring

### Rollback Strategy

- **Feature flags**: Quick disable of Cognito auth
- **Database backups**: Before migration starts
- **Infrastructure rollback**: Terraform state management
- **User communication**: Clear migration timeline

## Monitoring and Observability

### CloudWatch Metrics

- **Authentication success/failure rates**
- **Token refresh patterns**
- **Migration progress tracking**
- **API Gateway performance**

### Alerts

- **High authentication failure rates**
- **Cognito service limits approaching**
- **Database migration errors**
- **Token validation failures**

### Phase 3: API Gateway Configuration

#### 3.1 Update Auction-Side Authorizer Configuration

**Path**: `infrastructure/common/auction-side/gateway-resource/authorizer/main.tf`

```hcl
# Update to use auction-specific Cognito User Pool
resource "aws_api_gateway_authorizer" "cognito_authorizer" {
  name                   = "${var.environment}-${var.project_name}-${var.prefix_function_name}-auction-cognito-authorizer"
  rest_api_id            = var.aws_api_gateway_rest_api_gateway_id
  type                   = "COGNITO_USER_POOLS"
  provider_arns          = [var.auction_cognito_user_pool_arn]  # Use auction-specific User Pool
  identity_source        = "method.request.header.Authorization"
}
```

#### 3.2 Update Private Endpoints Configuration

**Path**: `infrastructure/common/auction-side/gateway-resource/private.tf`

All private endpoints already have `authorization = "COGNITO_USER_POOLS"` configured. They will automatically use the updated authorizer that points to the auction-specific User Pool. No changes needed to individual endpoint configurations.

#### 5.2 Create New Cognito-Specific Endpoints

**Path**: `infrastructure/common/auction-side/gateway-resource/cognito-auth.tf`

```hcl
module "cognito-login" {
  source = "./cognito-login"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = merge(
    var.lambda_global_environment_variables,
    {
      "COGNITO_USER_POOL_ID" = var.cognito_user_pool_id,
      "COGNITO_CLIENT_ID" = var.cognito_client_id
    }
  )
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  authorization = "NONE"
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "cognito-register-member" {
  source = "./cognito-register-member"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = merge(
    var.lambda_global_environment_variables,
    {
      "COGNITO_USER_POOL_ID" = var.cognito_user_pool_id,
      "COGNITO_CLIENT_ID" = var.cognito_client_id
    }
  )
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  authorization = "NONE"
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}
```

### Phase 3: Database Functions

#### 3.1 Create Member Registration Function (Using Existing Schema)

**Path**: `dll/function/auction-side/f_create_member_with_cognito.sql`

```sql
CREATE OR REPLACE FUNCTION f_create_member_with_cognito(
  in_tenant_no BIGINT,
  in_free_field JSONB,
  in_email VARCHAR
)
RETURNS TABLE(
  member_no BIGINT,
  user_no BIGINT,
  member_id VARCHAR
) AS $BODY$
DECLARE
  new_member_no BIGINT;
  new_user_no BIGINT;
  new_member_id VARCHAR;
  enhanced_free_field JSONB;
BEGIN
  -- Generate member_no
  SELECT nextval('m_member_no_seq') INTO new_member_no;

  -- Generate member_id (format: M + tenant_no + member_no)
  new_member_id := 'M' || in_tenant_no || LPAD(new_member_no::TEXT, 6, '0');

  -- Enhance free_field with Cognito integration flag
  enhanced_free_field := in_free_field || jsonb_build_object('cognitoEnabled', true, 'authType', 'cognito');

  -- Insert member record using existing schema
  INSERT INTO m_member (
    member_no,
    tenant_no,
    member_id,
    classification,
    currency_id,
    exhibition_allow_flag,
    bid_allow_flag,
    free_field,
    status,
    email_delivery_flag,
    email_priority,
    create_datetime,
    update_datetime
  ) VALUES (
    new_member_no,
    in_tenant_no,
    new_member_id,
    1, -- Default classification
    'JPY', -- Default currency
    0, -- No exhibition by default
    1, -- Allow bidding by default
    enhanced_free_field,
    1, -- Active status
    1, -- Email delivery enabled
    0, -- Normal priority
    NOW(),
    NOW()
  );

  -- Generate user_no
  SELECT nextval('m_user_no_seq') INTO new_user_no;

  -- Insert user record using existing schema
  INSERT INTO m_user (
    user_no,
    tenant_no,
    member_no,
    user_id,
    password,
    require_password_change,
    bid_allow_flag,
    free_field,
    create_datetime,
    update_datetime
  ) VALUES (
    new_user_no,
    in_tenant_no,
    new_member_no,
    in_email,
    'COGNITO_MANAGED', -- Placeholder since Cognito manages passwords
    0, -- No password change required
    1, -- Allow bidding
    jsonb_build_object('memberName', in_free_field->>'memberName', 'authType', 'cognito'),
    NOW(),
    NOW()
  );

  RETURN QUERY SELECT new_member_no, new_user_no, new_member_id;
END;
$BODY$ LANGUAGE plpgsql;
```

#### 3.2 Create User Lookup Function (Using Email as Identifier)

**Path**: `dll/function/auction-side/f_get_member_by_email.sql`

```sql
CREATE OR REPLACE FUNCTION f_get_member_by_email(
  in_tenant_no BIGINT,
  in_email VARCHAR
)
RETURNS TABLE(
  member_no BIGINT,
  user_no BIGINT,
  member_id VARCHAR,
  user_id VARCHAR,
  free_field JSONB,
  status INTEGER,
  bid_allow_flag INTEGER
) AS $BODY$
BEGIN
  RETURN QUERY
  SELECT
    M.member_no,
    U.user_no,
    M.member_id,
    U.user_id,
    M.free_field,
    M.status,
    M.bid_allow_flag
  FROM m_member M
  JOIN m_user U ON U.member_no = M.member_no
  WHERE M.tenant_no = in_tenant_no
    AND U.user_id = in_email  -- Email stored as user_id
    AND M.delete_flag = 0
    AND U.delete_flag = 0
    AND M.status = 1
    AND (M.free_field->>'authType' = 'cognito' OR M.free_field->>'cognitoEnabled' = 'true');
END;
$BODY$ LANGUAGE plpgsql;
```

### Phase 7: Frontend Components

#### 7.1 Updated Login Component

**Path**: `auction-side/src/components/login/CognitoLoginPage.vue`

```vue
<template>
  <h2 class="page-ttl">
    <p class="ttl">ログイン</p>
    <p class="sub">login</p>
  </h2>
  <div class="container">
    <section id="login-form">
      <form @submit.prevent="handleLogin">
        <div class="id-pass-err" v-if="loginError">
          <span class="err-txt">{{ loginError }}</span>
        </div>

        <table class="tbl-login">
          <tbody>
            <tr>
              <th>メールアドレス<em class="req">※必須</em></th>
              <td>
                <input
                  type="email"
                  v-model="email"
                  :class="{err: emailError}"
                  placeholder="<EMAIL>"
                  required
                />
                <p class="err-txt" v-if="emailError">{{ emailError }}</p>
              </td>
            </tr>
            <tr>
              <th>パスワード<em class="req">※必須</em></th>
              <td>
                <input
                  type="password"
                  v-model="password"
                  :class="{err: passwordError}"
                  placeholder="8文字以上"
                  required
                />
                <p class="err-txt" v-if="passwordError">{{ passwordError }}</p>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="forget-pass">
          <a @click="navigateToReminder">パスワードを忘れた方はコチラ</a>
        </div>

        <div class="rule">
          <p class="tit-rule">入札会参加要項</p>
          <embed
            src="/pdf/sample.pdf"
            type="application/pdf"
            width="100%"
            height="150"
          />
          <div class="rule-check">
            <label for="rule-chk">
              <input
                type="checkbox"
                id="rule-chk"
                v-model="agreeToTerms"
                class="checkbox-input"
                required
              />
              <span class="checkbox-parts">参加規約に同意する</span>
            </label>
          </div>
        </div>

        <div class="btn-form">
          <input
            type="submit"
            :disabled="loading || !isFormValid"
            :value="loading ? '処理中...' : 'ログイン'"
          />
        </div>
      </form>

      <div class="request">
        <a class="register-btt" @click="() => router.push(PATH_NAME.REGISTER)">
          新規会員登録
        </a>
        <p>※商品の価格を見るには会員登録が必要です。</p>
      </div>
    </section>
  </div>
</template>

<script setup>
  import {ref, computed, onMounted} from 'vue'
  import {useRouter, useRoute} from 'vue-router'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth'
  import {PATH_NAME} from '@/defined/const'

  const router = useRouter()
  const route = useRoute()
  const authStore = useCognitoAuthStore()

  // Form state
  const email = ref('')
  const password = ref('')
  const agreeToTerms = ref(false)
  const loading = ref(false)
  const loginError = ref('')
  const emailError = ref('')
  const passwordError = ref('')

  // Computed properties
  const isFormValid = computed(() => {
    return (
      email.value &&
      password.value &&
      agreeToTerms.value &&
      !emailError.value &&
      !passwordError.value
    )
  })

  // Validation
  const validateEmail = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!email.value) {
      emailError.value = 'メールアドレスを入力してください。'
    } else if (!emailRegex.test(email.value)) {
      emailError.value = '正しいメールアドレスを入力してください。'
    } else {
      emailError.value = ''
    }
  }

  const validatePassword = () => {
    if (!password.value) {
      passwordError.value = 'パスワードを入力してください。'
    } else if (password.value.length < 8) {
      passwordError.value = 'パスワードは8文字以上で入力してください。'
    } else {
      passwordError.value = ''
    }
  }

  // Login handler
  const handleLogin = async () => {
    validateEmail()
    validatePassword()

    if (!isFormValid.value) return

    loading.value = true
    loginError.value = ''

    try {
      const result = await authStore.login(email.value, password.value)

      if (result.type === 'SUCCESS') {
        // Redirect to intended page or top page
        const redirectPath = route.query.redirect || PATH_NAME.TOP
        router.push(redirectPath)
      } else {
        loginError.value = result.message || 'ログインに失敗しました。'
      }
    } catch (error) {
      console.error('Login error:', error)
      loginError.value = 'ログイン処理中にエラーが発生しました。'
    } finally {
      loading.value = false
    }
  }

  const navigateToReminder = () => {
    router.push(PATH_NAME.REMINDER)
  }

  // Initialize
  onMounted(() => {
    // Check if already authenticated
    if (authStore.isAuthenticated) {
      router.push(PATH_NAME.TOP)
    }
  })
</script>
```

## Implementation Timeline

### Week 1-2: Infrastructure Setup

- [ ] Create separate auction Cognito User Pool (`saas-demo2-auction`)
- [ ] Configure auction-specific custom attributes and tenant groups
- [ ] Update auction-side module to use new User Pool
- [ ] Create database schema changes for Cognito integration
- [ ] Set up development environment variables for separate User Pool
- [ ] Create new Lambda function scaffolding

### Week 3-4: Backend Development

- [ ] Implement Cognito registration Lambda (using auction User Pool)
- [ ] Implement Cognito login Lambda (using auction User Pool)
- [ ] Create database functions for Cognito integration
- [ ] Update API Gateway authorizer to use auction User Pool
- [ ] Ensure login/registration endpoints remain public (no authorizer)
- [ ] Write unit tests for Lambda functions

### Week 5-6: Frontend Development

- [ ] Set up AWS Amplify configuration for auction User Pool
- [ ] Create Cognito authentication store (separate from admin)
- [ ] Implement new login component with auction-specific validation
- [ ] Update router guards for authentication
- [ ] Integrate with existing API calls using new token structure

### Week 7-8: Migration & Testing

- [ ] Create user migration scripts
- [ ] Implement parallel authentication system
- [ ] Conduct integration testing
- [ ] Performance testing and optimization
- [ ] Security audit and penetration testing

### Week 9-10: Deployment & Monitoring

- [ ] Deploy to demo2 environment
- [ ] Set up monitoring and alerting
- [ ] Conduct user acceptance testing
- [ ] Create rollback procedures
- [ ] Documentation and training

## Risk Assessment

### High Risk

- **User Migration Complexity**: Migrating existing users without data loss
- **Multi-tenant Isolation**: Ensuring proper tenant separation in Cognito
- **Performance Impact**: Cognito API limits and latency

### Medium Risk

- **Frontend Integration**: Compatibility with existing Vue 3 components
- **Database Schema Changes**: Impact on existing functionality
- **Token Management**: Secure handling of JWT tokens

### Low Risk

- **Infrastructure Changes**: Terraform updates are well-tested
- **Rollback Capability**: Feature flags allow quick rollback
- **Documentation**: Comprehensive plan reduces implementation risks

## Success Criteria

### Technical Metrics

- [ ] 99.9% authentication success rate
- [ ] < 2 second login response time
- [ ] Zero security vulnerabilities in audit
- [ ] 100% user migration success rate

### Business Metrics

- [ ] No disruption to existing users
- [ ] Improved user experience scores
- [ ] Reduced support tickets for authentication issues
- [ ] Compliance with security requirements

## Next Steps

1. **Review and approve this implementation plan**
2. **Set up development environment with Cognito**
3. **Implement Phase 1: Database schema changes**
4. **Develop and test Lambda functions**
5. **Create frontend authentication components**
6. **Implement migration scripts**
7. **Conduct thorough testing**
8. **Deploy to demo2 environment**
9. **Monitor and optimize performance**
10. **Plan production rollout**

---

**Document Version**: 1.0
**Last Updated**: 2025-01-19
**Author**: AI Assistant
**Review Status**: Pending Review
