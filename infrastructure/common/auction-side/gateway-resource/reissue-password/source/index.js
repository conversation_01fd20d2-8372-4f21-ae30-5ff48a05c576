const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

exports.handle = function (e, ctx, cb) {
  const params = e.body;
  const header = e.headers;
  const authorizer = e.authorizer;
  const base = new Base(pool, params.languageCode);
  const validator = new Validator(base);

  // Set message
  pool.setMessage(base.define.message);

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => validator.validate(params))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant => {
      if (params.validateFlag) {
        return Promise.resolve({});
      } else {
        const newPassword = base.randomString(14);
        return base
          .hashPassword(newPassword)
          .then(hash => {
            console.log('Reissue password');
            return pool.query(
              'SELECT * FROM "f_reissue_password"($1,$2,$3,$4);',
              [tenant.tenant_no, params.id, params.email, hash]
            );
          })
          .then(() => {
            console.log('Get member info');
            return pool
              .query('SELECT * FROM "f_get_member_info_by_email"($1,$2);', [
                params.email,
                tenant.tenant_no,
              ])
              .then(members => {
                const member =
                  members && members.length > 0 ? members[0] : null;
                return Promise.resolve(member);
              });
          })
          .then(member => {
            console.log('Get constants');
            const lang =
              member.free_field?.emailLang ||
              member.free_field?.lang ||
              base.language;
            return pool
              .query(
                'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
                [
                  tenant.tenant_no,
                  [
                    'EMAIL_FORGOT_PASSWORD_FOR_MEMBER',
                    'EMAIL_COMMON_FOOTER',
                    'EMAIL_FROM',
                  ],
                  lang,
                ]
              )
              .then(constants => {
                return Promise.resolve({
                  member,
                  constants,
                });
              });
          })
          .then(data => {
            console.log('Send email notification');

            const constants = data.constants;
            const member = data.member;

            const mailFrom =
              constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
              null;
            const mail =
              constants.find(
                x => x.key_string === 'EMAIL_FORGOT_PASSWORD_FOR_MEMBER'
              ) || {};
            const footer =
              constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') || {};
            const title = mail.value1;
            const sender = mailFrom
              ? `"${mailFrom}"<${mail.value2}>`
              : mail.value2;
            const receivers = [params.email];
            const bcc = [];

            const content = Common.format(mail.value4, [
              newPassword,
              footer.value4,
            ]);

            return Common.sendMailBySES(title, content, sender, receivers, bcc);
          });
      }
    })
    .then(data => {
      console.log(`data: ${JSON.stringify(data)}`);
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => {
      console.log(`error: ${JSON.stringify(error)}`);
      return base.createErrorResponse(cb, error);
    });
};
