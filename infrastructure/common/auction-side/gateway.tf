resource "aws_api_gateway_rest_api" "gateway" {
  name = "${var.environment}-${var.project_name}-auction-side-gateway"
  description = "${var.environment}-${var.project_name}-auction-side-gateway"
  endpoint_configuration {
    types = ["REGIONAL"]
  }
}

locals {
  file_hashes = [
    md5(file("${path.module}/gateway.tf")),
    md5(file("${path.module}/gateway-resource/no-authorizer.tf")),
    md5(file("${path.module}/gateway-resource/private.tf")),
    md5(file("${path.module}/gateway-resource/public.tf")),
    md5(file("${path.module}/gateway-resource/authorizer.tf"))
  ]
  combined_hash = join(",", local.file_hashes)
}

resource "aws_api_gateway_deployment" "gateway_and_lambda_deploy" {
  depends_on = [module.gateway-resource]
  rest_api_id = aws_api_gateway_rest_api.gateway.id
  stage_description = "Deployed with ${local.combined_hash}"
  stage_name = var.api_gateway_stage_name
}

resource "aws_api_gateway_deployment" "gateway_only_deploy" {
  rest_api_id       = aws_api_gateway_rest_api.gateway.id
  stage_description = "Deployed at ${timestamp()}"
  stage_name        = var.api_gateway_stage_name
  depends_on        = [module.gateway-resource]
}

resource "aws_cloudwatch_metric_alarm" "api_gateway_timeout_error_alarm" {
  alarm_name          = "${var.environment}-${var.project_name}-auction-side-gateway-metric-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  datapoints_to_alarm = 1
  threshold           = 29000
  alarm_description   = "【${var.environment}-${var.project_name}-会員側】リクエストタイムアウトが発生しました。"
  treat_missing_data  = "missing"
  metric_name         = "IntegrationLatency"
  namespace           = "AWS/ApiGateway"
  period              = "60"
  statistic           = "Maximum"
  alarm_actions       = [var.slack-notification-sns-topic-arn]
  dimensions = {
    ApiName = "${var.environment}-${var.project_name}-auction-side-gateway"
    Stage   = var.api_gateway_stage_name
  }
}
