data "aws_route53_zone" "site_zone" {
  count        = var.domain_name != "" ? 1 : 0
  name         = var.record_name
  private_zone = false
}

resource "aws_apigatewayv2_domain_name" "domain" {
  count       = var.domain_name != "" ? 1 : 0
  domain_name = var.domain_name

  domain_name_configuration {
    certificate_arn = module.acm.certificate_arn
    endpoint_type   = "REGIONAL"
    security_policy = "TLS_1_2"
  }
}

resource "aws_apigatewayv2_api_mapping" "mapping" {
  count       = var.domain_name != "" ? 1 : 0
  api_id      = aws_apigatewayv2_api.websocket-api.id
  domain_name = aws_apigatewayv2_domain_name.domain[0].id
  stage       = aws_apigatewayv2_stage.api-stage.id
}

resource "aws_route53_record" "route53_record" {
  count   = var.domain_name != "" ? 1 : 0
  zone_id = data.aws_route53_zone.site_zone[0].id
  name    = aws_apigatewayv2_domain_name.domain[0].domain_name
  type    = var.record-type

  alias {
    name                   = aws_apigatewayv2_domain_name.domain[0].domain_name_configuration[0].target_domain_name
    zone_id                = aws_apigatewayv2_domain_name.domain[0].domain_name_configuration[0].hosted_zone_id
    evaluate_target_health = false
  }
}

# TODO: ここでacmを作成する
module "acm" {
  source = "../../modules/acm"
  region = "ap-northeast-1"
  project_name = var.project_name
  environment = var.environment
  custom_domains = ""
  profile_name = var.profile_name
  domain_hosted_zone_id = var.domain_name != "" ? data.aws_route53_zone.site_zone[0].id : ""
  external_domain_name = var.external_domain_name
  acm_domain_name = ""
  zone_map = {}
}
