const Define = require(process.env.COMMON_LAYER_PATH + 'define')
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js')
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js')
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js')
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body)
  console.log('params = ' + JSON.stringify(params))

  ctx.callbackWaitsForEmptyEventLoop = false
  console.log('get-notice-list')
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      return pool.rlsQuery(
        Base.extractTenantId(e),
        Define.QUERY.GET_STATIC_PAGE_LIST,
        [Base.extractTenantId(e), null]
      )
    })
    .then(results => {
      console.log('results = ', results)
      return Base.createSuccessResponse(cb, results)
    })
    .catch(error => Base.createErrorResponse(cb, error))
}
