const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('update-admin-role', params.permission);
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(async () => {
      console.log('UPDATE ADMIN ROLE');
      let update_count = 0;

      for (const permission of params) {
        const sqlParams = [
          Base.extractTenantId(e),
          permission.target_group_id,
          permission.function_id,
          permission.allowed_role_id,
          Base.extractAdminNo(e),
        ];

        const result = await pool.rlsQuery(
          Base.extractTenantId(e),
          Define.QUERY.UPDATE_ADMIN_ROLE,
          sqlParams
        );

        update_count += result[0].update_count || 0;
      }
      return update_count;
    })
    .then(result => {
      console.log(`result = ${JSON.stringify(result)}`);
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
