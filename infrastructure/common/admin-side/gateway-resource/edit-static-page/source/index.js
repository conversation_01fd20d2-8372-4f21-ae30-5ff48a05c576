const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool()

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body)
  console.log(`params = ${JSON.stringify(params)}`)

  ctx.callbackWaitsForEmptyEventLoop = false
  console.log('edit-notice')
  const rules = require('./validation-rules.js')

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const validateResult = Validator.checkRules(params, rules)
      console.log('validateResult: ', validateResult)
      // Validation Fail
      if (Object.keys(validateResult).length > 0) {
        return Promise.reject(Validator.createErrorResponse(validateResult))
      }
      return Promise.resolve()
    })
    .then(() => {
      // Check if the page_path already exists
      return pool
        .rlsQuery(Base.extractTenantId(e), Define.QUERY.GET_STATIC_PAGE_LIST, [
          Base.extractTenantId(e),
          params.page_path,
        ])
        .then(result => {
          if (
            result.length > 0 &&
            result.some(x => x.static_page_no !== params.static_page_no) // Check if the page_path is already used by another static page
          ) {
            return Promise.reject({
              status: 400,
              errors: {
                page_path: Define.MESSAGE.E010007,
              },
            })
          }
          return Promise.resolve()
        })
    })
    .then(() => {
      // Get language list
      // Update static page
      const adminNo = Base.extractAdminNo(e)
      return pool.rlsQuery(
        Base.extractTenantId(e),
        Define.QUERY.INSERT_OR_UPDATE_STATIC_PAGE,
        [
          Base.extractTenantId(e),
          params.static_page_no || 0,
          params.page_path,
          params.localized,
          adminNo,
          adminNo,
        ]
      )
    })
    .then(result => {
      console.log('result = ', result)
      return Base.createSuccessResponse(cb, result)
    })
    .catch(error => {
      const localizedError = error?.errors?.localized
      if (localizedError) {
        delete error.errors.localized
        Object.keys(localizedError).map(chiErr => {
          Object.keys(localizedError[chiErr]).map(ele => {
            const key = `${ele}_${params.localized[chiErr].language_code}`
            error.errors[key] = localizedError[chiErr][ele]
          })
        })
      }
      return Base.createErrorResponse(cb, error)
    })
}
