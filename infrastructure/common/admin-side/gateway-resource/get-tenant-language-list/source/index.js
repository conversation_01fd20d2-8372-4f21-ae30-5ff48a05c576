const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      const sqlParams = [tenantNo];
      return pool.rlsQuery(
        tenantNo,
        Define.QUERY.GET_TENANT_LANGUAGE_LIST_FUNCTION,
        sqlParams
      );
    })
    .then(data => {
      const ret = {
        language_code_list: data[0]?.language_code_list || [],
      };
      return Promise.resolve(ret);
    })
    .then(res => {
      return Base.createSuccessResponse(cb, res);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
