const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);

exports.handle = (e, ctx, cb) => {
  const Rules = require('./validation-rules');
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('update-tenant-setting', params.tenant);
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      console.log('Rules : ', Rules);
      const errlist = Validator.checkRules(params, Rules);
      console.log('errlist : ', errlist);
      const resErrors = {};
      if (errlist && Object.keys(errlist).length > 0) {
        const errMsg = Validator.convertErrorCodeToErrorMessage(errlist);
        for (const key of Object.keys(errMsg)) {
          console.log('log:  errMsg -> key: ', key);

          switch (key) {
            case 'tenant_name':
              resErrors[key] = Common.format(errMsg[key], ['テナント名', Rules[key].MAX_LENGTH]);
              break;
            case 'operator_name':
              resErrors[key] = Common.format(errMsg[key], ['運営者名', Rules[key].MAX_LENGTH]);
              break;
            case 'contact':
              resErrors[key] = Common.format(errMsg[key], ['メールアドレス']);
              break;
            case 'mfa_required':
              resErrors[key] = Common.format(errMsg[key], ['認証']);
              break;
            case 'currency':
              resErrors[key] = Common.format(errMsg[key], ['通貨']);
              break;
            case 'language':
              resErrors[key] = Common.format(errMsg[key], ['言語']);
              break;
          }
        }
      }
      if (resErrors && Object.keys(resErrors).length > 0) {
        return Promise.reject({
          status: 400,
          errors: resErrors,
        });
      }
      return Promise.resolve();
    })
    .then(() => {
      console.log('UPDATE TENANT SETTING');
      const sqlParams = [
        Base.extractTenantId(e),
        params.tenant_name,
        params.operator_name,
        params.contact,
        params.mfa_required,
        params.currency,
        params.language,
      ];
      return pool.rlsQuery(
        Base.extractTenantId(e),
        Define.QUERY.UPDATE_TENANT_SETTING_FUNCTION,
        sqlParams
      );
    })
    .then(result => {
      console.log(`result = ${JSON.stringify(result)}`);
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
