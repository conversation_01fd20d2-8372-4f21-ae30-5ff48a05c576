module.exports = {
  tenant_name: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 100,
    MAX_LENGTH_ERROR_MESSAGE: 'E100417',
  },
  operator_name: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 100,
    MAX_LENGTH_ERROR_MESSAGE: 'E100417',
  },
  contact: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    PATTERN_CHECK: true,
    PATTERN:
      /^[a-zA-Z0-9_.+-]+@([a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.)+[a-zA-Z]{2,}$/,
    PATTERN_ERROR_MESSAGE: 'E001019',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 256,
    MAX_LENGTH_ERROR_MESSAGE: 'E000714',
  },
  mfa_required: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
  },
  currency: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
  },
  language: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
  },
};
