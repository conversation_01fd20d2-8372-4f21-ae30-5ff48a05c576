const Define = require(process.env.COMMON_LAYER_PATH + 'define')
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js')
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js')
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js')
const pool = new PgPool(process.env.PGHOST)

exports.handle = function (e, ctx, cb) {
  console.log('event = ' + JSON.stringify(e))
  console.log('context = ' + JSON.stringify(ctx))

  const params = Base.parseRequestBody(e.body)

  ctx.callbackWaitsForEmptyEventLoop = false
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      // Get tenant_no
      const tenant_no = Base.extractTenantId(e)
      console.log('tenant_no = ' + tenant_no)
      // Get tenant options
      const sql = Define.QUERY.UPDATE_TENANT_OPTIONS
      return pool.rlsQuery(tenant_no, sql, [
        tenant_no,
        params.function_options,
        params.bid_options,
      ])
    })
    .then(result => {
      console.log('result: ', result)
      return Base.createSuccessResponse(cb, result)
    })
    .catch(error => Base.createErrorResponse(cb, error))
}
