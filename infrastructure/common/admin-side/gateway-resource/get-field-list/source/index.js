const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  console.log('log of event : ', e);
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      console.log('GET COUNT');
      const sqlParams = [tenantNo, params.resource_type, params.language_code];
      return pool.rlsQuery(
        tenantNo,
        Define.QUERY.GET_FIELD_LIST_COUNT,
        sqlParams
      );
    })
    .then(data => {
      console.log('SEARCH');

      let total_count = 0;
      if (data && data.length > 0) {
        total_count = data[0] && data[0].count ? data[0].count : 0;
      }

      if (total_count === 0) {
        return Promise.resolve({
          data: [],
          current_count: 0,
          total_count: 0,
        });
      }

      const sqlParams = [tenantNo, params.resource_type, params.language_code];
      return pool
        .rlsQuery(tenantNo, Define.QUERY.GET_FIELD_LIST_FUNCTION, sqlParams)
        .then(constants => {
          const ret = {
            data: constants,
            current_count: constants ? constants.length : 0,
            total_count: total_count,
          };
          return Promise.resolve(ret);
        });
    })
    .then(res => {
      return Base.createSuccessResponse(cb, res);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
