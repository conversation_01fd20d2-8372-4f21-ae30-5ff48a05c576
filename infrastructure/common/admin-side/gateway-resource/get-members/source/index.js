/* eslint-disable camelcase */
const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = (e, ctx, cb) => {
  console.log(
    '%c 🤼: exports.handle -> ctx ',
    'font-size:16px;background-color:#ebf5c4;color:black;',
    ctx
  );
  console.log(
    '%c 🛌: exports.handle -> e ',
    'font-size:16px;background-color:#0f2206;color:white;',
    e
  );
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('get-members');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      console.log('GET COUNT');
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_MEMBERS_COUNT_FUNCTION, [
        Base.extractTenantId(e),
        null,
        null,
        null,
        null,
      ]);
    })
    .then(data => {
      console.log('SEARCH');
      const totalCount =
        data && data.length > 0 && data[0]?.count ? data[0].count : 0;
      if (totalCount === 0) {
        return Promise.resolve({
          data: [],
          current_count: 0,
          total_count: 0,
        });
      }
      // Get member list
      return pool
        .rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_MEMBERS_FUNCTION, [
          Base.extractTenantId(e),
          params.memberId || null,
          params.companyName,
          params.customerCode,
          params.memberStatus,
        ])
        .then(members => {
          console.log('🎁members: ', members);
          const res = {
            data: members,
            current_count: members ? members.length : 0,
            total_count: totalCount,
          };
          return Promise.resolve(res);
        });
    })
    .then(data => {
      console.log(`data = ${JSON.stringify(data)}`);
      return Base.createSuccessResponse(cb, data);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
