variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "allow_origin" {
  description = "allow_origin"
}

variable "s3-bucket-arn" {
  description = "File upload bucket arn"
}

variable "s3-bucket-id" {
  description = "File upload bucket id"
}

variable "aws_api_gateway_rest_api_gateway_id" {
  description = "Aws api gateway rest api"
  default     = ""
}

variable "parent_id" {
  description = "Aws api gateway parent resource id"
  default     = ""
}

variable "parent_path" {
  description = "Aws api gateway parent path"
  default     = ""
}

variable "prefix_function_name" {
  description = "prefix_function_name"
  default     = ""
}

variable "aws_api_gateway_rest_api_gateway_execution_arn" {
  description = "Aws api gateway execution arn"
  default     = ""
}

variable "lambda_subnet_ids" {
  description = "lambda_subnet_ids"
  default       = []
}

variable "lambda_security_group_id" {
  description = "lambda_security_group_id"
  default     = ""
}

variable "lambda_global_environment_variables" {
  description = "lambda_global_environment_variables"
  default     = {}
}

variable "lambda_layer" {
  description = "lambda_layer"
  default     = []
}

variable "aws_api_gateway_authorizer_id" {
 description = "aws_api_gateway_authorizer_id"
}

variable "slack-notification-lambda-arn" {
  description = "slack-notification-lambda-arn"
}
