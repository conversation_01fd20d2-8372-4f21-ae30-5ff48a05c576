module.exports = {
  updateConstantRule: {
    field_division: {
      REQUIRED_CHECK: true,
      REQUIRED_ERROR_MESSAGE: 'E000351',
    },
    physical_name: {
      REQUIRED_CHECK: true,
      REQUIRED_ERROR_MESSAGE: 'E000352',
    },
    input_type: {
      REQUIRED_CHECK: true,
      REQUIRED_ERROR_MESSAGE: 'E000353',
    },
    data_type: {
      REQUIRED_CHECK: true,
      REQUIRED_ERROR_MESSAGE: 'E000354',
    },
    order_no: {
      REQUIRED_CHECK: true,
      REQUIRED_ERROR_MESSAGE: 'E000355',
    },
    language_code: {
      REQUIRED_CHECK: true,
      REQUIRED_ERROR_MESSAGE: 'E000356',
    },
    logical_name: {
      REQUIRED_CHECK: true,
      REQUIRED_ERROR_MESSAGE: 'E000357',
    },
    order_no: {
      PATTERN_CHECK: true,
      PATTERN: RegExp('^[0-9]{1,5}$'),
      PATTERN_ERROR_MESSAGE: 'E000358',
    },
    max_length: {
      PATTERN_CHECK: true,
      PATTERN: RegExp('^[0-9]{1,10}$'),
      PATTERN_ERROR_MESSAGE: 'E000361',
    },
    max_value: {
      PATTERN_CHECK: true,
      PATTERN: RegExp('^[0-9]{1,10}$'),
      PATTERN_ERROR_MESSAGE: 'E000362',
    },
  },
};
