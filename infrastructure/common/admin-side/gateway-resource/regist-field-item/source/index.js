const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = function (e, ctx, cb) {
  const Rules = require('./validation-rules');
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log(params);

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      let validateRule = {};
      validateRule = Object.assign(validateRule, Rules.updateConstantRule);

      return Validator.validation(params, validateRule)
        .then(() => {
          // 新規または更新のチェック
          const isUpdate = params.field_no && params.field_localized_no;

          // 使用するクエリ、パラメータの取得
          const queryName = isUpdate
            ? Define.QUERY.CHECK_FIELD_EXISTS_UPDATE
            : Define.QUERY.CHECK_FIELD_EXISTS;

          const queryParams = isUpdate
            ? [
                tenantNo,
                params.field_division,
                params.physical_name,
                params.field_no,
              ]
            : [
                tenantNo,
                params.field_division,
                params.physical_name,
                params.language_code,
              ];

          // 項目マスタの重複チェック
          return pool.rlsQuery(tenantNo, queryName, queryParams);
        })
        .then(existsResult => {
          if (existsResult && existsResult.length > 0) {
            is_exists =
              existsResult[0] && existsResult[0].is_exists
                ? existsResult[0].is_exists
                : false;
            if (is_exists) {
              const error = {
                status: 409,
                errors: {
                  duplicate_field: '指定された物理名は既に登録されています。',
                },
              };
              throw error;
            }
          }

          const sqlParams = [
            params.field_no,
            params.field_localized_no,
            tenantNo,
            params.field_division,
            params.physical_name,
            params.input_type,
            params.input_data_list,
            params.data_type,
            params.max_length,
            params.max_value,
            params.regular_expressions,
            params.required_flag,
            params.order_no,
            params.language_code,
            params.logical_name,
          ];
          return pool.rlsQuery(
            tenantNo,
            Define.QUERY.REGIST_UPDATE_FIELD_FUNCTION,
            sqlParams
          );
        });
    })
    .then(result => {
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => {
      if (error.status === 400 && error.errors.chilConstants) {
        const chilConstantsError = error.errors.chilConstants;
        delete error.errors.chilConstants;
        Object.keys(chilConstantsError).map(chiErr => {
          Object.values(chilConstantsError[chiErr]).map(ele => {
            const key = `${params.chilConstants[chiErr].languageCode}${Base.randomString(3)}`;
            error.errors[key] = Common.format(ele, [
              params.chilConstants[chiErr].language
                ? `${params.chilConstants[chiErr].language}の`
                : '',
            ]);
          });
        });
      }
      return Base.createErrorResponse(cb, error);
    });
};
