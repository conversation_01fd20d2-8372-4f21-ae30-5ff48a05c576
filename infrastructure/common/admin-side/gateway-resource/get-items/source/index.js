const Define = require(process.env.COMMON_LAYER_PATH + 'define');
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

async function countItem(e, sqlParams) {
  console.log('GET COUNT');
  const result = await pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_ITEMS_COUNT, sqlParams);
  let total_count = 0;
  if (result && result.length > 0) {
    total_count = result[0] && result[0].count ? result[0].count : 0;
  }
  return total_count;
}

async function getItemsList(e, sqlParams) {
  const items = await pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_ITEMS, sqlParams);
  return items;
}

async function getItems(e) {
  await Base.startRequest(e);
  await Base.checkAccessIpAddress(
    pool,
    e.requestContext.identity.sourceIp,
    Base.extractTenantId(e)
  );

  const params = Base.parseRequestBody(e.body);

  // TODO unreadMessage, requestNo : 未実装
  const unreadMessageArray =
    params.unread_message && Array.isArray(params.unread_message)
      ? params.unread_message.map(s => parseInt(s, 10)).filter(n => !isNaN(n))
      : [];
  const requestNoFrom = params.request_no?.from || null;
  const requestNoTo = params.request_no?.to || null;

  const statusArray =
    params.status.length > 0
      ? params.status.map(s => parseInt(s, 10)).filter(n => !isNaN(n))
      : [1, 2, 3, 4];
  const manageNoFrom = params.manage_no?.from || null;
  const manageNoTo = params.manage_no?.to || null;

  const sqlParams = [
    statusArray, // $1: in_status integer[]
    params.new_flag || false, // $2: in_new_flag boolean
    manageNoFrom, // $3: in_manage_no_from
    manageNoTo, // $4: in_manage_no_to
    null, // $5: in_serial_from (using request_no)
    null, // $6: in_serial_to (using request_no)
    params.area_id || null, // $7: in_area_id
    params.category || null, // $8: in_category
    params.maker || null, // $9: in_maker
    Base.extractTenantId(e), // $10: in_tenant_no
    Base.extractAdminLanguageCode(e), // $11: in_language_code
  ];
  console.log('log of sqlParams : ', sqlParams);

  const total_count = await countItem(e, sqlParams);
  const items = await getItemsList(e, sqlParams);
  console.log('log of GET_ITEMS : ', items);

  // 検索結果が1000件以上の場合にエラーメッセージを表示する
  if (items && items.length > 1000) {
    const err = {
      status: 400,
      name: 'ValidationError',
      message: 'E000610',
    };
    throw Validator.createErrorResponse(err);
  }

  const res = {
    data: items,
    current_count: items ? items.length : 0,
    total_count: total_count,
  };
  console.log('log of res : ', res);
  return res;
}

/**
 * JSDOC: FRONTEND DATA STRUCTURE (Numeric Arrays):
 * {
 *   "classification": string[],     // [1, 2] "1" = 出品代行, "2" = 自社商品
 *   "status": string[],             // [1, 2, 3, 4] "1" = 成約, "2" = 出品中, "3" = 出品中(キャンセル), "4" = 在庫
 *   "manage_no": { "from": string, "to": string },
 *   "request_no": { "from": string, "to": string },
 *   "exhibition_name": string,
 *   "unread_message": string[]      // [0, 1] "0" = 無, "1" = 有
 * }
 */
exports.handle = (e, ctx, cb) => {
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('get-items', e);
  getItems(e)
    .then(result => Base.createSuccessResponse(cb, result))
    .catch(error => {
      console.error('Error in get-items handler:', error);
      const statusCode = error.status || 500;
      const response = {
        status: statusCode,
        name: error.name || 'InternalServerError',
        message: error.message || Define.MESSAGE.E900001,
      };
      Base.createErrorResponse(cb, response);
    });
};
