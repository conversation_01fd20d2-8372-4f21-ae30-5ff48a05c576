module.exports = {
  page_path: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E010000',
    PATTERN_CHECK: true,
    PATTERN: /^[a-zA-Z0-9_/-]+$/,
    PATTERN_ERROR_MESSAGE: 'E010001',
  },
  localized: {
    LIST_CHECK: true,
    ELEMENT_RULES: {
      language_code: {
        REQUIRED_CHECK: true,
        REQUIRED_ERROR_MESSAGE: 'E010005',
      },
      page_name: {
        REQUIRED_CHECK: true,
        REQUIRED_ERROR_MESSAGE: 'E010002',
        MAX_LENGTH_CHECK: true,
        MAX_LENGTH: 254,
        MAX_LENGTH_ERROR_MESSAGE: 'E010003',
      },
      file_url: {
        REQUIRED_CHECK: true,
        REQUIRED_ERROR_MESSAGE: 'E010004',
      },
    },
  },
}
