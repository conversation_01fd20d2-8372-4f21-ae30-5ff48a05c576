const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool()

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body)
  console.log(`params = ${JSON.stringify(params)}`)

  const rules = require('./validation-rules.js')

  ctx.callbackWaitsForEmptyEventLoop = false
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const validateResult = Validator.checkRules(params, rules)
      console.log('validateResult: ', validateResult)
      // Validation Fail
      if (Object.keys(validateResult).length > 0) {
        return Promise.reject(Validator.createErrorResponse(validateResult))
      }
      return Promise.resolve()
    })
    .then(() => {
      // Check if the page_path already exists
      return pool
        .rlsQuery(Base.extractTenantId(e), Define.QUERY.GET_STATIC_PAGE_LIST, [
          Base.extractTenantId(e),
          params.page_path,
        ])
        .then(result => {
          if (result.length > 0) {
            return Promise.reject({
              status: 400,
              errors: {
                page_path: Define.MESSAGE.E010007,
              },
            })
          }
          return Promise.resolve()
        })
    })
    .then(() => {
      const admin_no = Base.extractAdminNo(e)
      const tenant_no = Base.extractTenantId(e)
      console.log(`admin_no = ${admin_no}`)
      console.log(`tenant_no = ${tenant_no}`)

      // Start insert data
      const sql = Define.QUERY.INSERT_OR_UPDATE_STATIC_PAGE
      const sql_params = [
        tenant_no,
        params.static_page_no,
        params.page_path,
        params.localized,
        admin_no,
        admin_no,
      ]
      return pool
        .rlsQuery(Base.extractTenantId(e), sql, sql_params)
        .then(result => {
          console.log('result = ', result)
          return Promise.resolve()
        })
    })
    .then(() => {
      return Base.createSuccessResponse(cb, {})
    })
    .catch(error => {
      const localizedError = error?.errors?.localized
      if (localizedError) {
        delete error.errors.localized
        Object.keys(localizedError).map(chiErr => {
          Object.keys(localizedError[chiErr]).map(ele => {
            const key = `${ele}_${params.localized[chiErr].language_code}`
            error.errors[key] = localizedError[chiErr][ele]
          })
        })
      }
      return Base.createErrorResponse(cb, error)
    })
}
