const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = function (e, ctx, cb) {
  const Rules = require('./validation-rules');
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  ctx.callbackWaitsForEmptyEventLoop = false;

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      let validateRule = {};
      validateRule = Object.assign(validateRule, Rules.updateConstantRule);

      return Validator.validation(params, validateRule).then(() => {
        const sqlParams = [
          params.field_no,
          params.field_localized_no,
          tenantNo,
        ];
        return pool.rlsQuery(
          tenantNo,
          Define.QUERY.DELETE_FIELD_ITEM_FUNCTION,
          sqlParams
        );
      });
    })
    .then(result => {
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => {
      if (error.status === 400 && error.errors.chilConstants) {
        const chilConstantsError = error.errors.chilConstants;
        delete error.errors.chilConstants;
        Object.keys(chilConstantsError).map(chiErr => {
          Object.values(chilConstantsError[chiErr]).map(ele => {
            const key = `${params.chilConstants[chiErr].languageCode}${Base.randomString(3)}`;
            error.errors[key] = Common.format(ele, [
              params.chilConstants[chiErr].language
                ? `${params.chilConstants[chiErr].language}の`
                : '',
            ]);
          });
        });
      }
      return Base.createErrorResponse(cb, error);
    });
};
