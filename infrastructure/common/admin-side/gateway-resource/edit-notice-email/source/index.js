const async = require('async');
const {DeleteObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool();

const client = new S3Client({region: process.env.AWS_REGION});

const updateNotice = (index, languageCode, notice, tenant_no, admin_no) => {
  const sendDatetime = `${notice.send_date} ${notice.send_time}`;
  // Update notice
  const sqlParams = [
    tenant_no,
    notice.notice_email_no,
    sendDatetime,
    0,
    languageCode,
    notice.title[index],
    notice.body_title_upper_row[index],
    notice.body_title_lower_row[index],
    notice.body[index],
    null,
    notice.file[index],
    admin_no,
    admin_no,
  ];
  return pool
    .rlsQuery(tenant_no,Define.QUERY.INSERT_OR_UPDATE_NOTICE_EMAIL_FUNCTION, sqlParams)
    .then(result => {
      console.log('result = ', JSON.stringify(result));
      // [{"f_insert_or_update_notice_email":215}]
      const noticeNo = result[0].f_insert_or_update_notice_email;
      return Promise.resolve(noticeNo);
    })
    .catch(error => {
      console.log('error', error);
      return Promise.resolve(null);
    });
};

// Delete all files
const deleteAllFiles = fileList => {
  // Check max length
  if (
    typeof fileList === 'undefined' ||
    fileList === null ||
    fileList.length <= 0
  ) {
    return Promise.resolve();
  }
  // Delete copied files
  return Promise.all(
    fileList.map(file => {
      const command = new DeleteObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: file,
      });
      return client.send(command);
    })
  )
    .then(res => {
      console.log('delete file from S3 done', res);
      return Promise.resolve();
    })
    .catch(err => {
      console.log('delete file from S3 error', err);
      return Promise.resolve();
    });
};

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);

  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('edit-notice-email');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      // Validation
      const validateResult = [];

      const notice = params.data;
      if (typeof notice === 'undefined' || notice === null) {
        validateResult.push(Define.MESSAGE.E000705);
      }
      // Send date empty
      if (
        typeof notice.send_date === 'undefined' ||
        notice.send_date === null ||
        notice.send_date === ''
      ) {
        validateResult.push(Define.MESSAGE.E000706);
      }
      // Send time empty
      if (
        typeof notice.send_time === 'undefined' ||
        notice.send_time === null ||
        notice.send_time === ''
      ) {
        validateResult.push(Define.MESSAGE.E000722);
      }
      // Send datetime invalid
      tmpDate = new Date(`${notice.send_date} ${notice.send_time}`);
      console.log(`tmpDate = ${tmpDate}`);
      if (tmpDate instanceof Date && !isNaN(tmpDate)) {
        // Compare to now
        const now = new Date(
          Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000
        );
        const compareWithNow = Date.parse(tmpDate) - Date.parse(now);
        if (compareWithNow < 0) {
          validateResult.push(Define.MESSAGE.E000718);
        }
      } else {
        validateResult.push(Define.MESSAGE.E000707);
      }

      // 日付が1年以内かのチェック
      const today = new Date(Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000);
      const maxDate = new Date(today);
      maxDate.setDate(today.getDate() + 364);
      maxDate.setHours(0, 0, 0, 0); // 時間をリセットして日付のみ取得
      console.log(`maxDate = ${maxDate}`)
      // 送信日のチェック
      const sendDate = new Date(`${notice.send_date}T00:00:00`);
      const sendDateDiff = maxDate - sendDate;
      if (sendDateDiff < 0) {
        validateResult.push(Define.MESSAGE.E000727);
      }

      // Get language name list
      const languages = notice.language_name;
      if (
        typeof languages === 'undefined' ||
        languages === null ||
        languages.length <= 0
      ) {
        validateResult.push(Define.MESSAGE.E000708);
      }

      // Title, Body check
      for (let i = 0; i < languages.length; i++) {
        if (!notice.title[i] || notice.title[i].trim().length === 0) {
          validateResult.push(
            Define.MESSAGE.E000723.replace(
              '#',
              `${languages.length > 1 ? `${languages[i]}の` : ''}タイトル`
            )
          );
        }
        if (notice.title[i] && notice.title[i].trim().length > 78) {
          validateResult.push(
            Define.MESSAGE.E000719.replace(
              '#',
              languages.length > 1 ? languages[i] : ''
            )
          );
        }
        if (!notice.body[i] || notice.body[i].trim().length === 0) {
          validateResult.push(
            Define.MESSAGE.E000723.replace(
              '#',
              `${languages.length > 1 ? `${languages[i]}の` : ''}本文`
            )
          );
        }
      }

      // Validation Fail
      if (validateResult.length > 0) {
        const error = {
          status: 400,
          message: validateResult,
        };
        return Promise.reject(error);
      }

      return Promise.resolve();
    })
    .then(() => {
      console.log('Validate sent flag');

      const notice = params.data;
      if (notice?.notice_email_no) {
        return Promise.resolve()
          .then(() => {
            console.log('Get original notice email');
            const sql = Define.QUERY.GET_NOTICE_EMAIL_FOR_VALIDATION_FUNCTION;
            return pool.rlsQuery(Base.extractTenantId(e),sql, [
              Base.extractTenantId(e),
              notice.notice_email_no,
            ]);
          })
          .then(notices => {
            console.log('notices: ', notices);
            const validateResult = [];
            const temp = notices || [];
            temp.map(noticeEmail => {
              if (noticeEmail && noticeEmail.sent_flag === 1) {
                validateResult.push(Define.MESSAGE.E000716);
              }
              if (noticeEmail && noticeEmail.delete_flag === 1) {
                validateResult.push(Define.MESSAGE.E000717);
              }
            });

            // Validation Fail
            if (validateResult.length > 0) {
              const error = {
                status: 400,
                message: validateResult,
              };
              return Promise.reject(error);
            }
            return Promise.resolve();
          });
      }
      return Promise.resolve();
    })
    .then(() => {
      // Validation successful
      if (params.validation_mode === true) {
        const response = {
          status: 200,
          message: '',
        };
        return Promise.reject(response);
      }
      return Promise.resolve();
    })
    .then(() => {
      const notice = params.data;
      // Get language list
      const languages = notice.language_code;
      if (!languages) {
        const error = {
          status: 400,
          message: Define.MESSAGE.E000708,
        };
        return Promise.reject(error);
      }
      // Updating notice data
      return Promise.all(
        languages.map((lang, index) => {
          return updateNotice(index, lang, notice, Base.extractTenantId(e), Base.extractAdminNo(e));
        })
      );
    })
    .then(noticeEmail => {
      // Then move or delete physical files
      return deleteAllFiles(params.delete_files).then(() => {
        return Promise.resolve(noticeEmail);
      });
    })
    .then(notice => {
      const response = {
        data: notice,
      };
      return Base.createSuccessResponse(cb, response);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
