const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
// const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);

const pool = new PgPool();

exports.handle = (e, ctx, cb) => {
  console.log('Received registration request:', e);
  const params = Base.parseRequestBody(e.body);
  console.log('log of params : ', params);

  ctx.callbackWaitsForEmptyEventLoop = false;
  const tenantId = Base.extractTenantId(e);
  console.log('log of tenantId : ', tenantId);

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() => {
      // return pool.rlsQuery(Base.extractTenantId(e),
      //   "SELECT * FROM t_member_request WHERE tenant_no = $1;",
      //   [tenantId]
      // );
      return tenantId;
    })
    .then(response => {
      console.log('Registration response:', response);
      const responseData = {
        tenantId,
        data: response,
      };
      return Base.createSuccessResponse(cb, responseData);
    })
    .catch(error => {
      console.error('Error during registration:', error);
      let response = {
        status: 500,
        error: 'Internal Server Error',
        message: 'An unexpected error occurred',
      };
      return Base.createErrorResponse(cb, response);
    });
};
