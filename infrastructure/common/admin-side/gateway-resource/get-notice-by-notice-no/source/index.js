const Define = require(process.env.COMMON_LAYER_PATH + 'define');
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  console.log('params = ' + JSON.stringify(params));

  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('get-notice-by-notice-no');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      // Get notice list
      const sql = Define.QUERY.GET_NOTICE_BY_NOTICE_NO_FUNCTION;
      const sql_params = [Base.extractTenantId(e), params['notice_no']];

      console.log('sql = ' + JSON.stringify(sql));
      console.log('sql_params = ' + JSON.stringify(sql_params));

      return pool.rlsQuery(Base.extractTenantId(e),sql, sql_params);
    })
    .then(notices => {
      const response = {
        data: notices,
      };
      console.log('notices = ' + JSON.stringify(notices));
      return Base.createSuccessResponse(cb, response);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
