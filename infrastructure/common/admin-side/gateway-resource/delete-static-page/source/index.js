const {DeleteObjectCommand, S3Client} = require('@aws-sdk/client-s3')
const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool()

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body)
  console.log(`params = ${JSON.stringify(params)}`)

  ctx.callbackWaitsForEmptyEventLoop = false
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      // Get file list
      return pool
        .rlsQuery(Base.extractTenantId(e), Define.QUERY.GET_STATIC_PAGE_LIST, [
          Base.extractTenantId(e),
          null,
        ])
        .then(pages => {
          console.log(`pages: ${JSON.stringify(pages)}`)
          if (pages && pages.length > 0) {
            console.log('fileList NULL')
            const fileList = pages
              .filter(x => x.static_page_no === params.static_page_no)
              .map(x => x.localized.map(y => y.file_url))
              .flat()
            return Promise.resolve(fileList)
          }
          // Return file list
          return Promise.resolve([])
        })
    })
    .then(fileList => {
      // Delete physical files
      console.log(`fileList: ${JSON.stringify(fileList)}`)
      if (
        typeof fileList === 'undefined' ||
        fileList === null ||
        fileList.length <= 0
      ) {
        console.log('fileList NULL')
        return Promise.resolve()
      }
      console.log('delete file from S3')
      const client = new S3Client({})
      // Delete copied files
      return Promise.all(
        fileList.map(file => {
          const command = new DeleteObjectCommand({
            Bucket: process.env.S3_BUCKET,
            Key: file,
          })
          return client.send(command)
        })
      )
        .then(res => {
          console.log('delete file from S3 done', res)
          return Promise.resolve()
        })
        .catch(err => {
          console.log('delete file from S3 error', err)
          return Promise.resolve()
        })
    })
    .then(() => {
      // Update delete flag
      const admin_no = Base.extractAdminNo(e)
      const sql = Define.QUERY.DELETE_STATIC_PAGE
      return pool.rlsQuery(Base.extractTenantId(e), sql, [
        Base.extractTenantId(e),
        params.static_page_no,
        admin_no,
      ])
    })
    .then(result => {
      console.log('result = ', result)
      return Base.createSuccessResponse(cb, result)
    })
    .catch(error => Base.createErrorResponse(cb, error))
}
