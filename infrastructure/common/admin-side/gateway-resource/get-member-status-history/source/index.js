const Define = require(process.env.COMMON_LAYER_PATH + 'define');
const Validator = require(process.env.COMMON_LAYER_PATH + 'validator.js');
const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  console.log('get-member-status-history');
  const params = Base.parseRequestBody(e.body);
  console.log('params = ' + JSON.stringify(params));
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      // Get tenant_no
      const tenant_no = Base.extractTenantId(e);
      console.log('tenant_no = ' + tenant_no);

      // Get member status change history
      const sql = Define.QUERY.GET_MEMBER_STATUS_HISTORY_FUNCTION;
      const sql_params = [tenant_no, params['member_request_no']];

      console.log('sql_params = ' + JSON.stringify(sql_params));
      console.log('sql = ' + JSON.stringify(sql));

      return pool.rlsQuery(Base.extractTenantId(e),sql, sql_params);
    })
    .then(histories => {
      console.log('histories = ' + JSON.stringify(histories));
      return Base.createSuccessResponse(cb, histories);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
