const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('update_tenant_credit_remaining');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const sqlParams = [
        Base.extractTenantId(e),
        params.is_bid_limit_flag,
        params.limit_amount,
        params.reset_type,
        params.reset_date,
        params.start_date,
        params.end_date,
        Base.extractAdminNo(e),
      ];
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.UPDATE_TENANT_CREDIT_REMAINING_FUNCTION, sqlParams);
    })
    .then(creditRemaining => {
      console.log(`creditRemaining = ${JSON.stringify(creditRemaining)}`);
      return Base.createSuccessResponse(cb, creditRemaining);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
