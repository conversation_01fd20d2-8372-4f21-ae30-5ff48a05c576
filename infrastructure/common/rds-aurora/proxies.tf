 resource "aws_secretsmanager_secret" "secret" {
  description         = "${var.environment}-${var.project_name}-secretsmanager-secret-database-proxies"
  name                = "${var.environment}-${var.project_name}-secretsmanager-secret-database-proxies"
}

resource "aws_secretsmanager_secret_version" "secret" {
  secret_id     = aws_secretsmanager_secret.secret.id
  secret_string = <<EOF
{
  "username": "${var.master_username}",
  "password": "${random_password.password.result}",
  "engine": "postgres",
  "host": "${aws_rds_cluster.database_cluster.endpoint}",
  "port": "${var.port}",
  "dbname": "${var.project_name}",
  "dbClusterIdentifier": "${aws_rds_cluster.database_cluster.cluster_identifier}"
  }
EOF
}

resource "aws_iam_role" "database_proxy_role" {
  name = "${var.environment}-${var.project_name}-database_proxy_role"

  assume_role_policy = data.aws_iam_policy_document.rds-trust-rel-policy.json
}

resource "aws_iam_role_policy" "database_proxy_role_policy" {
  name = "${var.environment}-${var.project_name}-logs-policy"
  role = aws_iam_role.database_proxy_role.id

  policy = data.aws_iam_policy_document.database_proxy_policy_document.json
}


resource "aws_db_proxy" "database_proxy" {
  name                   = "${var.environment}-${var.project_name}-db-proxy"
  debug_logging          = false
  engine_family          = "POSTGRESQL"
  idle_client_timeout    = 1800
  require_tls            = true
  role_arn               = aws_iam_role.database_proxy_role.arn
  vpc_security_group_ids = var.proxies_security_group_ids
  vpc_subnet_ids         = var.proxies_subnet_ids

  auth {
    auth_scheme = "SECRETS"
    description = "example"
    iam_auth    = "DISABLED"
    secret_arn  = aws_secretsmanager_secret.secret.arn
  }
}

resource "aws_db_proxy_default_target_group" "database_proxy_target_group" {
  db_proxy_name = aws_db_proxy.database_proxy.name

  connection_pool_config {
    connection_borrow_timeout    = 5
    max_connections_percent      = 100
    max_idle_connections_percent = 50
    session_pinning_filters      = []
  }
}

resource "aws_db_proxy_target" "database_proxy_target" {
  db_cluster_identifier  = aws_rds_cluster.database_cluster.id
  db_proxy_name          = aws_db_proxy.database_proxy.name
  target_group_name      = aws_db_proxy_default_target_group.database_proxy_target_group.name

  depends_on = [aws_rds_cluster_instance.aurora_cluster_instance]
}

resource "aws_db_proxy_endpoint" "read_only" {
  db_proxy_name          = aws_db_proxy.database_proxy.name
  db_proxy_endpoint_name = "${var.environment}-${var.project_name}-database-proxy-read-only"
  vpc_subnet_ids = var.proxies_subnet_ids
  vpc_security_group_ids = var.proxies_security_group_ids
  target_role            = "READ_ONLY"

  depends_on = [aws_db_proxy.database_proxy]
}
