const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool();

exports.handle = function (e, ctx, cb) {
  return Promise.resolve()
    .then(result => {
      return pool.rlsQuery('SELECT * FROM "f_batch_soldout_delete"();');
    })
    .then(result => {
      return cb(null, null);
    })
    .catch(error => {
      return Common.createErrorResponse(cb, error);
    });
};
