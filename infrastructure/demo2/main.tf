provider "aws" {
  region = "ap-northeast-1"
  profile = local.profile_name
}

terraform {
  backend "s3" {
    bucket         = "gmo-demo-saas-terraformed"
    encrypt        = true
    region         = "ap-northeast-1"
    key            = "demo2/terraform.tfstate"
    profile        = "saas-demo"
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.100.0"
    }
  }
}
