resource "aws_s3_bucket" "bucket" {
  bucket = "gmo-demo2-saas-auction-bucket" # dev-saas-bucketはすでに存在しているので、新しいバケット名を指定する
}

# resource "aws_s3_bucket_acl" "bucket_acl" {
#   bucket = aws_s3_bucket.bucket.id
#   acl    = "private"
# }

resource "aws_s3_bucket_ownership_controls" "bucket_acl_replacement" {
  bucket = aws_s3_bucket.bucket.id

  rule {
    object_ownership = "ObjectWriter"
  }
}

resource "aws_s3_bucket_cors_configuration" "cors_config" {
  bucket = aws_s3_bucket.bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = [
            "GET",
            "PUT",
            "POST",
            "DELETE"
    ]
    expose_headers  = ["ETag"]
    allowed_origins = ["*"]
    max_age_seconds = 3000
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "lifecycle_rule_config" {
  bucket = aws_s3_bucket.bucket.id

  rule {
    id     = "clear-download-file-rule"
    status = "Enabled"

    filter {
      prefix = "csv-download/"
    }

    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }

    expiration {
      days = 7
    }
  }
}

resource "aws_s3_bucket_policy" "s3_access_policy" {
  bucket = aws_s3_bucket.bucket.id

  policy = data.aws_iam_policy_document.s3_policy.json
}
