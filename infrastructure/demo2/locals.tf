locals {
  project_name              = "saas"
  environment               = "demo2"
  profile_name              = "saas-demo"
  basic_auth_enable         = true
  admin_basic_auth_enable   = false
  external_domain_name      = false
  record_name               = ""
  auction_domain_name       = ""
  admin_domain_name         = ""
  web_socket_domain         = ""
  mail_from_domain          = ""
  nat_gateway_amount        = 1
  rds_postgres_instance_class = "db.t4g.medium" # 最小限 db.t4g.medium
  api_gateway_5xx_alarm_slack_hook_url = "*******************************************************************************"

  admin_lambda_global_environment_variables = {
    "JWT_KEY": "1111073198427807137232231262089010020123587350290187348031312887018790938534411834407306789251908665247269492987924177522229233751002949748257274877665617761140060607452136458097592753547729486234167197152010786269050707025330716247130358546954092845095476",
    "MAX_RESPONSE_SIZE": "5300000"
  }

  auction_lambda_global_environment_variables = {
    "JWT_KEY": "3454073198427807137232231262089010020123587350290187348031312887016756756734411834407306789251908665247269492987924177522229233751002949748257274877665617761140060607452136458097592753547729486234167197152010786269050707025330716247130358546954092845095476",
    "MAX_RESPONSE_SIZE": "5300000"
  }

  admin_acm_domain_name = ""
  admin_tenants = {}
  admin_custom_domains = []


  auction_tenants = {}
  auction_custom_domains = []
  auction_acm_domain_name = ""
}
