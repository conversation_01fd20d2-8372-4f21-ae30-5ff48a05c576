data "aws_iam_policy_document" "s3_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = [
      "${aws_s3_bucket.bucket.arn}/client/admin/*",
      "${aws_s3_bucket.bucket.arn}/item-ancillary/*"
      ]

    principals {
      type        = "AWS"
      identifiers = [module.common.admin_side_aws_cloudfront_origin_access_identity_iam_arn]
    }

  }

  statement {
    actions   = ["s3:GetObject"]
    resources = [
      "${aws_s3_bucket.bucket.arn}/client/auction/*",
      "${aws_s3_bucket.bucket.arn}/static/*",
      "${aws_s3_bucket.bucket.arn}/public/*",
      "${aws_s3_bucket.bucket.arn}/constant/*",
      "${aws_s3_bucket.bucket.arn}/item-ancillary/*",
    ]

    principals {
      type        = "AWS"
      identifiers = [module.common.auction_side_aws_cloudfront_origin_access_identity_iam_arn]
    }

  }
}
