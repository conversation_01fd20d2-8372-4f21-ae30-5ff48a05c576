{"name": "demo-server", "version": "1.0.0", "description": "test", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon app.js", "ec2": "pm2 start app.js", "deploy-ec2": "rsync -ravze \"ssh -i ../infrastructure/demo/key-pair/demo-saas-ec2-access-key.id_rsa\"  --exclude 'node_modules' ./*  ubuntu@**************:/home/<USER>/www", "restart-pm2": "ssh -i ../infrastructure/demo/key-pair/demo-saas-ec2-access-key.id_rsa ubuntu@************** 'pm2 restart all'"}, "author": "chuc", "license": "ISC", "devDependencies": {"body-parser": "^1.20.2", "morgan": "^1.10.0", "nodemon": "^3.1.4", "stylelint": "^14.8.2", "stylelint-config-standard": "^25.0.0"}, "dependencies": {"async": "^3.2.2", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "express": "^4.17.1", "fs-extra": "^10.1.0", "http": "^0.0.1-security", "iconv-lite": "^0.6.3", "json2csv": "^5.0.6", "jsonwebtoken": "^8.5.1", "zlib": "^1.0.5"}}