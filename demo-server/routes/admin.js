import express from "express";
import { dashboardData } from "./sample-data/admin-side/dashboard.js";
import { getAdminByAdminNo } from "./sample-data/admin-side/get-admin-by-admin-no.js";
import { getAdminList } from "./sample-data/admin-side/get-admin-list.js";
import { getBatchResultDetail } from "./sample-data/admin-side/get-batch-result-detail.js";
import { getBatchResultList } from "./sample-data/admin-side/get-batch-result-list.js";
import { getBidLimitList } from "./sample-data/admin-side/get-bid-limit-list.js";
import { getChatRequestDetail } from "./sample-data/admin-side/get-chat-request-detail.js";
import { getChat } from "./sample-data/admin-side/get-chat.js";
import { getConstantKey } from "./sample-data/admin-side/get-constant-key.js";
import { getConstantList } from "./sample-data/admin-side/get-constant-list.js";
import { getConstantsByKeys } from "./sample-data/admin-side/get-constants-by-keys.js";
import { getConstantsLanguageList } from "./sample-data/admin-side/get-constants-language-list.js";
import { getEmailNotificationList } from "./sample-data/admin-side/get-email-notification-list.js";
import { getExhibitionByName } from "./sample-data/admin-side/get-exhibition-by-name.js";
import { getExhibitionInformation } from "./sample-data/admin-side/get-exhibition-information.js";
import { getExhibitionNamePulldown } from "./sample-data/admin-side/get-exhibition-name-pulldown.js";
import { getExhibitionPulldown } from "./sample-data/admin-side/get-exhibition-pulldown.js";
import { getExhibitionStatusList } from "./sample-data/admin-side/get-exhibition-status-list.js";
import { getExhibitionSummary } from "./sample-data/admin-side/get-exhibition-summary.js";
import { getExhibitions } from "./sample-data/admin-side/get-exhibitions.js";
import { getItemDetail } from "./sample-data/admin-side/get-item-detail.js";
import { getItemMappingList } from "./sample-data/admin-side/get-item-mapping-list.js";
import { getItems } from "./sample-data/admin-side/get-items.js";
import { getLots } from "./sample-data/admin-side/get-lots.js";
import { getMemberStatusHistory } from "./sample-data/admin-side/get-member-status-history.js";
import { getMember } from "./sample-data/admin-side/get-member.js";
import { getMembers } from "./sample-data/admin-side/get-members.js";
import { getNoticeByNoticeNo } from "./sample-data/admin-side/get-notice-by-notice-no.js";
import { getNoticeEmailByNoticeEmailNo } from "./sample-data/admin-side/get-notice-email-by-notice-email-no.js";
import { getNoticeEmailList } from "./sample-data/admin-side/get-notice-email-list.js";
import { getNoticeList } from "./sample-data/admin-side/get-notice-list.js";
import { getPermissionList } from "./sample-data/admin-side/get-permission-list.js";
import { getPostageList } from "./sample-data/admin-side/get-postage-list.js";
import { getRequestDetail } from "./sample-data/admin-side/get-request-detail.js";
import { getRequestHistory } from "./sample-data/admin-side/get-request-history.js";
import { getRequest } from "./sample-data/admin-side/get-request.js";
import { getResourceList } from "./sample-data/admin-side/get-resource-list.js";
import { getStaticPageList } from "./sample-data/admin-side/get-static-page-list.js";
import { getStaticPage } from "./sample-data/admin-side/get-static-page.js";
import { getUseBouncesList } from "./sample-data/admin-side/get-use-bounces-list.js";
import { searchExhibitions } from "./sample-data/admin-side/search-exhibitions.js";

const router = express.Router();

router.post("/login", (req, res) => {
  console.log("request: ");

  const admin = {
    login_id: "oec",
    password: "TEST1234",
  };
  if (
    req.body.login_id === admin.login_id &&
    req.body.password === admin.password
  ) {
    res.json({
      admin_no: 1,
      tenant_no: 1,
      admin_name: "OEC",
      role_id: "10",
      admin_language_code: "ja",
      language_code_list: "ja,en",
      token:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9ubyI6MSwidGVuYW50X25vIjoxLCJhZG1pbl9uYW1lIjoiT0VDIiwicm9sZV9pZCI6IjEwIiwiYWRtaW5fbGFuZ3VhZ2VfY29kZSI6ImphIiwibGFuZ3VhZ2VfY29kZV9saXN0IjoiamEsZW4iLCJpYXQiOjE3Mzg1NjMyMDgsImV4cCI6MTczODYwNjQwOH0.gBt3QG80OeTpF5bcJ3Y2D7K-MJ3CYetoLJme3ysMlUU",
    });
  } else {
    res.status(400).json({
      status: 400,
      errors: { password: "パスワードを8文字以上入力してください。" },
    });
  }
});

router.post("/get-exhibition-pulldown", (req, res) => {
  const data = getExhibitionPulldown;
  res.status(200).json(data);
});

router.post("/get-dashboard", (req, res) => {
  const data = dashboardData;
  res.status(200).json(data);
});

router.post("/get-admin-list", (req, res) => {
  const data = getAdminList;
  res.status(200).json(data);
});

router.post("/get-admin-by-admin-no", (req, res) => {
  const data = getAdminByAdminNo;
  res.status(200).json(data);
});

router.post("/get-constants-by-keys", (req, res) => {
  const data = getConstantsByKeys || [];
  const language_code = req.body.language_code || "ja";
  const key_strings = req.body.key_strings;
  const ret = data.filter(
    (item) =>
      (item.language_code === language_code ||
        item.language_code === "common") &&
      key_strings.includes(item.key_string),
  );
  res.status(200).json(ret);
});

router.post("/get-constants-by-keys-language", (req, res) => {
  const data = getConstantsByKeys || [];
  const key_strings = req.body.key_strings;
  const ret = data.filter((item) => key_strings.includes(item.key_string));
  res.status(200).json(ret);
});

router.post("/get-members", (req, res) => {
  const data = getMembers;
  res.status(200).json(data);
});

router.post("/get-member", (req, res) => {
  const data = getMember; // .filter(item => item.member_request_no === req.body.member_request_no)
  res.status(200).json(data);
});

router.post("/update-member-status", (req, res) => {
  res.status(200).json({});
});

router.post("/update-member", (req, res) => {
  res.status(200).json({ status: 200, message: null });
});

router.post("/get-member-status-history", (req, res) => {
  const data = getMemberStatusHistory;
  res.status(200).json(data);
});

router.post("/get-exhibitions", (req, res) => {
  const data = getExhibitions;

  if (req.body.exhibition_no) {
    res.status(200).json(data.filter((item) => item.exhibition_no === 301));
  } else {
    res.status(200).json(data);
  }
});

router.post("/upsert-exhibitions", (req, res) => {
  res.status(200).json({ status: 200, message: null });
});

router.post("/get-lots", (req, res) => {
  const data = getLots;
  res.status(200).json(data);
});

router.post("/search-exhibitions", (req, res) => {
  const data = searchExhibitions;
  res.status(200).json(data);
});

router.post("/get-notice-list", (req, res) => {
  const data = getNoticeList;
  res.status(200).json(data);
});

router.post("/get-notice-by-notice-no", (req, res) => {
  const data = getNoticeByNoticeNo;
  res.status(200).json(data);
});

router.post("/edit-notice", (req, res) => {
  res.status(200).json({ status: 200, message: null });
});

router.post("/delete-notice", (req, res) => {
  res.status(200).json({ data: [{ f_delete_notice: 14 }] });
});

router.post("/get-email-notification-list", (req, res) => {
  const data = getEmailNotificationList;
  res.status(200).json(data);
});

router.post("/get-use-bounces-list", (req, res) => {
  const data = getUseBouncesList;
  res.status(200).json(data);
});

router.post("/get-notice-email-list", (req, res) => {
  const data = getNoticeEmailList;
  res.status(200).json(data);
});

router.post("/get-notice-email-by-notice-email-no", (req, res) => {
  const data = getNoticeEmailByNoticeEmailNo;
  res.status(200).json(data);
});

router.post("/edit-notice-email", (req, res) => {
  const data = {
    message: "システムエラーが発生しました。",
  };
  res.status(200).json(data);
});

router.post("/get-exhibition-status-list", (req, res) => {
  const data = getExhibitionStatusList;
  res.status(200).json(data);
});

router.post("/get-exhibition-summary", (req, res) => {
  const data = getExhibitionSummary;
  res.status(200).json(data);
});

router.post("/get-exhibition-by-name", (req, res) => {
  const data = getExhibitionByName;
  res.status(200).json(data);
});

router.post("/update-negotiation-flag", (req, res) => {
  res.status(200).json({ status: 200, message: null });
});

router.post("/get-batch-result-list", (req, res) => {
  const data = getBatchResultList;
  res.status(200).json(data);
});

router.post("/get-batch-result-detail", (req, res) => {
  const data = getBatchResultDetail;
  res.status(200).json(data);
});

router.post("/get-postage-list", (req, res) => {
  const data = getPostageList;
  res.status(200).json(data);
});

router.post("/get-request", (req, res) => {
  const data = getRequest;
  res.status(200).json(data);
});

router.post("/get-request-detail", (req, res) => {
  const request_no = getRequest.data.find(
    (x) => String(x.detail_request_no) === String(req.body.detail_request_no),
  )?.request_no;
  const data = getRequestDetail.find((x) => x.request_no === request_no);
  res.status(200).json(data);
});

router.post("/get-request-history", (req, res) => {
  const data = getRequestHistory;
  res.status(200).json(data);
});

router.post("/get-exhibition-name-pulldown", (req, res) => {
  const data = getExhibitionNamePulldown;
  res.status(200).json(data);
});

router.post("/get-exhibition-information", (req, res) => {
  const data = req.body.exhibitionName
    ? getExhibitionInformation[1]
    : getExhibitionInformation[0];
  res.status(200).json([data]);
});

router.post("/get-exhibition-email-language-list", (req, res) => {
  const data = [];
  res.status(200).json(data);
});

router.post("/regist-request-result", (req, res) => {
  const data = {
    status: 200,
    message: null,
  };
  res.status(200).json(data);
});

router.post("/get-chat-request-detail", (req, res) => {
  const data = getChatRequestDetail;
  res.status(200).json(data);
});

router.post("/get-chat", (req, res) => {
  const data = getChat;
  res.status(200).json(data);
});

router.post("/get-constant-key", (req, res) => {
  const data = getConstantKey;
  res.status(200).json(data);
});

router.post("/get-constant-list", (req, res) => {
  const data = getConstantList;
  res.status(200).json(data);
});

router.post("/get-constants-language-list", (req, res) => {
  const data = getConstantsLanguageList;
  res.status(200).json(data);
});

router.post("/delete-constant", (req, res) => {
  const data = {
    status: 200,
    message: null,
  };
  res.status(200).json(data);
});

router.post("/regist-constants-language-list", (req, res) => {
  const data = {
    status: 200,
    message: null,
  };
  res.status(200).json(data);
});

router.post("/get-bid-history", (req, res) => {
  const data = {
    url: "https://coreui.io/vue/docs/images/vue.jpg",
  };
  res.status(200).json(data);
});

router.post("/get-bid-order", (req, res) => {
  const data = {
    url: "https://coreui.io/vue/docs/images/vue.jpg",
  };
  res.status(200).json(data);
});

router.post("/get-bid-result", (req, res) => {
  const data = {
    url: "https://coreui.io/vue/docs/images/vue.jpg",
  };
  res.status(200).json(data);
});

router.post("/export-member-csv-file", (req, res) => {
  const data = {
    url: "https://coreui.io/vue/docs/images/vue.jpg",
  };
  res.status(200).json(data);
});

router.post("/import-member-csv-file", (req, res) => {
  const data = {
    status: 200,
    message: null,
  };
  res.status(200).json(data);
});

router.post("/get-aws-credentials", (req, res) => {
  const data = {
    region: "ap-northeast-1",
    bucket: "bucket",
    prefix_key: "prefix_key",
    credentials: {
      accessKeyId: "accessKeyId",
      secretAccessKey: "secretAccessKey",
      sessionToken: "sessionToken",
    },
  };
  res.status(200).json(data);
});

router.post("/get-resource-list", (req, res) => {
  const data = getResourceList;
  res.status(200).json(data);
});

router.post("/create-resource-item", (req, res) => {
  const data = {
    status: 200,
    message: null,
  };
  res.status(200).json(data);
});

router.post("/get-permission-list", (req, res) => {
  const data = getPermissionList;
  res.status(200).json(data);
});

router.post("/get-item-mapping-list", (req, res) => {
  const data = getItemMappingList;
  res.status(200).json(data);
});

router.post("/get-item-detail", (req, res) => {
  const data = getItemDetail;
  res.status(200).json(data);
});

router.post("/get-bid-limit-list", (req, res) => {
  const data = getBidLimitList;
  res.status(200).json(data);
});

router.post("/get-static-page-list", async (req, res) => {
  const data = getStaticPageList;
  res.status(200).json(data);
});

router.post("/delete-static-page", async (req, res) => {
  res.status(200).json({ status: 200, message: null });
});

router.post("/get-static-page", (req, res) => {
  const data = getStaticPage;
  res.status(200).json(data);
});

router.post("/get-exhibition-pulldown-for-items", (req, res) => {
  const data = getExhibitionPulldown;
  res.status(200).json(data);
});
router.post("/get-items", (req, res) => {
  const data = getItems;
  res.status(200).json(data);
});
export default router;
