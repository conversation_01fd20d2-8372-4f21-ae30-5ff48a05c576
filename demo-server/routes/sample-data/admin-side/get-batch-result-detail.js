export const getBatchResultDetail = [
  {
    'error_details_field' : [
      {
        'product_id'              : '615',
        'start_price'             : '200000',
        'ubrand_code'             : '100902',
        'end_datetime'            : '2024/10/09 18:00',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/10/09 13:35',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'              : '614',
        'start_price'             : '200000',
        'ubrand_code'             : '1009',
        'end_datetime'            : '2024/10/09 10:50',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/10/09 10:40',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'              : '613',
        'start_price'             : '200000',
        'ubrand_code'             : '1007',
        'end_datetime'            : '2024/10/09 10:40',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/10/09 10:30',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'              : '612',
        'start_price'             : '200000',
        'ubrand_code'             : '1007',
        'end_datetime'            : '2024/10/07 17:45',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/10/07 17:35',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'              : '611',
        'start_price'             : '20000',
        'ubrand_code'             : '20241007_001',
        'end_datetime'            : '2024/10/07 17:12',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/10/07 16:30',
        'hasRecommendation'       : '有',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '450000'
      },
      {
        'product_id'              : '610',
        'start_price'             : '200000',
        'ubrand_code'             : '1007',
        'end_datetime'            : '2024/10/07 16:30',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/10/07 16:00',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'              : '607',
        'start_price'             : '200000',
        'ubrand_code'             : '060602',
        'end_datetime'            : '2024/06/06 14:30',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/06/06 13:00',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'              : '606',
        'start_price'             : '200000',
        'ubrand_code'             : '060601',
        'end_datetime'            : '2024/06/06 14:30',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/06/06 13:00',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'    : '605',
        'start_price'   : '200000',
        'ubrand_code'   : '060305',
        'end_datetime'  : '2024/06/04 18:00',
        'error_message' : [
          '閲覧開始日時は現在日時より後の日時を指定して下さい。',
          '閲覧終了日時は現在日時より後の日時を指定して下さい。',
          '開始日時は現在日時より後の日時を指定して下さい。',
          '延長最大時間は現在日時より後の日時を指定してください。'
        ],
        'start_datetime'          : '2024/06/03 17:05',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '2024-06-07 00:00',
        'preview_start_datetime'  : '2024-06-03 17:00',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'    : '604',
        'start_price'   : '200000',
        'ubrand_code'   : '060304',
        'end_datetime'  : '2024/06/04 18:00',
        'error_message' : [
          '閲覧開始日時は現在日時より後の日時を指定して下さい。',
          '閲覧終了日時は現在日時より後の日時を指定して下さい。',
          '開始日時は現在日時より後の日時を指定して下さい。',
          '延長最大時間は現在日時より後の日時を指定してください。'
        ],
        'start_datetime'          : '2024/06/03 17:05',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '2024-06-07 00:00',
        'preview_start_datetime'  : '2024-06-03 17:00',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'    : '603',
        'start_price'   : '200000',
        'ubrand_code'   : '060303',
        'end_datetime'  : '2024/06/03 18:00',
        'error_message' : [
          '閲覧開始日時は現在日時より後の日時を指定して下さい。',
          '閲覧終了日時は現在日時より後の日時を指定して下さい。',
          '開始日時は現在日時より後の日時を指定して下さい。',
          '延長最大時間は現在日時より後の日時を指定してください。'
        ],
        'start_datetime'          : '2024/06/03 17:05',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '2024-06-07 00:00',
        'preview_start_datetime'  : '2024-06-03 17:00',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'    : '602',
        'start_price'   : '200000',
        'ubrand_code'   : '060302',
        'end_datetime'  : '2024/06/03 18:00',
        'error_message' : [
          '閲覧開始日時は現在日時より後の日時を指定して下さい。',
          '閲覧終了日時は現在日時より後の日時を指定して下さい。',
          '開始日時は現在日時より後の日時を指定して下さい。',
          '延長最大時間は現在日時より後の日時を指定してください。'
        ],
        'start_datetime'          : '2024/06/03 17:05',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '2024-06-07 00:00',
        'preview_start_datetime'  : '2024-06-03 17:00',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'    : '601',
        'start_price'   : '200000',
        'ubrand_code'   : '060301',
        'end_datetime'  : '2024/06/03 18:00',
        'error_message' : [
          '閲覧開始日時は現在日時より後の日時を指定して下さい。',
          '閲覧終了日時は現在日時より後の日時を指定して下さい。',
          '開始日時は現在日時より後の日時を指定して下さい。',
          '延長最大時間は現在日時より後の日時を指定してください。'
        ],
        'start_datetime'          : '2024/06/03 17:05',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '2024-06-07 00:00',
        'preview_start_datetime'  : '2024-06-03 17:00',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'              : '599',
        'start_price'             : '200000',
        'ubrand_code'             : 'kanri2024-05-17',
        'end_datetime'            : '2024/05/22 18:15',
        'error_message'           : ['開始日時は現在日時より後の日時を指定して下さい。'],
        'start_datetime'          : '2024/05/22 18:05',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '',
        'preview_start_datetime'  : '',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'    : '598',
        'start_price'   : '200000',
        'ubrand_code'   : 'kanri2024-05-17',
        'end_datetime'  : '2024/05/17 18:15',
        'error_message' : [
          '閲覧開始日時は現在日時より後の日時を指定して下さい。',
          '閲覧終了日時は現在日時より後の日時を指定して下さい。',
          '開始日時は現在日時より後の日時を指定して下さい。',
          '延長最大時間は現在日時より後の日時を指定してください。'
        ],
        'start_datetime'          : '2024/05/17 18:05',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '2024-05-17 23:00',
        'preview_start_datetime'  : '2024-05-17 18:00',
        'lowest_bid_accept_price' : '400000'
      },
      {
        'product_id'    : '597',
        'start_price'   : '200000',
        'ubrand_code'   : 'kanri2024-05-14',
        'end_datetime'  : '2024/05/14 16:30',
        'error_message' : [
          '閲覧開始日時は現在日時より後の日時を指定して下さい。',
          '閲覧終了日時は現在日時より後の日時を指定して下さい。',
          '開始日時は現在日時より後の日時を指定して下さい。',
          '延長最大時間は現在日時より後の日時を指定してください。'
        ],
        'start_datetime'          : '2024/05/14 16:05',
        'hasRecommendation'       : '無',
        'preview_end_datetime'    : '2024-05-14 20:00',
        'preview_start_datetime'  : '2024-05-14 16:00',
        'lowest_bid_accept_price' : '400000'
      },
    ]
  }
]
