export const getConstantKey = [
  {
    'key_string' : 'AUCTION_END_COUNTDOWN_CONSTANT'
  },
  {
    'key_string' : 'CHAT_INQUIRY_EMAIL_TEMPLATE_FOR_ADMIN'
  },
  {
    'key_string' : 'CHAT_INQUIRY_EMAIL_TEMPLATE_FOR_MEMBER'
  },
  {
    'key_string' : 'COUNTRY_CODE'
  },
  {
    'key_string' : 'DEFAULT_PITCH_WIDTH'
  },
  {
    'key_string' : 'EMAIL_BID_RECEPTION_FOR_MEMBER'
  },
  {
    'key_string' : 'EMAIL_CHANGE_MEMBER_INFO_FOR_ADMIN'
  },
  {
    'key_string' : 'EMAIL_CHANGE_MEMBER_INFO_FOR_MEMBER'
  },
  {
    'key_string' : 'EMAIL_COMMON_FOOTER'
  },
  {
    'key_string' : 'EMAIL_EXHIBITION_AUCTION_END_FOR_MEMBER'
  },
  {
    'key_string' : 'EMAIL_EXHIBITION_AUCTION_END_FOR_MEMBER_POSTAGE'
  },
  {
    'key_string' : 'EMAIL_FORGOT_PASSWORD_FOR_MEMBER'
  },
  {
    'key_string' : 'EMAIL_INQUIRY_REQUEST_FOR_ADMIN'
  },
  {
    'key_string' : 'EMAIL_INQUIRY_REQUEST_FOR_MEMBER'
  },
  {
    'key_string' : 'EMAIL_MEMBER_REQUEST_APPROVED_FOR_MEMBER'
  },
  {
    'key_string' : 'EMAIL_MEMBER_REQUEST_FOR_ADMIN'
  },
  {
    'key_string' : 'EMAIL_MEMBER_REQUEST_FOR_MEMBER'
  },
  {
    'key_string' : 'EMAIL_MEMBER_WITHDRAWAL_FOR_ADMIN'
  },
  {
    'key_string' : 'EMAIL_MEMBER_WITHDRAWAL_FOR_MEMBER'
  },
  {
    'key_string' : 'EMAIL_NOTICE_PRIORITY'
  },
  {
    'key_string' : 'EMAIL_TOP_HAS_CHANGED_FOR_MEMBER'
  },
  {
    'key_string' : 'END_OPTION'
  },
  {
    'key_string' : 'EXHIBITION_AUCTION_NOTICE_EMAIL'
  },
  {
    'key_string' : 'EXHIBITION_EMAIL'
  },
  {
    'key_string' : 'EXTEND_FLAG'
  },
  {
    'key_string' : 'EXTEND_JUDGE_MINUTE'
  },
  {
    'key_string' : 'EXTEND_MINUTE'
  },
  {
    'key_string' : 'FIRMED_OPTIONS'
  },
  {
    'key_string' : 'MAKER_BRAND_TYPE'
  },
  {
    'key_string' : 'MAX_EXTEND_DATETIME'
  },
  {
    'key_string' : 'MEMBER_SEX'
  },
  {
    'key_string' : 'MEMBER_STATUS_CODE'
  },
  {
    'key_string' : 'MORE_LITTELE_JUDGE_DISPLAY'
  },
  {
    'key_string' : 'NEW_MARK_DISPLAY_DATE'
  },
  {
    'key_string' : 'NOTICE_EMAIL'
  },
  {
    'key_string' : 'NOTICE_PRIORITY'
  },
  {
    'key_string' : 'PITCH_FOLLOW_BID_PRICE'
  },
  {
    'key_string' : 'PITCH_OPTION'
  },
  {
    'key_string' : 'PITCH_WIDTH'
  },
  {
    'key_string' : 'PRODUCT_BRAND'
  },
  {
    'key_string' : 'PRODUCT_CATEGORY'
  },
  {
    'key_string' : 'PRODUCT_MODEL'
  },
  {
    'key_string' : 'PRODUCT_POSTAGE'
  },
  {
    'key_string' : 'RECOMMEND_DISPLAY_DATE'
  },
  {
    'key_string' : 'SEARCH_DATETIME_FROM'
  },
  {
    'key_string' : 'SEND_MAIL_TO_USER_BEFORE_AUCTION_END'
  },
  {
    'key_string' : 'SOLD_OUT_DISPLAY_DATE'
  },
  {
    'key_string' : 'TAX_RATE'
  }
]
