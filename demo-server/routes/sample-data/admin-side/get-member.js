export const getMember = [
  {
    'tenant_no'             : 1,
    'member_no'             : 107,
    'member_id'             : '00107',
    'member_request_no'     : 124,
    'member_request_type'   : null,
    'currency_id'           : 'USD',
    'exhibition_allow_flag' : 0,
    'bid_allow_flag'        : 1,
    'email_delivery_flag'   : 1,
    'email_priority'        : 0,
    'free_field'            : {
      'tel'                     : '9029445400',
      'lang'                    : 'en',
      'email'                   : '<EMAIL>',
      'wechat'                  : 'wechat1',
      'ceoName'                 : 'Taro',
      'country'                 : 'IS',
      'whatsApp'                : 'whatapp1',
      'companyHp'               : 'yokohama',
      'emailLang'               : 'en',
      'invoiceNo'               : '',
      'memberName'              : 'Taro1',
      'ceoBirthday'             : '2023-05-05',
      'ceoNameKana'             : '',
      'companyName'             : 'Taro Company',
      'emailConfirm'            : '<EMAIL>',
      'personalInfo'            : '',
      'companyAddress'          : 'Yokohama',
      'memberLastName'          : '',
      'telCountryCode'          : '81',
      'antiquePermitNo'         : '',
      'businessContent'         : '',
      'companyNameKana'         : '',
      'antiquePermitDate'       : '',
      'establishmentDate'       : '2020-01-05',
      'antiquePermitCommission' : ''
    },
    'status' : 1
  }
]
