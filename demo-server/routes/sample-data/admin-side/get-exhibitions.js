export const getExhibitions = [
  {
    'row_number'           : 1,
    'exhibition_no'        : 301,
    'tenant_no'            : 1,
    'category_id'          : 0,
    'pitch_option'         : 1,
    'end_option'           : 0,
    'localized_json_array' : [
      {
        'f1' : 'en',
        'f2' : 'Seriage test 1'
      },
      {
        'f1' : 'ja',
        'f2' : '競上テスト１'
      }
    ],
    'preview_start_datetime'         : '2025-01-31 15:22',
    'preview_end_datetime'           : '2025-05-09 15:23',
    'start_datetime'                 : '2025-01-31 15:23',
    'end_datetime'                   : '2025-05-08 15:23',
    'status'                         : '落札結果未確定',
    'max_extend_datetime'            : null,
    'extend_judge_minutes'           : null,
    'extend_minutes'                 : null,
    'currency_id'                    : 'USD',
    'pitch_width'                    : 1,
    'more_little_judge_pitch'        : 5,
    'bid_count'                      : 22,
    'exhibition_item_count'          : 42,
    'all_exhibition_item_count'      : 42,
    'contract_count'                 : 7,
    'exhibition_classification_info' : {
      'bidLimit'                   : 2,
      'dealFlag'                   : 0,
      'extendFlag'                 : 0,
      'bidShortFlag'               : 1,
      'bidCancelFlag'              : 0,
      'bidCountLimit'              : 0,
      'bidMinUseFlag'              : 1,
      'showBidCountFlag'           : 1,
      'topReductionFlag'           : 1,
      'openClassification'         : 1,
      'bidCommitMinUseFlag'        : 1,
      'auctionClassification'      : 1,
      'littleMoreDisplayFlag'      : 1,
      'bidCommitClassification'    : 2,
      'bidCommitMinDisplayFlag'    : 1,
      'bidderCommitClassification' : 1
    }
  },
  {
    'row_number'           : 2,
    'exhibition_no'        : 300,
    'tenant_no'            : 1,
    'category_id'          : 0,
    'pitch_option'         : 1,
    'end_option'           : 0,
    'localized_json_array' : [
      {
        'f1' : 'en',
        'f2' : 'Fuin test 1'
      },
      {
        'f1' : 'ja',
        'f2' : '封印テスト１'
      }
    ],
    'preview_start_datetime'         : '2025-01-31 13:21',
    'preview_end_datetime'           : '2025-01-31 13:51',
    'start_datetime'                 : '2025-01-31 13:22',
    'end_datetime'                   : '2025-01-31 13:50',
    'status'                         : '落札結果確定済',
    'max_extend_datetime'            : null,
    'extend_judge_minutes'           : null,
    'extend_minutes'                 : null,
    'currency_id'                    : 'USD',
    'pitch_width'                    : 1,
    'more_little_judge_pitch'        : null,
    'bid_count'                      : 20,
    'exhibition_item_count'          : 24,
    'all_exhibition_item_count'      : null,
    'contract_count'                 : 18,
    'exhibition_classification_info' : {
      'bidLimit'                   : 0,
      'dealFlag'                   : 0,
      'extendFlag'                 : 0,
      'bidShortFlag'               : 1,
      'bidCancelFlag'              : 1,
      'bidCountLimit'              : 0,
      'bidMinUseFlag'              : 1,
      'showBidCountFlag'           : 1,
      'topReductionFlag'           : 0,
      'openClassification'         : 1,
      'bidCommitMinUseFlag'        : 1,
      'auctionClassification'      : 2,
      'littleMoreDisplayFlag'      : 0,
      'bidCommitClassification'    : 1,
      'bidCommitMinDisplayFlag'    : 1,
      'bidderCommitClassification' : 1
    }
  },
  {
    'row_number'           : 3,
    'exhibition_no'        : 299,
    'tenant_no'            : 1,
    'category_id'          : 0,
    'pitch_option'         : 1,
    'end_option'           : 0,
    'localized_json_array' : [
      {
        'f1' : 'en',
        'f2' : 'Demo Fuin 2'
      },
      {
        'f1' : 'ja',
        'f2' : '封印テスト２'
      }
    ],
    'preview_start_datetime'         : '2025-01-31 10:39',
    'preview_end_datetime'           : '2025-02-08 10:40',
    'start_datetime'                 : '2025-01-31 10:40',
    'end_datetime'                   : '2025-02-07 10:41',
    'status'                         : '落札結果未確定',
    'max_extend_datetime'            : null,
    'extend_judge_minutes'           : null,
    'extend_minutes'                 : null,
    'currency_id'                    : 'USD',
    'pitch_width'                    : 1,
    'more_little_judge_pitch'        : null,
    'bid_count'                      : 11,
    'exhibition_item_count'          : 36,
    'all_exhibition_item_count'      : 36,
    'contract_count'                 : 4,
    'exhibition_classification_info' : {
      'bidLimit'                   : 0,
      'dealFlag'                   : 0,
      'extendFlag'                 : 0,
      'bidShortFlag'               : 1,
      'bidCancelFlag'              : 1,
      'bidCountLimit'              : 0,
      'bidMinUseFlag'              : 1,
      'showBidCountFlag'           : 1,
      'topReductionFlag'           : 0,
      'openClassification'         : 1,
      'bidCommitMinUseFlag'        : 1,
      'auctionClassification'      : 2,
      'littleMoreDisplayFlag'      : 0,
      'bidCommitClassification'    : 1,
      'bidCommitMinDisplayFlag'    : 1,
      'bidderCommitClassification' : 1
    }
  },
  {
    'row_number'           : 4,
    'exhibition_no'        : 298,
    'tenant_no'            : 1,
    'category_id'          : 0,
    'pitch_option'         : 1,
    'end_option'           : 0,
    'localized_json_array' : [
      {
        'f1' : 'en',
        'f2' : 'Demo Seriagari 2'
      },
      {
        'f1' : 'ja',
        'f2' : '競上テスト2'
      }
    ],
    'preview_start_datetime'         : '2025-01-31 09:41',
    'preview_end_datetime'           : '2025-01-31 21:43',
    'start_datetime'                 : '2025-01-31 09:42',
    'end_datetime'                   : '2025-01-31 15:43',
    'status'                         : '落札結果確定済',
    'max_extend_datetime'            : null,
    'extend_judge_minutes'           : null,
    'extend_minutes'                 : null,
    'currency_id'                    : 'USD',
    'pitch_width'                    : 1,
    'more_little_judge_pitch'        : 8,
    'bid_count'                      : 21,
    'exhibition_item_count'          : 23,
    'all_exhibition_item_count'      : null,
    'contract_count'                 : 2,
    'exhibition_classification_info' : {
      'bidLimit'                   : 2,
      'dealFlag'                   : 0,
      'extendFlag'                 : 0,
      'bidShortFlag'               : 1,
      'bidCancelFlag'              : 0,
      'bidCountLimit'              : 0,
      'bidMinUseFlag'              : 1,
      'showBidCountFlag'           : 0,
      'topReductionFlag'           : 1,
      'openClassification'         : 1,
      'bidCommitMinUseFlag'        : 1,
      'auctionClassification'      : 1,
      'littleMoreDisplayFlag'      : 1,
      'bidCommitClassification'    : 2,
      'bidCommitMinDisplayFlag'    : 1,
      'bidderCommitClassification' : 1
    }
  },
]
