export const getNoticeList = {
  'data' : [
    {
      'notice_no'      : 34,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2025/01/14',
      'end_datetime'   : '2025/01/16',
      'language_code'  : 'ja',
      'body'           : '<h1 class="ql-align-justify">電話番号は半角数値チェック</h1><p class="ql-align-justify"><strong>国コード：半角チェック</strong></p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 34,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2025/01/14',
      'end_datetime'   : '2025/01/16',
      'language_code'  : 'en',
      'body'           : '<h1 class="ql-align-justify">電話番号は半角数値チェック</h1><p class="ql-align-justify"><strong>国コード：半角チェック</strong></p><p class="ql-align-justify">対象画面：</p><p>入札サイト(会員申請、会員情報編集)、</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 24,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/12/12',
      'end_datetime'   : '2024/12/12',
      'language_code'  : 'ja',
      'body'           : '<h1><s>テスト</s></h1><p><u>お知らせテスト<span class="ql-cursor">﻿﻿﻿﻿</span></u></p><p><br></p><p><u style="background-color: rgb(0, 138, 0);">あ</u></p><p><br></p><p><u style="background-color: rgb(255, 255, 255); color: rgb(0, 71, 178);">あ<span class="ql-cursor">﻿﻿</span></u></p><p><br></p><ol><li><u style="background-color: rgb(255, 255, 255); color: rgb(0, 71, 178);">あ</u></li><li><u style="background-color: rgb(255, 255, 255); color: rgb(0, 71, 178);">い</u></li><li><u style="background-color: rgb(255, 255, 255); color: rgb(0, 71, 178);">う</u></li></ol>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 24,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2024/12/12',
      'end_datetime'   : '2024/12/12',
      'language_code'  : 'en',
      'body'           : '<p>お知らせテスト</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 14,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/12/02',
      'end_datetime'   : '2024/12/04',
      'language_code'  : 'ja',
      'body'           : '<p>test</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 14,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2024/12/02',
      'end_datetime'   : '2024/12/04',
      'language_code'  : 'en',
      'body'           : '<p>test</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 15,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/12/02',
      'end_datetime'   : '2024/12/02',
      'language_code'  : 'ja',
      'body'           : '<p>test2</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 15,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2024/12/02',
      'end_datetime'   : '2024/12/02',
      'language_code'  : 'en',
      'body'           : '<p>test2</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 12,
      'tenant_no'      : 1,
      'display_code'   : '重要',
      'start_datetime' : '2024/11/26',
      'end_datetime'   : '2024/12/02',
      'language_code'  : 'ja',
      'body'           : '<p>1126test</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 12,
      'tenant_no'      : 1,
      'display_code'   : 'Emergency',
      'start_datetime' : '2024/11/26',
      'end_datetime'   : '2024/12/02',
      'language_code'  : 'en',
      'body'           : '<p>1126test</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 10,
      'tenant_no'      : 1,
      'display_code'   : '重要',
      'start_datetime' : '2024/11/20',
      'end_datetime'   : '2025/11/20',
      'language_code'  : 'ja',
      'body'           : '<p><span style="color: rgb(0, 0, 0);">お客様各位</span></p><p><br></p><p><span style="color: rgb(255, 0, 0);">平素よりゲオモバイル 格安SIM・モバイルネットをご利用いただき、誠にありがとうございます。</span></p><p><span style="color: rgb(255, 0, 0);">SIMのみ契約者限定キャンペーンの特典選択において、中古Apple Watchに下記機種が追加されます。</span></p><p><br></p><p><span style="color: rgb(0, 0, 0);">■追加機種</span></p><p><span style="color: rgb(0, 0, 0);">・Apple Watch Series8 41mm／45mm GPSモデル (アルミニウム)</span></p><p><span style="color: rgb(0, 0, 0);">・Apple Watch SE 第2世代 GPSモデル</span></p><p><span style="color: rgb(0, 0, 0);">40mm／44mm (アルミニウム)</span></p><p><br></p><p><span style="color: rgb(0, 0, 0);">■概要</span></p><p><span style="color: rgb(0, 0, 0);">SIMのみ契約のお客様は「選べる電子マネープレゼント」または「中古Apple Watch同時購入」どちらかをお選びできます。</span></p><p><br></p><p><span style="color: rgb(0, 0, 0);">※在庫状況により、ご希望の端末がお選びいただけない場合がございます。</span></p><p><span style="color: rgb(0, 0, 0);">※中古Apple Watchを特典として選択した場合は割引額において差額が生じます。</span></p><p><span style="color: rgb(0, 0, 0);">※最低でも1円のお会計が発生いたします。</span></p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 10,
      'tenant_no'      : 1,
      'display_code'   : 'Emergency',
      'start_datetime' : '2024/11/20',
      'end_datetime'   : '2025/11/20',
      'language_code'  : 'en',
      'body'           : "<p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">Dear Customers</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">Thank you for using Geo Mobile's low-cost SIM and mobile internet.</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">The following models will be added to the used Apple Watch when selecting a bonus for the campaign limited to SIM-only contract customers.</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">■Additional models</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">・Apple Watch Series 8 41mm/45mm GPS model (aluminum)</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">・Apple Watch SE 2nd generation GPS model</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">40mm/44mm (aluminum)</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">■Overview</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">Customers with SIM-only contracts can choose either \"Selectable electronic money gift\" or \"Used Apple Watch simultaneous purchase\".</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">*Depending on stock availability, you may not be able to select the device you want.</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">*If you select a used Apple Watch as a bonus, there will be a difference in the discount amount.</span></p><p><br></p><p><span style=\"color: rgb(60, 64, 67); background-color: rgb(245, 245, 245);\">*A minimum of 1 yen will be charged.</span></p>",
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 13,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/11/20',
      'end_datetime'   : '2024/11/29',
      'language_code'  : 'ja',
      'body'           : '<p><span style="color: rgba(37, 43, 54, 0.95);">本</span></p><p><span style="color: rgba(37, 43, 54, 0.95);">文</span></p>',
      'link_url'       : '',
      'file'           : ['public/20241129102251-Eu3pty3stl/無題.png']
    },
    {
      'notice_no'      : 13,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2024/11/20',
      'end_datetime'   : '2024/11/29',
      'language_code'  : 'en',
      'body'           : '<p>本</p><p>文</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 9,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/11/13',
      'end_datetime'   : '2024/11/13',
      'language_code'  : 'ja',
      'body'           : '<p>test</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 9,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2024/11/13',
      'end_datetime'   : '2024/11/13',
      'language_code'  : 'en',
      'body'           : '<p>test</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 8,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/11/08',
      'end_datetime'   : '2024/11/08',
      'language_code'  : 'ja',
      'body'           : '<p>sdfsdfdfgdgryhtgfdgsfascxc</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 8,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2024/11/08',
      'end_datetime'   : '2024/11/08',
      'language_code'  : 'en',
      'body'           : '<p>abfdhbcfbhfgh</p>',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 3,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/10/28',
      'end_datetime'   : '2024/10/31',
      'language_code'  : 'ja',
      'body'           : '<p>tewerttyyasgdfgxfhxdfbfdxg</p><p>dsfgdsfhfghgfhtgfあ</p><p><br></p><p>あ</p>',
      'link_url'       : '',
      'file'           : ['public/20241028094154-1HcMrpb6PG/iphone1.jpg']
    },
    {
      'notice_no'      : 3,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2024/10/28',
      'end_datetime'   : '2024/10/31',
      'language_code'  : 'en',
      'body'           : '',
      'link_url'       : '',
      'file'           : []
    },
    {
      'notice_no'      : 1,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/10/25',
      'end_datetime'   : '2024/11/09',
      'language_code'  : 'ja',
      'body'           : '<p>this is test 11111111</p>',
      'link_url'       : '',
      'file'           : ['public/20241025185824-yKoemCN9g7/iphone2.jpg']
    },
    {
      'notice_no'      : 2,
      'tenant_no'      : 1,
      'display_code'   : '普通',
      'start_datetime' : '2024/10/25',
      'end_datetime'   : '2024/10/31',
      'language_code'  : 'ja',
      'body'           : '<p>This is test 222344555</p>',
      'link_url'       : '',
      'file'           : [
        'public/20241025193957-aKYIecn9hr/iphone1.jpg',
        'public/20241025194002-mMDogIUblk/iphone3.jpg'
      ]
    },
    {
      'notice_no'      : 2,
      'tenant_no'      : 1,
      'display_code'   : 'Normal',
      'start_datetime' : '2024/10/25',
      'end_datetime'   : '2024/10/31',
      'language_code'  : 'en',
      'body'           : '',
      'link_url'       : '',
      'file'           : []
    }
  ],
  'current_count' : 23,
  'total_count'   : 12
}
