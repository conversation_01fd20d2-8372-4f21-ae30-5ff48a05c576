export const getNoticeByNoticeNo = {
  'data' : [
    {
      'notice_no'         : 34,
      'tenant_no'         : 1,
      'display_code'      : 1,
      'display_code_name' : '普通',
      'start_datetime'    : '2025/01/14',
      'end_datetime'      : '2025/01/16',
      'language_code'     : 'ja',
      'title'             : 'お知らせの装飾チェック',
      'title1'            : '',
      'sub_title'         : '',
      'body'              : '<h1 class="ql-align-justify">電話番号は半角数値チェック</h1><p class="ql-align-justify"><strong>国コード：半角チェック</strong></p><p class="ql-align-justify">対象画面：</p><p>入札サイト(会員申請、会員情報編集)、</p><p class="ql-align-justify">管理サイト(新規会員登録、会員情報編集、エクセルアップロード)</p><p class="ql-align-justify">国コードの場合は以下のような実装済み：</p><p class="ql-align-justify">正しいインプット：</p><p class="ql-align-justify"><br></p><h1 class="ql-align-justify">Heading</h1><h1 class="ql-align-justify">Bold heading 1</h1><h2 class="ql-align-justify">Bold heading  2</h2><h3 class="ql-align-justify"><span style="color: rgba(37, 43, 54, 0.95);">Bold heading  3</span></h3><h4 class="ql-align-justify">Bold heading 4</h4><h5 class="ql-align-justify">Bold heading5</h5><h6 class="ql-align-justify">Bold heading 6</h6><p class="ql-align-justify"><br></p><p class="ql-align-justify"><strong>test bold text</strong></p><p class="ql-align-justify"><em>test italic text</em></p><p class="ql-align-justify"><u>test underline text </u></p><p class="ql-align-justify"><s>test throught strike</s></p><p class="ql-align-justify"><br></p><ol><li class="ql-align-justify">ordered 1</li><li class="ql-align-justify">ordered 2</li><li class="ql-align-justify">ordered 3</li></ol><p class="ql-align-justify"><br></p><ul><li class="ql-align-justify">bullet 1</li><li class="ql-align-justify">bullet 2</li><li class="ql-align-justify">bullet 3</li></ul><p class="ql-align-justify"><br></p><p><span style="color: rgb(255, 0, 0);">Test color</span></p><p><span style="color: rgb(255, 255, 0);">Test color</span></p><p><span style="color: rgb(0, 138, 0);">Test color</span></p><p><span style="color: rgba(37, 43, 54, 0.95);">Test color</span></p><p><br></p><p><span style="background-color: rgb(255, 0, 0);">test background</span></p><p><span style="color: rgba(37, 43, 54, 0.95); background-color: rgb(255, 153, 0);">test background</span></p><p><span style="color: rgba(37, 43, 54, 0.95); background-color: rgb(0, 102, 204);">test background</span></p><p><span style="color: rgba(37, 43, 54, 0.95); background-color: rgb(194, 133, 255);">test background</span></p><p><span style="color: rgba(37, 43, 54, 0.95); background-color: rgb(102, 102, 0);">test background</span></p><p class="ql-align-justify"><span style="background-color: rgb(230, 0, 0);">Hello123!</span></p><p class="ql-align-justify"><br></p><p class="ql-align-justify"><a href="https://www.google.co.jp" rel="noopener noreferrer" target="_blank">link</a></p><p class="ql-align-justify"><br></p><p class="ql-align-justify">Test link video</p><p class="ql-align-justify"><br></p><iframe class="ql-video" frameborder="0" allowfullscreen="true" src="https://www.youtube.com/embed/8Ebqe2Dbzls?showinfo=0"></iframe><p class="ql-align-justify"><br></p><p class="ql-align-justify"><br></p><p class="ql-align-justify"><br></p><ul><li class="ql-indent-1 ql-align-justify">あいうえお</li><li class="ql-indent-1 ql-align-justify">アイウエオ</li><li class="ql-align-justify">正しくないインプット</li><li class="ql-indent-1 ql-align-justify">ＡＢＣＤＥ</li><li class="ql-indent-1 ql-align-justify">１２３４５６</li></ul><p><br></p>',
      'link_url'          : '',
      'file'              : []
    },
    {
      'notice_no'         : 34,
      'tenant_no'         : 1,
      'display_code'      : 1,
      'display_code_name' : 'Normal',
      'start_datetime'    : '2025/01/14',
      'end_datetime'      : '2025/01/16',
      'language_code'     : 'en',
      'title'             : 'Check about deco',
      'title1'            : '',
      'sub_title'         : '',
      'body'              : '<h1 class="ql-align-justify">電話番号は半角数値チェック</h1><p class="ql-align-justify"><strong>国コード：半角チェック</strong></p><p class="ql-align-justify">対象画面：</p><p>入札サイト(会員申請、会員情報編集)、</p><p class="ql-align-justify">管理サイト(新規会員登録、会員情報編集、エクセルアップロード)</p><p class="ql-align-justify">国コードの場合は以下のような実装済み：</p><p class="ql-align-justify">正しいインプット：</p><p class="ql-align-justify"><br></p><h1 class="ql-align-justify">Heading</h1><h1 class="ql-align-justify">Bold heading 1</h1><h2 class="ql-align-justify">Bold heading 2</h2><h3 class="ql-align-justify"><span style="color: rgba(37, 43, 54, 0.95);">Bold heading 3</span></h3><h4 class="ql-align-justify">Bold heading 4</h4><h5 class="ql-align-justify">Bold heading5</h5><h6 class="ql-align-justify">Bold heading 6</h6><p class="ql-align-justify"><br></p><p class="ql-align-justify"><strong>test bold text</strong></p><p class="ql-align-justify"><em>test italic text</em></p><p class="ql-align-justify"><u>test underline text </u></p><p class="ql-align-justify"><s>test throught strike</s></p><p class="ql-align-justify"><br></p><ol><li class="ql-align-justify">ordered 1</li><li class="ql-align-justify">ordered 2</li><li class="ql-align-justify">ordered 3</li></ol><p class="ql-align-justify"><br></p><ul><li class="ql-align-justify">bullet 1</li><li class="ql-align-justify">bullet 2</li><li class="ql-align-justify">bullet 3</li></ul><p class="ql-align-justify"><br></p><p><span style="color: rgb(255, 0, 0);">Test color</span></p><p><span style="color: rgb(255, 255, 0);">Test color</span></p><p><span style="color: rgb(0, 138, 0);">Test color</span></p><p><span style="color: rgba(37, 43, 54, 0.95);">Test color</span></p><p><br></p><p><span style="background-color: rgb(255, 0, 0);">test background</span></p><p><span style="background-color: rgb(255, 153, 0); color: rgba(37, 43, 54, 0.95);">test background</span></p><p><span style="background-color: rgb(0, 102, 204); color: rgba(37, 43, 54, 0.95);">test background</span></p><p><span style="background-color: rgb(194, 133, 255); color: rgba(37, 43, 54, 0.95);">test background</span></p><p><span style="background-color: rgb(102, 102, 0); color: rgba(37, 43, 54, 0.95);">test background</span></p><p class="ql-align-justify"><span style="background-color: rgb(230, 0, 0);">Hello123!</span></p><p class="ql-align-justify"><br></p><p class="ql-align-justify"><a href="https://www.google.co.jp" rel="noopener noreferrer" target="_blank">link</a></p><p class="ql-align-justify"><br></p><p class="ql-align-justify">Test link video</p><p class="ql-align-justify"><br></p><iframe class="ql-video" frameborder="0" allowfullscreen="true" src="https://www.youtube.com/embed/8Ebqe2Dbzls?showinfo=0"></iframe><p class="ql-align-justify"><br></p><p class="ql-align-justify"><br></p><p class="ql-align-justify"><br></p><p class="ql-align-justify"><br></p><ul><li class="ql-align-justify ql-indent-1">あいうえお</li><li class="ql-align-justify ql-indent-1">アイウエオ</li><li class="ql-align-justify">正しくないインプット</li><li class="ql-align-justify ql-indent-1">ＡＢＣＤＥ</li><li class="ql-align-justify ql-indent-1">１２３４５６</li></ul><p><br></p>',
      'link_url'          : '',
      'file'              : []
    }
  ]
}
