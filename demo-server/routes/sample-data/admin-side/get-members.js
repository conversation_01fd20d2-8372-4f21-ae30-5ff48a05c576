export const getMembers = {
  'data' : [
    {
      'tenant_no'         : 1,
      'member_request_no' : 67,
      'member_no'         : null,
      'member_id'         : '未採番',
      'free_field'        : {
        'memo'           : '',
        'ceoName'        : '太郎第1',
        'companyName'    : 'ABC株式会社',
        'customerCode'   : '*********',
        'companyAddress' : 'ABC株式会社'
      },
      'bid_allow_flag'       : null,
      'email_delivery_flag'  : null,
      'status'               : 0,
      'last_user_name'       : null,
      'last_update_datetime' : null,
      'create_admin_no'      : 1,
      'create_datetime'      : '2024/12/22 12:15',
      'update_admin_no'      : 1,
      'update_datetime'      : '2024/12/24',
      'last_login_datetime'  : '',
      'sort_create_datetime' : '2024/12/22 12:15:21.766'
    },
    {
      'tenant_no'         : 1,
      'member_request_no' : 126,
      'member_no'         : null,
      'member_id'         : '未採番',
      'free_field'        : {
        'memo'           : '',
        'ceoName'        : '太郎第2',
        'companyName'    : 'A2株式会社',
        'customerCode'   : '',
        'companyAddress' : 'fsd'
      },
      'bid_allow_flag'       : null,
      'email_delivery_flag'  : null,
      'status'               : 0,
      'last_user_name'       : null,
      'last_update_datetime' : null,
      'create_admin_no'      : 1,
      'create_datetime'      : '2025/01/14 12:55',
      'update_admin_no'      : 1,
      'update_datetime'      : '2025/01/15',
      'last_login_datetime'  : '',
      'sort_create_datetime' : '2025/01/14 12:55:45.956'
    },
    {
      'tenant_no'         : 1,
      'member_request_no' : 13,
      'member_no'         : 13,
      'member_id'         : '13',
      'free_field'        : {
        'memo'           : '',
        'ceoName'        : 'Tarou To',
        'companyName'    : 'XYZ Company',
        'customerCode'   : 'test3',
        'companyAddress' : 'New York'
      },
      'bid_allow_flag'       : 1,
      'email_delivery_flag'  : 1,
      'status'               : 1,
      'last_user_name'       : 'OEC',
      'last_update_datetime' : '2024/11/06',
      'create_admin_no'      : 1,
      'create_datetime'      : '2024/11/06 18:09',
      'update_admin_no'      : 1,
      'update_datetime'      : '2024/11/30',
      'last_login_datetime'  : '',
      'sort_create_datetime' : '2024/11/06 18:09:28.102'
    },
    {
      'tenant_no'         : 1,
      'member_request_no' : 127,
      'member_no'         : 109,
      'member_id'         : '00109',
      'free_field'        : {
        'memo'           : '備考１２３４',
        'ceoName'        : '太郎第３',
        'companyName'    : 'A3株式会社',
        'customerCode'   : '',
        'companyAddress' : '横浜市神奈川区神奈川本町'
      },
      'bid_allow_flag'       : 1,
      'email_delivery_flag'  : 0,
      'status'               : 1,
      'last_user_name'       : 'OEC',
      'last_update_datetime' : '2025/01/15',
      'create_admin_no'      : 1,
      'create_datetime'      : '2025/01/15 14:29',
      'update_admin_no'      : 1,
      'update_datetime'      : '2025/01/15',
      'last_login_datetime'  : '',
      'sort_create_datetime' : '2025/01/15 14:29:32.706'
    },
    {
      'tenant_no'         : 1,
      'member_request_no' : 95,
      'member_no'         : null,
      'member_id'         : '未採番',
      'free_field'        : {
        'memo'           : '',
        'ceoName'        : 'John Doe',
        'companyName'    : 'Company 4',
        'customerCode'   : '',
        'companyAddress' : 'tokyo'
      },
      'bid_allow_flag'       : null,
      'email_delivery_flag'  : null,
      'status'               : 0,
      'last_user_name'       : null,
      'last_update_datetime' : null,
      'create_admin_no'      : null,
      'create_datetime'      : '2024/12/26 10:29',
      'update_admin_no'      : null,
      'update_datetime'      : '2024/12/26',
      'last_login_datetime'  : '',
      'sort_create_datetime' : '2024/12/26 10:29:30.361'
    },
    {
      'tenant_no'         : 1,
      'member_request_no' : 36,
      'member_no'         : 30,
      'member_id'         : '000000000030',
      'free_field'        : {
        'memo'           : '',
        'ceoName'        : '太郎第４',
        'companyName'    : 'たろう株式会社',
        'customerCode'   : '',
        'companyAddress' : '東京都千代田区'
      },
      'bid_allow_flag'       : 1,
      'email_delivery_flag'  : 1,
      'status'               : 1,
      'last_user_name'       : 'OEC',
      'last_update_datetime' : '2024/12/03',
      'create_admin_no'      : 1,
      'create_datetime'      : '2024/12/03 11:31',
      'update_admin_no'      : 1,
      'update_datetime'      : '2024/12/03',
      'last_login_datetime'  : '2024/12/03 15:59',
      'sort_create_datetime' : '2024/12/03 11:31:56.335'
    }
  ],
  'current_count' : 107,
  'total_count'   : 107
}
