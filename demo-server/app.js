import express from 'express'
import http from 'http'
import logger from 'morgan'
// var cookieParser = require('cookie-parser');
import bodyParser from 'body-parser'
import cors from 'cors'
import adminApi from './routes/admin.js'
import auctionApi from './routes/auction.js'
import page_404 from './routes/page_404.js'

const app = express()
app.use(logger('dev'))

// add Access-Control-Allow-Origin header
app.use(cors())

app.use(bodyParser.json({limit : '1024mb'}))
app.use(bodyParser.urlencoded({limit : '1024mb', extended : true, parameterLimit : 1073741824}))

// Add error handling middleware that Express will call
// in the event of malformed JSON.
app.use((err, req, res, next) => {
  let json = null
  console.log(`req.url = ${req.url}`)
  console.log(`err = ${JSON.stringify(err)}`)

  if (req.url.includes('/api/admin') && err.type === 'entity.parse.failed') {
    console.log('JSON format validate: FAILED')
    json = {}
    json.errorCode = 'E001'
    json.errorMessage = 'JSONフォーマットが間違っています。'
    res.status(400).send(JSON.stringify(json))
    return
  }

  next(err)
})

app.use('/api/admin', adminApi)
app.use('/api/auction', auctionApi)
app.use('/*', page_404)

const webServer = http.createServer(app)
const port = 3001
webServer.listen(port, () => {
  console.log(`Server listening at port ${port}`)
})
