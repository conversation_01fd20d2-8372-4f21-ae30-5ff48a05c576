name: unit-test-auction-side

on:
  push:
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2.1.0

      - name: Setup Nodejs
        uses: actions/setup-node@v3
        with:
          node-version: '20.0.0'

      # - name: Unit test
      #   run: |
      #     cd auction-side
      #     rm -rf node_modules
      #     npm install
      #     npm run test:unit
      #   env:
      #     CI: true
