name: ESLint

on:
  push:
  pull_request:
    branches: [ develop ]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2.1.0
        with:
          fetch-depth: 0
      - name: Setup NodeJs
        uses: actions/setup-node@v1
        with:
          node-version: '14.18.0'
      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJSON(github) }}
        run: echo "$GITHUB_CONTEXT"
      # - name: Auction Side Vue Package Install
      #   run: cd auction-side && npm install
      - name: Package Install
        run: npm install
      # - name: <PERSON><PERSON> added and modified javaScript files
      #   run: |
      #     git fetch origin
      #     export GIT_NEW_B=$(git log --decorate=short \
      #         | grep '^commit' \
      #         | head -n 1 \
      #         | cut -d ' ' -f 2
      #     )
      #     export GIT_DIFF=$(git diff --name-only \
      #         --diff-filter=d  origin/develop..${GIT_DEV_B} \
      #         | grep '\.js$'
      #     )
      #     if [[ -n "${GIT_DIFF}" ]]; then
      #         echo "${GIT_DIFF}" \
      #             | xargs node_modules/eslint/bin/eslint.js -c .eslintrc.js --ignore-path .eslintignore
      #     else
      #         echo "No JavaScript files to lint. Moving on..."
      #     fi
