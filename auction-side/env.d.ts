/// <reference types="vite/client" />

type ImportMetaEnv = {
  readonly VITE_API_ENDPOINT: string
  readonly VITE_LOCALSTORAGE_LOGIN_INFO_LABEL: string
  readonly VITE_CRYPTOJS_KEY: string
  readonly VITE_CRYPTOJS_IV: string
}

type ImportMeta = {
  readonly env: ImportMetaEnv
}

// Vue component types
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Global types for existing JavaScript modules during transition
declare module '*.js' {
  const content: any
  export default content
}

// jQuery global
declare global {
  interface Window {
    jQuery: any
    $: any
    mobileCheck: () => boolean
  }

  const $: any
  const jQuery: any
}
