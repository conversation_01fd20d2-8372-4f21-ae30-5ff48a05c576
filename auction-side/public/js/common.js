// viewport処理
var isSp = window.matchMedia('(max-width: 767px)')
$(window).on('load resize', function () {
  if (isSp.matches) {
    $("meta[name='viewport']").attr(
      'content',
      'width=device-width,initial-scale=1'
    )
  }
})

// picture要素 IE対策
document.createElement('picture')

//ハンバーガーメニュー
$(function () {
  $('header p.btnMenu').click(function () {
    $('header .gNav').slideToggle()
    $('header p.btnMenu').toggleClass('close')
  })
})
//ハンバーガーメニュースクロール後固定

//SPナビ 入れ子の開閉
$(function () {
  $('header .gNav nav ul.only_sp li p').click(function () {
    $(this).next('ul').slideToggle()
    $(this).toggleClass('close')
  })
})
$(function () {
  $('footer nav .fNav_sp > ul > li > p').click(function () {
    $(this).next('ul,dl').slideToggle()
    $(this).toggleClass('close')
  })
})

//スムーススクロール
$(document).ready(function () {
  var urlHash = location.hash
  if (urlHash) {
    $('body,html').stop().scrollTop(0)
    setTimeout(function () {
      scrollToAnker(urlHash)
    }, 100)
  }
  $('a[href^="#"]').click(function () {
    var href = $(this).attr('href')
    var hash = href == '#' || href == '' ? 'html' : href
    scrollToAnker(hash)
    return false
  })

  function scrollToAnker(hash) {
    var target = $(hash)
    var position = target.offset().top
    $('body,html').stop().animate({scrollTop: position}, 500)
  }
})

//　ヘッダー・フッター
// ログイン後
function headerin() {
  $.ajax({
    url: '/inc/header_in.html',
    cache: false,
    async: false,
    dataType: 'html',
    success: function (html) {
      document.write(html)
    },
  })
}
// ログイン前
function headerout() {
  $.ajax({
    url: '/inc/header_out.html',
    cache: false,
    async: false,
    dataType: 'html',
    success: function (html) {
      document.write(html)
    },
  })
}
function footer() {
  $.ajax({
    url: '/inc/footer.html',
    cache: false,
    async: false,
    dataType: 'html',
    success: function (html) {
      document.write(html)
    },
  })
}

//ユーザーエージェント判別
var ua = navigator.userAgent.toLowerCase()
var ver = navigator.appVersion.toLowerCase()

var isMSIE = ua.indexOf('msie') > -1 && ua.indexOf('opera') == -1
var isIE6 = isMSIE && ver.indexOf('msie 6.') > -1
var isIE7 = isMSIE && ver.indexOf('msie 7.') > -1
var isIE8 = isMSIE && ver.indexOf('msie 8.') > -1
var isIE9 = isMSIE && ver.indexOf('msie 9.') > -1
var isIE10 = isMSIE && ver.indexOf('msie 10.') > -1
var isIE11 = ua.indexOf('trident/7') > -1
var isIE = isMSIE || isIE11
var isEdge = ua.indexOf('edge') > -1
var isChrome = ua.indexOf('chrome') > -1 && ua.indexOf('edge') == -1
var isFirefox = ua.indexOf('firefox') > -1
var isSafari = ua.indexOf('safari') > -1 && ua.indexOf('chrome') == -1
var isOpera = ua.indexOf('opera') > -1

$(function () {
  if (isOpera) {
    $('body').addClass('js_isOpera')
  } else if (isIE) {
    $('body').addClass('js_isIe')
  } else if (isChrome) {
    $('body').addClass('js_isChrome')
  } else if (isSafari) {
    $('body').addClass('js_isSafari')
  } else if (isEdge) {
    $('body').addClass('js_isEdge')
  } else if (isFirefox) {
    $('body').addClass('js_isFirefox')
  } else {
    return false
  }
})

var _ua = (function (u) {
  return {
    Tablet:
      (u.indexOf('windows') != -1 &&
        u.indexOf('touch') != -1 &&
        u.indexOf('tablet pc') == -1) ||
      u.indexOf('ipad') != -1 ||
      (u.indexOf('android') != -1 && u.indexOf('mobile') == -1) ||
      (u.indexOf('firefox') != -1 && u.indexOf('tablet') != -1) ||
      u.indexOf('kindle') != -1 ||
      u.indexOf('silk') != -1 ||
      u.indexOf('playbook') != -1,
    Mobile:
      (u.indexOf('windows') != -1 && u.indexOf('phone') != -1) ||
      u.indexOf('iphone') != -1 ||
      u.indexOf('ipod') != -1 ||
      (u.indexOf('android') != -1 && u.indexOf('mobile') != -1) ||
      (u.indexOf('firefox') != -1 && u.indexOf('mobile') != -1) ||
      u.indexOf('blackberry') != -1,
  }
})(window.navigator.userAgent.toLowerCase())

$(function () {
  if (_ua.Mobile) {
    $('body').addClass('js_isMobile')
  } else if (_ua.Tablet) {
    $('body').addClass('js_isTablet')
  } else {
    $('body').addClass('js_isPc')
  }
})

if (navigator.platform.indexOf('Win') != -1) {
  $('body').addClass('js_isWin')
} else {
  $('body').addClass('js_isNotWin')
}

$(function () {
  if (ua.indexOf('iphone') > 0) {
    $('body').addClass('iphone')
  } else if (ua.indexOf('android') > 0 && ua.indexOf('mobile') > 0) {
    $('body').addClass('android')
  } else if (ua.indexOf('ipad') > 0) {
    $('body').addClass('ipad')
  }
})

// 高さ合わせ
$(function () {
  $('.matchH').matchHeight()
})
/* jquery-match-height 0.7.2 by @liabru
   http://brm.io/jquery-match-height/
   License MIT */
!(function (t) {
  'use strict'
  'function' == typeof define && define.amd
    ? define(['jquery'], t)
    : 'undefined' != typeof module && module.exports
      ? (module.exports = t(require('jquery')))
      : t(jQuery)
})(function (t) {
  var e = -1,
    o = -1,
    n = function (t) {
      return parseFloat(t) || 0
    },
    a = function (e) {
      var o = 1,
        a = t(e),
        i = null,
        r = []
      return (
        a.each(function () {
          var e = t(this),
            a = e.offset().top - n(e.css('margin-top')),
            s = r.length > 0 ? r[r.length - 1] : null
          null === s
            ? r.push(e)
            : Math.floor(Math.abs(i - a)) <= o
              ? (r[r.length - 1] = s.add(e))
              : r.push(e),
            (i = a)
        }),
        r
      )
    },
    i = function (e) {
      var o = {
        byRow: !0,
        property: 'height',
        target: null,
        remove: !1,
      }
      return 'object' == typeof e
        ? t.extend(o, e)
        : ('boolean' == typeof e
            ? (o.byRow = e)
            : 'remove' === e && (o.remove = !0),
          o)
    },
    r = (t.fn.matchHeight = function (e) {
      var o = i(e)
      if (o.remove) {
        var n = this
        return (
          this.css(o.property, ''),
          t.each(r._groups, function (t, e) {
            e.elements = e.elements.not(n)
          }),
          this
        )
      }
      return this.length <= 1 && !o.target
        ? this
        : (r._groups.push({elements: this, options: o}),
          r._apply(this, o),
          this)
    })
  ;(r.version = '0.7.2'),
    (r._groups = []),
    (r._throttle = 80),
    (r._maintainScroll = !1),
    (r._beforeUpdate = null),
    (r._afterUpdate = null),
    (r._rows = a),
    (r._parse = n),
    (r._parseOptions = i),
    (r._apply = function (e, o) {
      var s = i(o),
        h = t(e),
        l = [h],
        c = t(window).scrollTop(),
        p = t('html').outerHeight(!0),
        u = h.parents().filter(':hidden')
      return (
        u.each(function () {
          var e = t(this)
          e.data('style-cache', e.attr('style'))
        }),
        u.css('display', 'block'),
        s.byRow &&
          !s.target &&
          (h.each(function () {
            var e = t(this),
              o = e.css('display')
            'inline-block' !== o &&
              'flex' !== o &&
              'inline-flex' !== o &&
              (o = 'block'),
              e.data('style-cache', e.attr('style')),
              e.css({
                display: o,
                'padding-top': '0',
                'padding-bottom': '0',
                'margin-top': '0',
                'margin-bottom': '0',
                'border-top-width': '0',
                'border-bottom-width': '0',
                height: '100px',
                overflow: 'hidden',
              })
          }),
          (l = a(h)),
          h.each(function () {
            var e = t(this)
            e.attr('style', e.data('style-cache') || '')
          })),
        t.each(l, function (e, o) {
          var a = t(o),
            i = 0
          if (s.target) i = s.target.outerHeight(!1)
          else {
            if (s.byRow && a.length <= 1) return void a.css(s.property, '')
            a.each(function () {
              var e = t(this),
                o = e.attr('style'),
                n = e.css('display')
              'inline-block' !== n &&
                'flex' !== n &&
                'inline-flex' !== n &&
                (n = 'block')
              var a = {
                display: n,
              }
              ;(a[s.property] = ''),
                e.css(a),
                e.outerHeight(!1) > i && (i = e.outerHeight(!1)),
                o ? e.attr('style', o) : e.css('display', '')
            })
          }
          a.each(function () {
            var e = t(this),
              o = 0
            ;(s.target && e.is(s.target)) ||
              ('border-box' !== e.css('box-sizing') &&
                ((o +=
                  n(e.css('border-top-width')) +
                  n(e.css('border-bottom-width'))),
                (o += n(e.css('padding-top')) + n(e.css('padding-bottom')))),
              e.css(s.property, i - o + 'px'))
          })
        }),
        u.each(function () {
          var e = t(this)
          e.attr('style', e.data('style-cache') || null)
        }),
        r._maintainScroll &&
          t(window).scrollTop((c / p) * t('html').outerHeight(!0)),
        this
      )
    }),
    (r._applyDataApi = function () {
      var e = {}
      t('[data-match-height], [data-mh]').each(function () {
        var o = t(this),
          n = o.attr('data-mh') || o.attr('data-match-height')
        n in e ? (e[n] = e[n].add(o)) : (e[n] = o)
      }),
        t.each(e, function () {
          this.matchHeight(!0)
        })
    })
  var s = function (e) {
    r._beforeUpdate && r._beforeUpdate(e, r._groups),
      t.each(r._groups, function () {
        r._apply(this.elements, this.options)
      }),
      r._afterUpdate && r._afterUpdate(e, r._groups)
  }
  ;(r._update = function (n, a) {
    if (a && 'resize' === a.type) {
      var i = t(window).width()
      if (i === e) return
      e = i
    }
    n
      ? o === -1 &&
        (o = setTimeout(function () {
          s(a), (o = -1)
        }, r._throttle))
      : s(a)
  }),
    t(r._applyDataApi)
  var h = t.fn.on ? 'on' : 'bind'
  t(window)[h]('load', function (t) {
    r._update(!1, t)
  }),
    t(window)[h]('resize orientationchange', function (t) {
      r._update(!0, t)
    })
})

/////[共通]ページトップボタン
$(function () {
  var pageTop = $('#page_top')
  pageTop.hide()
  $(window).scroll(function () {
    if ($(this).scrollTop() > 100) {
      //100pxスクロールしたら表示
      pageTop.fadeIn()
    } else {
      pageTop.fadeOut()
    }
  })
  pageTop.click(function () {
    $('body,html').animate(
      {
        scrollTop: 0,
      },
      500
    ) //0.5秒かけてトップへ移動

    pageTop.fadeOut()
    // ボタンをクリックした後、すぐに非表示にする
    return false
  })

  // フッター手前でストップ
  $('#page_top').hide()
  $(window).on('scroll', function () {
    var scrollHeight = $(document).height()
    var scrollPosition = $(window).height() + $(window).scrollTop()
    var footHeight = $('footer').innerHeight() - 20 // footHeightの値を減らしボタン位置調整
    // var footOffsetTop = $("footer").offset().top; TODO:　使わないようです、また、エラーになったため、一旦コメントアウトします。

    if ($(window).width() <= 768) {
      if (scrollPosition >= footOffsetTop) {
        $('#page_top').css({
          position: 'absolute',
          top: '-15px',
        })
      } else {
        $('#page_top').css({
          position: 'fixed',
          bottom: '15px',
          top: 'auto',
        })
      }
    } else {
      if (scrollHeight - scrollPosition <= footHeight) {
        $('#page_top').css({
          position: 'absolute',
          bottom: footHeight,
        })
      } else {
        $('#page_top').css({
          position: 'fixed',
          bottom: '15px',
          top: 'auto',
        })
      }
    }
  })
})

/////[商品一覧]表示並べ替えパネルボタン
$(function () {
  // ヘッダー条件検索開閉
  $('.menu_trigger').on('click', function () {
    var filterPanel = $('.sorting_panel')
    if (!filterPanel.hasClass('is-active')) {
      filterPanel.addClass('is-active')
    } else {
      filterPanel.removeClass('is-active')
    }
  })
  // ボタン以外のエリアクリックで閉じる
  $(document).on('click', function (e) {
    if (!$(e.target).closest('.sorting').length) {
      $('.sorting_panel').removeClass('is-active')
    }
  })
  // リストアイテムをクリックした際の動作
  $('.option_item').on('click', function () {
    var selectedText = $(this).text() // クリックされたリストのテキストを取得
    $('.option_selected').text(selectedText) // 選択されたテキストを表示している要素に反映
    $('.sorting_panel').removeClass('is-active')
  })
})

/////[商品一覧]ディスプレイタイプ切り替え
$(function () {
  function toggleActiveClass(button, itemListClass) {
    $('.btn.row, .btn.panel').removeClass('is-active')
    button.addClass('is-active')
    $('.item-list').removeClass('row panel').addClass(itemListClass)
  }

  $('.btn.row').on('click', function () {
    toggleActiveClass($(this), 'row')
  })

  $('.btn.panel').on('click', function () {
    toggleActiveClass($(this), 'panel')
  })
})

/////[商品詳細]モーダル（入札）
$(function () {
  var open = $('.modal-open'),
    close = $('.modal-close'),
    container = $('.modal-container')
  open.on('click', function () {
    container.addClass('active')
    return false
  })
  close.on('click', function () {
    container.removeClass('active')
  })
  $(document).on('click', function (e) {
    if (!$(e.target).closest('.modal-body').length) {
      container.removeClass('active')
    }
  })
})

/////[QA]
$(function () {
  $('#ac-menu .label').on('click', function () {
    $(this).next().slideToggle()
    $(this).toggleClass('open')
  })
})

/////[TOP]すべてのカテゴリボタン
$(function () {
  // 初期化：最初に「すべてのカテゴリ」に .active を付与
  $('.filter-panel .label.pre').addClass('active')

  // ヘッダー条件検索開閉
  $('.btn-category a').on('click', function () {
    var filterPanel = $('.filter-panel')

    if (!filterPanel.hasClass('is-active')) {
      filterPanel.addClass('is-active')
    } else {
      filterPanel.removeClass('is-active')
    }
  })

  $('.close-filter span').on('click', function () {
    $('.filter-panel').removeClass('is-active')
  })

  // ▼▼ カテゴリ項目クリックでラベル変更＋activeクラス付与 ▼▼
  $('.filter-panel .label').on('click', function () {
    var selectedText = $(this).text().trim()

    // ボタンのラベルを変更
    $('.btn-category a span').text(selectedText)

    // パネルを閉じる
    $('.filter-panel').removeClass('is-active')

    // activeの切り替え
    $('.filter-panel .label').removeClass('active')
    $(this).addClass('active')
  })
  // ▲▲ ここまで追加 ▲▲

  // .nav-btn-searchのクリック時の制御
  $(function () {
    $('.nav-btn-search').on('click', function () {
      var filterPanelSp = $('.filter-panel-sp')
      filterPanelSp.slideToggle()
    })

    $('.close-panel span').on('click', function () {
      $('.filter-panel-sp').slideUp()
    })
  })
})

/////[TOP]検索欄カテゴリをもっと見るボタン
$(function () {
  const $list = $('.list-category')
  const $btn = $('.btn.more-category')

  $btn.on('click', function () {
    const isExpanded = $list.hasClass('expanded')

    if (isExpanded) {
      // 閉じる：アニメーション完了後にテキスト変更
      $list.removeClass('expanded')
      $list.one('transitionend', function () {
        $btn.removeClass('expanded').find('span').text('もっと見る')
      })
    } else {
      // 展開：即時テキスト変更
      $list.addClass('expanded')
      $btn.addClass('expanded').find('span').text('閉じる')
    }
  })
})
/////[TOP]おすすめ商品スライダー
$(function () {
  const $slider = $('.list-item-gallery.top')

  // Slick初期化
  $slider.slick({
    arrows: true,
    dots: true,
    infinite: true,
    slidesToShow: 5,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 1081,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  })

  // 高さを揃える関数
  function equalizeSlideHeights() {
    let maxHeight = 0

    $slider.find('.slick-slide').each(function () {
      $(this).css('height', 'auto') // 高さリセット
      const h = $(this).outerHeight()
      if (h > maxHeight) maxHeight = h
    })

    $slider.find('.slick-slide').css('height', maxHeight + 'px')
  }

  // 初期化直後・スライド変更時に高さを揃える
  $slider.on('setPosition', function () {
    equalizeSlideHeights()
  })

  // 画像読み込み後やリサイズ時にも高さ調整
  $(window).on('load resize', function () {
    setTimeout(equalizeSlideHeights, 300)
  })
})

$(function () {
  const $slider = $('.list-item-gallery.top')

  const initSliderIfEnoughItems = function () {
    const itemCount = $slider.find('li').length

    let slidesToShow = 5
    const windowWidth = window.innerWidth

    if (windowWidth <= 767) {
      slidesToShow = 2
    } else if (windowWidth <= 1080) {
      slidesToShow = 3
    }

    // すでにSlickが初期化されていたら一度破棄
    if ($slider.hasClass('slick-initialized')) {
      $slider.slick('unslick')
    }

    // アイテム数が表示数以上 → slick初期化
    if (itemCount > slidesToShow) {
      $slider.removeClass('not-slick').slick({
        arrows: true,
        dots: true,
        infinite: true,
        slidesToShow: slidesToShow,
        slidesToScroll: 1,
        responsive: [
          {breakpoint: 1080, settings: {slidesToShow: 3}},
          {breakpoint: 767, settings: {slidesToShow: 2}},
        ],
      })
    } else {
      // 少ない場合は not-slick クラス付与
      $slider.addClass('not-slick')
    }
  }

  // 初回実行
  initSliderIfEnoughItems()

  // リサイズ時に再判定（オプション）
  let resizeTimer
  $(window).on('resize', function () {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(function () {
      initSliderIfEnoughItems()
    }, 300)
  })
})
