import pluginJs from '@eslint/js'
import prettierConfig from '@vue/eslint-config-prettier'
import jsdoc from 'eslint-plugin-jsdoc'
import pluginVue from 'eslint-plugin-vue'
import globals from 'globals'
import tseslint from '@typescript-eslint/eslint-plugin'
import tsparser from '@typescript-eslint/parser'
import commonRules from '../.eslintRules.js'

export default [
  { files: ['**/*.{js,mjs,cjs,vue,ts}'] },
  {
    ignores: ['node_modules/*', '/dist/**', '*.d.ts']
  },
  { languageOptions: { globals: globals.browser } },
  pluginJs.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  jsdoc.configs['flat/recommended'],
  commonRules,
  // TypeScript configuration
  {
    files: ['**/*.ts', '**/*.vue'],
    languageOptions: {
      parser: tsparser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        extraFileExtensions: ['.vue']
      }
    },
    plugins: {
      '@typescript-eslint': tseslint
    },
    rules: {
      // TypeScript specific rules (relaxed for gradual migration)
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/ban-ts-comment': 'off'
    }
  },
  {
    plugins: { prettierConfig },
    rules: {
      'linebreak-style': 0,
      'multiline-comment-style': 0,
      'capitalized-comments': 0,
      'require-jsdoc': 0,
      semi: 0,
      'no-invalid-this': 0,
      'jsdoc/require-returns': 0,
      curly: 0,
      camelcase: 0
    }
  }
]
