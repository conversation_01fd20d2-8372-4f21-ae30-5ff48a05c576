import vue from '@vitejs/plugin-vue'
import { URL, fileURLToPath } from 'node:url'
import { configDefaults, defineConfig } from 'vitest/config'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  test: {
    globals: true,
    environment: 'jsdom',
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: [...configDefaults.exclude, 'e2e/*', 'src/assets/*'],
    root: fileURLToPath(new URL('./', import.meta.url)),
    transformMode: {
      web: [/\.vue$/, /\.js$/, /\.ts$/]
    },
    coverage: {
      provider: 'v8',
      include: ['src/**/*.{vue,js,ts}'],
      reporter: ['text', 'json', 'html'],
      enabled: true
    },
    reporters: ['default', 'html']
  }
})
