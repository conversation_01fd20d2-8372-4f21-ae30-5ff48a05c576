const fs = require('fs')
const path = require('path')

const messagesPath = path.join(__dirname, '../src/language/translate.ts')

function validateTranslations() {
  console.log(' Validating translation keys...\n')

  try {
    // Read the messages file
    const messagesContent = fs.readFileSync(messagesPath, 'utf8')
    console.log(' Successfully read translate.ts file')

    // Look for: export const translate: Translate = { ja: { ... }, en: { ... } }
    const translateMatch = messagesContent.match(
      /export\s+const\s+translate:\s*Translate\s*=\s*{([\s\S]*?)}\s*$/m
    )

    if (!translateMatch) {
      console.error('❌ Could not find translate object export')
      console.error(
        ' Expected format: export const translate: Translate = { ja: {...}, en: {...} }'
      )
      process.exit(1)
    }

    console.log(' Found translate object export')

    const translateContent = translateMatch[1]

    // Find the positions of ja: and en: to split the content
    const jaStart = translateContent.indexOf('ja:')
    const enStart = translateContent.indexOf('en:')

    if (jaStart === -1) {
      console.error('❌ Could not find "ja:" section')
      process.exit(1)
    }

    if (enStart === -1) {
      console.error('❌ Could not find "en:" section')
      process.exit(1)
    }

    // Extract content between ja: { and }, en:
    const jaSection = translateContent.substring(jaStart, enStart)
    const jaMatch = jaSection.match(/ja:\s*{([\s\S]*?)},?\s*$/s)

    // Extract content from en: { to the end
    const enSection = translateContent.substring(enStart)
    // The en section should end with } followed by optional whitespace and newlines
    const enMatch = enSection.match(/en:\s*{([\s\S]*?)}\s*,?\s*$/s)

    if (!jaMatch) {
      console.error('❌ Could not parse Japanese translations section')
      console.error(' Looking for pattern: ja: { ... },')
      console.error(' Japanese section preview:')
      console.error(jaSection.substring(0, 300) + '...')
      process.exit(1)
    }

    if (!enMatch) {
      console.error('❌ Could not parse English translations section')
      console.error(' Looking for pattern: en: { ... }')
      console.error(' English section preview:')
      console.error(enSection.substring(0, 300) + '...')
      console.error(' English section length:', enSection.length)
      process.exit(1)
    }

    console.log(' Successfully parsed both language sections')

    // Extract keys from both languages
    const jaKeys = extractKeys(jaMatch[1])
    const enKeys = extractKeys(enMatch[1])

    console.log(` Found ${jaKeys.size} Japanese keys`)
    console.log(` Found ${enKeys.size} English keys\n`)

    // Find missing keys
    const missingInEn = [...jaKeys].filter(key => !enKeys.has(key))
    const missingInJa = [...enKeys].filter(key => !jaKeys.has(key))

    let hasErrors = false

    if (missingInEn.length > 0) {
      console.error('❌ Keys missing in English translations:')
      missingInEn.forEach(key => console.error(`   - ${key}`))
      console.error('')
      hasErrors = true
    }

    if (missingInJa.length > 0) {
      console.error('❌ Keys missing in Japanese translations:')
      missingInJa.forEach(key => console.error(`   - ${key}`))
      console.error('')
      hasErrors = true
    }

    if (!hasErrors) {
      console.log(' ✅️ All translation keys are consistent between languages!')
    } else {
      console.error(' Fix these missing keys to ensure type safety.')
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Error validating translations:', error.message)
    console.error(' Stack trace:', error.stack)
    process.exit(1)
  }
}

function extractKeys(content) {
  const keys = new Set()

  // Match key patterns like: KEY_NAME: 'value',
  const keyRegex = /^\s*([A-Z_][A-Z0-9_]*)\s*:/gm
  let match
  let matchCount = 0

  while ((match = keyRegex.exec(content)) !== null) {
    const key = match[1]
    matchCount++

    // Skip special keys
    if (key !== '$vuetify' && !key.startsWith('//')) {
      keys.add(key)
    }
  }

  console.log(` Extracted ${keys.size} keys from ${matchCount} total matches`)

  // Debug: show first few keys found
  if (keys.size > 0) {
    const firstKeys = [...keys].slice(0, 5)
    console.log(
      ` Sample keys: ${firstKeys.join(', ')}${keys.size > 5 ? '...' : ''}`
    )
  }

  return keys
}

// Run validation
if (require.main === module) {
  validateTranslations()
}

module.exports = {validateTranslations}
