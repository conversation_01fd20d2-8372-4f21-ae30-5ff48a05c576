import { URL, fileURLToPath } from 'node:url'

import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'

import vuetify from 'vite-plugin-vuetify'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  return {
    plugins: [
      vue({
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),
      vuetify()
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    define: {
      global: 'window'
    },
    esbuild: {
      drop: mode === 'production' ? ['console', 'debugger'] : [],
      target: 'es2020'
    },
    // Support for both JS and TS during migration
    optimizeDeps: {
      include: ['vue', 'vue-router', 'pinia']
    }
  }
})
