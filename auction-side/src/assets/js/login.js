// 補足文表示
var isSp = window.matchMedia('(max-width: 767px)')
$(window).on('load resize', function () {
  if (isSp.matches) {
    $(function () {
      $('.note').on('click', function () {
        $('.js-modal').fadeIn()
        return false
      })
      $('.js-modal-close').on('click', function () {
        $('.js-modal').fadeOut()
        return false
      })
    })
  } else {
  }
})

//ログインの活性・非活性
$(function () {
  $(function () {
    $('#sbm-login').attr('disabled', 'disabled')
    $('#rule-chk').click(function () {
      if ($(this).prop('checked') == false) {
        $('#sbm-login').attr('disabled', 'disabled')
      } else {
        $('#sbm-login').removeAttr('disabled')
      }
    })
  })
})
