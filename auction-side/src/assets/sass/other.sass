@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *共通パーツ
 ***********************************************************************

/* table
 *==========================================

#main
  table.tbl-otherItem
    width: 100%
    margin: 0
    border-top: 1px solid #eaeaea
    @include sp
      border: none

    th, td
      border-bottom: 1px solid #eaeaea
      vertical-align: top
      font-size: 18px
      @include sp
        width: 100%
        display: block
        padding: 15px 10px
        font-size: 1rem
        border: none

    th
      font-weight: 700
      background-color: #F7F7F7

    td ul li
      line-height: 1.6
      + li
        margin: .8rem 0 0
        @include sp
          margin: .5rem 0 0

    td ul.decimal
      list-style-type: decimal
      padding: 0 0 0 1.3rem

    td ul.disc
      list-style-type: disc
      padding: 0 0 0 1.3rem

    td dl
      display: flex
      flex-direction: row
      flex-wrap: wrap
      margin: .8rem 0 0
      line-height: 1.6
      dt
        width: 5rem
        margin: 0 0 .8rem
      dd
        width: calc(100% - 5rem)
        margin: 0 0 .8rem

    td dl.w-grad-equal
      dt
        width: 8rem
      dd
        width: calc(100% - 8rem)

    td
      ul
        margin: .8rem 0 0
      p
        margin: .8rem 0 0
        line-height: 1.6
      .ma-0
        margin: 0
      .ma-l
        margin: 2.6rem 0 0
      .ttl
        font-weight: 500


  #privacy table.tbl-otherItem
    margin-top: 20px
    th, td
      font-size: 17px
      @include sp
        padding: 15px 30px
        font-size: 15px

  #profile table.tbl-otherItem
    th, td
      padding: 35px 60px
      @include sp
        padding: 1rem
    th
      width: 300px
      @include sp
        width: 100%


/* リスト
 *==========================================
/* ---------------------------
 *introduction
 *----------------------------

#main
  #static
    padding: 60px 2rem 60px
    font-size: 1rem
    @include sp
      padding: 30px 1rem
      font-size: 15px

    .introduction
      padding: 0 3rem
      @include sp
        padding: 0
      p
        margin: .5rem 0
        font-size: 1rem
        line-height: 1.8

/* ---------------------------
 *DL リスト - 1
 *----------------------------

dl.list_main
  padding: 0 3rem 60px
  @include sp
    padding: 0 0 40px

  > dt
    margin: 3.5rem 0 2rem
    font-weight: 400
    font-size: 1.3rem
    line-height: 1.4
    @include sp
      font-size: 1.4rem

  > dd
    p
      margin: .5rem 0
      line-height: 1.8

#main
  #rule dl.list_main > dt:first-of-type
    margin-top: 30px

  #privacy dl.list_main > dt:first-of-type
    margin-top: 70px

dl
  &.list_sub >
    dt
      margin-top: 30px
      font-weight: 700
      text-indent: -2.15em
      padding-left: 2.15em

      &:first-of-type
        margin-top: 5px

      span
        display: inline-block
        margin-right: 1em
        font-weight: 700

    dd
      padding-left: 2.15em
      font-size: 17px

  &.list_nmb-d1
    font-size: 17px
    padding-left: 2.15em
    margin-top: 1em

    >
      dt
        font-weight: 700
        text-indent: -1.5em
        padding-left: 1.5em

        &:not(:first-of-type)
          margin-top: 10px

      dd
        padding-left: 1.5em

@media only screen and (max-width: 767px)
  #main
    #rule dl.list_main > dt:first-of-type
      margin-top: 20px

    #privacy dl.list_main > dt:first-of-type
      margin-top: 40px

/* ---------------------------
 *DL リスト - 2
 *-----------------------------

@media only screen and (max-width: 767px)
  dl.list_sub > dd
    font-size: 15px

/* ---------------------------
 *DL リスト - 3
 *-----------------------------

@media only screen and (max-width: 767px)
  dl.list_nmb-d1
    font-size: 15px
    padding-left: 0

/* ---------------------------
 *OL リスト - 1
 *-----------------------------

#main ol.list_nmb-o1
  list-style: disc
  margin: .5rem 0
  padding: 0 0 0 1.7rem
  @include sp
    padding: 0 0 0 1.3rem

ol.list_nmb-o1 > li
  line-height: 1.8
  @include sp
    line-height: 1.8

  + li
    margin: .3rem 0 0

#main ol.list_nmb-o2
  list-style: circle
  margin: .5rem 0 0
  padding: 0 0 0 1.7rem
  @include sp
    padding: 0 0 0 1.3rem

ol.list_nmb-o2 > li
  line-height: 1.8
  @include sp
    line-height: 1.8

#main ol.list_nmb-o3
  list-style: none
  margin: .5rem 0
  padding: 0 0 0 1.7rem
  @include sp
    padding: 0 0 0 1.3rem

ol.list_nmb-o3 > li
  line-height: 1.8
  text-indent: -1rem
  @include sp
    line-height: 1.8

  + li
    margin: .3rem 0 0

/* ---------------------------
 *UL リスト - 1
 *-----------------------------

ul.list_nmb-u1 li
  text-indent: -2.15em
  padding-left: 2.15em

  span
    display: inline-block
    margin-right: 1em

@media only screen and (max-width: 767px)
  ul.list_nmb-u1 li + li
    margin-top: 7px

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *会社概要
 ***********************************************************************

#main
  #profile
    padding: 60px 2rem
    font-size: 1rem
    line-height: 1.8
    @include sp
      padding: 40px 1rem

    span.enCoName
      word-break: normal

  #rule
    font-size: 1rem
    line-height: 1.8
    padding-bottom: 110px

    p.intro_rule
      margin-top: 70px
      padding: 0 45px

    p.additional
      padding: 0 3rem
      @include sp
        padding: 0



/***********************************************************************
 *
 *------------------------------------------------------------------------
 *条件・ルール
 ***********************************************************************

#main
  #rule
    padding: 60px 3rem 60px
    @include sp
      padding: 40px 1rem 40px

    p.intro_rule
      margin: 0
      padding: 0

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *個人情報の保護
 ***********************************************************************

  #privacy
    font-size: 18px
    line-height: 1.8
    padding-bottom: 110px

    .rec
      margin-top: 0.5em
      padding-left: 2.15em
      font-size: 17px

    p.sign
      text-align: right
      margin-top: 10px
      font-size: 14px
      color: #7D7D7D
      line-height: 1.4

    ul
      &[class^="list_group-"]

      &.list_group-domestic
        margin-bottom: 35px

      &.list_group-foreign li
        word-break: normal

@media only screen and (min-width: 768px)
  #main #privacy ul.list_group-domestic
    display: flex
    flex-wrap: wrap

    li:not(:last-of-type)::after
      content: "、"
      display: inline-block

@media only screen and (max-width: 767px)
  #main #privacy
    font-size: 16px
    padding-bottom: 40px

    .rec
      font-size: 15px
      padding-left: 0

    ul
      &[class^="list_group-"]
        margin-top: 5px

        li
          line-height: 1.6
          border-bottom: 1px solid #EAEAEA
          padding-bottom: 5px

          + li
            margin-top: 5px

      &.list_group-domestic
        margin-bottom: 25px
