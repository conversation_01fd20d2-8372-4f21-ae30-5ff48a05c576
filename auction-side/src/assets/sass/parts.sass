@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *共通パーツ
 ***********************************************************************

/* アニメーション
 *==========================================

@keyframes fade-basic
  from
    opacity: 0

  to
    opacity: 1

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *ボタン
 ***********************************************************************

/* 基本形ボタン
 *==========================================

[class^="btnBsc-"]
  display: flex
  justify-content: center
  align-items: center
  height: 60px
  border-radius: 100vh
  margin: 0 auto
  padding: 0
  color: #fff
  font-size: 18px
  font-weight: 700
  text-align: center
  width: 100%
  transition: all 0.08s linear
  @include sp
    height: 50px
    font-size: 14px

  &:hover
    opacity: 0.8

  img
    display: inline-block

/* リスト下ボタン

main
  section
    .wrap-btn
      width: 100%
      margin: 3rem 0
      text-align: center

      .list-more
        width: 300px
        height: 50px
        margin: 0 auto
        padding: .5rem 2rem
        color: #fff
        font-size: 1rem
        font-weight: 700
        background-color: #bf2a24
        border-radius: 4px
        @include sp
          height: 60px

/* 退会ボタン
.container
  .wrap-btn
    margin: 0 0 60px
    padding: 1rem
    button.withdraw
      width: 240px
      height: 50px
      display: flex
      justify-content: center
      align-items: center
      margin: 0 auto
      color: #000
      font-size: 1rem
      font-weight: 700
      background-color: #d3d3d3
      border-radius: 4px


/* ---------------------------
 *ダウンロードボタン
 *-----------------------------

.btnBsc-DL
  background-color: $main-color

  img
    width: 24px
    margin-left: 10px
    @include sp
      width: 18px

/* ---------------------------
 *黒（濃グレー）ボタン
 *-----------------------------

.btnBsc-Black
  background-color: $main-gray

  img
    width: 19px
    position: relative
    top: -2px
    margin-left: 15px
    @include sp
      width: 13px
      top: -1px

/* ---------------------------
 *色ボタン（コーポレートカラー）
 *-----------------------------

.btnBsc-CoCor
  background-color: $main-color

  img
    width: 19px
    position: relative
    top: -2px
    margin-left: 15px
    @include sp
      width: 13px
      top: -1px

/* ---------------------------
 *アクションボタン（会員登録など）
 *-----------------------------
.btn-form
  width: 100%
  text-align: center
  margin: 60px 0
  padding: 0
  @include sp
    margin: 7vw 0

/* ---------------------------
 *リンク
 *-----------------------------

a.link-std
  color: $main-color
  text-decoration: underline
  &:hover
    text-decoration: none
    opacity: 1


/***********************************************************************
 *
 *------------------------------------------------------------------------
 *お気に入りマーク
 ***********************************************************************

.com-item-box
  display: inline-block
  position: relative

  p.fav-mark
    display: block
    background: url("../img/common/icn_favorite_detail.svg") (center 7px) no-repeat
    background-size: 22px 22px
    width: 70px
    margin: 0 0 0 1rem
    padding: 23px 5px 2px
    text-align: center
    border: 1px solid $line-gray
    border-radius: 4px
    cursor: pointer
    @include sp
      background: url("../img/common/icn_favorite_detail.svg") (center 1.8vw) no-repeat
      background-size: 5vw 5vw
      width: 16vw
      margin: 0 0 0 4vw
      padding: 6.3vw 1vw .2vw
      border-radius: 1vw

    span
      color: #333
      font-size: 10px
      font-weight: 500
      @include sp
        font-size: 2.2vw

    &.active
      background-image: url("../img/common/icn_favorite_blue.svg")
      border: 1px solid $main-color
      span
        color: $main-color

    &:hover
      background-image: url("../img/common/icn_favorite_blue.svg")
      border: 1px solid $main-color
      span
        color: $main-color


/* ログイン前は非表示

body
  &.state-out span.fav-mark
    display: none !important

  &.item_p-detail #terms.com-item-box
    display: block

    span.fav-mark
      width: 40px
      height: 40px
      right: 20px

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *見出し
 ***********************************************************************

#main
  h2.page-ttl
    display: flex
    align-items: center
    justify-content: center
    flex-direction: column
    width: 100%
    margin: 0
    padding: 2rem 1rem
    @include sp
      margin: 4vw 0 0
      padding: 4vw

    .ttl
      width: 100%
      text-align: center
      margin: .5rem 0
      padding: 0
      font-size: 2rem
      font-weight: 500
      line-height: 1.2
      letter-spacing: 2px
      @include sp
        margin: 0 0 .5rem
        font-size: 5.3vw
        font-weight: 600

      .red
        color: $main-color

    .sub
      margin: 0 0 .2rem
      font-size: .8rem
      font-weight: 500
      text-align: center
      letter-spacing: 2px
      font-family: 'Noto Sans JP', sans-serif
      line-height: 1.2
      @include sp
        margin: 0 0 2vw
        font-size: 3vw

    .ttl.specified
      font-size: 1.2rem

    &.list
      height: 80px
      margin: 40px 0 0
      @include sp
        height: 14vw
        margin: 7vw 0 0

  h3
    margin: 1rem 0 4rem
    padding: 0
    font-size: 2rem
    font-weight: 400
    line-height: 1.2
    text-align: center
    @include sp
      font-size: 1.6rem

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *ページトップ
 ***********************************************************************

#page_top
  position: fixed
  display: block
  width: 42px
  height: 42px
  right: 1rem
  bottom: 1.2rem
  background-color: rgba(9,46,86,0.5)
  border-radius: 50%
  z-index: 10

  a
    display: block
    width: 100%
    height: 100%
    text-decoration: none
    cursor: pointer
    z-index: 11

    &:hover
      opacity: .7

    &:before
      content: ''
      width: 6px
      height: 6px
      border: 0
      border-top: solid 2px #fff
      border-right: solid 2px #fff
      position: absolute
      top: calc(50% + 1px)
      left: calc(50% - 4px)
      margin-top: -4px
      transform: rotate(-45deg)

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *QA
 ***********************************************************************

#qa
  padding: 60px 3rem
  @include sp
   padding: 40px 1rem

  .qa-wrapper

    li
      padding: 2.5rem 3rem 2.7rem
      border-bottom: 1px solid #eee
      &:first-child
        border-top: 1px solid #eee
      @include sp
        padding: 1.5rem 1rem 1.7rem

      dl
        font-size: 1.2rem
        line-height: 1.8
        @include sp
          font-size: 1rem

        dt
          position: relative
          margin: 0 0 1.5rem
          padding: 0 0 0 4.5rem
          color: $main-color
          @include sp
            margin: 0 0 1rem
            padding: 0 0 0 3rem

          &:before
            position: absolute
            top: 0
            left: 0
            content: "Q. "
            font-size: 2rem
            line-height: 1
            @include sp
              font-size: 1.6rem

        dd
          position: relative
          padding: 0 0 0 4.5rem
          @include sp
            padding: 0 0 0 3rem

          &:before
            position: absolute
            top: 0
            left: .1rem
            content: "A. "
            font-size: 2rem
            line-height: 1
            @include sp
              font-size: 1.6rem

          picture
            display: block
            max-width: 1000px
            margin: 1rem 0
            @include sp
              max-width: calc(100%)




/* ---------------------------
 *アコーディオンの場合　#ac-menu
 *-----------------------------

#qa
  #ac-menu
    li
      border-top: solid 1px #000

      &:last-child
        border-bottom: solid 1px #000

    .label
      cursor: pointer
      font-size: 1.125rem
      font-weight: bold
      padding: 40px 30px
      position: relative
      transition: .5s

      &:hover
        background-color: #ffda5f

      &::before
        content: ''
        width: 20px
        height: 1px
        background: #000
        position: absolute
        top: 50%
        right: 5%
        transform: translateY(-50%)

      &::after
        content: ''
        width: 20px
        height: 1px
        background: #000
        position: absolute
        top: 50%
        right: 5%
        transform: translateY(-50%)
        transform: translateY(-50%) rotate(90deg)
        transition: .5s

      &.open
        /* ラベルの背景色を変更
        background-color: #ffda5f

        &::before
          /* ラベルアイコンの横棒を非表示
          opacity: 0

        &::after
          /* ラベルアイコンの縦棒を横向きに回転
          transform: rotate(180deg)

    .detail
      border-top: solid 1px #ccc
      padding: 35px 30px
      display: none

      dl
        display: flex
        flex-wrap: wrap

      dt
        width: 20%
        font-weight: bold
        margin-bottom: 40px

      dd
        width: 80%
        margin-bottom: 40px

// ------------------------------------------
// SP
// ------------------------------------------

  #ac-menu
    @include sp
      .label
        padding: 40px 0

      .detail
        padding: 35px 0

        dl
          flex-direction: column

        dt
          width: 100%
          margin-bottom: 10px

        dd
          width: 100%
          padding-left: 10px


/***********************************************************************
*
*------------------------------------------------------------------------
*モーダル
***********************************************************************

#sample
  padding: 100px 0

/*------------------------------------------
*モーダルコンテンツ
*-------------------------------------------

#main
  .place-modal
    position: relative
    width: 100%
    padding: 1rem
    text-align: center
    @include sp
      padding: 1rem

    .btn.modal-open
      width: 280px
      max-width: 100%
      height: 56px
      color: #fff
      font-size: 1rem
      font-weight: 500
      background-color: $main-color
      border-radius: 4px

      /* ---------------------------
      *モーダル（入札）ModalBid
      *-----------------------------

    .modal-container
      position: fixed
      top: 0
      left: 0
      width: 100%
      height: 100%
      text-align: center
      background: rgba(0, 0, 0, .7)
      padding: 40px 20px
      overflow: auto
      opacity: 0
      visibility: hidden
      transition: .3s
      box-sizing: border-box
      z-index: 100

      &:before
        content: ""
        display: inline-block
        vertical-align: middle
        height: 100%

      &.active
        opacity: 1
        visibility: visible

      .modal-body
        position: relative
        display: inline-block
        vertical-align: middle
        max-width: calc(100% - 2rem)
        width: 600px
        margin: 0 auto

        .modal-close
          position: absolute
          display: flex
          align-items: center
          justify-content: center
          top: -30px
          right: -30px
          width: 60px
          height: 60px
          font-size: 22px
          color: #fff
          background-color: $main-color
          border-radius: 30px
          cursor: pointer
          z-index: 120
          @include sp
            top: -36px

        .modal-content
          position: relative
          padding: 2rem
          background-color: #fff
          border-radius: 4px
          z-index: 110
          @include sp
            padding: 2.5rem 1.5rem 1rem

          .note
            width: 100%
            margin: 0 0 1rem
            padding: 0
            text-align: center
            span
              display: inline-block
              text-align: left

          .button-wrap
            display: flex
            flex-direction: row

            button
              width: calc(50% - 1rem)
              max-width: calc(100% - 1rem)
              height: 56px
              margin: 1rem 0
              color: #fff
              font-size: 1rem
              font-weight: 500
              border-radius: 4px
              @include sp
                width: calc((100% - 1rem) / 2)
                font-size: .9rem
                line-height: 1.2
              &.goto-signup
                background-color: $main-color
              &.cancel
                background-color: #ababab

              + button
                margin: 1rem 0 1rem 1rem