@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *商品情報
 ***********************************************************************

#main
  #heroes
    display: flex
    justify-content: center
    align-items: center
    position: relative
    width: 100%
    height: 340px
    background: url("../img/home/<USER>") center (top / cover) no-repeat
    @include sp
      width: 100%
      height: 40vw
      background: url("../img/home/<USER>") center no-repeat
      background-size: auto 100%

    .catch
      color: #fff
      font-size: 1.6rem
      font-weight: 600
      font-family: "Futura", "Avenir", "Helvetica Neue", Helvetica, Arial, sans-serif
      @include sp
        font-size: 4vw

    .bnr
      position: absolute
      top: 5px
      right: 20px
      width: 200px
      @include sp
        top: calc(50% - 46px)
        right: 7px
        width: 110px

      a:hover
        opacity: 1

      img
        width: 100%
        height: auto

      &:hover
        transform: translateY(3px)
        transition: all 0.5s 0s ease
        cursor: pointer

  #search
    width: 100%
    padding: 50px 2rem 30px
    background-color: $main-bg-color
    @include sp
      padding: 4vw 4vw

    .cont-wrap
      display: flex
      justify-content: center
      align-items: center
      width: 1180px
      max-width: 100%
      margin: 0 auto

      .search-keyword
        position: relative
        display: flex
        justify-content: space-between
        align-items: center
        width: 860px
        max-width: 100%
        height: 50px
        background-color: #fff
        border: 1px solid #e5e5e5
        border-radius: 4px
        @include sp
          width: 100%
          height: 12vw

        .btn-category
          display: flex
          align-items: center
          position: static
          width: auto
          height: 36px
          margin: 0 1rem 0 0
          padding: 0
          background-color: #e5eef5
          border: 1px solid $sub-color
          border-radius: 4px
          @include sp
            margin: 0 1.3vw 0 0
            height: 9vw

          a
            position: relative
            padding: 0 26px 0 10px
            display: flex
            align-items: center
            justify-content: flex-start
            width: 100%
            height: 100%
            @include sp
              padding: 1vw 2vw

            span
              color: $main-text
              font-size: .7rem
              line-height: 1.2
              @include sp
                font-size: 2.7vw
                font-weight: 500

            &:after
              content: ""
              position: absolute
              right: 10px
              top: 50%
              width: 4px
              height: 4px
              border-right: 2px solid $sub-color
              border-bottom: 2px solid $sub-color
              -webkit-transform: translateY(-50%) rotate(45deg)
              transform: translateY(-50%) rotate(45deg)
              @include sp
                content: none

        .filter-panel
          position: absolute
          top: 50px
          left: 0
          display: none/*design確認用*/
          width: calc(100% - 52px)
          max-width: 100%
          margin: 0 auto
          padding: 0
          z-index: 10
          @include sp
            width: 100%

          &.is-active
            display: block
            @include sp
              display none

          .panel-body
            padding: 8px
            background-color: #fff
            border: 1px solid $sub-color
            box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.15)

            .list-wrap
              display: flex
              flex-direction: row
              margin: .8rem 0 0
              @include sp
                flex-wrap: wrap

              .list
                width: 164px
                padding: 0
                @include sp
                  width: 50%

                li
                  margin: 0 12px 10px 12px
                  font-size: 1rem
                  font-weight: 400
                  line-height: 1.2
                  cursor: pointer
                  @include sp
                    margin: 0 1vw 2.5vw 2vw

                  &:hover
                    color: $main-color

                  &.label
                    font-size: .75rem
                    @include sp
                      font-size: 3.2vw
                  &.active
                    color: $main-color
                    font-weight: 600

            .close-filter
              width: 100%
              display: flex
              justify-content: flex-end

              span
                display: block
                position: relative
                width: 13px
                height: 13px

                &:hover
                  cursor: pointer

                &:before, &:after
                  content: ""
                  position: absolute
                  top: 50%
                  left: 50%
                  width: 2px /* 棒の幅（太さ） */
                  height: 16px /* 棒の高さ */
                  background: $main-text

                &:before
                  transform: translate(-50%,-50%) rotate(45deg)
                &:after
                  transform: translate(-50%,-50%) rotate(-45deg)

                &:hover:before, &:hover:after
                  background: $main-color


        button
          position: relative
          width:52px
          height:48px
          background-color: $main-color
          border: 1px solid $main-color
          border-left: none
          border-top-right-radius: 4px
          border-bottom-right-radius: 4px
          @include sp
            width: 11vw
            height: 12vw

          img
            width: 30px
            height: auto
            @include sp
              width: 6.5vw

        input
          flex: 1 1 400px
          height: 48px
          padding: 0 0 0 20px
          font-size: .8rem
          line-height: 1
          background-color: transparent
          border: none
          @include md
            height: 30px
          @include sp
            flex: 1 1 100px
            max-width: 70vw
            padding: 0 0 0 2vw

          &::placeholder
            color: #ccc
            @include md

            @include sp
              font-size: 3vw
              transform: translateY(0)

          &.side-search-keyword
            height: 48px
            line-height: 40px
            padding-top: 0
            padding-bottom: 0
            @include md
              height: 30px
              line-height: 30px

  .search-category
    width: 100%
    padding: 20px 2rem 30px
    background-color: $main-bg-color
    @include sp
      padding: 7vw 4vw

    .ttl
      width: 100%
      margin: 0 0 2rem
      font-size: 1.2rem
      font-weight: 500
      text-align: center
      @include sp
        margin: 0 0 4vw
        font-size: 4.5vw

    .list-category
      display: flex
      flex-direction: row
      flex-wrap: wrap
      justify-content: center
      width: 980px
      max-width: 100%
      margin: 20px auto
      gap: 30px 20px
      max-height: 100px
      overflow: hidden
      transition: max-height 0.4s ease
      @include sp
        width: 100%
        max-height: 24vw
        margin: 4vw auto 4vw
        gap: 2vw 2vw

      &.expanded
        max-height: 2000px

      li
        width: 70px
        transition: all 0.3s ease
        @include sp
          width: 16vw

        a
          display: flex
          flex-direction: column
          align-items: center
          justify-content: flex-start

          figure
            width: 70px
            height: 70px
            @include sp
              width: 16vw
              height: 16vw

            img
              max-width: 100%
              max-height: 100%

          p
            margin: 5px 0 0
            font-size: 10px
            line-height: 1.3
            word-break: break-word
            text-align: center
            display: -webkit-box
            -webkit-line-clamp: 2 /* 表示したい行数 */
            -webkit-box-orient: vertical
            overflow: hidden
            @include sp
              font-size: 2.7vw

    .btn.more-category
      display: flex
      justify-content: center
      align-items: center
      width: 200px
      height: 40px
      margin: 20px auto
      background-color: #fff
      border: 1px solid #ccc
      border-radius: 50px
      @include sp
        width: 60vw
        height: 11vw

      &:hover
        opacity: .8

      span
        font-size: .8rem
        font-weight: 500
        line-height: 1.2
        @include sp
          font-size: 3.5vw

  #sch_info
    width: 100%
    margin: 0
    padding: 0
    background-color: #e5e5e5

    p
      display: flex
      align-items: center
      justify-content: center
      padding: 7px
      background-color: #D3D1D0

      img
        width: 30px
        height: auto
        margin: 0 10px

      span
        color: $main-color
        font-weight: bold

    .sch_list
      padding: 1rem
      background-color: #eee

      ul
        display: flex
        flex-direction: row
        flex-wrap: wrap
        max-width: 1280px
        margin: 0 auto
        @include sp
          flex-direction: column

        li
          width: 50%
          padding: 10px 20px
          text-indent: -1rem
          font-size: .9rem
          line-height: 1.4
          @include sp
            width: 100%
            padding: 10px 5px 10px 20px
            font-size: .8rem

          a
            /*text-decoration: underline*/
            &:hover
              color: $main-color
              opacity: 1

          &:before
            content: "・"
            margin: 0 .5rem 0 0

  #caution
    margin: 0 0 20px
    padding: 40px 1rem
    @include sp
      margin: 0
      padding: 1rem
    .message
      margin: 2rem 0 2rem
      @include sp
        margin: 1rem 0 2rem
      p
        color: $main-color
        font-size: .9rem
        font-weight: 400
        line-height: 1.8
        text-align: center
        @include sp
          font-size: .7rem

    .info-lowest-price
      width: 980px
      max-width: 100%
      margin: 0 auto
      padding: .5rem 1rem 1rem
      line-height: 1.8
      border: 1px solid $main-color
      /*background-color: #f8f4f3*/
      @include sp
        padding: .5rem .7rem .7rem
      p
        font-size: .9rem
        text-align: center
        @include sp
          font-size: .7rem

        span
          display: block
          color: $main-color
          font-weight: 600
          text-align: center

  #list-recommend
    padding: 2rem 1.5rem 3.5rem
    background-color: $main-bg-color
    @include md
      padding: 2rem calc(1.5rem - 15px) 3.5rem 1.5rem
    @include sp
      padding: 7vw 0 7vw 4vw

    h2
      @include sp
        padding: 0 4vw 0 0

    .item-list
      ul.list-item-gallery
        justify-content: center

        li
          position: relative
          display: flex
          flex-direction: column
          justify-content: space-between
          width: calc((100% - 75px) / 5)
          margin: 0 15px 15px 0
          background-color: transparent
          @include md
            width: calc((100% - 30px) / 3)
          @include sp
            width: calc((100% - 15px) / 2)
            margin: 0 4vw 4vw 0


          a
            .tab-wrap
              display: flex
              flex-direction: row
              justify-content: flex-start
              align-items: center
              gap: 3px
              width: 100%
              margin: 0 0 .2rem
              padding: 0 .2rem

              li
                width: auto
                margin: 0
                padding: 2px 7px
                border: 1px solid $main-color
                font-size: .68rem
                font-weight: 500
                display: flex
                justify-content: center
                align-items: center
                @include sp
                  font-size: 2vw

                &.tab-main
                  color: #fff
                  background-color: $main-color
                &.tab-sub
                  color: #83b8cf
                  background-color: #fff
                  border: 1px solid #83b8cf
                &.tab-standard
                  color: $main-color
                  background-color: #fff
                  border: 1px solid $main-color

                &.top
                  padding: 3px 10px
                  color: #fff
                  font-weight: 600
                  background-color: $main-red
                  border: 1px solid $main-red
                  border-radius: 4px

                &.min-bid
                  padding: 3px 8px
                  color: $main-red
                  font-weight: 600
                  background-color: #fff
                  border: 1px solid $main-red
                  border-radius: 4px

            .pre-bid
              display: flex
              flex-direction: column
              flex-wrap: wrap
              gap: 0
              width: 100%
              min-height: 38px
              margin: 0
              padding: .5rem .2rem .5rem

              @include sp
                width: 100%
                flex-wrap: wrap

              li
                display: flex
                align-items: center
                min-height: 16px
                margin: 0 0 .7rem
                font-size:.8rem
                border: none
                @include sp
                  margin: 0 0 2vw
                  font-size: 3.4vw

                &.bid-v
                  width: 70px
                  padding: 0 0 0 .6rem
                  background: url("../img/common/icn_hammer_list.png") no-repeat
                  background-size: 14px auto
                  background-position: left calc(50%)
                  @include sp
                    width: 15vw
                    padding: 0 0 0 5vw
                    background-size: 3.5vw auto
                    background-position: 0 center

                  p
                    font-weight: 700
                    line-height: 1.1
                    @include sp
                      width: 100%
                      padding: 0

                &.end-v
                  width: auto
                  padding: 0 0 0 24px
                  background: url("../img/common/icn_clock_list.png") no-repeat
                  background-size: 16px auto
                  background-position: left calc(50%)
                  @include sp
                    width: 100%
                    padding: 0 0 0 6vw
                    background-size: 3.5vw auto
                    background-position: 0 center

                  p
                    font-weight: 700
                    line-height: 1.1
                    @include sp
                      width: 100%
                      padding: 0

                    span
                      display: inline-block
                      font-weight: 700

                      &.red
                        color: #E50A09

#main
  #list-new
    padding: 80px 2rem 120px
    @include sp
      padding: 20vw 4vw

#main
  .list-item
    padding: 1rem 1.5rem
    position: relative
    @include sp
      padding: 7vw 4vw

    h2
      position: relative
      display: flex
      flex-direction: row
      align-items: baseline
      width: 1280px
      max-width: 100%
      margin: 3.5rem auto 2.5rem
      @include sp
        flex-direction: column
        margin: 1.5rem 0 2rem
        align-items: center

      .ttl
        display: flex
        align-items: center
        justify-content: center
        flex: 1 auto
        max-width: 100%
        margin: 0 auto
        font-size: 1.8rem
        font-weight: 600
        @include sp
          justify-content: center
          width: 100%
          padding: 0
          font-size: 5.8vw

      .btn.more
        position: absolute
        top: calc(50% - 14px)
        right: 0
        display: flex
        justify-content: center
        align-items: center
        width: auto
        height: 30px
        margin: 0 5px 0 auto
        padding: 0 1.5rem
        color: #fff
        font-size: .8rem
        font-weight: 500
        background-color: $main-color
        border: 1px solid $main-color
        border-radius: 50px
        @include sp
          width: auto
          height: 7vw
          padding: 0 4vw
          font-size: 3vw

    .item-list
      margin: 0
      padding: 0
      text-align: center

      &.no-item
        padding: 15px
        display: flex
        justify-content: center
        align-items: center
        background-color: #fff

        p.no-item-msg
          text-align: center
          font-size: 22px
          font-weight: 700
          color: #000

      ul
        display: flex
        flex-wrap: wrap
        justify-content: flex-start
        align-items: stretch
        width: 100%
        padding: 0
        gap: 40px 15px
        @include sp
          gap: 6vw 4vw

        li
          position: relative
          display: flex
          flex-direction: column
          justify-content: space-between
          align-items: stretch
          width: calc((100% - 60px) / 5)
          height: auto
          margin: 0
          background-color: #fff
          @include md
            width: calc((100% - 30px) / 3)
          @include sp
            width: calc((100% - 4vw) / 2)


          &.nego
            a
              position: relative

              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                aspect-ratio: 1
                padding: 0
                background-image: url(../img/common/icn_nego.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10

          &.soldout
            a
              position: relative
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                aspect-ratio: 1
                padding: 0
                background-image: url(../img/common/icn_soldout.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10

          a
            position: relative
            display: flex
            flex-direction: column
            flex-grow: 1
            height: 100%
            cursor: pointer

            &.pct-wrap
              flex: 0 0 auto

            &:not(.pct-wrap)
              display: flex
              flex-grow: 1
              flex-direction: column
              justify-content: space-between

            figure
              position: relative
              aspect-ratio: 1
              border: 1px solid $line-gray

              img
                height: 100%
                object-fit: contain

              .tab-f
                position: absolute
                top: 0
                left: 0

                span
                  display: block
                  padding: 2px 14px
                  font-size: .7rem
                  font-weight: 600
                  @include sp
                    font-size: 2.4vw

                  &.delivery
                    color: #fff
                    background-color: #8b8b8b

            .item-name
              width: 100%
              margin: 0
              padding: .5rem .2rem
              color: $main-text
              font-size: .8rem
              font-weight: 500
              line-height: 1.4
              text-align: left
              @include sp
                font-size: 3.6vw
                border-bottom: none

            .current-price
              margin: 0 .2rem .2rem
              padding: 0
              @include sp
                margin: 0 .2rem 2vw
                font-size: 3.6vw

              .price-c
                display: inline-block
                margin-right: 5px
                color: #231914
                font-size: .8rem
                transform: translateY(-1px)
                @include sp
                  font-size: 2.3vw

              .price-v
                color: #E50A09
                font-size: 1.2rem
                font-weight: 700
                @include sp
                  font-size: 4.1vw

              .price-u
                color: #E50A09
                font-size: 1rem
                font-weight: 700
                @include sp
                  font-size: 2.6vw

              .tax-u
                display: inline-block
                margin-left: 5px
                font-size: .65rem
                @include sp
                  font-size: 2.3vw

            .tab-wrap
              display: flex
              flex-direction: row
              justify-content: flex-start
              align-items: center
              gap: 3px
              width: 100%
              margin: 0 0 .2rem
              padding: 0 .2rem

              li
                width: auto
                margin: 0
                padding: 2px 7px
                border: 1px solid $main-color
                font-size: .68rem
                font-weight: 500
                display: flex
                justify-content: center
                align-items: center
                @include sp
                  font-size: 2vw

                &.tab-main
                  color: #fff
                  background-color: $main-color
                &.tab-sub
                  color: #83b8cf
                  border: 1px solid #83b8cf
                &.tab-standard
                  color: $main-color
                  border: 1px solid $main-color

                &.top
                  padding: 3px 10px
                  color: #fff
                  font-weight: 600
                  background-color: $main-red
                  border: 1px solid $main-red
                  border-radius: 4px

                &.min-bid
                  padding: 3px 8px
                  color: $main-red
                  font-weight: 600
                  border: 1px solid $main-red
                  border-radius: 4px

            .pre-bid
              display: flex
              flex-direction: column
              flex-wrap: wrap
              gap: 0
              width: 100%
              min-height: 38px
              margin: auto auto 0 0
              padding: .5rem .2rem .5rem
              @include sp
                width: 100%
                flex-wrap: wrap

              li
                display: flex
                align-items: center
                min-height: 16px
                margin: 0 0 .7rem
                font-size:.8rem
                border: none
                @include sp
                  margin: 0 0 2vw
                  font-size: 3.4vw

                &.bid-v
                  width: 70px
                  padding: 0 0 0 .5rem
                  background: url("../img/common/icn_hammer_list.png") no-repeat
                  background-size: 14px auto
                  background-position: left calc(50%)
                  @include sp
                    width: 15vw
                    padding: 0 0 0 5vw
                    background-size: 3.5vw auto
                    background-position: 0 center

                  p
                    font-weight: 700
                    line-height: 1.1
                    @include sp
                      width: 100%
                      padding: 0

                &.view
                  width: 70px
                  padding: 0 0 0 1.5rem
                  background: url("../img/common/icn_eye_list.svg") no-repeat
                  background-size: 16px auto
                  background-position: left calc(50%)
                  @include sp
                    width: 15vw
                    padding: 0 0 0 5vw
                    background-size: 3.5vw auto
                    background-position: 0 center

                  p
                    font-weight: 700
                    line-height: 1.1
                    @include sp
                      width: 100%
                      padding: 0

                &.end-v
                  width: auto
                  padding: 0 0 0 24px
                  background: url("../img/common/icn_clock_list.png") no-repeat
                  background-size: 16px auto
                  background-position: left calc(50%)
                  @include sp
                    width: 100%
                    padding: 0 0 0 6vw
                    background-size: 3.5vw auto
                    background-position: 0 center

                  p
                    font-weight: 700
                    line-height: 1.1
                    @include sp
                      width: 100%
                      padding: 0

                    span
                      display: inline-block
                      font-weight: 700

                      &.red
                        color: #E50A09

                &.favo
                  width: 70px
                  padding: 0 0 0 1.4rem
                  background: url("../img/common/icn_favorite.svg") no-repeat
                  background-size: 16px auto
                  background-position: 0 calc(50%)
                  @include sp
                    width: calc(100% - 15vw)
                    padding: 0 0 0 6vw
                    background-size: 3.5vw auto
                    background-position: 0 center

                  p
                    font-weight: 700
                    line-height: 1.1
                    @include sp
                      width: 100%
                      padding: 0

                    span
                      display: inline-block
                      font-weight: 700

                      &.red
                        color: #E50A09

            dl
              display: flex
              flex-direction: row
              width: calc(100% - (46px + 34px))
              min-height: 38px
              margin: auto auto 0 0
              padding: .5rem .2rem .5rem
              @include sp
                width: calc(100% - 16.5vw)

              dt, dd
                display: flex
                align-items: center
                margin: 0
                font-size:.8rem
                @include sp
                  font-size: 3.4vw

              dt
                width: 50px
                padding: 0 0 0 1.5rem
                background: url("../img/common/icn_hammer_list.png") no-repeat
                background-size: 16px auto
                background-position: left calc(50%)
                @include sp
                  width: 12vw
                  padding: 0 0 0 5vw
                  background-size: 3.5vw auto
                  background-position: 0 center

                .bid-v
                  font-weight: 700
                  line-height: 1.1
                  @include sp
                    width: 100%
                    padding: 0

              dd
                width: calc(100% - 50px)
                padding: 0 0 0 2rem
                background: url("../img/common/icn_clock_list.png") no-repeat
                background-size: 16px auto
                background-position: 10px calc(50%)
                @include sp
                  width: 14vw
                  padding: 0 0 0 6vw
                  background-size: 3.5vw auto
                  background-position: 0 center

                .end-v
                  font-weight: 700
                  line-height: 1.1
                  @include sp
                    width: 100%
                    padding: 0

                  span
                    display: inline-block
                    font-weight: 700

          .refresh
            position: absolute
            bottom: .2rem
            right: 42px
            width: 34px
            height: 34px
            padding: 2px 2px
            background-color: #fff
            border: 1px solid $main-red
            border-radius: 50%
            background-image: url(../img/common/icn_refresh.svg)
            background-size: 18px auto
            background-repeat: no-repeat
            background-position: 50% 47%
            cursor: pointer
            z-index: 1
            @include sp
              bottom: 1vw
              right: 9.5vw
              width: 7vw
              height: 7vw
              background-size: 3.5vw auto

            &:hover
              opacity: 1
              background-color: $main-bg-red

          .favorite
            position: absolute
            bottom: .2rem
            right: .2rem
            width: 34px
            height: 34px
            padding: 2px 2px
            background-color: #fff
            border: 1px solid $line-gray
            border-radius: 50%
            background-image: url(../img/common/icn_favorite.svg)
            background-size: 16px auto
            background-repeat: no-repeat
            background-position: 50% 56%
            cursor: pointer
            z-index: 1
            @include sp
              bottom: 1vw
              right: 1vw
              width: 7vw
              height: 7vw
              background-size: 3.5vw auto

            &:hover
              opacity: 1
              background-image: url(../img/common/icn_favorite_blue.svg)
              border: 1px solid $main-color

            &.active
              background-image: url(../img/common/icn_favorite_blue.svg)
              border: 1px solid $main-color

      .wrap-btn
        width: 100%
        margin: 3rem 0
        text-align: center

        .list-more
          width: 300px
          height: 56px
          margin: 0 auto
          padding: .5rem 2rem
          color: #fff
          font-size: 1rem
          font-weight: 700
          background-color: $main-color
          border-radius: 4px
          @include sp
            height: 60px


#info
  padding: 100px 2rem
  @include sp
    padding: 7vw 4vw 10vw

  .container-grid
    display: grid
    gap: 10px 40px
    grid-template-columns: 200px auto
    grid-template-rows: auto auto
    width: 1180px
    max-width: 100%
    margin: 0 auto
    @include sp
      display: block
      width: 100%

    h2
      grid-column: 1
      grid-row: 1
      display: flex
      flex-direction: column
      justify-content: center
      align-items: flex-start
      gap: 0
      padding: 0
      font-size: 3rem
      font-weight: 600
      @include sp
        min-height: 50px
        padding: 4vw 2vw
        font-size: 8vw

      span
        display: block
        font-size: 1rem
        font-weight: 500
        @include sp
          font-size: 4.5vw

    .more-btn-wrap
      grid-column: 1
      grid-row: 2
      @include sp
        padding: 7vw 0

      .btn
        display: flex
        justify-content: center
        align-items: center
        width: 100%
        height: 40px
        font-size: .8rem
        font-weight: 500
        border: 1px solid #ccc
        border-radius: 50px
        @include sp
          width: 60vw
          height: 11vw
          margin: 0 auto
          font-size: 3.5vw

        &:hover
          background-color: #f5f5f5

    .info-item
      grid-column: 2
      grid-row: 1 / span 2
      padding: 1rem
      @include sp
        grid-column: 1
        grid-row: 2
        padding: 0

      li
        padding: 0
        border-bottom: 1px solid #ccc

        &:first-child
          border-top: 1px solid #ccc

        a
          display: block
          width: 100%
          height: 100%
          padding: 1rem 1.5rem
          font-weight: 400
          position: relative
          letter-spacing: 0
          @include sp
            padding: 4vw 2vw
            font-size: 4vw
            line-height: 1.3

          &:hover
            background-color: #f5f5f5

          span
            display: inline-block
            margin-right: 1.5rem
            font-size: .9rem
            @include sp
              display: block
              margin: 0 0 2vw
              font-size: 3.5vw


#live_contents
  margin: 0

  .wrap
    width: 100%
    position: relative
    padding-top: 56.25%

    iframe
      position: absolute
      top: 0
      left: 0
      width: 100%
      height: 100%

#about
  width: 100%
  padding: 160px 4rem
  display: flex
  justify-content: center
  align-items: center
  position: relative
  width: 100%
  height: auto
  background: url("../img/home/<USER>") center (top / cover) no-repeat
  @include sp
    width: 100%
    height: auto
    padding: 18vw 11vw
    background: url("../img/home/<USER>") center no-repeat
    background-size: cover

  .cont-wrap
    width: 1280px
    max-width: 100%
    margin: 0 auto

    h2
      display: flex
      flex-direction: column
      justify-content: baseline
      align-items: flex-start
      margin: 0
      .ttl
        margin: 0 0 .5rem
        font-size: 2rem
        font-weight: 600

      .s-ttl
        margin: 0 0 1rem
        font-size: 1.2rem
        font-weight: 600

    .read
      p
        font-weight: 600
        &.sb
          margin: .5rem 0 0

    .btn-wrap
      width: 100%
      margin: 3rem 0
      @include sp
        margin: 10vw 0

      a.btn
        display: flex
        justify-content: center
        align-items: center
        width: 280px
        height: 60px
        color: $main-text
        font-size: 1rem
        font-weight: 600
        background-color: #fff
        border: 1px solid $line-gray
        border-radius: 50px
        @include sp
          width: 100%
          height: 15vw
          margin: 4vw auto
          font-size: 3.9vw


#signup
  width: 100%
  padding: 80px 2rem 100px
  display: flex
  justify-content: center
  align-items: center
  position: relative
  width: 100%
  height: auto
  @include sp
    width: 100%
    height: auto
    padding: 6vw 4vw 8vw

  .cont-wrap
    width: 1280px
    max-width: 100%
    margin: 0 auto
    padding: 70px 2rem 60px
    background-color: $main-bg-color
    @include sp
      padding: 14vw 7vw

    h2
      display: flex
      flex-direction: column
      height: auto
      margin: 0

      &:after
        content: none

      .s-ttl
        position: relative
        display: block
        width: max-content
        margin: 0 auto
        font-size: 1.2rem
        font-weight: 600
        text-align: center
        @include sp
          margin: 0 auto 2vw

        &:before
          content: ""
          position: absolute
          top: 6px
          left: -32px
          width: 1px
          height: 100%
          margin: 0
          background-color: $main-text
          transform: rotate(-45deg)
          transform-origin: top left

        &:after
          content: ""
          position: absolute
          top: 5px
          right: -30px
          width: 1px
          height: 100%
          margin: 0
          background-color: #000
          transform: rotate(45deg)
          transform-origin: top left

      .ttl
        margin: 0
        font-size: 2rem
        font-weight: 600
        text-align: center
        line-height: 1.3
        @include sp
          font-size: 7vw

    .read
      width: 700px
      max-width: 100%
      margin: 0 auto
      padding: 2rem
      @include sp
        width: 100%
        padding: 4vw 0

      p
        font-size: .9rem
        @include sp
          font-size: 3.5vw

        &.sb
          margin: 1rem 0 0
          font-size: 1rem
          font-weight: 600
          @include sp
            font-size: 3.9vw

        a
          color: $main-color
          &:hover
            text-decoration: underline

    .btn-wrap
      display: flex
      flex-direction: row
      justify-content: center
      gap: 40px
      width: 100%
      margin: 2rem auto
      @include sp
        flex-direction: column
        gap: 6vw
        margin: 4vw 0

      a.btn
        display: flex
        justify-content: center
        align-items: center
        width: 280px
        height: 60px
        color: #fff
        font-size: 1rem
        font-weight: 600
        background-color: $main-color
        border-radius: 50px
        @include sp
          width: 100%
          height: 15vw
          margin: 0 auto
          font-size: 3.9vw

        &.more
          color: $main-color
          background-color: #fff
          border: 1px solid $main-color
