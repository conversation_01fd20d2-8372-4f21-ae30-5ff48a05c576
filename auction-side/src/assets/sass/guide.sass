@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *ガイド
 ***********************************************************************

#main #guide
  padding-bottom: 60px
  @include sp
    padding-bottom: 40px

  ul.step
    padding-top: 10px

    > li
      margin-top: 30px
      background-color: #F7F7F7
      position: relative

      &::before
        content: ""
        display: block
        position: absolute
        top: 70px
        bottom: 20px
        left: 48px
        width: 34px
        height: auto
        background-color: $main-color
        @include sp
          display: none

      &::after
        content: ""
        display: block
        position: absolute
        bottom: -20px
        left: 35px
        border-top: 40px solid $main-color
        border-right: 30px solid transparent
        border-left: 30px solid transparent
        @include sp
          left: 50%
          transform: translateX(-50%)
          border-top-width: 30px

      &:last-of-type
        &::before, &::after
          display: none

      .conts
        padding: 40px 40px 40px 160px
        @include sp
          padding: 20px 15px 35px


      h3
        display: flex
        align-items: center
        height: 70px
        margin: 1rem 0 2rem
        font-weight: 700
        font-size: 22px
        border-bottom: 2px solid #EAEAEA
        position: relative
        @include sp
          height: auto
          min-height: 55px
          margin: 1rem 0
          padding: 10px 15px 10px 0
          font-size: 18px

        &::after
          content: ""
          display: block
          position: absolute
          bottom: -2px
          left: 0
          width: 130px
          height: 2px
          background-color: $main-color
          @include sp
            width: 100px

        em
          display: block
          width: 130px
          font-weight: 700
          color: $main-color
          text-align: center
          margin-right: 30px
          flex-shrink: 0
          @include sp
            width: 100px
            margin-right: 15px

      .outline
        display: flex
        font-size: 18px
        justify-content: space-between
        height: auto
        @include sp
          display: block
          font-size: 1rem


        .out-txt
          align-self: start

      &.step-1 .outline .out-txt
        padding-top: 10px
        @include sp
          padding-top: 0

      .outline
        .out-txt p.att
          text-indent: -2em
          padding-left: 2em

        .out-img
          flex-shrink: 0
          margin: 0 40px 0 80px
          align-self: start
          @include sp
            margin-left: 0
            margin: 20px 0 0
            text-align: center

          img
            max-width: 180px
            @include sp
              width: 45%

      &.step-1
        .btn-signup
          width: 100%
          max-width: 370px
          margin: 30px auto 0

          a span
            font-weight: 700
            font-size: 1rem
            @include sp
              font-size: 12px

        .judge
          background-color: #fff
          margin-top: 40px
          padding: 30px 10px 30px 40px
          @include sp
            padding: 20px

/* ---------------------------
 *STEP2
 *-----------------------------

/* タブ

main #guide ul.step > li.step-2
  #tab_btn
    overflow: hidden
    display: flex
    justify-content: space-between

    li
      width: 33.1%
      @include sp
        width: 32%

      a
        display: flex
        align-items: center
        flex-wrap: wrap
        height: 60px
        background-color: #EAEAEA
        color: $main-color
        font-size: 18px
        font-weight: 700
        text-decoration: none
        position: relative
        border-radius: 20px 20px 0 0
        padding: 5px 25px
        @include sp
          border-radius: 10px 10px 0 0
          padding: 10px
          height: 100%
          font-size: 13px
          line-height: 1.4

        &:hover
          opacity: 1

      &.active a
        background-color: $main-color
        color: #fff

  #tab_info > li
    border: 2px solid $main-color
    background-color: #fff
    padding: 40px
    font-size: 18px
    @include sp
      padding: 1rem

    h4
      font-weight: 700
      color: $main-color
      margin-top: 40px
      @include sp
        margin-top: 30px

      &:first-of-type
        margin-top: 0

      + p
        margin-top: 5px

    p.assist-h4
      font-size: 22px
      font-weight: 700
      margin-top: 50px
      @include sp
        margin-top: 40px
        font-size: 20px

      + h4
        margin-top: 20px
        @include sp
          margin-top: 10px

    a.btn-list
      margin: 40px 0 0
      line-height: 1.2
      @include sp
        margin: 20px 0 0

    .schedule
      display: flex
      justify-content: space-between
      margin-top: 15px
      @include sp
        display: block

      figure
        width: 340px
        @include sp
          width: 100%

      .schedule_txt
        width: 500px
        @include sp
          width: 100%
          margin-top: 20px

        .schedule_btn
          width: 100%
          max-width: 370px
          margin: 15px auto 0

    p.method
      font-size: 23px
      font-weight: 700
      color: $main-color
      background-color: #fef6f6
      border: 1px solid $main-color
      border-radius: 100vh
      padding: 10px 20px
      text-align: center
      margin-top: 15px
      @include sp
        border-radius: 10px
        font-size: 18px
        padding: 10px 15px

    .bid-graph
      margin-top: 20px
      border: 1px solid #E3E3E3
      text-align: center
      padding: 30px 5px
      @include sp
        padding: 20px 10px 10px

      img
        display: inline-block
        margin-top: 20px

      p
        &.ex-txt
          text-align: center
          font-weight: 700
          font-size: 1rem
          @include sp
            font-size: .9rem

        &.ex-fds
          border: 2px solid #E3E3E3
          border-radius: 20px
          width: 800px
          max-width: calc(100% - 1.6rem)
          margin: 15px auto 0
          font-size: .9rem
          text-align: center
          padding: 15px
          position: relative
          @include sp
            width: 100%
            max-width: calc(100% - .4rem)
            margin: 20px auto 1rem
            font-size: 14px
            text-align: left
            border-radius: 10px
            padding: 10px 15px

          &::before, &::after
            content: ""
            display: block
            position: absolute
            right: 50%
            transform: translateX(50%)

          &::before
            border-right: 10px solid transparent
            border-bottom: 16px solid #E3E3E3
            border-left: 10px solid transparent
            top: -16px

          &::after
            border-right: 10px solid transparent
            border-bottom: 16px solid #fff
            border-left: 10px solid transparent
            top: -13px
