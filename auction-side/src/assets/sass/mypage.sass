@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *マイページ
 ***********************************************************************

/* Nav
 *==========================================

#main
  #mypage-head
    padding: 0

    .nav-wrap
      display: flex
      flex-direction: row
      gap: 0
      width: 100%
      margin: 3rem 0
      border-bottom: 3px solid #000
      @include sp
        flex-wrap: wrap

      .nav-content
        flex: 1
        height: 60px
        margin: 0
        background-color: #f1f1f1
        @include sp
          width: calc((100% - .5rem)/2)
          height: 50px
          margin: 0

        a
          position: relative
          display: flex
          justify-content: center
          align-items: center
          flex-direction: row
          width: 100%
          height: 100%
          padding: 0

          &:hover
            background-color: #f5f5f5

          span
            position: absolute
            top: calc(50% - 9px)
            left: 28%
            display: inline-block
            content: ""
            background-position: center
            background-size: 20px auto
            width: 20px
            height: 20px

            &.favorite
              background-image: url(../img/common/icn_mypage_nav_favorite.svg)
            &.bidding
              background-image: url(../img/common/icn_mypage_nav_bid_w.svg)
            &.winning-history
              background-image: url(../img/common/icn_mypage_nav_winning-history.svg)
            &.account
              background-image: url(../img/common/icn_mypage_nav_account.svg)

          .label
            display: inline-block
            padding: 0 0 0 1.5rem
            color: $main-gray
            font-size: 1rem
            font-weight: 600
            line-height: 1.2

      .nav-content.active
        background-color: $main-gray

        a
          cursor: default
          &:hover
            opacity: 1

        .label
          color: #fff

  #mypage-form
    margin: 0 0 60px
    padding: 0 0 1rem
    @include sp
      margin: 0 0 40px
      padding: 0 0 1rem