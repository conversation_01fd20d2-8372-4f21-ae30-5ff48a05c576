@charset "utf-8"
@import "./mixin"

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *wrap
 ***********************************************************************/

.container
  width: 1280px
  max-width: 100%
  margin: 0 auto
  padding: 1rem
  @include sp
    width: 100%
    max-width: 100%
    padding: 4vw


/***********************************************************************
 *
 *------------------------------------------------------------------------
 *header
 ***********************************************************************/

header
  border-bottom: 1px solid $main-p-gray
  background-color: #fff
  position: relative
  @include sp
    height: 12vw
    border-bottom: 1px solid $line-gray

  .wrap-header-elm
    display: flex
    flex-direction: row
    justify-content: space-between
    align-items: center
    width: calc(1280px + 2rem)
    height: 94px
    max-width: calc(100%)
    margin: 0 auto
    padding: 0 1rem
    @include md
      height: auto
      padding: .5rem 1.5rem
    @include sp
      width: 100%
      max-width: 100%
      height: 12vw
      margin: 0
      padding: 0

    .h-top
      display: flex
      justify-content: space-between
      align-items: center
      flex: 0 1 200px
      height: 65px
      padding: 0
      @include md
        height: auto
      @include sp
        position: relative
        display: flex
        flex-direction: row
        justify-content: space-between
        width: 100%
        min-height: 35px
        padding: 0 1.5vw
        height: 12vw
        flex: auto

      .h-top-logo
        display: flex
        align-items: center
        @include sp
          position: absolute
          left: 50%
          transform: translateX(-50%)
          padding-left: 15px
          padding-right: 15px
          height: 50px

        a.logo
          display: flex
          align-items: center
          width: 200px
          max-width: 38vw
          transition: none
          @include sp
            width: 30vw
            max-width: 200px

          img
            width: 100%
            height: auto

        p
          color: #fff
          font-size: 16px
          position: relative
          line-height: 1
          margin-left: 22px
          padding-left: 20px
          @include sp
            font-size: 2.5vw
            margin-left: 11px
            padding-left: 10px
            &::before
              width: 1px
              height: 15px

          &::before
            content: ""
            display: block
            width: 2px
            height: 28px
            background-color: #fff
            position: absolute
            top: 50%
            left: 0
            transform: translateY(-50%)

      .h-top-menu
        display: flex
        flex: 1 1 45%
        align-items: center
        justify-content: flex-end
        margin: 0 .2rem 0 0
        @include sp
          flex: 0
          margin: 0 1vw 0 0

        ul
          display: flex
          flex-direction: row
          @include sp
            gap: .8vw

          li
            display: flex
            align-items: center
            justify-content: center
            width: 38px
            height: 38px
            border: .5px solid #fff
            @include sm
              width: 7vw
              height: 7vw
              margin-right: 0

            a
              display: flex
              align-items: center
              justify-content: center
              width: 100%
              height: 100%
              color: #fff
              font-weight: 700
              font-size: 1rem
              background-repeat: no-repeat
              background-position: left center
              @include sp
                font-size: 3.5vw

              &.btn-favorite
                img
                  width: 24px
                  height: auto
                  @include sp
                    width: 5.2vw

              &.btn-hammer
                img
                  width: 20px
                  height: auto

              &.btn-member
                img
                  width: 22px
                  height: auto
                  @include sp
                    width: 4.8vw

        .lang
          padding-left: 20px
          position: relative
          @include sp
            position: absolute
            top: 14px
            right: 50px
            padding-left: 0
            padding-right: 15px

          &::before
            content: ""
            display: block
            width: 2px
            height: 20px
            background-color: #fff
            position: absolute
            top: 50%
            left: 0
            transform: translateY(-50%)
            @include sp
              width: 1px
              height: 15px
              left: auto
              right: 0
          a
            @include sp
              font-size-adjust: 13px

    .nav-elm
      display: flex
      flex-direction: row
      @include md
        flex-direction: column

      .search-elm
        display: flex
        align-items: center
        height: auto
        margin: 0 1.5rem
        @include md
          justify-content: flex-end
          margin: 0 .5rem
          padding: .5rem 0

        .search-category
          display: flex
          align-items: center
          position: relative
          width: 116px
          height: 100%
          margin: 0 1rem 0 0
          color: $main-gray

          li
            display: flex
            align-items: center
            position: relative
            width: 100%
            height: 50px
            padding: 0
            list-style-type: none
            @include md
              height: 30px

            a.nav-label
              padding: 0
              display: flex
              align-items: center
              justify-content: flex-start
              width: 100%
              height: 100%
              color: $main-text
              font-size: .8rem
              font-weight: 500

              &:after
                content: ""
                position: absolute
                right: 6px
                top: 50%
                width: 4px
                height: 4px
                border-right: 2px solid #bcbcbc
                border-bottom: 2px solid #bcbcbc
                transform: translateY(-50%) rotate(45deg)

              &:hover
                color: $main-color
                opacity: 1
                &:after
                  border-color: $main-color

          &:hover .menu-list
            display: block

          .menu-list
            display: none/*design確認用*/
            position: absolute
            top: 50px
            left: -180px
            width: 800px
            margin: 0 auto
            padding: 0
            z-index: 20
            @include md
              top: 30px
              left: -180px
              width: 650px

            .arrow-box
              width: 100%
              height: 16px
              @include md

            .panel-wrap
              width: 100%
              padding: 20px 10px
              background-color: #f5f5f5
              border-radius: 10px
              box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35)

              .category-box
                display: flex
                flex-direction: row
                padding: .5rem 1rem

                .category
                  &-top
                    width: 150px
                    padding: 2px 5px
                    border-right: 1px dotted #ccc

                    p
                      font-size: .8rem
                      font-weight: 500

                      a
                        display: inline-block
                        padding: 1px 16px
                        color: $main-text
                        font-size: .8rem
                        font-weight: 500
                        transition: all 0.3s ease
                        white-space: nowrap
                        cursor: pointer

                        &:hover
                          opacity: 1
                          color: #fff
                          background-color: $main-color
                          border-radius: 50px


                  &-secondary
                    flex: 1

                    ul
                      display: flex
                      flex-direction: row
                      flex-wrap: wrap
                      width: 100%
                      padding-left: 10px

                      &:before
                        content: ""
                        position: absolute
                        top: 0
                        left: 220px
                        width: 0
                        height: 0
                        border-left: 10px solid transparent
                        border-right: 10px solid transparent
                        border-bottom: 16px solid #f5f5f5
                        @include md
                          left: 230px

                      li
                        position: relative
                        width: auto
                        height: auto
                        padding: 1px 2px

                        a
                          display: inline-block
                          padding: 1px 16px
                          color: $main-text
                          font-size: .8rem
                          font-weight: 500
                          transition: all 0.3s ease
                          white-space: nowrap

                          &:hover
                            opacity: 1
                            color: #fff
                            background-color: $main-color
                            border-radius: 50px


        .info-menu
          display: flex
          align-items: center
          position: relative
          width: 106px
          height: 100%
          margin: 0 0 0 1.5rem
          color: $main-gray

          li
            display: flex
            align-items: center
            position: relative
            width: 100%
            height: 50px
            padding: 0
            list-style-type: none
            @include md
              height: 30px

            a.nav-label
              padding: 0
              display: flex
              align-items: center
              justify-content: flex-start
              width: 100%
              height: 1rem
              color: $main-text
              font-size: .75rem
              font-weight: 500

              &:after
                content: ""
                position: absolute
                right: 6px
                top: calc(50% - 0px)
                width: 4px
                height: 4px
                border-right: 2px solid #bcbcbc
                border-bottom: 2px solid #bcbcbc
                transform: translateY(-50%) rotate(45deg)

              &:hover
                color: $main-color
                opacity: 1
                &:after
                  border-color: $main-color

          &:hover .menu-list
            display: block

          .menu-list
            display: none/*design確認用*/
            position: absolute
            top: 50px
            z-index: 20
            @include md
              top: 26px
              left: -100px

            .arrow-box
              width: 100%
              height: 16px

            ul
              width: 220px
              padding: 10px 0
              background-color: #f5f5f5
              border-radius: 10px
              box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35)

              &:before
                content: ""
                position: absolute
                top: 0
                left: 30px
                width: 0
                height: 0
                border-left: 10px solid transparent
                border-right: 10px solid transparent
                border-bottom: 16px solid #f5f5f5
                @include md
                  left: 134px
                @include sp
                  left: 80px

              li
                position: relative
                width: 220px
                height: 40px

                a
                  display: flex
                  align-items: center
                  justify-content: flex-start
                  width: 100%
                  height: 100%
                  padding: 20px 20px
                  color: $main-gray
                  font-size: .8rem
                  font-weight: 500
                  transition: all 0.3s ease

                  &:hover
                    opacity: 1
                    color: #fff
                    background-color: $main-color

        .search-keyword
          width: 260px
          height: 50px
          border: 1px solid #e5e5e5
          border-radius: 60px
          @include md
            width: auto
            height: 30px

          button
            position: relative
            width:52px
            height:48px
            background-color: transparent
            border-top-left-radius: 50px
            border-bottom-left-radius: 50px
            @include md
              width: 34px
              height: 30px
              transform: translateY(-2px)
            @include sp
              width: 40px
              border-left: none

            img
              width: 22px
              height: auto
              @include md
                width: 24px

          input
            width: 200px
            height: 48px
            padding: 0 0 0 20px
            font-size: .8rem
            line-height: 1
            background-color: transparent
            border: none
            @include md
              height: 30px
              padding: 0 0 0 16px
            @include sp
              width: 170px

            &::placeholder
              color: #ccc
              @include md
                font-size: .8rem
                transform: translateY(-2px)

            &.side-search-keyword
              height: 40px
              line-height: 40px
              padding-top: 0
              padding-bottom: 0
              @include md
                height: 30px
                line-height: 30px

      .nav-btn
        display: flex
        flex-direction: row
        @include md
          justify-content: flex-end
          gap: 20px
          margin: 0
          padding: .5rem

        .nav-mypage
          width: 56px
          height: 50px
          color: $main-text
          display: flex
          justify-content: center
          align-items: center
          @include md
            width: auto
            height: auto
            margin: 0

          a
            text-align: center
            @include md

            &:hover
              @include sp
                color: $main-color

          img
            width: 20px
            height: auto
            margin: 0 auto
            @include md
              display: none
            @include sp
              width: 24px

            &.bid
              width: 18px

            &.bidded
              width: 18px

          span
            width: 100%
            display: block
            color: $main-text
            font-size: 10px
            font-weight: 500
            text-align: center
            line-height: 1
            @include md
              font-size: 13px
            @include sp
              display: none


/* ヘッダー
 *==========================================

/* ---------------------------
 *ボタン
 *-----------------------------

@media only screen and (max-width: 767px)

  header .h-top .h-top-menu a

    &.btn-bid
      background-size: 18px auto
      padding-left: 23px

    &.btn-fav
      background-size: 15px auto
      padding-left: 20px

    &.btn-logout
      background-size: 15px auto
      padding-left: 18px

    &.btn-page img
      width: 16px

    &.btn-lang
      background-size: 15px auto
      padding-left: 20px


/* ---------------------------
 *SPハンバーガーメニュー
 *-----------------------------

header .h-top p.btnMenu
  position: relative
  width: 60px
  height: 60px
  flex: 0 0 60px

  span.ham
    &::before
      content: ""
      display: block
      background-color: #484439
      width: 20px
      height: 2px
      border-radius: 2px
      position: absolute
      top: 29px
      left: 1px
      transform: translateX(14px)
      transition: all 0.3s ease

  &::before, &::after
    content: ""
    display: block
    background-color: #484439
    width: 32px
    height: 2px
    border-radius: 2px
    position: absolute
    right: 50%
    transform: translateX(50%)
    transition: all 0.3s ease

  &::before
    top: 20px

  &::after
    bottom: 20px

  &.close
    &::before
      top: 50%
      transform: translate(50%, -50%) rotate(45deg)
      transition: all 0.3s ease

    &::after
      top: 50%
      bottom: auto
      transform: translate(50%, -50%) rotate(-45deg)
      transition: all 0.3s ease

    span.ham
      &::before
        display: none
        transition: all 0.3s ease


/* グロナビ
 *==========================================

/* ---------------------------
 *PC
 *-----------------------------

  header .gNav
    background-color: #fff
    border-bottom: 1px solid #d9d9d9
    display: block !important

    nav
      a
        font-size: 14px
        font-weight: 700

      > ul
        display: flex

        > li
          border-left: 1px solid #d9d9d9
          width: calc(100% / 9)
          height: 50px
          position: relative

          &:last-of-type
            border-right: 1px solid #d9d9d9

          &::after
            content: ""
            display: block
            width: 100%
            height: 2px
            position: absolute
            bottom: -1px
            left: 0
            right: 0
            background-color: transparent
            transition: all 0.08s linear

          &:hover::after
            background-color: #01a7ac

          > a
            display: flex
            align-items: center
            justify-content: center
            width: 100%
            height: 100%
            padding: 5px 10px

          &.nav-black > a
            background-color: #333F48
            color: #fff

          > a:hover
            opacity: 1

          width: calc((100% - 80px - 130px) / 8)

          &.nav-top
            width: 80px

          &.nav-first
            width: 130px

          > ul
            display: none
            position: absolute
            top: 51px
            left: 0
            background-color: rgba(255, 255, 255, 0.9)
            width: 100%
            padding: 15px 0
            z-index: 1

          &.nav-access > ul
            width: 190px

            /* 親メニュ幅より大きく

          &.nav-entry > ul
            width: 125px

            /* 親メニュ幅より大きく

          &.nav-overview > ul
            width: 140px

            /* 親メニュ幅より大きく

          &:hover > ul
            display: block
            animation-name: fade-basic
            animation-duration: .5s

          > ul li a
            display: block
            padding: 7px 15px
            color: #000

/* ---------------------------
 *SP　ヘッダーナビ
 *-----------------------------

header .gNav
  @include sp
    display: none
    position: absolute
    top: 66px
    left: 0
    right: 0
    width: 100%
    height: 100vh
    background-color: #fbfbfb
    z-index: 100

    nav
      a
        font-size: 16px

      > ul > li
        border-bottom: 1px solid $line-gray

        >
          a
            min-height: 50px
            padding: 5px 40px 5px 30px
            position: relative

          p
            font-size: 16px
            display: flex
            align-items: center
            font-weight: 700
            min-height: 50px
            padding: 4vw 10vw 4vw 4vw
            position: relative

        &.account
          display: flex
          flex-direction: row
          justify-content: center
          gap: 3vw
          padding: 3vw 4vw

          .btn
            display: flex
            justify-content: center
            align-items: center
            width: calc(50% - 6vw)
            padding: 4vw 4vw
            color: #fff
            font-size: 3.8vw
            font-weight: 600
            line-height: 1.1
            border-radius: 4px

            &.entry
              background-color: $main-red
            &.login
              background-color: $main-color
            &.mypage
              background-color: $main-red
            &.logout
              color: $main-text
              background-color: #fff
              border: 1px solid $main-text

        &.search
          display: flex
          align-items: center
          padding: .8rem 1rem
          background-color: #5D5958

          input
            height: 44px
            width: calc(100% - 45px)
            padding: 11px 18px 13px
            border-right: none
            border-top-left-radius: 4px
            border-bottom-left-radius: 4px
            @include sp
              border: none

          button
            width: 43px
            height: 44px
            background-color: #fff
            border: 1px solid #e4e4e4
            border-left: none
            border-top-right-radius: 4px
            border-bottom-right-radius: 4px
            @include sp
              border: none

        &.nav-black >
          a, p
            background-color: #f5f5f5
            color: $main-text
            font-size: 3.8vw

        >
          a::after
            content: ""
            display: block
            position: absolute
            top: 50%

          p
            &::before, &::after
              content: ""
              display: block
              position: absolute
              top: 50%

        &:not(.nav-black)
          > ul li a::after
            content: ""
            display: block
            position: absolute
            top: 50%

          > ul li a::after
            width: 8px
            height: 8px
            border-top: #000 2px solid
            border-right: #000 2px solid
            transform: rotate(45deg) translateY(-50%)
            right: 28px

        > a::after
          width: 8px
          height: 8px
          border-top: #000 2px solid
          border-right: #000 2px solid
          transform: rotate(45deg) translateY(-50%)
          right: 28px

        &.nav-black
          > a::after
            border-top-color: $line-gray
            border-right-color: $line-gray

          > p
            &::before, &::after
              background-color: #666

          > ul li
            border-top: none
            display: block
            width: 100%

        > p
          &::before
            width: 16px
            height: 2px
            background-color: #000
            transform: translateY(-50%)
            right: 20px

          &::after
            width: 2px
            height: 16px
            background-color: #000
            transform: translateY(-50%) rotate(0deg)
            transition: all 0.3s ease
            right: 27px

        >
          p.close::before
            display: none
            transition: all 0.3s ease

          p.close::after
            transform: translateY(-50%) rotate(90deg)
            transition: all 0.3s ease

          ul
            display: none
            padding: 0
            border-top: 1px solid $line-gray
            background-color: #fff

            li
              + li
                border-top: 1px solid $line-gray

              &:first-child > a
                border-top: none

              a
                display: flex
                align-items: center
                width: 100%
                color: $main-text
                font-size: 3.6vw
                font-weight: 500
                min-height: 40px
                padding: 4vw 10vw
                position: relative
                border-top: 1px solid #e7e7e7

                &:after
                  content: ""
                  width: 6px
                  height: 6px
                  border: 0
                  border-top: solid 2px #666
                  border-right: solid 2px #666
                  position: absolute
                  top: calc(50% + 1px)
                  right: 5.7vw
                  margin-top: -4px
                  -webkit-transform: rotate(45deg)
                  transform: rotate(45deg)

          p.ttl
            padding: 0
            &:before,&:after
              display: none
            a
              display: block
              width: 100%
              height: 100%
              padding: 5px 1rem
              color: #fff
              font-weight: 700

        &:not(.nav-black) > ul li a
          padding-left: calc(30px + 1em)

      .line-logo
        width: 100%
        padding: 60px 0
        background-color: #fff
        @include sp
          padding: 10vw

        .cont-wrap
          display: flex
          flex-direction: row
          justify-content: space-between
          align-items: center
          width: 1180px
          max-width: 100%
          margin: 0 auto
          padding: 0 2rem
          @include sp
            width: 100%
            flex-direction: column

          .pct
            display: flex
            justify-content: space-between
            align-items: center
            width: 160px
            height: 100%
            @include sp
              justify-content: center
              width: 60%
              margin: 0 auto 5vw

            a

              &:hover
                opacity: .8

            img
              width: 100%
              height: auto

          .sns
            ul
              display: flex
              flex-direction: row
              gap: 8px

              li
                width: 40px
                height: 40px
                padding: 0

                a
                  display: flex
                  justify-content: center
                  align-items: center
                  width: 100%
                  height: 100%

                  img
                    width: 30px
                    height: auto

                    &.facebook
                      width: 26px
                    &.x
                      width: 22px
                    &.instagram
                      width: 24px

      .line-copyright
        max-width: 100%
        margin: 0 auto
        width: 100%
        padding: 24px 1rem
        border-top: 1px solid $line-gray

        .cont-wrap
          display: flex
          flex-direction: row
          justify-content: space-between
          align-items: center
          width: 1180px
          max-width: 100%
          margin: 0 auto
          padding: 0 2rem
          @include sp
            width: 100%
            flex-direction: column

          ul
            display: flex
            justify-content: space-between
            align-items: center
            flex-direction: row
            @include sp
              width: 100%
              flex-direction: column
              padding: 2vw 0 6vw

            li
              width: auto
              padding: 0 1.5rem 0 0
              @include sp
                padding: 2vw 4vw

              a
                padding: 0
                font-size: 13px

                &:hover
                  text-decoration: underline


          .copyright
            small
              padding: 0
              font-size: 10px
              font-weight: 400
              @include sp
                font-size: 2vw



/***********************************************************************
 *
 *------------------------------------------------------------------------
 *main
 ***********************************************************************

#main
  display: block

  &.stock, &.auction
    padding-bottom: 60px
    @include sp
      padding-bottom: 40px

  #pNav
    padding: 0 1rem
    border-bottom: 1px solid $main-p-gray
    overflow: hidden

    ul
      width: 100%
      max-width: 1280px
      display: flex
      flex-wrap: nowrap
      padding: 10px 0
      margin: 0 auto
      overflow-x: auto
      white-space: nowrap
      @include sp
        width: 100%



      li
        font-size: .75rem

        &:not(:last-of-type)::after
          content: "/"
          display: inline-block
          margin: 0 .8rem

        a
          color: $main-color
          &:hover
            text-decoration: underline

/* パンくずリスト
 *==========================================

  #main #pNav > ul
    @include sp
      overflow-x: auto
      word-break: keep-all
      white-space: wrap
      -webkit-overflow-scrolling: touch

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *footer
 ***********************************************************************

footer
  position: relative
  background-color: #e8e8e8
  color: #fff
  padding: 0
  @include sp
    padding: 0
    background-color: #fff

  #page_top
    position: fixed
    display: block
    width: 42px
    height: 42px
    right: 1rem
    bottom: 1rem
    background-color: $main-color
    border-radius: 50%
    z-index: 10

    a
      display: block
      width: 100%
      height: 100%
      text-decoration: none
      cursor: pointer
      z-index: 11

      &:hover
        opacity: .7

      &:before
        content: ''
        width: 6px
        height: 6px
        border: 0
        border-top: solid 2px #fff
        border-right: solid 2px #fff
        position: absolute
        top: calc(50% + 1px)
        left: calc(50% - 4px)
        margin-top: -4px
        transform: rotate(-45deg)

  nav
    width: 1180px
    max-width: 100%
    margin: 0 auto
    padding: 60px 2rem 80px
    @include md
      padding: 60px 2rem 80px
    @include sp
      margin: 0
      padding: 0

    ul li a
      display: inline-block
      color: $main-text
      font-size: 14px
      @include sp
        position: relative
        padding: 5px 1rem

      &[target="_blank"]::after
        content: ""
        display: inline-block
        background: url("../img/common/ic_link_blank_gray.svg") (center / cover) no-repeat
        width: 12px
        height: 12px
        position: relative
        top: 1px
        margin-left: 6px
        @include sp
          content: none
          background: none


    .fNav_pc
      display: flex
      flex-direction: row
      justify-content: center
      gap: 60px
      @include md
        gap: 4vw

      .fNav-1
        width: 77%
        padding: 0 20px 0 0
        @include md
          width: 70%

        ul.list
          display: flex
          flex-direction: row
          flex-wrap: wrap
          justify-content: space-between

          li
            p
              margin: 0 0 .5rem
              color: $main-text
              font-size: .9rem
              font-weight: 700

            ul
              display: flex
              flex-direction: column
              li
                a
                  font-size: .8rem

      .fNav-2
        display: flex
        justify-content: flex-end
        width: 23%
        padding: 0 0 0 40px
        border-left: 1px solid #ccc
        @include md
          width: 20%
          padding: 0 0 0 4vw

        ul
          margin: 0 auto 0 0
          li
            padding: 0 0 .4rem
            p
              a
                inline-block
                font-size: .9rem
                font-weight: 600
                cursor: pointer

    .fNav_sp
      display: none
      flex-direction: column
      ul li a::after
      display: flex
      flex-direction: column
      padding: 0

      ul
        li
          &.bg-gray
            background-color: #f5f5f5

          p,a
            color: $main-text

    .fNav_sp > ul > li

      &:first-child
        border-top: 1px solid #e7e7e7

      ul a
        &::after
          border-top-color: #666
          border-right-color: #666

        &[target="_blank"]::after

      a
        min-height: 48px
        padding: 4vw 10vw 4vw 4vw
        font-size: 3.8vw
        font-weight: 700
        border-bottom: 1px solid #e7e7e7
        display: flex
        align-items: center
        position: relative

      p
        display: flex
        align-items: center
        position: relative
        min-height: 48px
        padding: 4vw 10vw 4vw 4vw
        font-size: 3.8vw
        font-weight: 700
        border-bottom: 1px solid #e7e7e7

        &::before, &::after
          content: ""
          display: block
          position: absolute
          top: 50%

        &::before
          width: 16px
          height: 2px
          background-color: #666
          transform: translateY(-50%)
          right: 20px

        &::after
          width: 2px
          height: 16px
          background-color: #666
          transform: translateY(-50%) rotate(0deg)
          transition: all 0.3s ease
          right: 27px

        &.close::before
          display: none
          transition: all 0.3s ease

        &.close::after
            transform: translateY(-50%) rotate(90deg)
            transition: all 0.3s ease

        ul li a::after
          width: 8px
          height: 8px
          border-top: #666 2px solid
          border-right: #666 2px solid
          transform: rotate(45deg) translateY(-50%)
          right: 22px

      ul, dl
        display: none
        background-color: #fff

      ul a
        color: $main-text
        padding-left: 42px

      dl a
        color: #01a7ac
        padding-left: 42px

        &::after
          display: none
          padding-right: 0

      ul a
        min-height: 48px
        font-size: 3.6vw
        font-weight: 500
        border-bottom: 1px solid #e7e7e7
        display: flex
        align-items: center

        &:after
          content: ""
          width: 6px
          height: 6px
          border: 0
          border-top: solid 2px #666
          border-right: solid 2px #666
          position: absolute
          top: calc(50% + 1px)
          right: 5.7vw
          margin-top: -4px
          -webkit-transform: rotate(45deg)
          transform: rotate(45deg)

        &[target="_blank"] span:after
          content: ""
          display: inline-block
          background: url("../img/common/ic_link_blank_gray.svg") no-repeat
          width: 3.5vw
          height: 3.5vw
          position: absolute
          top: calc(50% - 1.7vw)
          margin-left: 1vw

  .line-logo
    width: 100%
    padding: 60px 0
    background-color: #fff
    @include sp
      padding: 10vw

    .cont-wrap
      display: flex
      flex-direction: row
      justify-content: space-between
      align-items: center
      width: 1180px
      max-width: 100%
      margin: 0 auto
      padding: 0 2rem
      @include sp
        width: 100%
        flex-direction: column

      .pct
        display: flex
        justify-content: space-between
        align-items: center
        width: 160px
        height: 100%
        @include sp
          justify-content: center
          width: 64%
          margin: 0 auto 5vw

        a

          &:hover
            opacity: .8

        img
          width: 100%
          height: auto

      .sns
        ul
          display: flex
          flex-direction: row
          gap: 8px

          li
            width: 40px
            height: 40px
            padding: 0

            a
              display: flex
              justify-content: center
              align-items: center
              width: 100%
              height: 100%

              img
                width: 30px
                height: auto

                &.facebook
                  width: 26px
                &.x
                  width: 22px
                &.instagram
                  width: 24px

  .line-copyright
    max-width: 100%
    margin: 0 auto
    width: 100%
    padding: 24px 1rem
    background-color: #333

    .cont-wrap
      display: flex
      flex-direction: row
      justify-content: space-between
      align-items: center
      width: 1180px
      max-width: 100%
      margin: 0 auto
      padding: 0 2rem
      @include sp
        width: 100%
        flex-direction: column

      ul
        display: flex
        justify-content: space-between
        align-items: center
        flex-direction: row
        @include sp
          width: 100%
          flex-direction: column
          padding: 2vw 0 6vw

        li
          width: auto
          padding: 0 1.5rem 0 0
          @include sp
            padding: 2vw 4vw

          a
            color: #fff
            padding: 0
            font-size: 13px

            &:hover
              text-decoration: underline


      .copyright
        small
          padding: 0
          color: #fff
          font-size: 10px
          font-weight: 400
          @include sp
            font-size: 2vw
