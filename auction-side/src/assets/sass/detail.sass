@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *共通パーツ
 ***********************************************************************

/* 前へ・次へボタン
 *==========================================

#item-detail

  .back
    width: 1280px
    max-width: 100%
    margin: 2rem auto 0
    padding: 0 1.5rem
    @include sp
      width: 100%
      margin: 3vw 0 2vw
      padding: 0 4vw

    .text
      display: flex
      align-items: center
      justify-content: flex-start
      position: relative
      width: auto
      padding: .5rem 1.3rem
      color: $main-color
      font-size: .8rem
      text-decoration: none
      background-color: transparent
      cursor: pointer
      @include sp
        padding: 0 4vw
        font-size: 3vw

      &:before
        content: ""
        position: absolute
        left: 0
        top: calc(50% + 0px)
        width: 5px
        height: 5px
        border-right: 2px solid $main-color
        border-bottom: 2px solid $main-color
        transform: translateY(-50%) rotate(135deg)
        @include sp
          top: calc(50% + .3vw)
          width: 1.2vw
          height: 1.2vw
          border-right: 1px solid $main-color
          border-bottom: 1px solid $main-color

      &:hover
        text-decoration: underline
        &:before
          opacity: .8


  .container
    padding: 1rem 1rem 100px
    @include sp
      padding: 4vw 4vw 20vw


#main
  .item-name
    display: flex
    justify-content: space-between
    position: relative
    margin: 0 auto 1rem
    padding: 1rem 2px
    @include sp
      margin: 0
      flex-direction: column
      padding: 0

    .name
      flex: 1 1 auto
      padding: 0 1rem 0 0
      font-size: 1.2rem
      font-weight: 500
      line-height: 1.4
      @include sp
        padding: 0
        font-size: 4vw
        line-height: 1.5

    .tag_status
      flex: 0 1 auto
      display: flex
      flex-direction: row
      align-items: flex-start
      margin: 0
      padding: .1rem 0 0
      text-align: right
      @include sp
        margin: 0
        padding: 2vw 0
        flex: 0 1 auto

      p
        display: flex
        justify-content: center
        align-items: center
        padding: 6px 14px 7px
        font-size: .8rem
        font-weight: 700
        line-height: 1
        border-radius: 20px
        white-space: nowrap
        @include sp
          padding: 1.4vw 3.8vw 1.6vw
          font-size: 3vw
          border-radius: 20vw

      .free-shipping
        color: #fff
        background-color: #7f7f7f

      .campaign
        color: #fff
        background-color: $main-color

    .bnr_live
      position: absolute
      top: calc(50% - 22px)
      right: 0
      width: 140px
      height: 50px
      text-align: right
      @include sp
        position: static
        width: 100%
        text-align: left

      img
        width: auto
        max-width: 100%
        height: 50px
        max-height: 50px
        @include sp
          transform: translateX(-6px)

  #item-data
    position: relative

    .item_d-main
      width: 100%
      margin-top: 40px
      display: flex
      flex-direction: row
      justify-content: space-between
      padding: 0
      @include sp
        flex-direction: column
        margin: 2vw 0 0

      .item_d-main-visual
        width: calc(100% - 497px)
        padding: 0 2rem
        @include md
          width: calc(100% - 427px)
        @include sp
          width: 100%
          padding: 0 0 4vw

        .slider_wrap
          width: 100%

          ul.slider-nav
            margin: 15px 0 0

            li
              margin: 0 3px
              overflow: hidden
              display: flex
              justify-content: center
              align-items: center
            button
              display: none !important

      .item_d-main-txt
        width: 497px
        display: flex
        flex-wrap: wrap
        align-content: flex-start
        padding: 0 0 0 3rem
        @include md
          width: 427px
          padding: 0
        @include sp
          width: 100%

        .info-lowest-price
          width: 100%
          margin: 1rem auto 0
          padding: .7rem 1rem
          font-size: 12px
          line-height: 1.4
          border: 1px solid $main-color
          background-color: #f8f4f3

        p
          &.auction-system
            display: inline-block
            color: #01a7ac
            font-size: 15px
            font-weight: 700
            background-color: #E8FEFF
            border: 1px solid #01A7AC
            border-radius: 100vh
            height: 32px
            padding: 3px 20px 0
            margin-bottom: 20px
            @include sp
              font-size: .8rem

        .item_d-main-data
          border: none

          dl.bid-price
            display: flex
            flex-wrap: wrap
            margin: 0
            padding: 1.5rem 0 1.5rem
            border-top: 1px solid $main-p-gray
            @include sp
              padding: 5vw 0 6vw

            dt,dd
              display: flex
              flex-direction: row
              align-items: flex-end
              min-height: 30px
              margin: 0
              padding: 0
              @include sp
                min-height: 5vw

              &.price-start
                margin: 0 0 1.5rem
                @include sp
                  margin: 0 0 7vw

              &.price-buyit
                margin: 0 0 1rem

            dt
              width: 40%
              padding-right: 5px
              font-size: .8rem
              font-weight: 600
              @include sp
                width: 50%
                font-size: 3.5vw
                font-weight: 500

            dd
              justify-content: flex-end
              width: 60%
              font-size: .9rem
              font-weight: 600
              font-family: $font-price
              @include sp
                width: 50%
                font-size: 3.5vw
                font-weight: 500

              &.price-now
                color: #ff0000
                font-size: 1.4rem
                font-weight: 600
                font-family: $font-price
                @include sp
                  font-size: 5vw

              &.price-buyit
                font-size: 1.4rem
                font-weight: 600
                font-family: $font-price
                @include sp
                  font-size: 5vw

              &.min
                font-weight: 600
                font-family: $font-price

          .bid-status
            margin: 0 0 1rem
            padding: .8rem
            color: $main-red
            font-size: 1rem
            font-weight: 600
            text-align: left
            background-color: #fff4f4
            border: 1px solid $main-red
            border-radius: 4px
            @include sp
              font-size: 4vw
              text-align: center

          .bid-history
            display: flex
            flex-direction: row
            flex-wrap: wrap
            align-items: baseline
            margin: 0
            padding: 1rem 0 1.2rem
            border-top: 1px solid $main-p-gray
            border-bottom: 1px solid $main-p-gray
            @include sp
              flex-direction: column
              padding: 1rem .2rem 1rem

            dt,dd
              display: flex
              flex-direction: row
              align-items: baseline
              height: auto
              margin: 0
              font-size: .8rem
              font-weight: 400
              font-family: $font-price
              @include sp
                height: 5.7vw
                font-size: 3.5vw

            dt
              width: 40%
              padding-right: 5px
              @include sp
                width: 100%

              &.w_full
                margin: 0 0 .5rem
                font-size: .8rem
                font-weight: 600
                @include sp
                  margin: 0 0 2vw
                  font-size: 3.5vw

            dd
              width: 60%
              line-height: 1.2
              @include sp
                width: 100%

              &.w_full
                margin: 0 0 .5rem
                font-size: .8rem
                font-weight: 600
                @include sp
                  display: none

              .max-bid
                display: inline-block
                min-width: 80px
                margin: 0 0 0 1rem
                padding: .1rem .3rem
                color: #fff
                font-size: .7rem
                font-weight: 600
                text-align: center
                vertical-align: middle
                background-color: $main-color
                border-radius: 2px
                @include sp
                  padding: 1vw 2vw


        .status-panel
          display: flex
          flex-direction: row
          align-items: center
          justify-content: space-between
          gap: 10px
          width: 100%
          padding: 1rem 0

          .status-wrap
            display: flex
            flex-direction: row
            align-items: center
            justify-content: flex-start
            gap: 10px

            .status
              height: 32px
              padding: 3px 10px
              color: #fff
              font-size: .9rem
              font-weight: 600
              border-radius: 4px
              @include sp
                font-size: 3.3vw

              &.top
                background-color: $main-red
                border: 1px solid $main-red

              &.overbid
                color: $main-red
                border: 1px solid $main-red


          .update
            position: relative
            width: 55px
            height: 55px
            margin: 0 0 0 1rem
            padding: 1.5rem 0 0
            color: $main-red
            text-align: center
            background-color: #fff
            border: 1px solid $main-red
            border-radius: 30px
            cursor: pointer
            @include sp
              width: 14vw
              height: 14vw
              margin: 0 0 0 4vw
              padding: 7vw 0 0
              border-radius: 10vw

            span
              font-size: .8rem
              @include sp
                font-size: 2.7vw
                font-weight: 600

            &::after
              content: ""
              display: inline-block
              background: url("../img/common/icn_update_list.svg") center 8px no-repeat
              background-size: 20px auto
              width: 30px
              height: 30px
              position: absolute
              top: 0
              left: calc(50% - 15px)
              @include sp
                background: url("../img/common/icn_update_list.svg") 1vw 2.2vw no-repeat
                background-size: 5vw auto
                width: 8vw
                height: 8vw
                left: calc(50% - 15px)


            &:hover
              opacity: 1
              background-color: #fff9f9

  .item-note
    width: calc(100%)
    max-width: calc(100%)
    margin: 60px auto 0
    padding: 0
    @include sp
      margin: 40px auto 0

    h2
      margin: 0
      padding: 0 0 .8rem
      font-size: 1.3rem
      font-weight: 700
      border-bottom: 1px solid #eee
      @include sp
        height: auto
        font-size: 5vw

    .contents-wrap
      padding: 0

      h3
        width: 100%
        margin: .8rem 0 1rem
        padding: .8rem .8rem
        font-size: 1.1rem
        font-weight: 600
        text-align: left
        background-color: #e7eef3
        @include sp
          font-size: 4vw

      p
        margin: 1.2rem 0 0
        font-size: .9rem
        @include sp
          font-size: 3.5vw

      ul
        li
          padding: 0 1rem
          text-indent: -.8rem
          font-size: .9rem
          @include sp
            padding: 0 4vw
            font-size: 3.5vw
            text-indent: -2.8vw

      table.spec
        width: 100%
        margin: 2rem 0 0

        tr
          th,td
            padding: 1rem 1rem
            font-size: .9rem
            background-color: #f5f5f5
            border: 1px solid #fff
            line-height: 1.4
            @include sp
              padding: 4vw
              font-size: 3.5vw

          th
            width: 200px
            font-weight: 700
            white-space: nowrap
            text-align: center
            background-color: #e1e1e1
            @include sp
              vertical-align: middle
              width: 32%


@media only screen and (max-width: 767px)
  #main #npNav
    position: fixed
    left: 0
    right: 0
    bottom: 0
    width: 100%
    display: flex
    background-color: #ccc
    z-index: 2

    a
      font-size: 15px
      position: relative
      transform: none
      width: 50%
      height: 46px
      border-radius: 0 !important
      border-width: 1px
      border-bottom: none
      writing-mode: horizontal-tb
      -webkit-writing-mode: horizontal-tb
      -ms-writing-mode: horizontal-tb

      + a
        border-left: none

      &::after
        position: absolute
        top: 10px

      &.prev::after
        left: 30px

      &.next::after
        right: 30px


/***********************************************************************
 *
 *------------------------------------------------------------------------
 *商品データ
 ***********************************************************************


/* スライド
 *==========================================

#main #item-data .item_d-main .item_d-main-visual
  .slider_wrap ul.slider-nav
    .slick-list
      .slick-track
        @include sp
          flex-wrap: wrap
          width: calc(100%) !important
          transform: unset !important
        li
          @include sp
            width: calc(25% - 6px) !important
            height: auto
            margin: 0 3px 6px
          &.slick-cloned
            @include sp
              display: none

/* 商品情報・価格 など
 *==========================================

/* ---------------------------
 *情報・価格 など
 *-----------------------------

/*///// 商品情報ブロック ////

#main #item-data
  .item_d-main .item_d-main-txt
    .item_d-main-data
      width: 100%

    .status-panel

      .tab-wrap
        width: calc(100% - 70px)
        display: flex
        flex-direction: row
        align-items: center
        justify-content: flex-start
        flex-wrap: wrap
        align-items: center
        @include sp
          width: 100%
          margin: 0 0 1rem

        p
          margin: 2px .5rem 2px 0
          display: flex
          align-items: center
          justify-content: center
          width: calc(50% - 1rem)
          height: 34px
          padding: 2px 12px
          color: #fff
          font-size: 1.1rem
          font-weight: 700
          text-align: center
          line-height: 1
          background-color: #ddd
          border-radius: 20px
          @include sp
            font-size: .9rem

          &.status_top
            &.is-active
              background-color: #ff0000

          &.status_overbid
            color: #ff0000
            background-color: #D3D1D0
            &.is-active
              color: #fff
              background-color: #ddd

        p.circle
          display: flex
          align-items: center
          justify-content: center
          width: 64px
          height: 64px
          margin: 2px .3rem 2px 0
          padding: 3px 2px 0
          color: #fff
          font-size: .8rem
          font-weight: 700
          text-align: center
          line-height: 1.05
          background-color: #ddd
          border-radius: 50%
          @include sp
            width: 16.5vw
            max-width: 68px
            height: 16.5vw
            max-height: 68px
            font-size: .9rem
          @media screen and (max-width: 400px)
            min-width: 55px
            min-height: 55px
            font-size: 12px


          &.status_top
            &.is-active
              background-color: #ff0000

          &.status_overbid
            color: #ff0000
            background-color: #D3D1D0
            &.is-active
              color: #fff
              background-color: #ddd

      .update-wrap
        width: 70px
        display: flex
        justify-content: flex-end
        @include sp
          width: 100%

        .update
          position: relative
          width: 55px
          height: 56px
          padding: 1.7rem 0 0
          color: $main-color
          text-align: center
          background-color: #fff
          border: 1px solid $main-color
          border-radius: 30px

          &:hover
            cursor: pointer

          &::after
            content: ""
            display: inline-block
            background: url("../img/common/icn_update_list.svg") center 8px no-repeat
            background-size: 20px auto
            width: 30px
            height: 30px
            position: absolute
            top: 0
            left: calc(50% - 15px)

          span
            font-size: 0.8rem


        .square.update
          position: relative
          width: 64px
          height: 64px
          margin: 2px 0 0
          padding: 1.8rem 0 0
          color: $main-color
          text-align: center
          background-color: #fff
          border: 1px solid $main-color
          border-radius: 0
          @include sp
            width: 100%
            padding: 2rem 0 0

          &:hover
            cursor: pointer
            opacity: .7

          &::after
            content: ""
            display: inline-block
            background: url("../img/common/icn_update_list.svg") center 8px no-repeat
            background-size: 20px auto
            width: 38px
            height: 38px
            position: absolute
            top: 5px
            left: calc(50% - 19px)

          span
            font-size: .8rem

    .place-bid
      position: relative
      width: 100%
      padding: 1rem 1.5rem
      background-color: #f0f0f0
      @include sp
        padding: 1rem
        border-bottom: 1px solid #fff

      .bid-price
        display: flex
        flex-direction: row
        justify-content: flex-start
        align-items: baseline
        margin: 1rem 0 1rem
        @include sp
          width: 100%

        dt
          width: 40%
          font-weight: 600
          @include sp
            width: 38%
            font-size: 3.5vw

        dd
          width: 60%
          text-align: right
          @include sp
            width: 62%
            text-align: right

          span.price
            color: #ff0000
            font-size: 1.2rem
            font-weight: 600
            font-family: $font-price
            @include sp
              font-size: 5vw

      .bid_head
        width: 100%
        display: flex
        flex-direction: row
        justify-content: space-between
        align-items: center

        .ttl
          margin: .4rem 2rem 0 0
          padding: 0
          @include sp
            margin: 0

          span
            display: block
            font-size: 1rem
            font-weight: 700
            line-height: 1.4
            @include sp
              display: inline-block
              font-size: 3.5vw

        .price
          font-size: 1rem
          font-weight: 700
          font-family: $font-price
          @include sp
            font-size: 4vw

          input
            width: 13rem
            margin: 0 .5rem 0 0
            padding: 5px
            font-size: 1.4rem
            font-weight: 700
            text-align: right
            border: none
            border-radius: 4px
            @include sp
              width: 50vw
              height: 12vw
              font-size: 4vw

            &::placeholder
              color: #ddd
              font-size: 1.4rem
              @include sp
                font-size: 5vw

      ul
        display: flex
        flex-direction: row
        flex-wrap: wrap
        gap: .5rem
        width: 100%
        margin: .8rem 0 1.5rem
        @include sp
          gap: 2vw
          margin: 2vw 0 4vw

        li
          flex: 1
          padding: 0
          @include sp
            width: auto
            padding: 0

          button.bid-unit
            display: flex
            align-items: center
            justify-content: space-between
            width: 100%
            margin: 0
            padding: .2rem .6rem .2rem .2rem
            font-size: 1rem
            font-weight: 500
            font-family: $font-price
            background-color: #fff
            border-radius: 4px
            white-space: nowrap
            @include sp
              padding: 1vw 2vw 1vw .2vw
              font-size: 3.8vw
              border-radius: 1vw

            span
              display: inline-block
              position: relative
              width: 20px
              height: 20px
              margin: 2px 5px 2px 2px
              padding: 0 7px
              color: #fff
              line-height: 1
              background-color: $main-color
              border-radius: 20px
              @include sp
                width: 5.5vw
                height: 5.5vw
                margin: 0 1.5vw 0 1vw
                padding: 0 7px

              &::after
                content: "+"
                position: absolute
                top: calc(50% - 0.6rem)
                left: calc(50% - 0.45rem)
                width: 1rem
                height: 1rem
                color: #fff
                font-size: 1.1rem
                @include sp
                  top: 0
                  left: 1.2vw
                  width: 5vw
                  height: 5vw


      .btn-wrap
        display: flex
        flex-direction: column
        gap: 20px

        .btn
          display: flex
          justify-content: center
          align-items: center
          width: 100%
          height: 55px
          margin: 0 auto
          color: #fff
          font-size: 1rem
          font-weight: 600
          background-color: $main-color
          border-radius: 50px
          line-height: 1.2
          @include sp
            height: 15vw
            font-size: 4.5vw

          &:hover
            opacity: .8

          .pct
            width: 16px
            height: auto
            @include sp
              width: 4.6vw

          .bid-text
            position: relative
            width: auto
            display: inline-block
            padding-left: 14px
            font-weight: 600
            @include sp
              font-size: 4.2vw
              font-weight: 600

          &.chat
            width: 100%
            height: 44px
            background-color: #333
            @include sp
              width: 100%
              height: 11vw

            .pct
              width: 16px
              height: auto
              @include sp
                width: 4.2vw

            .text
              display: inline-block
              padding-left: 14px
              font-size: .9rem
              font-weight: 600
              @include sp
                font-size: 3.5vw

        .view_comment
          display: inline-block
          margin: 0 auto 1rem
          text-decoration: none
          color: $main-color
          font-weight: 500
          cursor: pointer
          @include sp
            font-size: 4vw

          &:hover
            text-decoration: underline


      /* ---------------------------
      *モーダル（入札）ModalBid
      *-----------------------------

      .modal-container
        position: fixed
        top: 0
        left: 0
        width: 100%
        height: 100%
        text-align: center
        background: rgba(0, 0, 0, .7)
        padding: 40px 20px
        overflow: auto
        opacity: 0
        visibility: hidden
        transition: .3s
        box-sizing: border-box
        z-index: 100

        &:before
          content: ""
          display: inline-block
          vertical-align: middle
          height: 100%

        &.active
          opacity: 1
          visibility: visible

        .modal-body
          position: relative
          display: inline-block
          vertical-align: middle
          max-width: calc(100% - 4rem)
          width: 1000px
          margin: 0 auto
          @include sp
            max-width: calc(100% - 4vw)


          .modal-close
            position: absolute
            display: flex
            align-items: center
            justify-content: center
            top: -20px
            right: -20px
            width: 40px
            height: 40px
            font-size: 18px
            font-weight: 600
            color: #fff
            background-color: $main-color
            border-radius: 40px
            cursor: pointer
            z-index: 120

          .modal-content
            position: relative
            padding: 1rem
            background-color: #fff
            border-radius: 8px
            z-index: 110

            .matching-dir
              display: flex
              flex-direction: column
              width: 100%
              margin: 0

              li
                display: flex
                flex-direction: row
                width: 100%
                margin: 0
                padding: 0 0 1rem
                border-bottom: 1px solid $line-gray
                  width: 100%
                  margin: 0 0 1.5 rem
                  padding: 0
                  flex-direction: column
                  border-bottom: none

                figure
                  width: 180px
                  @include sp
                    width: 100%
                    height: auto

                  .wrap_pct
                    display: flex
                    justify-content: center
                    align-items: flex-start
                    position: relative
                    width: 100%
                    height: 100%

                    img
                      max-width: 100%
                      max-height: 100%
                      object-fit: contain
                      object-position: center

                    .status
                      position: absolute
                      bottom: 10px
                      left: 12px
                      display: flex
                      flex-direction: column
                      width: 100%
                      height: auto
                      > p
                        display: inline-block
                        width: auto
                        max-width: 110px
                        margin: 2px 0
                        padding: 2px 12px
                        font-size: .8rem
                        font-weight: 700
                        text-align: center
                        border-radius: 20px
                      .status_top
                        color: #fff
                        background-color: #ff0000
                      .status_overbid
                        color: #ff0000
                        background-color: #D3D1D0

                    .status_soldout.active
                      display: block
                      position: absolute
                      top: 26%
                      left: 10%
                      width: 80%
                      img
                        width: 100%

                    .status_soldout
                      display: none

                .product-wrap
                  display: grid
                  grid-template-columns: calc(100% - 270px) 270px
                  grid-auto-rows: minmax(70px, auto)
                  width: 100%
                  padding: 0 0 0 1rem
                  @include sp
                    grid-template-columns: 1fr
                    width: 100%
                    padding: 0

                  .item-name
                    grid-row: 1 / 2
                    grid-column: 1 / 2
                    position: relative
                    width: 100%
                    margin: 0
                    padding: .5rem 1rem 1rem 1rem
                    font-size: 1rem
                    font-weight: 500
                    line-height: 1.5
                    border-bottom: 1px solid $main-p-gray
                    @include sp
                      grid-row: 1 / 2
                      grid-column: 1 / 2
                      font-size: 3.8vw
                      line-height: 1.5
                      padding: 4vw 0 4vw

                    .tag_status
                      position: absolute
                      top: 1rem
                      right: 1rem
                      @include sp
                        position: static
                        margin: 0 2px 2px 0
                        padding: .5rem 0 0
                      > p
                        display: inline-block
                        padding: 4px 12px 5px
                        font-size: .8rem
                        font-weight: 700
                        line-height: 1
                        border-radius: 20px
                        @include sp
                          padding: 5px 16px 6px
                      .status_recommend
                        color: #fff
                        background-color: #ff0000

                  .status-panel
                    padding: 1rem
                    @include sp
                      grid-row: 2 / 3
                      grid-column: 1 / 2
                      justify-content: flex-start
                      padding: 4vw 0


                    .status-wrap
                      display: flex
                      flex-wrap: wrap
                      @include sp
                        justify-content: center

                  .sell
                    grid-row: 3 / 4
                    grid-column: 1 / 2
                    display: flex
                    flex-direction: row
                    background-color: #fff
                    @media screen and (max-width: 1200px)
                      flex-direction: column
                    @include sp
                      grid-row: 3 / 4
                      grid-column: 1 / 2

                    .price
                      display: flex
                      flex-direction: column
                      width: 270px
                      padding: 0 1rem
                      font-family: $font-price
                      @media screen and (max-width: 1200px)
                        width: 100%
                        margin: 0 0 1rem
                      @include sp
                        margin: 4vw 0 2vw
                        padding: 0
                        text-align: center
                        border-bottom: 1px solid #fff

                    .price-c
                      font-size: .8rem
                      font-weight: 600

                    .price-v
                      display: inline-block
                      margin: 0
                      color: $price-red
                      font-size: 1.4rem
                      font-weight: 600
                      font-family: $font-price
                      white-space: nowrap
                      @include sp
                        font-size: 5vw

                    .current-status
                      width: 100%
                      padding: 0 1rem
                      @include sp
                        margin: 4vw 0
                        padding: 0
                        background-color: transparent

                      .end-date
                        display: flex
                        flex-direction: row
                        justify-content: flex-start
                        align-items: center
                        width: 100%
                        margin: 0 0 .5rem
                        padding: 0
                        border: none

                        img
                          width: 14px
                          height: auto

                      .other-info
                        display: flex
                        flex-direction: row
                        justify-content: flex-start
                        align-items: center
                        width: 100%
                        margin: 0
                        padding: 0
                        font-size: .8rem
                        @include sp
                          width: 100%

                        .view
                          img
                            width: 16px
                            height: auto
                            margin: 0 5px 0 0

                        .favorite
                          img
                            width: 14px
                            height: auto
                            margin: 0 5px 0 0

                        .bid
                          img
                            width: 12px
                            height: auto
                            margin: 0 5px 0 0

                  .place-bid
                    grid-row: 1 / 4
                    grid-column: 2 / 3
                    display: flex
                    flex-direction: column
                    justify-content: center
                    align-items: center
                    position: relative
                    width: auto
                    margin: 0 0 0 1rem
                    padding: 1rem
                    background-color: $main-bg-color
                    @include sp
                      grid-row: 4 / 5
                      grid-column: 1 / 2
                      padding: 8vw 4vw 10vw
                      margin: 0

                    .ttl
                      width: 100%
                      margin: 0
                      font-size: 1rem
                      font-weight: 700

                    .price
                      width: 100%
                      font-size: 1.6rem
                      font-weight: 600
                      font-family: $font-price
                      @include sp
                        font-size: 6vw

                    input
                      width: 4.5rem
                      margin: 0 0 0 1rem
                      padding: 5px
                      font-size: 1.4rem
                      font-weight: 700
                      text-align: right
                      &::placeholder
                        color: #ddd

                    ul
                      display: flex
                      flex-direction: row
                      flex-wrap: wrap
                      margin: 1rem 0 1rem

                    ul > li > button
                      display: flex
                      align-items: center
                      margin: 0 5px 5px 0
                      padding: 0 7px 0 0
                      font-size: 1rem
                      background-color: #fff
                      border: 1px solid #CDCBCA
                      border-radius: 30px
                      white-space: nowrap
                      @include sp
                        font-size: 1rem
                      span
                        display: inline-block
                        position: relative
                        width: 20px
                        height: 20px
                        margin: 2px 5px 2px 2px
                        padding: 0 7px
                        color: #fff
                        line-height: 1
                        background-color: $main-color
                        border-radius: 20px
                        &::after
                          content: "+"
                          position: absolute
                          top: .7px
                          left: 5.5px
                          width: 14px
                          height: 14px
                          color: #fff

                    .other-info-detail
                      display: none
                      width: 100%
                      margin: 1rem 0 0
                      @include sp
                        margin: 2vw 0 2vw

                      button
                        position: relative
                        width: 100%
                        height: 46px
                        margin: 0
                        padding: .5rem 1rem
                        color: $main-color
                        font-size: 1rem
                        font-weight: 500
                        background-color: #fff
                        border: 2px solid $main-color
                        border-radius: 30px
                        &::after
                          position: absolute
                          top: calc(50% - 4px)
                          right: 15px
                          display: block
                          width: 10px
                          height: 10px
                          padding: 0
                          color: $main-color
                          font-size: 16px
                          font-weight: 900
                          font-family: "Material Icons"
                          content: "\e5cc"
                          line-height: 0.6

                  .place-bid.soldout::after
                    position: absolute
                    top: 0
                    left: 0
                    content: ""
                    width: 100%
                    height: 100%
                    background: rgba(240,240,240,0.5)
                    z-index: 2

                  .place-bid.soldout::before
                    position: absolute
                    top: 26%
                    left: 10%
                    display: inline-block
                    content: ""
                    width: 80%
                    height: 50%
                    background-image: url(../img/common/icn_soldout_list.png)
                    background-repeat: no-repeat
                    background-position: center center
                    background-size: contain
                    z-index: 3
                    @include sp
                      top: 15%

            .note-bid
              width: 100%
              padding: 1rem 1rem 0
              text-align: center
              @include sp
                padding: 10vw 0 0
                font-size: 3vw

            .button-bid
              display: flex
              flex-direction: row

              button
                width: 280px
                max-width: calc(100% - 2rem)
                height: 56px
                margin: 1rem auto 1rem
                color: #fff
                font-size: 1.2rem
                font-weight: 500
                background-color: $main-color
                border-radius: 50px
                @include sp
                  width: 100%
                  max-width: 100%
                  margin: 6vw auto 10vw
                  border-radius: 20vw


      .current-status
        width: 100%
        padding: 0
        text-align: center
        @include sp
          width: 100%
          background-color: #f0f0f0

        .end-date
          display: flex
          flex-direction: row
          justify-content: center
          align-items: center
          gap: 10px
          padding: 1rem 1rem
          border-top: 1px solid #e1e1e1
          border-bottom: 1px solid #e1e1e1
          @include sp
            padding: 4vw

          img
            width: 16px
            height: auto
            margin: 0
            transform: translateY(1px)
            @include sp
              width: 4vw

          .end-l,.end-v
            font-size: .9rem
            font-weight: 500
            font-family: $font-price
            @include sp
              font-size: 3.8vw

        .other-info
          display: flex
          flex-direction: row
          align-items: center
          justify-content: center
          gap: 1.5rem
          width: 300px
          margin: 0 auto
          padding: 1rem 0 .3rem
          font-size: .8rem
          @include sp
            width: 100%
            padding: 4vw 4vw 2vw
            gap: 4vw

          .view
            img
              width: 16px
              height: auto
              margin: 0 5px 0 0
              @include sp
                width: 4vw

          .favorite
            img
              width: 14px
              height: auto
              margin: 0 5px 0 0
              @include sp
                width: 3.8vw

          .bid
            img
              width: 12px
              height: auto
              margin: 0 5px 0 0
              @include sp
                width: 3.4vw

          div
            width: auto
            display: flex
            align-items: center

            span
              font-size: .9rem
              font-weight: 700
              font-family: $font-price
              @include sp
                font-size: 3.5vw

  #terms
    margin: 1rem 0 1rem
    display: flex
    flex-wrap: nowrap
    align-items: center
    width: 100%
    @include sp
      order: 5

    #sns-share
      display: flex
      flex-wrap: nowrap
      align-items: center
      min-height: 48px
      flex: 1

      >
        p
          width: auto
          font-size: .8rem
          font-weight: 500
          color: #7D7D7D
          @include sp
            font-size: 3vw

        ul
          width: auto
          display: flex
          margin-left: 5px
          @include sp
            margin-left: 1vw

          > li
            margin-left: 10px

            img
              width: 30px
              @include sp
                width: 7vw


    .tag-terms
      width: calc(100% - 80px)
      display: flex
      flex-wrap: wrap
      @include sp
        width: 100%

      a
        display: inline-block
        background-color: #EAEAEA
        border-radius: 100vh
        font-size: 16px
        font-weight: 700
        min-width: 100px
        min-height: 30px
        text-align: center
        padding: 1px 20px
        margin-left: 10px
        margin-top: 10px
        @include sp
          margin-left: 0
          margin-right: 5px
          margin-top: 5px
          font-size: 16px
          min-width: 0

    p
      &.tit-period
        width: 90px
        font-size: 15px
        padding-top: 0
        margin-top: 20px
        @include sp
          font-size: 14px

      &.txt-period
        width: calc(100% - 90px)
        font-size: 15px
        margin-top: 20px
        @include sp
          width: 100%
          margin-top: 0
          font-size: 14px

        span
          font-weight: 700
          display: inline-block


    .desc
      padding: 1rem 0

      dl
        display: flex
        flex-direction: row
        flex-wrap: wrap
        align-items: stretch
        margin: 0
        dt
          width: 30%
          margin: .5rem 0 0
          padding: 0 0 .5rem 1rem
          font-size: .8rem
          font-weight: 700
          border-bottom: 1px solid #eee
          @include sp
            padding: 0 0 .5rem .2rem
        dd
          width: 70%
          margin: .5rem 0 0
          padding: 0 0 .5rem .5rem
          font-size: .8rem
          font-weight: 400
          border-bottom: 1px solid #eee
          @include sp
            padding: 0 0 .5rem .2rem

      .condition
        padding: .8rem 1rem
        border-bottom: 1px solid #eee
        @include sp
          padding: .8rem .2rem 1.2rem
        p
          font-size: .8rem
        ul
          display: flex
          flex-direction: row
          margin: 1rem 0 0
          font-size: .8rem
          @include sp
            flex-wrap: wrap

          li.pct
            width: 22px
            padding: 4px 0
            background-color: transparent
            img
              width: 100%
              height: auto
          li
            margin: 0 0 5px
            padding: 4px 10px
            background-color: #eee
            + li
              margin: 0 0 5px 5px
            span
              white-space: nowrap
              font-weight: 700
            &.active
              color: #fff
              background-color: #5b5b5b


    .item_d-main-system
      background-color: #F7F7F7
      border: none
      padding: 15px 20px 20px

    .item-main-info
      width: 100%
      padding: 0 1.5rem
      @include sp
        padding: 0
        order: 5
      .btn-box a
        display: flex
        justify-content: center
        align-items: center
        margin-top: 10px
        border-radius: 100vh
        text-align: center
        font-size: 1rem
        font-weight: 700
        height: 60px
        padding-left: 20px
        border: 2px solid #000

        &.btn-contact
          padding-left: 0
          padding-right: 20px
          line-height: 1.2
          @include sp
            padding: 0 6px

        &.btn-signup
          background-color: #01a7ac
          height: 45px

        &.btn-contact img
          display: inline-block
          width: 23px
          margin-right: 20px
          position: relative
          top: 1px
          @include sp
            margin: 0 5px 0 0

        &.btn-login img, &.btn-signup img
          display: inline-block
          width: 19px
          margin-left: 10px
          position: relative
          top: -1px


  a.btn-back-search
    display: flex
    margin: 60px auto 0
    width: 100%
    max-width: 360px
    min-height: 60px
    justify-content: center
    align-items: center
    background-color: #000
    color: #fff
    font-size: 18px
    font-weight: 700
    border-radius: 100vh
    margin-top: 60px
    padding: 10px 20px

    img
      width: 19px
      position: relative
      top: -2px
      margin-left: 15px


#main #item-data.nego
  .item_d-main
    .item_d-main-visual
      .slider_wrap
        .slick-slider
          .slick-list
            .slick-track
              figure.slick-active
                position: relative
                &:after
                  content: ""
                  position: absolute
                  top: 0
                  left: 0
                  width: 100%
                  aspect-ratio: 1
                  padding: 0
                  background-image: url(../img/common/icn_nego.png)
                  background-size: 60% auto
                  background-position: center center
                  background-repeat: no-repeat
                  z-index: 10

    .item_d-main-txt
      .place-bid:after
        display: block
        position: absolute
        top: 0
        left: 0
        content: ""
        width: 100%
        height: 100%
        background: rgba(240,240,240,0.5)
        z-index: 2

#main #item-data.soldout
  .item_d-main
    .item_d-main-visual
      .slider_wrap
        .slick-slider
          .slick-list
            .slick-track
              figure.slick-active
                position: relative
                &:after
                  content: ""
                  position: absolute
                  top: 0
                  left: 0
                  width: 100%
                  aspect-ratio: 1
                  padding: 0
                  background-image: url(../img/common/icn_soldout.png)
                  background-size: 60% auto
                  background-position: center center
                  background-repeat: no-repeat
                  z-index: 10

    .item_d-main-txt
      .place-bid:after
        display: block
        position: absolute
        top: 0
        left: 0
        content: ""
        width: 100%
        height: 100%
        background: rgba(240,240,240,0.5)
        z-index: 2




/*///// 価格情報ブロック ////

/* 入札状況

@media only screen and (max-width: 767px)
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-price .status
    padding: 15px
    justify-content: center

    p
      margin-right: 5px

/* ボタン

@media only screen and (max-width: 767px)
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .btn-box a
    font-size: 16px

/* 閲覧・お気に入り・入札数

@media only screen and (max-width: 767px)
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system ul.action li + li
    margin-left: 15%

/* 入札フォーム

@media only screen and (max-width: 767px)
  #main #item-data .item_d-main .item_d-main-txt .item_d-main-system .bidForm
    .bidPrice
      display: block

      p.bidP-tit
        font-size: 16px

      .bidP-txt
        font-size: 34px
        margin-top: 5px

        span.yen
          font-size: 26px

        input.ipt-price
          font-size: 34px
          width: 130px
          height: 50px