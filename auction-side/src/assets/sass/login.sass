@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *ログインページ
 ***********************************************************************

#main

  #login-form
    margin: 40px auto 100px
    padding: 0
    @include sp
      margin: 0 auto
      padding: 7vw 0 20vw

    .id-pass-err
      width: 560px
      max-width: 100%
      text-align: center
      margin: 1.5rem auto
      @include sp
        margin: 4vw 0 10vw

      .err-txt
        color: $err-text
        font-weight: 500
        @include sp
          font-size: 3.5vw

    table.tbl-login
      width: 560px
      max-width: 100%
      margin: 0 auto
      @include sp
        width: 100%
        margin: 0

      tr
        display: flex
        flex-direction: column
        margin: 0 0 1.5rem
        @include sp
          margin: 0 0 4vw

        &:last-child
          margin: 0

      th
        display: block
        position: relative
        width: 100%
        padding: .2rem .8rem
        font-size: .9rem
        font-weight: 600
        vertical-align: bottom
        @include sp
          display: block
          width: 100%
          padding: 0 3vw
          font-size: 3.5vw
          font-weight: 500

        em.req
          display: inline-block
          position: absolute
          top: 7px
          right: 1rem
          font-size: 12px
          font-weight: 500
          color: #f00
          @include sp
            top: .5vw
            right: 4.3vw
            padding: 0
            font-size: 3vw


      td
        padding: 0
        position: relative
        @include sp
          display: block
          width: 100%
          padding: .5vw 0 0

        input
          width: 100%
          @include sp
            width: 100%

        .err-txt
          font-size: .9rem
          @include sp
            position: static
            transform: none
            max-width: 100%
            margin-top: 5px
            font-size: 3.8vw

      th
        span.note
          display: inline-block
          width: 16px
          height: 16px
          text-align: center
          line-height: 1
          padding-top: 3px
          @include sp
            width: 4.4vw
            height: 4.4vw

          &:hover
            cursor: pointer

        .note-modal
          display: none
          z-index: 1
          position: absolute
          top: 85px
          left: -60px
          @include sp
            height: 100vh
            position: fixed
            top: 0
            left: 0
            right: 0
            bottom: 0
            width: 100vw

          .note-txt
            text-align: center
            background-color: #fff
            box-shadow: 0px 5px 15px 1px rgba(0, 0, 0, 0.08)
            width: 500px
            padding: 30px 15px
            border-radius: 8px
            @include sp
              position: fixed
              width: calc(100vw - 50px)
              top: 50%
              right: 50%
              transform: translate(50%, -50%)

            &:before
              content: ""
              display: block
              border-right: 10px solid transparent
              border-bottom: 14px solid #fff
              border-left: 10px solid transparent
              position: absolute
              top: -14px
              left: 150px
              @include sp
                display: none

            p
              display: inline-block
              font-size: 16px
              color: $main-color
              font-size: .9rem
              font-weight: 500
              line-height: 1.8
              @include sp
                font-size: 3.8vw
                line-height: 1.6

        .modal__bg
          @include sp
            background: rgba(0, 0, 0, 0.7)
            height: 100vh
            position: absolute
            width: 100vw

    .check-idpass
      display: flex
      justify-content: center
      margin: 1.5rem 0 0
      @include sp
        margin: 7vw 0 0
        padding-left: 0
        text-align: center


    .forget-pass
      margin: 1.5rem 0 0
      text-align: center

      a
        display: inline-block
        font-weight: 700
        color: $main-color
        font-size: 14px
        text-decoration: underline
        padding: 0
        @include sp
          font-size: 3.5vw

    .rule
      max-width: 550px
      margin: 70px auto 0

      p.tit-rule
        font-weight: 500
        color: #000
        font-size: .9rem
        padding: 0 .5rem .2rem
        @include sp
          padding: 0 2vw .5vw

      embed
        border: 1px solid $line-gray

      .rule-check
        margin: 1.8rem 0 0
        text-align: center
        @include sp
          margin: 6vw 0 0

    .btn-form
      display: flex
      justify-content: center
      flex-wrap: wrap
      @include sp
        width: 100%

      input[type=button]
        height: 56px
        border-radius: 40px
        @include sp
          width: 100%
          height: calc(15vw + 2px)
          max-width: 100%
          margin:0
          padding: 0
          font-size: 4.2vw
          border-radius: 20vw


    .rule .rule-check p.err-txt
      margin-top: 5px
      text-align: center
      @include sp
        margin: 2vw 0 0

    .request
      margin-top: 40px
      text-align: center
      @include sp
        margin: 10vw 0 0

      a
        display: flex
        justify-content: center
        align-items: center
        width: 280px
        max-width: 100%
        height: 54px
        margin: 0 auto
        padding: 0
        color: $main-color
        font-weight: 700
        font-size: 1rem
        background-color: #fff
        border: 1px solid $main-color
        border-radius: 50px
        @include sp
          width: 100%
          height: 15vw
          margin: 0
          font-size: 4.2vw
          border-radius: 20vw

        &:hover
          color: #fff
          background-color: $main-color

      p
        text-align: center
        font-weight: 500
        color: $main-color
        font-size: 12px
        margin: 10px 0 0
        @include sp
          margin: 3vw 0 0
          font-size: 3.5vw


  &.reminder

    [class^="remind-msg"]
      width: 560px
      max-width: 100%
      margin: 0 auto
      padding: 1.5rem 1.5rem
      color: $main-red
      background-color: #fff4f4
      border: 1px solid $main-red
      border-radius: 4px
      @include sp
        width: 100%
        margin: 0 auto
        padding: 5vw 5vw
        font-size: 3.8vw

      p
        text-align: center
        font-weight: 500
        font-size: .9rem
        text-align: left
        @include sp
          font-size: 3.8vw

        &.remind-txt-att
          span
            display: inline-block
            font-weight: 500

          a
            color: $main-red
            font-weight: 500
            text-decoration: underline
            &:hover
              text-decoration: none

    .container
      padding: 1rem 1rem 80px
      @include sp
        padding: 4vw 4vw 7vw

      .remind-msg-comp
        margin: 0 auto 80px
        text-align: center
        @include sp
          margin: 0 auto 14vw

        p
          text-align: center

      .remind-comp-btn
        margin: 40px 0
        @include sp
          margin: 10vw auto

        a
          display: flex
          justify-content: center
          align-items: center
          width: 280px
          max-width: 100%
          height: 54px
          margin: 0 auto
          padding: 0
          color: $main-color
          font-weight: 700
          font-size: 1rem
          background-color: #fff
          border: 1px solid $main-color
          border-radius: 50px
          @include sp
            width: 100%
            height: 15vw
            margin: 0
            font-size: 4.2vw
            border-radius: 20vw

          &:hover
            color: #fff
            background-color: $main-color
