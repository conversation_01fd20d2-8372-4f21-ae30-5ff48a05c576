@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *商品一覧ページ
 ***********************************************************************

#main
  h2
    display: flex
    align-items: center
    position: relative
    height: auto

    &.mypage
      background-color: #f5f5f5

    &::after
      content: ""
      display: block
      position: absolute
      bottom: 0
      right: 0
      width: 100%
      height: 100%

  #list-head
    margin: 40px 0 0
    padding: 0
    @include sp
      margin: 4vw 0 0
      padding: 0

    .search-panel-wrap
      padding: 24px 40px
      background-color: #f5f5f5
      border-radius: 8px
      @include sp
        padding: 0

      .head
        padding: 1rem 1rem 0
        font-size: 1.4rem
        font-weight: 600
        text-align: center
        @include sp
          padding: 4vw 4vw 0

      .contents
        padding: 1.5rem 0 2rem
        @include sp
          padding: 1rem 1rem 2rem

        .keyword
          display: flex
          flex-direction: row
          align-items: center
          padding: 0 0 1rem
          border-bottom: 1px solid $main-p-gray
          @include sp
            flex-direction: column
            padding: 0 .5rem 1.5rem

          &__label
            width: 180px
            padding: .3rem 1rem
            @include sp
              width: 100%
              font-size: 3.5vw
              padding: 1vw 0

          input
            width: calc(100% - 180px)
            max-width: 700px
            margin: 0 0 0 .5rem
            @include sp
              width: 100%
              margin: 0

        .model
          display: flex
          flex-direction: row
          align-items: center
          padding: 1rem 0
          border-bottom: 1px solid $main-p-gray
          @include sp
            flex-direction: column
            padding: .5rem .5rem 1rem

          .model__label
            width: 180px
            padding: .3rem 1rem
            line-height: 1.2
            @include sp
              width: 100%
              padding: 1vw 0
              font-size: 3.5vw

          .model__contents
            display: flex
            flex-direction: row
            flex-wrap: wrap
            width: calc(100% - 180px)
            @include sp
              width: 100%

            .label-item
              display: flex
              flex-direction: row
              margin: .15rem .3rem
              @include sp
                margin: .3rem .5rem .3rem 0

              input
                transform: scale(1.3)
                @include sp
                  transform: scale(1.3)

              label
                display: flex
                align-items: center
                margin: 0 .5rem
                @include sp
                  font-size: 3vw

        .wrap-btn
          display: flex
          flex-direction: row
          justify-content: center
          align-items: center
          gap: 40px
          margin: 0 auto
          padding: 2rem 1rem 0
          @include sp
            flex-direction: column
            gap: 4vw

          button,a
            cursor: pointer

            &:hover
              opacity: .8

            &.clear
              color: $main-color
              font-size: .9rem
              text-decoration: none
              @include sp
                font-size: 3.8vw

              &:hover
                opacity: 1
                text-decoration: underline

            &.search
              width: 280px
              height: 55px
              margin: 0
              padding: .5rem 1rem
              color: #fff
              font-size: 1rem
              font-weight: 500
              background-color: $main-color
              border-radius: 30px
              @include sp
                font-size: 3.8vw

    .conditions
      margin: 1rem auto 0
      padding: 20px 40px
      background-color: #f5f5f5
      border-radius: 8px
      @include sp
        padding: 4vw 4vw

      .conditions__label
        display: flex
        flex-direction: row
        align-items: center
        padding: .4rem 1rem 1rem
        border-bottom: 1px solid $main-p-gray
        @include sp
          flex-direction: column
          padding: 0 .5rem .3rem

        .ttl
          width: 100px
          padding: .2rem 0
          font-size: 1rem
          font-weight: 700
          @include sp
            width: 100%
            margin: 0 0 2vw
            font-size: 4vw
            text-align: center

        .elm
          display: flex
          flex-direction: row
          flex-wrap: wrap
          @include sp
            font-size: 3.5vw

          span
            display: inline-block
            margin: 0 .5rem  .5rem 0
            padding: .2rem 1.5rem
            background-color: #fff
            border-radius: 20px


      .results__label
        display: flex
        flex-direction: row
        align-items: center
        padding: 1.2rem 1rem .2rem
        @include sp
          padding: 1rem .5rem .2rem

        .ttl
          width: 100px
          font-size: 1rem
          font-weight: 700
          @include sp
            width: auto
            padding: 0 4vw 0 0
            font-size: 4vw

        .elm
          font-size: 1.2rem
          font-weight: 700
          font-family: $font-price
          @include sp
            font-size: 4vw

  #list
    width: calc(1280px + 2rem)
    max-width: calc(100%)
    margin: 0 auto
    padding: 0 0 100px
    position: relative
    @include sp
      padding: 0 0 20vw

    .container

      .display-option
        display: flex
        flex-direction: row
        justify-content: space-between
        margin: 0
        border-bottom: 1px solid $line-gray
        @include sp
          flex-direction: column


        .refine
          position: relative
          display: flex
          align-items: end
          justify-content: flex-start
          flex-wrap: nowrap
          width: auto
          padding: 1rem 0
          @include sp
            justify-content: space-between
            width: 100%

          .sorting

            button.menu_trigger
              position: relative
              width: auto
              min-width: 125px
              margin: 0 2rem 0 0
              padding: .2rem 1.5rem .2rem 1.2rem
              font-size: 1rem
              text-align: left
              background-color: #fff
              @include sp
                padding: 2vw 4vw 2vw 0

              &:after
                position: absolute
                top: calc(50% - 8px)
                right: 1px
                color: #000
                font-size: 1rem
                font-weight: 900
                font-family: "Material Icons"
                content: "\e5cc"
                line-height: 1
                transform: rotateZ(90deg)

              .option_selected
                @include sp
                  font-size: 3.8vw

            ul.sorting_panel
              display: none
              position: absolute
              top: 55px
              left: 0
              width: 200px
              max-width: calc(100% - 2rem)
              margin: 0
              padding: .8rem 0 1rem
              z-index: 50
              background-color: #fff
              border: 1ps solid #eee
              border-radius:8px
              box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35)

              &.is-active
                display: block

              .option_item
                margin: 0
                padding: .2rem 1.2rem
                font-size: .9rem
                @include sp
                  font-size: 3.8vw
                &:hover
                  background-color: #eee

                a
                  cursor: pointer

          .check-onsale
            width: auto
            margin: 0
            padding: .2rem 1.2rem
            white-space: nowrap
            @include sp
              padding: 2vw 0

            input
              margin: 0
              transform: scale(1.3)
              @include sp
                transform: scale(1.8) translateY(-.1vw)

            label
              margin: 0 .7rem
              white-space: nowrap
              @include sp
                font-size: 3.8vw
                margin: 0 0 0 4vw

        .switch
          display: flex
          flex-direction: row
          justify-content: flex-end
          width: auto
          padding: 1rem 0
          @include md
            flex-wrap: wrap
          @include sp
            width: 100%
            padding: 4vw 0
            border-top: 1px solid $line-gray

          .dl
            padding: .2rem 1rem
            @include md
              width: 100%
              text-align: right
            @include sp
              margin: 0 0 4vw
              padding: 0 0 4.5vw
              border-bottom: 1px solid $line-gray

            a
              text-decoration: underline
              span
                @include sp
                  font-size: 3.8vw

              &:hover
                text-decoration: none

          .number-switch
            display: flex
            flex-direction: row
            padding: .2rem 1rem
            border-left: 1px solid #999
            @include sp
              justify-content: flex-start
              align-items: baseline
              flex: 1
              padding: 2vw 0
              border-left: none

            .label
              margin: 0 1rem 0 0
              @include sp
                font-size: 3.8vw

            .num
              .btn
                display: inline-block
                margin: 0 .5rem
                padding: 0
                color: $main-p-gray
                font-size: 1rem
                background-color: transparent
                @include sp
                  font-size: 3.8vw

                &.is-active
                  color: #000
                  text-decoration: underline
                &:hover
                  color: #000
                  text-decoration: underline

          .display-switch
            display: flex
            flex-direction: row
            padding: .2rem 1rem
            border-left: 1px solid #999
            @include sp
              padding: 2vw 0 2vw 4vw

            p
              display: flex
              align-items: center
              margin: 0 .6rem
              @include sp
                margin: 0 .6rem

              .btn
                width: 21px
                height: 21px
                padding: 0
                background-color: transparent
                @include sp
                  width: 4vw
                  height: 4vw

                &.row
                  background: url("../img/common/icn_list_switch_row_off.svg") no-repeat
                  background-size: 21px 21px
                  background-position: center
                  background-clip: content-box
                  @include sp
                    background-size: 4vw 4vw

                  &.is-active,&:hover
                    background: url("../img/common/icn_list_switch_row_on.svg") no-repeat
                    @include sp
                      background-size: 4vw 4vw


                &.panel
                  background: url("../img/common/icn_list_switch_panel_off.svg") no-repeat
                  background-size: 21px 21px
                  background-position: center
                  background-clip: content-box
                  @include sp
                    background-size: 4vw 4vw

                  &.is-active,&:hover
                    background: url("../img/common/icn_list_switch_panel_on.svg") no-repeat
                    @include sp
                      background-size: 4vw 4vw


      .wrap-btn
        width: 100%
        margin: 2rem 0 0
        text-align: center
        @include sp
          margin: 10vw 0 0

        .list-more
          width: 300px
          height: 55px
          margin: 0 auto
          padding: .5rem 2rem
          color: #fff
          font-size: 1rem
          font-weight: 700
          background-color: #bf2a24
          border-radius: 4px
          @include sp
            height: 60px

      .item-list.row
        margin: 0
        text-align: center

        &.no-item
          padding: 15px
          display: flex
          justify-content: center
          align-items: center
          background-color: #fff

          p.no-item-msg
            text-align: center
            font-size: 22px
            font-weight: 700
            color: #000

        ul
          display: flex
          flex-direction: column
          width: 100%

          > li
            position: relative
            padding: 0
            width: 100%
            margin: 0
            border-top: 1px solid #ccc
            @include sp
              margin: 0 0 1.5 rem
              flex-direction: column

            + li
              margin: 0
              @include sp
                margin: 0

            &:last-child
              border-bottom: 1px solid #ccc

            a
              position: relative
              display: grid
              grid-template-columns: 150px 1fr
              grid-template-rows: repeat(5, auto)
              gap: 8px 16px
              align-items: start
              width: 100%
              margin: 0
              padding: 1rem 0
              @include sp
                grid-template-columns: 20vw 1fr
                padding: 4vw 0

              figure
                position: relative
                grid-column: 1 / 2
                grid-row: 1 / 6
                display: flex
                align-items: center
                justify-content: center
                width: 150px
                height: 150px
                border: 1px solid #ccc
                @include sp
                  width: 20vw
                  height: 20vw

                img
                  display: block
                  max-width: 100%
                  max-height: 100%
                  object-fit: contain

                .tab-f
                  position: absolute
                  top: 0
                  left: 0

                  span
                    display: block
                    padding: 2px 14px
                    font-size: .7rem
                    font-weight: 600
                    @include sp
                      font-size: 2.4vw

                    &.delivery
                      color: #fff
                      background-color: #8b8b8b

              .item-name
                grid-column: 2 / 3
                grid-row: 1 / 2
                width: 100%
                margin: 0 0 .5rem
                padding: 0 .5rem .5rem 0
                color: $main-color
                font-weight: 500
                font-size: .9rem
                line-height: 1.3
                border-bottom: 1px solid #ccc
                @include sp
                  margin: 0
                  padding: 0 0 3vw 0
                  font-size: 3.8vw

              .current-price
                grid-column: 2 / 3
                grid-row: 2 / 3
                margin: 0 .2rem .2rem
                padding: 0
                @include sp
                  margin: 0 .2rem 2vw
                  font-size: 3.6vw

                .price-c
                  display: inline-block
                  margin-right: 5px
                  color: #231914
                  font-size: .8rem
                  transform: translateY(-1px)
                  @include sp
                    font-size: 2.3vw

                .price-v
                  color: #E50A09
                  font-size: 1.2rem
                  font-weight: 700
                  font-family: $font-price
                  @include sp
                    font-size: 4.1vw

                .price-u
                  color: #E50A09
                  font-size: 1rem
                  font-weight: 700
                  @include sp
                    font-size: 2.6vw

                .tax-u
                  display: inline-block
                  margin-left: 5px
                  font-size: .65rem
                  @include sp
                    font-size: 2.3vw

              .tab-wrap,.tab-wrap-status
                display: flex
                flex-direction: row
                justify-content: flex-start
                align-items: center
                gap: 3px
                width: 100%
                margin: 0
                padding: 0

                li
                  width: auto
                  margin: 0
                  padding: 2px 7px
                  border: 1px solid $main-color
                  font-size: .68rem
                  font-weight: 500
                  display: flex
                  justify-content: center
                  align-items: center
                  @include sp
                    font-size: 2vw

                  &.tab-main
                    color: #fff
                    background-color: $main-color
                  &.tab-sub
                    color: #83b8cf
                    border: 1px solid #83b8cf
                  &.tab-standard
                    color: $main-color
                    border: 1px solid $main-color

                  &.top
                    padding: 3px 10px
                    color: #fff
                    font-weight: 600
                    background-color: $main-red
                    border: 1px solid $main-red
                    border-radius: 4px

                  &.min-bid
                    padding: 3px 8px
                    color: $main-red
                    font-weight: 600
                    border: 1px solid $main-red
                    border-radius: 4px

              .tab-wrap


              .tab-wrap-status
                li
                  width: 100px
                  @include sp
                    width: 24vw


              dl

                display: flex
                flex-direction: row
                width: 100%
                min-height: 38px
                margin: auto auto 0 0
                padding: .5rem .2rem .5rem
                @include sp
                  flex-direction: column
                  width: 100%

                dt, dd
                  display: flex
                  align-items: center
                  margin: 0 0 .2rem
                  font-size:.8rem
                  @include sp
                    margin: 0 0 2vw
                    font-size: 3.4vw


                dt
                  width: 220px
                  padding: 0 0 0 1.5rem
                  background: url("../img/common/icn_clock_list.png") no-repeat
                  background-size: 16px auto
                  background-position: left calc(50%)
                  @include sp
                    width: 100%
                    padding: 0 0 0 6vw
                    background-size: 3.5vw auto
                    background-position: 0 center

                  .end-v
                    font-weight: 700
                    line-height: 1.1
                    font-family: $font-price
                    @include sp
                      width: 100%
                      padding: 0

                    span
                      display: inline-block
                      font-weight: 700
                      font-family: $font-price

                      &.red
                        color: #E50A09

                dd
                  width: 100px
                  padding: 0 0 0 2rem
                  background: url("../img/common/icn_hammer_list.png") no-repeat
                  background-size: 16px auto
                  background-position: 10px calc(50%)
                  @include sp
                    width: calc(100% - 15vw)
                    padding: 0 0 0 6vw
                    background-size: 3.5vw auto
                    background-position: 0 center

                  .bid-v
                    font-weight: 700
                    line-height: 1.1
                    font-family: $font-price
                    @include sp
                      width: 100%
                      padding: 0

              .favorite
                position: absolute
                top: auto
                bottom: 15px
                right: 10px
                left: auto
                width: 34px
                height: 34px
                padding: 2px
                background-color: #fff
                border: 1px solid $line-gray
                border-radius: 20px
                background-image: url(../img/common/icn_favorite.svg)
                background-size: 26px auto
                background-position: 3px 3px
                background-repeat: no-repeat
                z-index: 1
                @include sp
                  top: 5px
                  left: 5px
                  bottom: auto
                  right: auto
                  width: 28px
                  height: 28px
                  background-size: 20px auto
                  background-position: 3px 3px
                  background-repeat: no-repeat

                &::hover
                  opacity: 1
                  background-image: url(../img/common/icn_favorite_blue.svg)
                  border: 1px solid $main-color

                &.active
                  background-image: url(../img/common/icn_favorite_blue.svg)
                  border: 1px solid $main-color

              .product-wrap
                position: relative
                padding: 0 0 44px
                width: calc(100% - 150px)
                min-height: 150px
                border-left: 1px solid $main-p-gray
                @include sp
                  width: calc(100% - 100px)
                  padding: 0

                .item-name
                  position: relative
                  width: 100%
                  margin: 0
                  padding: .5rem 110px .5rem 1rem
                  color: #fff
                  font-size: 1rem
                  font-weight: 500
                  line-height: 1.3
                  background-color: $main-gray
                  border: none
                  @include sp
                    height: auto !important
                    padding: .5rem 1rem .5rem 1rem
                    font-size: .8rem
                    line-height: 1.1rem

                  .tag_status
                    position: absolute
                    top: 1rem
                    right: 1rem
                    margin: 0
                    padding: 0
                    line-height: 1
                    @include sp
                      position: static
                      display: block
                      width: 100%
                      padding: .5rem 0 .3rem
                    > p
                      display: inline-block
                      margin: 0 2px 2px 0
                      padding: 4px 12px 5px
                      font-size: .8rem
                      font-weight: 700
                      line-height: 1
                      border-radius: 20px
                      @include sp
                        padding: 5px 16px 6px
                    .status_recommend
                      color: #fff
                      background-color: #ff0000


                .current-price
                  display: flex
                  flex-direction: row
                  align-items: baseline
                  width: 100%
                  padding: .5rem 1rem
                  background-color: #fff
                  @include sp

                  .price-v
                    display: inline-block
                    margin: 0 .5rem
                    color: #E50A09
                    font-size: 1.4rem
                    font-weight: 700
                    line-height: 1.2
                    white-space: nowrap
                    @include sp
                      font-size: 1.4rem

                dl
                  position: absolute
                  left: 0
                  right: 0
                  bottom: 0
                  display: flex
                  flex-direction: row
                  justify-content: flex-start
                  width: 100%
                  height: 44px
                  margin: 0 auto
                  padding: .5rem 1rem
                  background-color: #f0f0f0
                  @include sp
                    position: static
                    flex-direction: column
                    width: 100%
                    height: auto
                    border-bottom: 1px solid #fff

                  dt
                    width: 180px
                    @include sp
                      width: 100%
                    .bid-l
                      font-weight: 400
                      display: inline-block
                      @include sp
                        width: 4rem
                        font-size: .8rem
                    .bid-v
                      margin: 0 0 0 1rem
                      font-weight: 600
                      display: inline-block
                      @include sp
                        margin: 0 0 0 .5rem


                  dd
                    display: flex
                    flex-direction: row
                    width: calc(100% - 180px)
                    @include sp
                      flex-direction: column
                      width: 100%
                    .end-l
                      width: 140px
                      @include sp
                        padding: 0

                      span.label
                        display: inline-block
                        margin: 0 .5rem 0 0
                        @include sp
                          width: 4rem
                          font-size: .8rem
                      span.value
                        display: inline-block
                        font-size: 1rem
                        font-weight: 700
                        @include sp
                          font-size: .9rem
                      span.limit
                        color: #ff0000
                    .end-v
                      width: calc(100% - 140px)
                      @include sp
                        width: 100%
                        padding: 0

                      span.label
                        display: inline-block
                        margin: 0 .5rem 0 0
                        @include sp
                          width: 4rem
                          font-size: .8rem
                      span.value
                        display: inline-block
                        font-size: 1rem
                        font-weight: 700
                        @include sp
                          font-size: .9rem

                .place-bid
                  grid-row: 1 / 4
                  grid-column: 2 / 3
                  position: relative
                  width: 360px
                  padding: 1rem 1rem calc(46px + 2rem)
                  background-color: #f0f0f0
                  @include md
                    width: 240px
                    max-width: 100%
                  @include sp
                    grid-row: 3 / 4
                    grid-column: 1 / 2
                    width: 100%
                    max-width: 100%
                    border-bottom: 1px solid #fff
                  .ttl
                    margin: 0 0 .5rem
                    font-size: 1.2rem
                    font-weight: 700
                  .price
                    font-size: 1.4rem
                    font-weight: 700
                  input
                    width: 7rem
                    margin: 0 0 0 1rem
                    padding: 5px
                    font-size: 1.4rem
                    font-weight: 700
                    text-align: right
                    &::placeholder
                      color: #ddd
                    &.price-bid-comp
                      background-color: #e5e5e5
                      &::placeholder
                        color: #000

                  ul
                    display: flex
                    flex-direction: row
                    flex-wrap: wrap
                    margin: 1rem 0 1rem

                  ul > li > button
                    display: flex
                    align-items: center
                    margin: 0 5px 5px 0
                    padding: 0 7px 0 0
                    font-size: 1rem
                    background-color: #fff
                    border: 1px solid #CDCBCA
                    border-radius: 30px
                    white-space: nowrap
                    @include sp
                      font-size: 1rem
                    span
                      display: inline-block
                      position: relative
                      width: 20px
                      height: 20px
                      margin: 2px 5px 2px 2px
                      padding: 0 7px
                      color: #fff
                      line-height: 1
                      background-color: $main-red
                      border-radius: 20px
                      &::after
                        content: "+"
                        position: absolute
                        top: .7px
                        left: 5.5px
                        width: 14px
                        height: 14px
                        color: #fff

                  .button-bid
                    display: flex
                    flex-direction: row
                    &.invoice
                      margin: 3rem 0 0
                      @include sp
                        margin: 1rem 0 0
                    button
                      width: calc(100% - 50px - 1rem)
                      height: 55px
                      color: #fff
                      font-size: 1rem
                      font-weight: 500
                      background-color: $main-red
                      border-radius: 4px
                      line-height: 1.2
                      &.invoice
                        width: 100%
                        padding: 1px 25% 3px 10px
                        text-align: right
                        background-image: url(../img/common/icn_download_list.svg)
                        background-repeat: no-repeat
                        background-position: right 10% top 50%
                        @include md
                          text-align: center
                        @include sp
                          padding: 1px 10px 3px 10px
                          text-align: center
                          background-position: right calc(50% - 7.2rem) top 50%
                        @media screen and (max-width: 400px)
                          padding: 1px 46px 3px 10px
                          background-position: right 6% top 50%

                      @include md
                        width: auto
                        min-width: calc(100% - 55px - 1rem)

                      @include sp
                        width: calc(100% - 55px - .5rem)
                    .update
                      position: relative
                      width: 55px
                      height: 55px
                      margin: 0 0 0 1rem
                      padding: 1.5rem 0 0
                      color: $main-red
                      text-align: center
                      background-color: #fff
                      border: 1px solid #CDCBCA
                      border-radius: 30px
                      span
                        font-size: .8rem
                      &::after
                        content: ""
                        display: inline-block
                        background: url("../img/common/icn_update_list.svg") center 8px no-repeat
                        background-size: 20px auto
                        width: 30px
                        height: 30px
                        position: absolute
                        top: 0
                        left: calc(50% - 15px)

                  .other-info-detail
                    position: absolute
                    bottom: 1rem
                    left: 1rem
                    width: calc(100% - 2rem)
                    z-index: 10
                    button
                      position: relative
                      width: 100%
                      height: 46px
                      margin: 0
                      padding: .5rem 1rem
                      color: $main-red
                      font-size: 1rem
                      font-weight: 500
                      background-color: #fff
                      border: 2px solid $main-red
                      border-radius: 30px
                      &::after
                        position: absolute
                        top: calc(50% - 4px)
                        right: 15px
                        display: block
                        width: 10px
                        height: 10px
                        padding: 0
                        color: $main-red
                        font-size: 16px
                        font-weight: 900
                        font-family: "Material Icons"
                        content: "\e5cc"
                        line-height: 0.6

            .refresh
              position: absolute
              bottom: 1rem
              right: calc(46px)
              width: 34px
              height: 34px
              padding: 2px 2px
              background-color: #fff
              border: 1px solid $main-red
              border-radius: 50%
              background-image: url(../img/common/icn_refresh.svg)
              background-size: 18px auto
              background-repeat: no-repeat
              background-position: 50% 47%
              cursor: pointer
              z-index: 1
              @include sp
                bottom: 5vw
                left: 1vw
                width: 7vw
                height: 7vw
                background-size: 3.5vw auto

              &:hover
                opacity: 1
                background-color: #fff4f4

            .favorite
              position: absolute
              bottom: 1rem
              right: .2rem
              width: 34px
              height: 34px
              padding: 2px 2px
              background-color: #fff
              border: 1px solid $line-gray
              border-radius: 50%
              background-image: url(../img/common/icn_favorite.svg)
              background-size: 16px auto
              background-repeat: no-repeat
              background-position: 50% 56%
              cursor: pointer
              z-index: 1
              @include sp
                bottom: 5vw
                left: 10vw
                width: 7vw
                height: 7vw
                background-size: 3.5vw auto

              &:hover
                opacity: 1
                background-image: url(../img/common/icn_favorite_blue.svg)
                border: 1px solid $main-color

              &.active
                background-image: url(../img/common/icn_favorite_blue.svg)
                border: 1px solid $main-color


          > li.soldout
            a
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                padding: 0
                background-image: url(../img/common/icn_soldout.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10


      .item-list.panel
        margin: 0 0 2rem
        padding: 1rem 0 0
        text-align: center
        @include sp
          padding: 4vw 0 0

        &.no-item
          padding: 15px
          display: flex
          justify-content: center
          align-items: center
          background-color: #fff

          p.no-item-msg
            text-align: center
            font-size: 22px
            font-weight: 700
            color: #000

        ul
          display: flex
          flex-wrap: wrap
          justify-content: flex-start
          align-items: stretch
          width: 100%
          padding: 0
          gap: 40px 15px
          @include sp
            gap: 6vw 4vw

          li
            position: relative
            display: flex
            flex-direction: column
            justify-content: space-between
            align-items: stretch
            width: calc((100% - 60px) / 5)
            height: auto
            margin: 0
            background-color: #fff
            @include md
              width: calc((100% - 30px) / 3)
            @include sp
              width: calc((100% - 4vw) / 2)

            &.nego
              a
                position: relative

                figure:after
                  content: ""
                  position: absolute
                  top: 0
                  left: 0
                  width: 100%
                  aspect-ratio: 1
                  padding: 0
                  background-image: url(../img/common/icn_nego.png)
                  background-size: contain
                  background-position: center
                  background-repeat: no-repeat
                  z-index: 10

            &.soldout
              a
                position: relative
                figure:after
                  content: ""
                  position: absolute
                  top: 0
                  left: 0
                  width: 100%
                  aspect-ratio: 1
                  padding: 0
                  background-image: url(../img/common/icn_soldout.png)
                  background-size: contain
                  background-position: center
                  background-repeat: no-repeat
                  z-index: 10

            a
              position: relative
              display: flex
              flex-direction: column
              flex-grow: 1
              height: 100%
              cursor: pointer

              &.pct-wrap
                flex: 0 0 auto

              &:not(.pct-wrap)
                display: flex
                flex-grow: 1
                flex-direction: column
                justify-content: space-between

              figure
                position: relative
                aspect-ratio: 1
                border: 1px solid $line-gray

                img
                  height: 100%
                  object-fit: contain

                .tab-f
                  position: absolute
                  top: 0
                  left: 0

                  span
                    display: block
                    padding: 2px 14px
                    font-size: .7rem
                    font-weight: 600
                    @include sp
                      font-size: 2.4vw

                    &.delivery
                      color: #fff
                      background-color: #8b8b8b

              .item-name
                margin: 0 auto
                padding: .5rem .2rem
                color: $main-text
                font-size: .8rem
                font-weight: 500
                line-height: 1.4
                @include sp
                  font-size: 3.5vw
                  border-bottom: none

              .current-price
                margin: 0 .2rem .2rem
                padding: 0
                @include sp
                  margin: 0 .2rem 2vw
                  font-size: 3.6vw

                .price-c
                  display: inline-block
                  margin-right: 5px
                  color: #231914
                  font-size: .7rem
                  transform: translateY(-1px)
                  @include sp
                    font-size: 2.3vw

                .price-v
                  color: #E50A09
                  font-size: 1.2rem
                  font-weight: 700
                  font-family: $font-price
                  @include sp
                    font-size: 4.1vw

                .price-u
                  color: #E50A09
                  font-size: 1rem
                  font-weight: 700
                  @include sp
                    font-size: 2.6vw

                .tax-u
                  display: inline-block
                  margin-left: 5px
                  font-size: .65rem
                  @include sp
                    font-size: 2.3vw

              .tab-wrap,.tab-wrap-status
                display: flex
                flex-direction: row
                justify-content: flex-start
                align-items: center
                gap: 3px
                width: 100%
                margin: 0 0 .2rem
                padding: 0 .2rem

                li
                  width: auto
                  margin: 0
                  padding: 2px 7px
                  border: 1px solid $main-color
                  font-size: .68rem
                  font-weight: 500
                  display: flex
                  justify-content: center
                  align-items: center
                  @include sp
                    font-size: 2.5vw

                  &.tab-main
                    color: #fff
                    background-color: $main-color
                  &.tab-sub
                    color: #83b8cf
                    border: 1px solid #83b8cf
                  &.tab-standard
                    color: $main-color
                    border: 1px solid $main-color

                  &.top
                    padding: 3px 10px
                    color: #fff
                    font-weight: 600
                    background-color: $main-red
                    border: 1px solid $main-red
                    border-radius: 4px

                  &.min-bid
                    padding: 3px 8px
                    color: $main-red
                    font-weight: 600
                    border: 1px solid $main-red
                    border-radius: 4px

              dl
                display: flex
                flex-direction: column
                width: 100%
                min-height: 38px
                margin: auto auto 0 0
                padding: .5rem .2rem .5rem
                @include sp
                  flex-direction: column
                  gap: 1vw
                  width: 100%
                  margin: 0
                  padding: 1vw 1vw 4vw

                dt, dd
                  display: flex
                  align-items: baseline
                  margin: 0 0 .4rem
                  font-size:.8rem
                  @include sp
                    font-size: 3.4vw

                dd
                  width: 100%
                  padding: 0 0 0 1.5rem
                  background: url("../img/common/icn_hammer_list.png") no-repeat
                  background-size: 16px auto
                  background-position: left calc(0%)
                  @include sp
                    align-items: center
                    width: 100%
                    height: 4vw
                    padding: 0 0 0 6vw
                    background-size: 3.5vw auto
                    background-position: 0 center
                    order: 2

                  .bid-v
                    font-size: .9rem
                    font-weight: 700
                    line-height: 1.1
                    font-family: $font-price
                    @include sp
                      width: 100%
                      padding: 0
                      font-size: 3.2vw

                dt
                  width: 100%
                  padding: 0
                  background: url("../img/common/icn_clock_list.png") no-repeat
                  background-size: 16px auto
                  background-position: 0
                  @include sp
                    align-items: center
                    width: 100%
                    height: 4vw
                    padding: 0 0 0 6vw
                    background-size: 3.5vw auto
                    background-position: 0 center
                    order: 1

                  .end-v
                    display: flex
                    flex-direction: row
                    font-weight: 700
                    line-height: 1.1
                    @include sp
                      flex-direction: row
                      align-items: baseline
                      gap: 0
                      width: 100%
                      padding: 0

                    span
                      display: block
                      font-weight: 700

                      &.date
                        margin: 0 0 .3rem
                        padding: 0 0 0 24px
                        font-size: .9rem
                        font-family: $font-price
                        @include sp
                          margin: 0
                          padding: 0
                          font-size: 3.2vw

                      &.end
                        padding: 0 0 0 4px
                        font-size: 12px
                        font-weight: 400
                        line-height: 1.2
                        font-family: $font-price
                        @include sp
                          padding: 0
                          font-size: 2.5vw


            .refresh
              position: absolute
              bottom: .3rem
              right: 38px
              width: 30px
              height: 30px
              padding: 2px 2px
              background-color: #fff
              border: 1px solid $main-red
              border-radius: 50%
              background-image: url(../img/common/icn_refresh.svg)
              background-size: 16px auto
              background-repeat: no-repeat
              background-position: 50% 47%
              cursor: pointer
              z-index: 1
              @include sp
                bottom: 0
                right: 9.5vw
                width: 7vw
                height: 7vw
                background-size: 3.5vw auto

              &:hover
                opacity: 1
                background-color: #fff4f4

            .favorite
              position: absolute
              bottom: .3rem
              right: .2rem
              width: 30px
              height: 30px
              padding: 2px 2px
              background-color: #fff
              border: 1px solid $main-p-gray
              border-radius: 50%
              background-image: url(../img/common/icn_favorite.svg)
              background-size: 16px auto
              background-repeat: no-repeat
              background-position: 50% 56%
              cursor: pointer
              z-index: 1
              @include sp
                bottom: 0
                right: 1vw
                width: 7vw
                height: 7vw
                background-size: 3.5vw auto

              &:hover
                opacity: 1
                background-image: url(../img/common/icn_favorite_blue.svg)

              &.active
                background-image: url(../img/common/icn_favorite_blue.svg)

      .item-list.row-bid

        margin: 0
        text-align: center

        &.no-item
          padding: 15px
          display: flex
          justify-content: center
          align-items: center
          background-color: #fff

          p.no-item-msg
            text-align: center
            font-size: 22px
            font-weight: 700
            color: #000

        ul
          display: flex
          flex-direction: column
          gap: 0
          width: 100%

          >li
            display: grid
            grid-template-columns: 150px 1fr auto
            gap: 1rem
            padding: 1rem 0
            width: 100%
            margin: 0
            border-bottom: 1px solid #ccc
            @include sp
              grid-template-columns: 1fr
              margin: 0 0 1.5 rem
              flex-direction: column
              gap: 0 4vw

            figure
              position: relative
              grid-column: 1 / 2
              grid-row: 1 / 2
              display: flex
              align-items: center
              justify-content: center
              width: 150px
              height: 150px
              border: 1px solid #ccc
              @include sp
                grid-column: 1/2
                grid-row: 1/2
                width: 20vw
                height: 20vw

              img
                display: block
                max-width: 100%
                max-height: 100%
                object-fit: contain

              .tab-f
                position: absolute
                top: 0
                left: 0

                span
                  display: block
                  padding: 2px 14px
                  font-size: .7rem
                  font-weight: 600
                  @include sp
                    font-size: 2.4vw

                  &.delivery
                    color: #fff
                    background-color: #8b8b8b

            .item-p-desc
              position: relative
              grid-column: 2 / 3
              grid-row: 1 / 2

              .item-name
                grid-column: 2 / 3
                grid-row: 1 / 2
                width: 100%
                margin: 0
                padding: .2rem .5rem .8rem .2rem
                color: $main-color
                font-weight: 500
                font-size: .9rem
                line-height: 1.3
                border-bottom: 1px solid $main-p-gray
                @include sp
                  margin: 0
                  padding: 0 10vw 3vw 0
                  font-size: 3.8vw

                a
                  display: block
                  padding: 0
                  color: $main-color
                  font-weight: 500

              .desc-p-top
                display: flex
                flex-direction: row
                @include sp
                  flex-direction: column

                .price-box
                  display: flex
                  flex-direction: column
                  align-items: flex-start
                  justify-content: center
                  gap: 0
                  flex: 1
                  margin: 0 0 .5rem
                  padding: .5rem 0 0
                  @include sp
                    margin: 1vw 0 2vw

                  .price
                    margin: 0 .2rem
                    padding: 0
                    @include sp
                      margin: 0 .2rem 0
                      font-size: 3.6vw

                    .price-c
                      display: inline-block
                      margin-right: 5px
                      color: #231914
                      font-size: .7rem
                      transform: translateY(-1px)
                      @include sp
                        font-size: 2.5vw

                    .price-v
                      color: #E50A09
                      font-size: 1.1rem
                      font-weight: 700
                      font-family: $font-price
                      @include sp
                        font-size: 4.5vw

                      &.bl
                        color: $main-text

                      &.sm
                        font-size: .9rem
                        @include sp
                          font-size: 2.8vw

                    .price-u
                      color: #E50A09
                      font-size: 1rem
                      font-weight: 700
                      @include sp
                        font-size: 2.6vw

                      &.bl
                        color: $main-text

                      &.sm
                        font-size: .9rem
                        @include sp
                          font-size: 2.8vw

                      &.thin
                        font-weight: 400

                    .tax-u
                      display: inline-block
                      margin-left: 5px
                      font-size: .65rem
                      @include sp
                        font-size: 2.3vw

                .tab-wrap-status
                  display: flex
                  flex-direction: column
                  justify-content: flex-start
                  align-items: center
                  gap: 3px
                  width: auto
                  margin: 0 0 .5rem
                  padding: .8rem .2rem
                  @include sp
                    flex-direction: row
                    justify-content: flex-start
                    align-items: flex-start
                    padding: 0

                  li
                    width: 100px
                    margin: 0
                    padding: 2px 7px
                    border: 1px solid $main-color
                    font-size: .68rem
                    font-weight: 500
                    display: flex
                    justify-content: center
                    align-items: center
                    @include sp
                      width: 24vw
                      font-size: 2.5vw

                    &.top
                      padding: 3px 10px
                      color: #fff
                      font-weight: 600
                      background-color: $main-red
                      border: 1px solid $main-red
                      border-radius: 4px

                    &.min-bid
                      padding: 3px 8px
                      color: $main-red
                      font-weight: 600
                      border: 1px solid $main-red
                      border-radius: 4px

              .tab-wrap
                display: flex
                flex-direction: row
                justify-content: flex-start
                align-items: center
                gap: 3px
                width: 100%
                margin: 0
                padding: 0
                @include sp
                  margin: 0 0 1vw

                li
                  width: auto
                  margin: 0
                  padding: 2px 7px
                  border: 1px solid $main-color
                  font-size: .68rem
                  font-weight: 500
                  display: flex
                  justify-content: center
                  align-items: center
                  @include sp
                    font-size: 2vw

                  &.tab-main
                    color: #fff
                    background-color: $main-color
                  &.tab-sub
                    color: #83b8cf
                    border: 1px solid #83b8cf
                  &.tab-standard
                    color: $main-color
                    border: 1px solid $main-color

              .pre-bid
                display: flex
                flex-direction: row
                flex-wrap: wrap
                width: calc(100% - 70px)
                min-height: 38px
                margin: auto auto 0 0
                padding: .8rem .2rem .5rem
                @include sp
                  width: 100%
                  flex-wrap: wrap

                li
                  display: flex
                  align-items: center
                  min-height: 16px
                  margin: 0 0 .2rem
                  font-size:.8rem
                  border: none
                  @include sp
                    margin: 0 0 2vw
                    font-size: 3.4vw

                  &.bid-v
                    width: 70px
                    padding: 0 0 0 1.5rem
                    background: url("../img/common/icn_hammer_list.png") no-repeat
                    background-size: 14px auto
                    background-position: left calc(50%)
                    @include sp
                      width: 15vw
                      padding: 0 0 0 5vw
                      background-size: 3.5vw auto
                      background-position: 0 center

                    p
                      font-weight: 700
                      line-height: 1.1
                      font-family: $font-price
                      @include sp
                        width: 100%
                        padding: 0

                  &.view
                    width: 70px
                    padding: 0 0 0 1.5rem
                    background: url("../img/common/icn_eye_list.svg") no-repeat
                    background-size: 16px auto
                    background-position: left center
                    @include sp
                      width: 15vw
                      padding: 0 0 0 5vw
                      background-size: 3.5vw auto
                      background-position: 0 center

                    p
                      font-weight: 700
                      line-height: 1.1
                      font-family: $font-price
                      @include sp
                        width: 100%
                        padding: 0

                  &.end-v
                    width: auto
                    padding: 0 0 0 24px
                    background: url("../img/common/icn_clock_list.png") no-repeat
                    background-size: 16px auto
                    background-position: left calc(50%)
                    @include sp
                      width: calc(100% - 15vw)
                      padding: 0 0 0 6vw
                      background-size: 3.5vw auto
                      background-position: 0 center

                    p
                      font-weight: 700
                      line-height: 1.1
                      font-family: $font-price
                      @include sp
                        width: 100%
                        padding: 0

                      span
                        display: inline-block
                        font-weight: 700
                        font-family: $font-price

                        &.red
                          color: #E50A09

                  &.favo
                    width: 70px
                    padding: 0 0 0 1.4rem
                    background: url("../img/common/icn_favorite.svg") no-repeat
                    background-size: 16px auto
                    background-position: 0 calc(50%)
                    @include sp
                      width: calc(100% - 15vw)
                      padding: 0 0 0 6vw
                      background-size: 3.5vw auto
                      background-position: 0 center

                    p
                      font-weight: 700
                      line-height: 1.1
                      font-family: $font-price
                      @include sp
                        width: 100%
                        padding: 0

                      span
                        display: inline-block
                        font-weight: 700

                        &.red
                          color: #E50A09

              .favorite.row-bid
                position: absolute
                top: auto
                bottom: 10px
                right: 2px
                left: auto
                width: 34px
                height: 34px
                padding: 2px
                background-color: #fff
                border: 1px solid $line-gray
                border-radius: 50px
                background-image: url(../img/common/icn_favorite.svg)
                background-size: 16px auto
                background-position: center
                background-repeat: no-repeat
                z-index: 1
                @include sp
                  top: 0.08vw
                  right: 0.06vw
                  left: auto
                  bottom: auto
                  width: 7vw
                  height: 7vw
                  background-size: 3vw auto
                  border-radius: 20vw

                &::hover
                  opacity: 1
                  background-image: url(../img/common/icn_favorite_blue.svg)
                  border: 1px solid $main-color

                &.active
                  background-image: url(../img/common/icn_favorite_blue.svg)
                  border: 1px solid $main-color

            .place-bid
              position: relative
              width: 360px
              max-width: 40vw
              padding: 1.5rem 1rem
              background-color: $main-smoke-bg-color
              @include sp
                grid-column: 1 / 3
                grid-row: 2 / 3
                width: 100%
                max-width: 100%

              .price
                width: 100%
                font-size: 1.2rem
                font-weight: 600
                text-align: left

                .ttl
                  margin: 0 1rem .5rem 0
                  font-size: 1.2rem
                  font-weight: 700

                input
                  width: 11rem
                  margin: 0 .5rem 0 0
                  padding: 5px
                  font-size: 1.4rem
                  font-weight: 700
                  text-align: right
                  border: none
                  border-radius: 4px

                  &::placeholder
                    color: #ddd
                    font-size: 1.4rem

                  &.price-bid-comp
                    background-color: #e5e5e5
                    &::placeholder
                      color: #000

              ul.bidding-unit
                display: flex
                flex-direction: row
                flex-wrap: wrap
                gap: 10px 5px
                width: 100%
                margin: 1rem 0 .5rem

                li
                  flex: 1
                  margin: 0
                  padding: 0
                  border: none
                  grid-template-columns: 1fr

                  .bid-unit
                    display: flex
                    align-items: center
                    width: 100%
                    max-width: 100%
                    margin: 0
                    padding: 0
                    font-size: 1rem
                    font-family: $font-price
                    background-color: #fff
                    border-radius: 4px
                    white-space: nowrap
                    @include sp
                      max-width: 100%
                      font-size: 1rem

                    span
                      display: inline-block
                      position: relative
                      width: 20px
                      height: 20px
                      margin: 2px 5px 2px 2px
                      padding: 0 7px
                      color: #fff
                      line-height: 1
                      background-color: $main-color
                      border-radius: 20px
                      @include sp
                        width: 4vw
                        height:4vw

                      &::after
                        content: "+"
                        position: absolute
                        top: 0
                        left: 0
                        display: flex
                        justify-content: center
                        align-items: center
                        width: 100%
                        height: 100%
                        color: #fff
                        transform: translateY(-1px)

              .button-bid
                display: flex
                flex-direction: row

                .btn
                  display: flex
                  justify-content: center
                  align-items: center
                  width: calc(100% - 50px - 1rem)
                  height: 55px
                  color: #fff
                  font-size: 1rem
                  font-weight: 500
                  background-color: $main-color
                  border-radius: 50px
                  line-height: 1.2
                  @include sp
                    width: calc(100% - 55px - .5rem)

                  &:hover
                    opacity: .8

                  .pct
                    width: 16px
                    height: auto

                  .bid
                    position: relative
                    width: auto
                    display: inline-block
                    padding-left: 14px
                    font-weight: 600

                .update
                  position: relative
                  width: 55px
                  height: 55px
                  margin: 0 0 0 1rem
                  padding: 1.5rem 0 0
                  color: $main-red
                  text-align: center
                  background-color: #fff
                  border: 1px solid $main-red
                  border-radius: 30px
                  cursor: pointer

                  &:hover
                    opacity: .8

                  span
                    font-size: .8rem
                  &::after
                    content: ""
                    display: inline-block
                    background: url("../img/common/icn_update_list.svg") center 8px no-repeat
                    background-size: 20px auto
                    width: 30px
                    height: 30px
                    position: absolute
                    top: 0
                    left: calc(50% - 15px)



.wrap-btn.pagination
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center
  width: 100%
  margin: 40px 0
  font-family: 'Helvetica Neue', sans-serif

  p
    margin: 0 0 1rem
    color: #979797
    font-size: .7rem

  ul
    list-style: none
    display: flex
    flex-direction: row
    gap: 8px
    padding: 0
    margin: 0

    li
      position: relative
      width: auto
      height: 40px
      border: none

      &.prev
        width: 40px
        &:after
          content: ""
          width: 6px
          height: 6px
          border: 0
          border-top: solid 1px #333
          border-right: solid 1px #333
          position: absolute
          top: calc(50% + 1px)
          left: calc(50% - 3px)
          margin-top: -4px
          -webkit-transform: rotate(-135deg)
          transform: rotate(-135deg)

      &.next
        width: 40px
        &:after
          content: ""
          width: 6px
          height: 6px
          border: 0
          border-top: solid 1px #333
          border-right: solid 1px #333
          position: absolute
          top: calc(50% + 1px)
          left: calc(50% - 6px)
          margin-top: -4px
          -webkit-transform: rotate(45deg)
          transform: rotate(45deg)

      a
        display: block
        justify-content: center
        align-items: center
        width: 100%
        height: 100%
        padding: 6px 14px
        color: $main-gray
        font-weight: 400
        text-decoration: none
        border: 1px solid #ccc
        transition: all 0.2s ease
        background-color: #fff

        &:hover
          background-color: #f0f0f0
          border-color: #999

        &.active
          background-color: $main-gray
          color: #fff
          border-color: $main-gray
