@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *会員登録
 ***********************************************************************

#main
  #entry-info
    margin: 60px 0 60px
    padding: 0 1rem
    @include sp
      margin-top: 40px
    dl
      border: 1px solid #bebebe
      background-color: #efefef
      padding: 50px 50px 40px
      @include sp
        padding: 15px 15px 20px

      dt
        font-size: 20px
        font-weight: 700
        text-align: center
        color: $main-color
        @include sp
          font-size: 17px
          text-align: left

      dd
        margin-top: 30px
        @include sp
          margin-top: 20px

        p.jp-document
          font-weight: 700
          text-align: center
          font-size: 18px
          @include sp
            font-size: 1rem

        ol
          display: flex
          justify-content: space-between
          @include sp
            display: block

          li
            width: 300px
            background-color: #fff
            margin-top: 20px
            @include sp
              width: 100% !important
              padding-bottom: 30px

            &:nth-of-type(2)
              width: 380px

            a
              display: block
              text-align: center
              width: 100%
              height: 100%
              padding: 0 15px 20px

            p.entry-info-item
              position: relative
              text-align: center
              font-size: 18px
              font-weight: 700
              padding-top: 25px
              @include sp
                letter-spacing: 0
                font-size: 1rem

              &::after
                content: "?"
                display: inline-block
                font-size: 16px
                font-weight: 700
                color: $main-color
                width: 20px
                height: 20px
                border-radius: 50%
                border: 2px solid $main-color
                line-height: 18px
                text-align: center

              span
                display: inline-block
                padding-top: 5px
                padding-left: 18px
                font-size: 24px
                color: #fff
                font-weight: 700
                position: absolute
                top: 0
                left: -15px
                z-index: 0
                @include sp
                  font-size: 20px
                  padding-top: 6px
                  padding-left: 16px

                &::before
                  content: ""
                  display: block
                  position: absolute
                  top: 0
                  left: 0
                  border-top: 40px solid $main-color
                  border-right: 40px solid transparent
                  border-bottom: 40px solid transparent
                  border-left: 40px solid $main-color
                  z-index: -1
                  @include sp
                    border-width: 35px

            img
              display: inline-block
              margin-top: 5px
              transition: all 0.08s linear
              @include sp
                height: 130px

            a:hover img
              opacity: 0.8

            p.entry-info-note
              color: #f00
              text-align: center
              margin-top: 5px
              font-size: 16px
              @include sp
                margin-bottom: -20px
                font-size: 14px


        p.kojin
          font-size: 14px
          text-align: center
          margin-top: 10px
          @include sp
            text-align: left
            text-indent: -1em
            padding-left: 1em


        .entry-info-btn
          margin: 40px auto 0
          width: 360px
          @include sp
            width: 100%
            margin-top: 20px

    p.entry-info-att
      margin-top: 30px
      font-size: 16px

      a
        text-decoration: underline
        color: $main-color

  #entry-form
    width: 980px
    max-width: calc(100% - 2rem)
    margin: 60px auto 2rem
    padding: 60px 3rem 60px
    background-color: #f7f7f7
    @include md
      padding: 60px 1.5rem 60px
    @include sp
      margin-top: 40px
      margin-bottom: 40px
      padding: 15px 15px 20px

    p.entry-form-info
      text-align: center
      font-weight: 700
      font-size: 18px
      @include sp
        text-align: left
        font-size: 17px

    em.req
      display: inline-block
      background-color: #E80000
      width: 35px
      height: 20px
      color: #fff
      font-weight: 700
      font-size: 12px
      text-align: center
      line-height: 19px

    p.entry-form-info em.req
      margin-right: 5px
      position: relative
      top: -2px

    table.tbl-entry
      width: 100%
      margin: 0 auto

    p.entry-form-info + table.tbl-entry
      margin-top: 40px

    table.tbl-entry
      width: 100%
      tr
        padding: .3rem 0
        th
          font-size: 1rem
          font-weight: 700
          vertical-align: middle
          width: 280px
          position: relative
          padding:0 2rem
          line-height: 1.2
          @include md
            width: 230px
            min-width: 190px
            padding:0 1rem
          @include sp
            display: block
            width: 100%
            padding: 0
            font-size: 1rem

          em.req
            position: absolute
            top: 50%
            right: 10px
            transform: translateY(-50%)

        td
          width: calc(100% - 1rem)
          padding: 15px 0
          position: relative
          line-height: 1.2
          @include sp
            display: block
            width: 100%
            padding: 10px 0 20px

          p.privacy-link
            margin-top: -20px
            font-size: 1rem
            @include sp
              margin-top: -40px

            a
              text-decoration: underline
              color: $main-color

          .checkbox-parts
            font-size: 1.1rem

  &.comp #entry-form table.tbl-entry td, &[class^="contact-"] #entry-form table.tbl-entry td
    font-size: 1.1rem

  &.comp #entry-form table.tbl-entry tr.att-use
    display: none

  #entry-form
    table.tbl-entry td
      .ipt-wrap
        display: flex
        @include sp
          flex-wrap: wrap

        input.zip-search
          margin: 5px 0 5px 15px
          @include sp
            padding-left: 5px
            padding-right: 5px
            margin: 0 0 0 15px

        span
          &.int-no
            display: flex
            background-color: #F7F7F7
            border: 1px solid #E3E3E3
            border-right: none
            align-items: center
            font-size: 16px
            width: 55px
            justify-content: center
            @include sp
              width: calc(100% - 55px) !important

          &.ipt-rule
            display: flex
            font-size: 14px
            align-items: center
            margin-left: 10px
            @include sp
              width: calc(100% - 55px) !important
              margin-left: 0
              margin-top: 3px

      .select-style
        width: 460px
        max-width: calc(100% - 40px)
        @include sp
          width: 100%
          max-width: 100%

      input
        &.iptW-M
          width: 460px
          max-width: calc(100% - 40px)
          @include md
            width: 360px
          @include sp
            width: 100%
            max-width: 100%

        &.iptW-MM
          width: 300px
          max-width: calc(100% - 40px)
          @include md
            width: calc(360px - 55px)
          @include sp
            width: calc(100% - 55px)
            width: calc(100% - 55px)

        &.iptW-S
          width: 200px
          max-width: calc(100% - 40px)
          @include sp
            width: calc(100% - 40px - 19px)

      textarea
        width: 100%
        height: 200px
        resize: vertical

      .ipt-file-box
        display: flex
        align-items: center
        font-size: 16px
        @include sp
          display: block

        label
          margin-right: 10px
          @include sp
            margin-right: 0
            margin-bottom: 5px

      .file-up-att
        margin-top: -20px
        font-size: 16px
        @include sp
          margin-top: -40px

    .btn-form
      margin-top: 25px
      display: flex
      justify-content: center
      flex-wrap: wrap

  &.comp p.comp-msg
    margin: 0 0 60px
    text-align: center
    font-size: 18px
    font-weight: 700
    @include sp
      margin: 0 0 40px
      text-align: left
      font-size: 17px

  &[class^="contact-"]

  &.contact-used #target-item
    margin-top: 60px
    margin-bottom: -30px

    ul li
      display: flex
      align-items: flex-start
      border: 1px solid #EAEAEA

      + li
        border-top: none

      .target-item-img
        width: 267px
        text-align: center

      .target-item-txt
        width: calc(100% - 267px)
        padding: 30px 0 15px 25px
        display: flex
        flex-wrap: wrap
        letter-spacing: 0

        p
          &.target-item-no
            background-color: #000
            color: #fff
            font-weight: 700
            line-height: 1
            display: inline-block
            padding: 6px 10px 8px
            margin-right: 10px

          &.target-item-system
            color: #01a7ac
            font-size: 14px
            font-weight: 700
            background-color: #E8FEFF
            border: 1px solid #01A7AC
            border-radius: 100vh
            padding: 2px 15px 0

        .target-item-data
          display: flex
          flex-wrap: wrap
          margin-top: 10px

          dl
            min-width: calc(100% / 3)
            display: flex
            padding-right: 15px

            dt, dd
              font-size: 16px
              font-weight: 700
              padding: 4px 0

            dt
              color: #01A7AC
              min-width: 6.5em
              padding-right: 10px
              flex-shrink: 0
