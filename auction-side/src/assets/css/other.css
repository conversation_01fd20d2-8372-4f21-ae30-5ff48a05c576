@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
/* table
 * *========================================== */
#main table.tbl-otherItem {
  width: 100%;
  margin: 0;
  border-top: 1px solid #eaeaea;
}
@media screen and (max-width: 767px) {
  #main table.tbl-otherItem {
    border: none;
  }
}
#main table.tbl-otherItem th,
#main table.tbl-otherItem td {
  border-bottom: 1px solid #eaeaea;
  vertical-align: top;
  font-size: 18px;
}
@media screen and (max-width: 767px) {
  #main table.tbl-otherItem th,
  #main table.tbl-otherItem td {
    width: 100%;
    display: block;
    padding: 15px 10px;
    font-size: 1rem;
    border: none;
  }
}
#main table.tbl-otherItem th {
  font-weight: 700;
  background-color: #f7f7f7;
}
#main table.tbl-otherItem td ul li {
  line-height: 1.6;
}
#main table.tbl-otherItem td ul li + li {
  margin: 0.8rem 0 0;
}
@media screen and (max-width: 767px) {
  #main table.tbl-otherItem td ul li + li {
    margin: 0.5rem 0 0;
  }
}
#main table.tbl-otherItem td ul.decimal {
  list-style-type: decimal;
  padding: 0 0 0 1.3rem;
}
#main table.tbl-otherItem td ul.disc {
  list-style-type: disc;
  padding: 0 0 0 1.3rem;
}
#main table.tbl-otherItem td dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 0.8rem 0 0;
  line-height: 1.6;
}
#main table.tbl-otherItem td dl dt {
  width: 5rem;
  margin: 0 0 0.8rem;
}
#main table.tbl-otherItem td dl dd {
  width: calc(100% - 5rem);
  margin: 0 0 0.8rem;
}
#main table.tbl-otherItem td dl.w-grad-equal dt {
  width: 8rem;
}
#main table.tbl-otherItem td dl.w-grad-equal dd {
  width: calc(100% - 8rem);
}
#main table.tbl-otherItem td ul {
  margin: 0.8rem 0 0;
}
#main table.tbl-otherItem td p {
  margin: 0.8rem 0 0;
  line-height: 1.6;
}
#main table.tbl-otherItem td .ma-0 {
  margin: 0;
}
#main table.tbl-otherItem td .ma-l {
  margin: 2.6rem 0 0;
}
#main table.tbl-otherItem td .ttl {
  font-weight: 500;
}
#main #privacy table.tbl-otherItem {
  margin-top: 20px;
}
#main #privacy table.tbl-otherItem th,
#main #privacy table.tbl-otherItem td {
  font-size: 17px;
}
@media screen and (max-width: 767px) {
  #main #privacy table.tbl-otherItem th,
  #main #privacy table.tbl-otherItem td {
    padding: 15px 30px;
    font-size: 15px;
  }
}
#main #profile table.tbl-otherItem th,
#main #profile table.tbl-otherItem td {
  padding: 35px 60px;
}
@media screen and (max-width: 767px) {
  #main #profile table.tbl-otherItem th,
  #main #profile table.tbl-otherItem td {
    padding: 1rem;
  }
}
#main #profile table.tbl-otherItem th {
  width: 300px;
}
@media screen and (max-width: 767px) {
  #main #profile table.tbl-otherItem th {
    width: 100%;
  }
}

/* リスト
 * *========================================== */
/* ---------------------------
 * *introduction
 * *---------------------------- */
#main #static {
  padding: 60px 2rem 60px;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main #static {
    padding: 30px 1rem;
    font-size: 15px;
  }
}
#main #static .introduction {
  padding: 0 3rem;
}
@media screen and (max-width: 767px) {
  #main #static .introduction {
    padding: 0;
  }
}
#main #static .introduction p {
  margin: 0.5rem 0;
  font-size: 1rem;
  line-height: 1.8;
}

/* ---------------------------
 * *DL リスト - 1
 * *---------------------------- */
dl.list_main {
  padding: 0 3rem 60px;
}
@media screen and (max-width: 767px) {
  dl.list_main {
    padding: 0 0 40px;
  }
}
dl.list_main > dt {
  margin: 3.5rem 0 2rem;
  font-weight: 400;
  font-size: 1.3rem;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  dl.list_main > dt {
    font-size: 1.4rem;
  }
}
dl.list_main > dd p {
  margin: 0.5rem 0;
  line-height: 1.8;
}

#main #rule dl.list_main > dt:first-of-type {
  margin-top: 30px;
}
#main #privacy dl.list_main > dt:first-of-type {
  margin-top: 70px;
}

dl.list_sub > dt {
  margin-top: 30px;
  font-weight: 700;
  text-indent: -2.15em;
  padding-left: 2.15em;
}
dl.list_sub > dt:first-of-type {
  margin-top: 5px;
}
dl.list_sub > dt span {
  display: inline-block;
  margin-right: 1em;
  font-weight: 700;
}
dl.list_sub > dd {
  padding-left: 2.15em;
  font-size: 17px;
}
dl.list_nmb-d1 {
  font-size: 17px;
  padding-left: 2.15em;
  margin-top: 1em;
}
dl.list_nmb-d1 > dt {
  font-weight: 700;
  text-indent: -1.5em;
  padding-left: 1.5em;
}
dl.list_nmb-d1 > dt:not(:first-of-type) {
  margin-top: 10px;
}
dl.list_nmb-d1 > dd {
  padding-left: 1.5em;
}

@media only screen and (max-width: 767px) {
  #main #rule dl.list_main > dt:first-of-type {
    margin-top: 20px;
  }
  #main #privacy dl.list_main > dt:first-of-type {
    margin-top: 40px;
  }
}
/* ---------------------------
 * *DL リスト - 2
 * *----------------------------- */
@media only screen and (max-width: 767px) {
  dl.list_sub > dd {
    font-size: 15px;
  }
}
/* ---------------------------
 * *DL リスト - 3
 * *----------------------------- */
@media only screen and (max-width: 767px) {
  dl.list_nmb-d1 {
    font-size: 15px;
    padding-left: 0;
  }
}
/* ---------------------------
 * *OL リスト - 1
 * *----------------------------- */
#main ol.list_nmb-o1 {
  list-style: disc;
  margin: 0.5rem 0;
  padding: 0 0 0 1.7rem;
}
@media screen and (max-width: 767px) {
  #main ol.list_nmb-o1 {
    padding: 0 0 0 1.3rem;
  }
}

ol.list_nmb-o1 > li {
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  ol.list_nmb-o1 > li {
    line-height: 1.8;
  }
}
ol.list_nmb-o1 > li + li {
  margin: 0.3rem 0 0;
}

#main ol.list_nmb-o2 {
  list-style: circle;
  margin: 0.5rem 0 0;
  padding: 0 0 0 1.7rem;
}
@media screen and (max-width: 767px) {
  #main ol.list_nmb-o2 {
    padding: 0 0 0 1.3rem;
  }
}

ol.list_nmb-o2 > li {
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  ol.list_nmb-o2 > li {
    line-height: 1.8;
  }
}

#main ol.list_nmb-o3 {
  list-style: none;
  margin: 0.5rem 0;
  padding: 0 0 0 1.7rem;
}
@media screen and (max-width: 767px) {
  #main ol.list_nmb-o3 {
    padding: 0 0 0 1.3rem;
  }
}

ol.list_nmb-o3 > li {
  line-height: 1.8;
  text-indent: -1rem;
}
@media screen and (max-width: 767px) {
  ol.list_nmb-o3 > li {
    line-height: 1.8;
  }
}
ol.list_nmb-o3 > li + li {
  margin: 0.3rem 0 0;
}

/* ---------------------------
 * *UL リスト - 1
 * *----------------------------- */
ul.list_nmb-u1 li {
  text-indent: -2.15em;
  padding-left: 2.15em;
}
ul.list_nmb-u1 li span {
  display: inline-block;
  margin-right: 1em;
}

@media only screen and (max-width: 767px) {
  ul.list_nmb-u1 li + li {
    margin-top: 7px;
  }
}
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *会社概要
 * *********************************************************************** */
#main #profile {
  padding: 60px 2rem;
  font-size: 1rem;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #main #profile {
    padding: 40px 1rem;
  }
}
#main #profile span.enCoName {
  word-break: normal;
}
#main #rule {
  font-size: 1rem;
  line-height: 1.8;
  padding-bottom: 110px;
}
#main #rule p.intro_rule {
  margin-top: 70px;
  padding: 0 45px;
}
#main #rule p.additional {
  padding: 0 3rem;
}
@media screen and (max-width: 767px) {
  #main #rule p.additional {
    padding: 0;
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *条件・ルール
 * *********************************************************************** */
#main #rule {
  padding: 60px 3rem 60px;
}
@media screen and (max-width: 767px) {
  #main #rule {
    padding: 40px 1rem 40px;
  }
}
#main #rule p.intro_rule {
  margin: 0;
  padding: 0;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *個人情報の保護
 * ***********************************************************************
 *
 * #privacy
 *  font-size: 18px
 *  line-height: 1.8
 *  padding-bottom: 110px
 *
 *  .rec
 *    margin-top: 0.5em
 *    padding-left: 2.15em
 *    font-size: 17px
 *
 *  p.sign
 *    text-align: right
 *    margin-top: 10px
 *    font-size: 14px
 *    color: #7D7D7D
 *    line-height: 1.4
 *
 *  ul
 *    &[class^="list_group-"]
 *
 *    &.list_group-domestic
 *      margin-bottom: 35px
 *
 *    &.list_group-foreign li
 *      word-break: normal */
@media only screen and (min-width: 768px) {
  #main #privacy ul.list_group-domestic {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
  #main #privacy ul.list_group-domestic li:not(:last-of-type)::after {
    content: '、';
    display: inline-block;
  }
}
@media only screen and (max-width: 767px) {
  #main #privacy {
    font-size: 16px;
    padding-bottom: 40px;
  }
  #main #privacy .rec {
    font-size: 15px;
    padding-left: 0;
  }
  #main #privacy ul[class^='list_group-'] {
    margin-top: 5px;
  }
  #main #privacy ul[class^='list_group-'] li {
    line-height: 1.6;
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 5px;
  }
  #main #privacy ul[class^='list_group-'] li + li {
    margin-top: 5px;
  }
  #main #privacy ul.list_group-domestic {
    margin-bottom: 25px;
  }
}
/*# sourceMappingURL=other.css.map */
