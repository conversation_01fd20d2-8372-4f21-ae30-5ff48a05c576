/* Slider */
.slick-loading .slick-list {
  background: #fff url('./ajax-loader.gif') center center no-repeat;
}

/* Icons */
@font-face {
  font-family: 'slick';
  font-weight: normal;
  font-style: normal;

  src: url('./fonts/slick.eot');
  src:
    url('./fonts/slick.eot?#iefix') format('embedded-opentype'),
    url('./fonts/slick.woff') format('woff'),
    url('./fonts/slick.ttf') format('truetype'),
    url('./fonts/slick.svg#slick') format('svg');
}

/* Arrows */

.slick-prev {
  left: -6px;
}
@media only screen and (max-width: 767px) {
  .slick-prev {
    left: -2vw;
  }
}
.slick-next {
  right: 10px;
}
@media only screen and (max-width: 767px) {
  .slick-next {
    right: 2vw;
  }
}

.slick-prev,
.slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: calc(50% - 25px);
  display: block;
  width: 50px;
  height: 50px;
  padding: 0;
  -webkit-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  transform: translate(0, -50%);
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background-color: transparent;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .slick-prev,
  .slick-next {
    font-size: 0;
    line-height: 0;
    position: absolute;
    top: calc(46vw - 25px);
    display: block;
    width: 40px;
    height: 16vw;
    padding: 0;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: none;
    outline: none;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    z-index: 1;
  }
}

.slick-prev:hover:before,
.slick-prev:focus:before,
.slick-next:hover:before,
.slick-next:focus:before {
  opacity: 1;
}
.slick-prev:before {
  content: '';
  width: 26px;
  height: 26px;
  border: 0;
  border-top: solid 2px #333;
  border-right: solid 2px #333;
  position: absolute;
  top: 11px;
  right: calc(50% - 22px);
  -webkit-transform: rotate(-135deg);
  transform: rotate(-135deg);
}
@media only screen and (max-width: 767px) {
  .slick-prev:before {
    top: calc(50% - 13px);
    border-top: solid 2px #fff;
    border-right: solid 2px #fff;
  }
}
.slick-prev:hover:before {
  border-top: solid 2px #fff;
  border-right: solid 2px #fff;
}
.slick-next:before {
  content: '';
  width: 26px;
  height: 26px;
  border: 0;
  border-top: solid 2px #333;
  border-right: solid 2px #333;
  position: absolute;
  top: 11px;
  left: calc(50% - 23px);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
@media only screen and (max-width: 767px) {
  .slick-next:before {
    top: calc(50% - 13px);
    border-top: solid 2px #fff;
    border-right: solid 2px #fff;
  }
}
.slick-next:hover:before {
  border-top: solid 2px #fff;
  border-right: solid 2px #fff;
}
.slick-prev:hover,
.slick-next:hover {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}
.slick-prev.slick-disabled:before,
.slick-next.slick-disabled:before {
  opacity: 0.25;
}

/* メインギャラリー */
.my-gallery .slick-prev,
.my-gallery .slick-next {
  width: 60px;
  height: 60px;
  position: absolute;
  top: calc(50% - 5px);
}
.my-gallery .slick-prev {
  left: -36px !important;
}
.my-gallery .slick-next {
  right: -36px !important;
}
.my-gallery .slick-prev:hover,
.my-gallery .slick-next:hover {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}
.my-gallery .slick-prev:before {
  top: 16px;
}
.my-gallery .slick-next:before {
  top: 16px;
}
@media only screen and (max-width: 767px) {
  .my-gallery .slick-prev {
    left: -10px !important;
  }
  .my-gallery .slick-next {
    right: -10px !important;
  }
}
/* サブサムネイル */
.slider-nav .slick-prev,
.slider-nav .slick-next {
  width: 30px;
  height: 30px;
  position: absolute;
  top: calc(50% + 1px);
}
.slider-nav .slick-prev {
  left: -18px !important;
}
.slider-nav .slick-next {
  right: -18px !important;
}

.slider-nav .slick-prev:before,
.slider-nav .slick-next:before {
  content: '';
  width: 10px;
  height: 10px;
  top: 9px;
  border: 0;
  border-top: solid 2px #333;
  border-right: solid 2px #333;
}
.slider-nav .slick-prev:before {
  right: 5px;
  -webkit-transform: rotate(-135deg);
  transform: rotate(-135deg);
}
.slider-nav .slick-next:before {
  left: 5px;
}

.slider-nav .slick-prev:hover,
.slider-nav .slick-next:hover {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}
.slider-nav .slick-prev:hover:before,
.slider-nav .slick-next:hover:before {
  border-top: solid 2px #fff;
  border-right: solid 2px #fff;
}

/* Dots */
.slick-dotted.slick-slider {
  margin-bottom: 30px;
}

#main .list-item .item-list ul.slick-dots {
  position: absolute;
  bottom: -25px;
  display: none !important; /*切り替えblock/none*/
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

#main .list-item .item-list ul.slick-dots li {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  background-color: transparent;
  cursor: pointer;
}
#main .list-item .item-list ul.slick-dots li button {
  font-size: 0;
  line-height: 0;

  display: block;

  width: 20px;
  height: 20px;
  padding: 5px;

  cursor: pointer;

  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}
#main .list-item .item-list ul.slick-dots li button:hover,
#main .list-item .item-list ul.slick-dots li button:focus {
  outline: none;
}
#main .list-item .item-list ul.slick-dots li button:hover:before,
#main .list-item .item-list ul.slick-dots li button:focus:before {
  opacity: 1;
}
#main .list-item .item-list ul.slick-dots li button:before {
  font-family: 'slick';
  font-size: 6px;
  line-height: 20px;

  position: absolute;
  top: 0;
  left: 0;

  width: 20px;
  height: 20px;

  content: '•';
  text-align: center;

  opacity: 0.25;
  color: black;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#main .list-item .item-list ul.slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}
