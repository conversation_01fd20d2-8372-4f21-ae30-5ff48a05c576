export interface TranslationKeys {
  $vuetify: {
    dataIterator: {
      rowsPerPageText: string
      pageText: string
    }
    [key: string]: any
  }

  // COMMON TRANSLATIONS
  COMMON_BACK: string
  COMMON_BACK_LIST: string
  COMMON_MORE: string
  COMMON_JAPAN: string
  COMMON_DUBAI: string
  COMMON_HONGKONG: string
  COMMON_SEND: string
  COMMON_AGREE: string
  COMMON_ERROR: string
  COMMON_CONFIRM: string
  COMMON_INPUT_ERROR: string
  COMMON_DATE_FORMAT: string
  COMMON_REMOVE: string
  COMMON_UPDATE_AUCTION: string
  COMMON_DAY: string
  COMMON_HOUR: string
  COMMON_MINUTE: string
  COMMON_SECOND: string

  // SITE INFORMATION
  SITE_TITLE: string
  COPYRIGHT: string

  // TOP PAGE APP BAR
  TOP_APP_BAR_SELECT_CATEGORY: string
  TOP_APP_BAR_REGISTER: string
  TOP_APP_BAR_LOGIN: string
  TOP_APP_BAR_LOGOUT: string

  // ROUTE/PAGE TITLES
  ROUTE_TOP: string
  ROUTE_LOGIN: string
  ROUTE_REMINDER: string
  ROUTE_REGISTER: string
  ROUTE_NOTICE_LIST: string
  ROUTE_IMPORTANT_NOTICE_LIST: string
  ROUTE_IMPORTANT_NOTICE: string
  ROUTE_BID_HISTORY_ALL: string
  ROUTE_DETAILS: string
  ROUTE_CONTACT: string
  ROUTE_CONTACT_CONFIRM: string
  ROUTE_NOTICE_DETAILS: string
  ROUTE_FAVORITES: string
  ROUTE_BID_ONGOING: string
  ROUTE_BID_HISTORY: string
  ROUTE_MY_PAGE: string
  ROUTE_MY_PAGE_EDIT_CONFIRM: string
  ROUTE_COMPANY_OVERVIEW: string
  ROUTE_TERMS: string
  ROUTE_PRIVACY: string
  ROUTE_FIRST_TIME: string
  ROUTE_TOSHUHO: string

  // PRODUCT DETAIL PAGE
  PRODUCT_DETAIL_TITLE: string
  PRODUCT_DETAIL_CURRENCY: string
  PRODUCT_DETAIL_QUANTITY: string
  PRODUCT_DETAIL_LOWEST_BID_QUANTITY: string
  PRODUCT_DETAIL_LOWEST_BID_PRICE: string
  PRODUCT_DETAIL_BID_COUNT: string
  PRODUCT_DETAIL_BID_QUANTITY: string
  PRODUCT_DETAIL_BID_UNIT_PRICE: string
  PRODUCT_DETAIL_BID_PRICE_FOR_ASC_AUCTION: string
  PRODUCT_DETAIL_BID_TOTAL_PRICE: string
  PRODUCT_DETAIL_BID_BUTTON: string
  PRODUCT_DETAIL_CONTACT_BUTTON: string
  PRODUCT_DETAIL_ABOUT_RANK: string

  // PRODUCT DETAIL INFO SECTION
  PRODUCT_DETAIL_INFO_MAKER: string
  PRODUCT_DETAIL_INFO_PRODUCT_NAME: string
  PRODUCT_DETAIL_INFO_SIM: string
  PRODUCT_DETAIL_INFO_CAPACITY: string
  PRODUCT_DETAIL_INFO_COLOR: string
  PRODUCT_DETAIL_INFO_RANK: string
  PRODUCT_DETAIL_INFO_QUANTITY: string
  PRODUCT_DETAIL_INFO_NOTE1: string
  PRODUCT_DETAIL_INFO_NOTE2: string
  PRODUCT_DETAIL_INFO_LOWEST_BID_PRICE: string
  PRODUCT_DETAIL_INFO_LOWEST_BID_QUANTITY: string
  PRODUCT_DETAIL_INFO_FAVORITE: string
  PRODUCT_DETAIL_INFO_START_PRICE: string
  PRODUCT_DETAIL_INFO_CURRENT_PRICE: string

  // Filter box
  FILTER_BOX_TITLE: string
  FILTER_BOX_INPUT_PLACEHOLDER: string
  FILTER_BOX_KEYWORD: string
  FILTER_BOX_SEARCH_BUTTON: string
  FILTER_BOX_CATEGORY: string
  FILTER_BOX_CLEAR_CONDITIONS: string
  FILTER_BOX_AUCTION_COUNT: string
  FILTER_BOX_SEARCH_CRITERIA: string

  // Favorite
  FAVORITE_TITLE: string
  FAVORITE_EMPTY: string
  FAVORITE_CLEAR_PRICE_INPUT: string
  FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: string
  FAVORITE_SUB_TOTAL_BID_PRICE: string
  FAVORITE_LOGIN_REQUIRED_FAVORITE: string
  FAVORITE_BID_BUTTON: string
  FAVORITE_RE_BID_BUTTON: string
  FAVORITE_BID_QUANTITY: string
  FAVORITE_DELETE_FAVORITE1: string
  FAVORITE_DELETE_FAVORITE2: string

  // Bid history
  BID_HISTORY_END_DATE: string
  BID_HISTORY_BID_SUCCESS_UNIT_PRICE: string
  BID_HISTORY_BID_SUCCESS_PRICE: string
  BID_HISTORY_BID_SUCCESS_QUANTITY: string
  BID_HISTORY_BID_TOTAL_PRICE: string

  // Auth
  AUTH_LOGOUT_MESSAGE: string
  AUTH_LOGOUT: string
  AUTH_CLOSE: string
  AUTH_CANCEL: string

  // Login
  LOGIN_TITLE: string
  LOGIN_EMAIL: string
  LOGIN_PASSWORD: string
  LOGIN_SAVE_LOGIN_INFO: string
  LOGIN_FORGET_PASSWORD: string
  LOGIN_RULE: string
  LOGIN_AGREE_RULE: string
  LOGIN_AGREE: string
  LOGIN_ENTRY_INFO1: string
  LOGIN_ENTRY_INFO2: string
  LOGIN_CONFIRM_BUTTON: string
  LOGIN_PASSWORD_HINT: string

  // AUCTION & BIDDING SYSTEM
  COMMON_BID_LABEL: string
  BID_COUNT: string
  CLASSIFICATION_ASCENDING: string
  CLASSIFICATION_SEALED: string
  ASCENDING: string
  SEALED: string
  BID_STATUS_INPROGRESS: string
  BID_STATUS_CANCEL: string
  BID_STATUS_NOT_START_YET: string
  BID_STATUS_ENDED: string
  BID_STATUS_EXTENDING: string
  HIGHEST_BIDDER: string
  BID_STATUS: string
  REMAINING_TIME: string
  YOU_ARE_TOP: string
  RESERVE_PRICE_NOT_MET: string
  RESERVE_PRICE_EXCEEDED: string
  MORE_LITTLE: string
  SECOND_BIDDER: string
  END_DATE_TIME: string
  START_DATE_TIME: string
  RECORDED_BID_PRICE: string
}

export interface Translate {
  ja: TranslationKeys
  en: TranslationKeys
}

// Extract all translation keys as a union type for type-safe t() function
export type TranslationKey = keyof Omit<TranslationKeys, '$vuetify'>

// Type for the t() function
export type TranslateFunction = (key: TranslationKey) => string

// Utility type to ensure both languages have the same keys
export type EnsureKeysMatch<T extends Record<string, TranslationKeys>> = {
  [K in keyof T]: TranslationKeys
} & T

// Type guard to ensure translate object has matching keys
export function validateTranslate<T extends Record<string, TranslationKeys>>(
  translate: EnsureKeysMatch<T>
): EnsureKeysMatch<T> {
  return translate
}
