# Product List Components

Vue 3 Composition API components for displaying product lists with view mode switching functionality.

## Components

### ProductListDisplay.vue

A reusable component for displaying product lists with automatic view mode switching between panel and row-bid modes.

#### Features

- **View Mode Toggle**: Automatically responds to view mode changes (panel/row)
- **Context-Aware Configuration**: Different behaviors for different usage contexts
- **TypeScript Support**: Full type safety with comprehensive type definitions
- **Event Handling**: Configurable event handlers for user interactions
- **Responsive Design**: Adapts to different display modes seamlessly

#### Props

```typescript
interface Props {
  /** Array of product items to display */
  items: FormattedAuctionItem[]
  /** Context/usage type for the product list */
  context: ProductListContext
  /** Event handlers for user interactions */
  handlers?: ProductListHandlers
  /** Loading state */
  loading?: boolean
  /** Total count for pagination display */
  totalCount?: number
  /** Current page items count */
  currentCount?: number
  /** Custom CSS classes */
  customClasses?: string
}
```

#### Usage Examples

##### Basic Usage

```vue
<template>
  <ProductListDisplay
    :items="productItems"
    context="main-list"
    :handlers="eventHandlers"
  />
</template>

<script setup lang="ts">
  import ProductListDisplay from '@/components/common/product-list/ProductListDisplay.vue'
  import type {ProductListHandlers} from '@/components/common/product-list/types'

  const eventHandlers: ProductListHandlers = {
    onFavoriteToggle: async (exhibitionItemNo, currentFavorited) => {
      // Handle favorite toggle
    },
    onBid: async (item, bidPrice, bidQuantity) => {
      // Handle bid placement
    },
    onRefresh: async () => {
      // Handle refresh
    },
    onItemClick: item => {
      // Handle item navigation
    },
  }
</script>
```

##### MyPage Bidding Context

```vue
<template>
  <ProductListDisplay
    :items="biddingItems"
    context="mypage-bidding"
    :handlers="biddingHandlers"
    :loading="isLoading"
    :total-count="totalItems"
    :current-count="currentItems"
  />
</template>
```

##### Search Results Context

```vue
<template>
  <ProductListDisplay
    :items="searchResults"
    context="search-results"
    :handlers="searchHandlers"
    custom-classes="search-specific-styles"
  />
</template>
```

## Composables

### useViewMode()

Manages view mode state with localStorage persistence.

```typescript
const {viewMode, toggleView, isPanelMode, isRowMode} = useViewMode()

// Toggle between modes
toggleView('panel')
toggleView('row')

// Check current mode
if (isPanelMode.value) {
  // Panel mode specific logic
}
```

### useProductListConfig()

Provides reactive configuration based on context and view mode.

```typescript
const config = useProductListConfig(
  'main-list', // context
  viewMode, // current view mode
  customConfig, // optional overrides
  handlers // event handlers
)
```

## Types

### ProductListContext

Available contexts for different usage scenarios:

- `'main-list'` - Main product listing page
- `'mypage-bidding'` - MyPage bidding section
- `'mypage-favorite'` - MyPage favorite items
- `'mypage-history'` - MyPage bid history
- `'search-results'` - Search results page
- `'top-page'` - Top page featured items

### ViewMode

View mode types:

- `'panel'` - Grid-like panel view with larger product cards
- `'row'` - Row-based view with detailed bidding interface

### ProductListHandlers

Event handler interface:

```typescript
type ProductListHandlers = {
  onFavoriteToggle?: (
    exhibitionItemNo: string,
    currentFavorited: boolean
  ) => Promise<void>
  onBid?: (
    item: FormattedAuctionItem,
    bidPrice: string,
    bidQuantity: string
  ) => Promise<void>
  onRefresh?: () => Promise<void>
  onItemClick?: (item: FormattedAuctionItem) => void
}
```

## Integration with View Mode Toggle

The components automatically integrate with the display-switch buttons:

```vue
<template>
  <div class="display-switch">
    <button
      :class="['btn', 'panel', {'is-active': viewMode === 'panel'}]"
      @click="toggleView('panel')"
    ></button>
    <button
      :class="['btn', 'row', {'is-active': viewMode === 'row'}]"
      @click="toggleView('row')"
    ></button>
  </div>

  <ProductListDisplay :items="items" context="main-list" :handlers="handlers" />
</template>

<script setup lang="ts">
  import {useViewMode} from '@/components/product/list/useViewMode'

  const {viewMode, toggleView} = useViewMode()
</script>
```

## CSS Classes

The component automatically applies appropriate CSS classes based on view mode:

- **Panel Mode**: `.item-list.panel`
- **Row Mode**: `.item-list.row-bid`
- **Context Classes**: `.context-{context-name}`

## Migration Guide

To migrate existing product list implementations:

1. Replace hardcoded HTML with `<ProductListDisplay>`
2. Define event handlers using `ProductListHandlers` type
3. Use `useViewMode()` for view mode management
4. Configure context-specific behavior via `context` prop

## Performance Considerations

- Components use reactive computed properties for efficient updates
- View mode state is shared globally to prevent unnecessary re-renders
- Event handlers are wrapped with error handling
- Loading states are properly managed
