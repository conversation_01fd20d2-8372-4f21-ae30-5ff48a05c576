<script setup>
  import {computed} from 'vue'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'
  import {PATH_NAME} from '../../defined/const'

  const props = defineProps({
    customTitle: {
      type: String,
      default: null,
    },
  })

  const route = useRoute()
  const {t: translate} = useLocale()

  const breadcrumbTitle = computed(() => {
    return props.customTitle || translate(route.meta.name)
  })
</script>
<template>
  <div id="pNav">
    <ul>
      <li><router-link :to="PATH_NAME.TOP">TOP</router-link></li>
      <li>{{ breadcrumbTitle }}</li>
    </ul>
  </div>
</template>
