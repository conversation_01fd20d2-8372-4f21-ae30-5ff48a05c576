import type {ViewMode} from '@/components/common/product-list/types'
import {computed, ref, watch} from 'vue'

/**
 * Storage key for persisting view mode preference
 */
const STORAGE_KEY = 'product-list-view-mode'

/**
 * Get initial view mode from localStorage or default to 'panel'
 */
const getInitialViewMode = (): ViewMode => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored === 'panel' || stored === 'row') {
      return stored
    }
  }
  return 'panel'
}

/**
 * Global reactive view mode state
 * Shared across all components that use this composable
 */
const viewMode = ref<ViewMode>(getInitialViewMode())

/**
 * Composable for managing product list view mode
 * Provides reactive view mode state with localStorage persistence
 *
 * @returns Object containing reactive view mode state and toggle function
 */
export function useViewMode() {
  /**
   * Toggle view mode between 'panel' and 'row'
   * Automatically persists the selection to localStorage
   *
   * @param mode - The view mode to set ('panel' | 'row')
   */
  const toggleView = (mode: ViewMode) => {
    viewMode.value = mode
  }

  /**
   * Watch for view mode changes and persist to localStorage
   */
  watch(
    viewMode,
    newMode => {
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEY, newMode)
      }
    },
    {immediate: false}
  )

  /**
   * Check if current view mode is panel
   */
  const isPanelMode = computed(() => viewMode.value === 'panel')

  /**
   * Check if current view mode is row
   */
  const isRowMode = computed(() => viewMode.value === 'row')

  return {
    /** Reactive view mode state */
    viewMode,
    /** Function to toggle view mode */
    toggleView,
    /** Computed property for panel mode check */
    isPanelMode,
    /** Computed property for row mode check */
    isRowMode,
  }
}
