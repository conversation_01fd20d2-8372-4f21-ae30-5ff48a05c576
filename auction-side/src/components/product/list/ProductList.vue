<script setup lang="ts">
  import BreadCrumb from '@/components/common/BreadCrumb.vue'
import RowBidProductList from '@/components/common/product-list/RowBidProductList.vue'
import type {ProductListHandlers} from '@/components/common/product-list/types'
import ViewOnlyProductList from '@/components/common/product-list/ViewOnlyProductList.vue'
import useSearchProducts from '@/composables/searchProducts'
import useResetParams from '@/composables/state/useResetParams'
import useResetProductList from '@/composables/state/useResetProductList'
import type {FormattedAuctionItem} from '@/composables/state/useSearchResultState'
import useSearchResultState from '@/composables/state/useSearchResultState'
import useSetProductList from '@/composables/state/useSetProductList'
import useApi from '@/composables/useApi'
import {computed, onBeforeMount, ref} from 'vue'
import FilterBox from './FilterBox.vue'
import {useViewMode} from './useViewMode'

  const {viewMode, toggleView} = useViewMode()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const setProductList = useSetProductList()
  const {productList, totalCount} = useSearchResultState()
  const {getConstants} = useSearchProducts()

  const loading = ref(false)

  const items = computed(() => {
    return productList.all || []
  })
  // TODO
  const viewOnlyItems = computed(() => {
    const filtered = items.value.filter(item => item.free_field.product_name)
    return filtered
  })
  const biddableItems = computed(() => {
    return items.value
  })
  const currentPage = ref(1)
  const itemsPerPage = 20 // This should match the API limit parameter

  const totalPages = computed(() => {
    return Math.ceil(totalCount.value / itemsPerPage)
  })

  const pageNumbers = computed(() => {
    const total = totalPages.value
    const current = currentPage.value
    const pages: number[] = []

    if (total <= 7) {
      // Show all pages if total is 7 or less
      for (let i = 1; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // Show pages around current page
      if (current <= 4) {
        // Show first 5 pages + ... + last page
        for (let i = 1; i <= 5; i++) {
          pages.push(i)
        }
        if (total > 6) {
          pages.push(-1) // Ellipsis indicator
          pages.push(total)
        }
      } else if (current >= total - 3) {
        // Show first page + ... + last 5 pages
        pages.push(1)
        if (total > 6) {
          pages.push(-1) // Ellipsis indicator
        }
        for (let i = total - 4; i <= total; i++) {
          pages.push(i)
        }
      } else {
        // Show first + ... + current-1, current, current+1 + ... + last
        pages.push(1)
        pages.push(-1) // Ellipsis indicator
        for (let i = current - 1; i <= current + 1; i++) {
          pages.push(i)
        }
        pages.push(-1) // Ellipsis indicator
        pages.push(total)
      }
    }

    return pages
  })

  const canGoPrevious = computed(() => currentPage.value > 1)
  const canGoNext = computed(() => currentPage.value < totalPages.value)

  const currentItemsStart = computed(() => {
    return (currentPage.value - 1) * itemsPerPage + 1
  })

  const currentItemsEnd = computed(() => {
    return Math.min(currentPage.value * itemsPerPage, totalCount.value)
  })

  // Pagination navigation functions
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value && page !== currentPage.value) {
      currentPage.value = page
      console.log(`Navigating to page ${page}`)
    }
  }

  const goToPreviousPage = () => {
    if (canGoPrevious.value) {
      goToPage(currentPage.value - 1)
    }
  }

  const goToNextPage = () => {
    if (canGoNext.value) {
      goToPage(currentPage.value + 1)
    }
  }

  const handlers: ProductListHandlers = {
    onFavoriteToggle: async (
      exhibitionItemNo: string,
      currentFavorited: boolean
    ) => {
      console.log('Toggle favorite:', exhibitionItemNo, currentFavorited)
      const item = items.value.find(
        item => item.exhibition_item_no === exhibitionItemNo
      )
      if (item) {
        item.attention_info.is_favorited = !currentFavorited
        if (!currentFavorited) {
          item.attention_info.favorited_count =
            (item.attention_info.favorited_count || 0) + 1
        } else {
          item.attention_info.favorited_count = Math.max(
            0,
            (item.attention_info.favorited_count || 0) - 1
          )
        }
      }
    },
    onBid: async (
      item: FormattedAuctionItem,
      bidPrice: string,
      bidQuantity: string
    ) => {
      console.log('Place bid:', item.exhibition_item_no, bidPrice, bidQuantity)
      const targetItem = items.value.find(
        i => i.exhibition_item_no === item.exhibition_item_no
      )
      if (targetItem) {
        targetItem.attention_info.bid_count += 1
        const numericPrice =
          parseInt(bidPrice.replace(/,/g, '')) ||
          targetItem.bid_status.current_price
        targetItem.bid_status.current_price = numericPrice
        targetItem.currentPrice = numericPrice.toLocaleString()
      }
    },
    onRefresh: async () => {
      console.log('Refresh product list')
    },
    onItemClick: (item: FormattedAuctionItem) => {
      console.log('Item clicked:', item.exhibition_item_no)
    },
  }

  const fetchProducts = async () => {
    loading.value = true
    try {
      const params = {
        unSoldOut: false,
        favorite: false,
        bidding: false,
        languageCode: 'ja',
        initLimit: 20,
        limit: 20,
      }

      const response = await apiExecute('public/search-auction-items', params)
      setProductList(response)
    } catch (error) {
      console.error('Error fetching products:', error)
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  onBeforeMount(async () => {
    try {
      useResetParams()
      useResetProductList()
      await Promise.all([getConstants(), fetchProducts()])
    } catch (error) {
      const err = parseHtmlResponseError(error)
      console.log({err})
    }
  })
</script>

<template>
  <main id="main">
    <BreadCrumb customTitle="商品一覧" />

    <h2 class="page-ttl list">
      <p class="sub">競り上がり式オークション</p>
      <div class="ttl">商品一覧</div>
    </h2>

    <FilterBox />
    <section id="list" class="list-item">
      <div class="container">
        <!-- display-option -->
        <div class="display-option">
          <div class="refine">
            <div class="sorting">
              <button class="menu_trigger">
                <span class="option_selected">おすすめ順</span>
              </button>
              <ul class="sorting_panel">
                <li class="option_item"><a>おすすめ順</a></li>
                <li class="option_item"><a>新着順</a></li>
                <li class="option_item"><a>残り時間の少ない順</a></li>
                <li class="option_item"><a>残り時間の長い順</a></li>
                <li class="option_item"><a>現在価格の安い順</a></li>
                <li class="option_item"><a>現在価格の高い順</a></li>
                <li class="option_item"><a>入札件数の多い順</a></li>
                <li class="option_item"><a>入札件数の少ない順</a></li>
              </ul>
            </div>
            <div class="check-onsale">
              <div class="label-item">
                <input
                  id="checkbox"
                  class="checkbox-model"
                  type="checkbox"
                /><label for="checkbox">販売中のみ</label>
              </div>
            </div>
          </div>
          <div class="switch">
            <p class="dl">
              <a href="./"><span>商品譲歩ダウンロード（CSV）</span></a>
            </p>
            <div class="number-switch">
              <p class="label">表示件数</p>
              <div class="num">
                <button class="btn is-active">20件</button>
                <button class="btn">50件</button>
                <button class="btn">100件</button>
              </div>
            </div>
            <div class="display-switch">
              <p>
                <button
                  :class="['btn', 'panel', {'is-active': viewMode === 'panel'}]"
                  @click="toggleView('panel')"
                ></button>
              </p>
              <p>
                <button
                  :class="['btn', 'row', {'is-active': viewMode === 'row'}]"
                  @click="toggleView('row')"
                ></button>
              </p>
            </div>
          </div>
        </div>

        <ViewOnlyProductList
          :items="viewOnlyItems"
          :view-mode="viewMode"
          :handlers="handlers"
          custom-classes=""
        />

        <RowBidProductList
          :items="biddableItems"
          :handlers="handlers"
          custom-classes=""
        />

        <div v-if="totalPages > 1" class="wrap-btn pagination">
          <p>
            {{ totalCount }}件中 {{ currentItemsStart }}〜{{
              currentItemsEnd
            }}件を表示
          </p>
          <nav class="pagination">
            <ul>
              <!-- Previous button -->
              <li class="prev">
                <a
                  href="#"
                  :class="{disabled: !canGoPrevious}"
                  @click.prevent="goToPreviousPage"
                ></a>
              </li>

              <!-- Page numbers -->
              <li v-for="page in pageNumbers" :key="page">
                <template v-if="page === -1">
                  <!-- Ellipsis -->
                  <span class="ellipsis">...</span>
                </template>
                <template v-else>
                  <a
                    href="#"
                    :class="{active: page === currentPage}"
                    @click.prevent="goToPage(page)"
                  >
                    {{ page }}
                  </a>
                </template>
              </li>

              <!-- Next button -->
              <li class="next">
                <a
                  href="#"
                  :class="{disabled: !canGoNext}"
                  @click.prevent="goToNextPage"
                ></a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped>
  /* Dynamic pagination styling */
  .pagination .ellipsis {
    padding: 8px 12px;
    color: #666;
    cursor: default;
  }

  .pagination a.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .pagination a.active {
    background-color: #007bff;
    color: white;
    border-radius: 4px;
  }

  .pagination a:hover:not(.disabled):not(.active) {
    background-color: #f8f9fa;
    border-radius: 4px;
  }
</style>
