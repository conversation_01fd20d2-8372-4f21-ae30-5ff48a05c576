import {convertFullWidthToHalfWidth} from '../../composables/common'

/**
 * Convert full-width characters to half-width for specific fields
 */
export const fullWidthToHalfWidthConversion = (
  params: Record<string, any>
): Record<string, any> => {
  const registerData = {...params}
  const fieldsToConvert = [
    'antiquePermitNo',
    'password',
    'passwordConfirm',
    'tel',
  ]

  Object.keys(registerData).forEach(key => {
    if (fieldsToConvert.includes(key) && registerData[key]) {
      registerData[key] = convertFullWidthToHalfWidth(registerData[key])
    }
  })

  return registerData
}

/**
 * Email language options
 */
export const emailLangList = [
  {title: 'EN', value: 'en'},
  {title: 'JA', value: 'ja'},
]
