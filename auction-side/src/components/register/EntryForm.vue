<script setup lang="ts">
  import {computed, onMounted, reactive, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'
  import {PATH_NAME} from '../../defined/const'
  import {useLanguageStore} from '../../stores/language'
  import {useRegisterStore} from '../../stores/useRegister'
  import DatePicker from '../common/DatePicker.vue'
  import FirstLastName from '../common/FirstLastName.vue'
  import InputFormCommon from '../common/InputFormCommon.vue'
  import PhoneNumberInput from '../common/PhoneNumberInput.vue'
  import type {EmailLangOption, ErrorMsg} from './composables/useRegister'

  type ConstantItem = {
    key_string: string
    value1: string
    value2: string
  }

  type Props = {
    constants: ConstantItem[]
    errorMsg: ErrorMsg
    emailLangOptions: EmailLangOption[]
  }

  type Emits = {
    (e: 'confirmInputs', params: any, loading: boolean): void
    (
      e: 'update:errorMsg',
      params: {field: keyof ErrorMsg; message: string}
    ): void
  }

  const props = withDefaults(defineProps<Props>(), {
    constants: () => [],
    errorMsg: () => ({}) as ErrorMsg,
    emailLangOptions: () => [],
  })

  const emit = defineEmits<Emits>()

  const {t, current: locale} = useLocale()
  const {registInputs} = useRegisterStore()

  const agreed = ref(false)
  const loading = reactive({
    companyPostCode: false,
    postCode: false,
    requestSend: false,
  })

  const languageStore = useLanguageStore()
  const selectedEmailLangRef = ref('ja')

  const registMember = computed(() => {
    return registInputs.reduce((result: any, obj: any) => {
      result[obj.item] = obj.value
      return result
    }, {})
  })
  const selectedCountry = computed(
    () => registInputs.find(x => x.item === 'country')?.value
  )

  // Priority countries to be displayed first
  // Japan, UAE, Hongkong China
  const priorityCountries = ['JP', 'AE', 'CN']
  const countryList = computed(() => {
    if (!props.constants) return []
    return props.constants
      .filter(x => x.key_string === 'COUNTRY_CODE')
      .sort((a, b) => {
        const valueA = a.value2 ?? ''
        const valueB = b.value2 ?? ''

        // Find the index of each value in the priorityCountries array
        const indexA = priorityCountries.indexOf(a.value1)
        const indexB = priorityCountries.indexOf(b.value1)

        // If both values are in the priorityCountries array, sort by their index
        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB
        }
        // If only A is in the priorityCountries array, A stands before B
        if (indexA !== -1) {
          return -1
        }
        // If only B is in the priorityCountries array, B stands before A
        if (indexB !== -1) {
          return 1
        }
        // If neither value is in the priorityCountries array, sort alphabetically
        return valueA.localeCompare(valueB)
      })
  })

  const passwordCompareOk = computed(() => {
    const password = registInputs.find(x => x.item === 'password')?.value
    const passwordConfirm = registInputs.find(
      x => x.item === 'passwordConfirm'
    )?.value
    return password === passwordConfirm
  })
  const emailCompareOk = computed(() => {
    const email = registInputs.find(x => x.item === 'email')?.value
    const emailConfirm = registInputs.find(
      x => x.item === 'emailConfirm'
    )?.value
    return email === emailConfirm
  })
  const isButtonDisabled = computed(() => {
    return !agreed.value || !passwordCompareOk.value || !emailCompareOk.value
  })

  onMounted(() => {
    // Set default country as Japan
    const country = registInputs.find(x => x.item === 'country')
    if (!country.value) {
      if (locale.value === 'ja') {
        country.value = 'JP'
      } else {
        country.value = ''
      }
    }
  })
  const handleInputError = ({field, message}) => {
    if (message) {
      emit('update:errorMsg', {field, message})
    } else {
      emit('update:errorMsg', {field, message: null})
    }
  }

  onMounted(() => {
    // set default email language
    const initialLang = languageStore.language === 'ja' ? 'ja' : 'en'
    selectedEmailLangRef.value = initialLang

    const emailLangInput = registInputs.find(
      input => input.item === 'emailLang'
    )
    if (emailLangInput) {
      emailLangInput.value = initialLang
    }
  })

  // watch change email language
  watch(selectedEmailLangRef, newSelect => {
    const emailLangBind = registInputs.find(input => input.item === 'emailLang')
    if (emailLangBind) {
      emailLangBind.value = newSelect
    }
  })

  // watch change system language
  watch(
    () => languageStore.language,
    newLang => {
      const mappedLang = newLang === 'ja' ? 'ja' : 'en'
      selectedEmailLangRef.value = mappedLang
    }
  )
</script>
<template>
  <form v-on:submit.prevent>
    <section id="entry-form">
      <p class="entry-form-info" v-html="t('register.subtitle')"></p>
      <table class="tbl-entry">
        <tbody>
          <tr v-for="input in registInputs" :key="input.item">
            <template v-if="input.isVisible(selectedCountry)">
              <th class="d-flex align-items-center justify-space-between ga-1">
                <span
                  class="font-weight-bold work-break"
                  v-html="input.label(t)"
                ></span>
                <em v-if="input.required(selectedCountry)" class="req">{{
                  t('register.form.required')
                }}</em>
              </th>
              <td
                :class="{
                  'pb-1': errorMsg[input.item],
                  agree: input.item === 'personalInfo',
                }"
              >
                <!-- Select option -->
                <template v-if="input.type === 'select'">
                  <div class="select-style">
                    <select
                      :class="input.class"
                      :required="input.required(selectedCountry)"
                      v-model="input.value"
                    >
                      <option value="">
                        {{ t('register.form.countryNoSelect') }}
                      </option>
                      <option
                        v-for="option in countryList"
                        :key="option.value1"
                        :value="option.value1"
                      >
                        {{ option.value2 }}
                      </option>
                    </select>
                  </div>
                </template>
                <!-- Select email language -->
                <template v-else-if="input.type === 'emailLang'">
                  <div class="select-style">
                    <select :class="input.class" v-model="selectedEmailLangRef">
                      <option
                        v-for="option in emailLangOptions"
                        :key="option.title"
                        :value="option.value"
                      >
                        {{ option.title }}
                      </option>
                    </select>
                  </div>
                </template>
                <!-- Text input -->
                <template v-else-if="input.type === 'text'">
                  <!-- Password input -->
                  <template
                    v-if="
                      input.item === 'password' ||
                      input.item === 'passwordConfirm'
                    "
                  >
                    <input
                      :type="
                        input.item.toLowerCase()?.includes('password')
                          ? 'password'
                          : 'text'
                      "
                      :class="input.class"
                      :required="input.required(selectedCountry)"
                      v-model="input.value"
                      :maxlength="input.length"
                      autocomplete="new-password"
                    />
                    <p
                      v-if="input.item === 'password'"
                      class="ipt-rule text-break"
                    >
                      {{ t('register.form.passwordHint') }}
                    </p>
                  </template>
                  <!-- member name -->
                  <template
                    v-else-if="
                      input.item === 'memberName' ||
                      input.item === 'memberLastName'
                    "
                  >
                    <FirstLastName
                      :country="selectedCountry"
                      :value="{
                        firstName: registInputs.find(
                          x => x.item === 'memberName'
                        )?.value,
                        lastName: registInputs.find(
                          x => x.item === 'memberLastName'
                        )?.value,
                      }"
                      :maxlength="{
                        firstName: registInputs.find(
                          x => x.item === 'memberFirstName'
                        )?.length,
                        lastName: registInputs.find(
                          x => x.item === 'memberLastName'
                        )?.length,
                      }"
                      @update:value="
                        e => {
                          registInputs.find(
                            x => x.item === 'memberName'
                          ).value = e.firstName
                          registInputs.find(
                            x => x.item === 'memberLastName'
                          ).value = e.lastName
                        }
                      "
                    />
                  </template>
                  <!-- Tel -->
                  <template
                    v-else-if="
                      input.item === 'tel' || input.item === 'telCountryCode'
                    "
                  >
                    <PhoneNumberInput
                      :country="selectedCountry"
                      :value="{
                        countryCode: registInputs.find(
                          x => x.item === 'telCountryCode'
                        )?.value,
                        tel: registInputs.find(x => x.item === 'tel')?.value,
                      }"
                      :maxlength="{
                        telCountryCode: registInputs.find(
                          x => x.item === 'telCountryCode'
                        )?.length,
                        tel: registInputs.find(x => x.item === 'tel')?.length,
                      }"
                      @update:value="
                        e => {
                          registInputs.find(
                            x => x.item === 'telCountryCode'
                          ).value = e.countryCode
                          registInputs.find(x => x.item === 'tel').value = e.tel
                        }
                      "
                    />
                  </template>
                  <!-- Input text and wrap == true -->
                  <div v-else-if="input.wrap" class="ipt-wrap">
                    <InputFormCommon
                      :input="input"
                      autocomplete="off"
                      @error="handleInputError"
                    />
                  </div>
                </template>
                <!-- Date picker -->
                <template v-else-if="input.type === 'date'">
                  <DatePicker
                    :value="input.value"
                    :class="input.class"
                    @update:value="
                      e => {
                        input.value = e
                      }
                    "
                  />
                </template>
                <!-- チェックボックス -->
                <template
                  v-else-if="
                    input.type === 'checkbox' && input.item === 'personalInfo'
                  "
                >
                  <label for="rule-chk">
                    <input
                      type="checkbox"
                      id="rule-chk"
                      class="checkbox-input"
                      :maxlength="input.length"
                      :required="input.required(selectedCountry)"
                      v-model="agreed"
                    />
                    <span class="checkbox-parts">{{
                      t('register.form.agree')
                    }}</span>
                  </label>
                </template>

                <template v-else>
                  <p>{{ input.value === '' ? '未設定' : input.value }}</p>
                </template>

                <!-- エラーメッセージ -->
                <p
                  class="ime-dis iptW-S pb-1 text-red"
                  v-if="
                    input.item === 'memberName' &&
                    (errorMsg['memberName'] || errorMsg['memberLastName'])
                  "
                >
                  {{ errorMsg['memberLastName'] || errorMsg['memberName'] }}
                </p>
                <p
                  class="ime-dis iptW-S pb-1 text-red"
                  v-else-if="
                    input.item === 'tel' &&
                    (errorMsg['telCountryCode'] || errorMsg['tel'])
                  "
                >
                  {{ errorMsg['telCountryCode'] || errorMsg['tel'] }}
                </p>
                <p
                  v-else-if="errorMsg[input.item]"
                  class="ime-dis iptW-S pb-1 text-red"
                >
                  {{ errorMsg[input.item] }}
                </p>
                <p
                  class="ime-dis iptW-S pb-1 text-red"
                  v-if="input.item === 'emailConfirm' && !emailCompareOk"
                >
                  {{ t('register.form.emailCompareError') }}
                </p>
                <p
                  class="ime-dis iptW-S pb-1 text-red"
                  v-if="input.item === 'passwordConfirm' && !passwordCompareOk"
                >
                  {{ t('register.form.passwordCompareError') }}
                </p>
                <p
                  class="ime-dis iptW-S pb-1 text-red"
                  v-if="errorMsg['emailDuplicated'] && input.item === 'email'"
                >
                  {{ errorMsg['emailDuplicated'] }}
                </p>
                <p
                  class="ime-dis iptW-S pb-1 text-red"
                  v-if="errorMsg['nickname_dup'] && input.item === 'nickname'"
                >
                  {{ errorMsg['nickname_dup'] }}
                </p>
              </td>
            </template>
          </tr>
          <tr class="att-use">
            <th>&nbsp;</th>
            <td>
              <p class="privacy-link">
                {{ t('register.form.privacyMessage1')
                }}<RouterLink :to="PATH_NAME.PRIVACY" target="_blank">{{
                  t('register.form.privacyMessage2')
                }}</RouterLink
                >{{ t('register.form.privacyMessage3') }}
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </section>
    <div class="btn-form">
      <input
        type="button"
        :value="t('register.form.confirmButton')"
        :disabled="isButtonDisabled"
        @click="emit('confirmInputs', registMember, loading.requestSend)"
      />
    </div>
  </form>
</template>
<style scoped>
  #main #entry-form table.tbl-entry input.iptW-M,
  #main #entry-form table.tbl-entry input.iptW-MM {
    background-color: #fff;
  }
  .work-break {
    word-break: keep-all;
    max-width: 180px;
  }
  @media screen and (max-width: 767px) {
    .work-break {
      max-width: 300px;
    }
  }
</style>
