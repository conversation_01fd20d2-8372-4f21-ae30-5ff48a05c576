import {formatDateString} from '@/composables/common'

export const getRemainingTime = (endDatetime: string) => {
  const now = new Date()
  const end = new Date(endDatetime)
  const diff = end.getTime() - now.getTime()

  if (diff <= 0) return '終了'

  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (hours > 0) {
    return `残り ${hours}時間`
  } else {
    return `残り ${minutes}分`
  }
}

export const formatEndDateTime = (endDatetime: string) => {
  const {datePart, timePart} = formatDateString(endDatetime)
  return `（${datePart} ${timePart}終了）`
}
