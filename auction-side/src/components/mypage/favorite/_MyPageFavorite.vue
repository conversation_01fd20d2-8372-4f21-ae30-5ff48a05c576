<script setup lang="ts">
  import ClassificationTabs from '@/components/common/ClassificationTabs.vue'
  import useClassificationSwitch from '@/composables/mypage/useClassificationSwitch'
  import FavoriteAscending from './FavoriteAscending.vue'
  import FavoriteSealed from './FavoriteSealed.vue'

  const {currentAuctionType, isAscending} = useClassificationSwitch()
</script>

<template>
  <section id="list">
    <div class="container">
      <h3>お気に入り</h3>

      <ClassificationTabs v-model="currentAuctionType" />

      <FavoriteAscending v-if="isAscending" />
      <FavoriteSealed v-else />
    </div>
  </section>
</template>

<style lang="css" scoped></style>
