<script setup lang="ts">
  import ClassificationTabs from '@/components/common/ClassificationTabs.vue'
  import useClassificationSwitch from '@/composables/mypage/useClassificationSwitch'
  import BiddingAscending from './BiddingAscending.vue'
  import BiddingSealed from './BiddingSealed.vue'

  const {currentAuctionType, isAscending} = useClassificationSwitch()
</script>
<template>
  <section id="list">
    <div class="container">
      <h3>入札中</h3>

      <ClassificationTabs v-model="currentAuctionType" />

      <BiddingAscending v-if="isAscending" />
      <BiddingSealed v-else />
    </div>
  </section>
</template>
