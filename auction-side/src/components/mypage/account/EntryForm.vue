<script setup lang="ts">
  import {useChangeMemberStore} from '@/stores/change-member'
  import {computed, reactive, ref} from 'vue'
  import {useLocale} from 'vuetify'
  import type {
    ConstantItem,
    EmailLangOption,
    ErrorMsg,
  } from './composables/useChangeMember'

  type Props = {
    constants: ConstantItem[]
    errorMsg: ErrorMsg
    emailLangOptions: EmailLangOption[]
  }

  type Emits = {
    (e: 'confirmInputs', params: any, loading: boolean): void
    (
      e: 'update:errorMsg',
      params: {field: keyof ErrorMsg; message: string}
    ): void
    (e: 'withdrawal', params: any, loading: boolean): void
  }

  const props = withDefaults(defineProps<Props>(), {
    constants: () => [],
    errorMsg: () => ({}) as ErrorMsg,
    emailLangOptions: () => [],
  })

  const emit = defineEmits<Emits>()

  const {t, current: locale} = useLocale()
  const {registInputs} = useChangeMemberStore()

  const agreed = ref(false)
  const loading = reactive({
    companyPostCode: false,
    postCode: false,
    requestSend: false,
  })

  // Priority countries to be displayed first
  // Japan, UAE, HongKong China
  const priorityCountries = ['JP', 'AE', 'CN']
  const countryList = computed(() => {
    if (!props.constants) return []
    return props.constants
      .filter(x => x.key_string === 'COUNTRY_CODE')
      .sort((a, b) => {
        const valueA = a.value2 ?? ''
        const valueB = b.value2 ?? ''

        // Find the index of each value in the priorityCountries array
        const indexA = priorityCountries.indexOf(a.value1)
        const indexB = priorityCountries.indexOf(b.value1)

        // If both values are in the priorityCountries array, sort by their index
        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB
        }
        // If only A is in the priorityCountries array, A stands before B
        if (indexA !== -1) {
          return -1
        }
        // If only B is in the priorityCountries array, B stands before A
        if (indexB !== -1) {
          return 1
        }
        // If neither value is in the priorityCountries array, sort alphabetically
        return valueA.localeCompare(valueB)
      })
  })

  const registMember = computed(() => {
    return registInputs.reduce((result, obj) => {
      result[obj.item] = obj.value
      return result
    }, {})
  })
  const selectedCountry = computed(() => {
    const countryInput = registInputs.find(x => x.item === 'country')
    return countryInput && typeof countryInput.value === 'string'
      ? countryInput.value
      : ''
  })
  const passwordCompareOk = computed(() => {
    const password = registInputs.find(x => x.item === 'password')?.value
    const passwordConfirm = registInputs.find(
      x => x.item === 'passwordConfirm'
    )?.value
    if (passwordConfirm) {
      return password === passwordConfirm
    } else {
      return true
    }
  })

  const emailCompareOk = computed(() => {
    const email = registInputs.find(x => x.item === 'email')?.value
    const emailConfirm = registInputs.find(
      x => x.item === 'emailConfirm'
    )?.value
    if (emailConfirm) {
      return email === emailConfirm
    } else {
      return true
    }
  })
  const isButtonDisabled = computed(() => {
    return !agreed.value || !passwordCompareOk.value
  })
  const handleInputError = ({
    field,
    message,
  }: {
    field: keyof ErrorMsg
    message: string | null
  }) => {
    if (message) {
      emit('update:errorMsg', {field, message})
    } else {
      emit('update:errorMsg', {field, message: null})
    }
  }
</script>
<template>
  <form v-on:submit.prevent>
    <section id="entry-form">
      <table class="tbl-entry">
        <tbody>
          <tr>
            <th>ニックネーム<em class="req">※必須</em></th>
            <td>
              <input type="text" class="iptW-M" required />
            </td>
          </tr>
          <tr>
            <th>会社名</th>
            <td>
              <input type="text" class="iptW-M" />
            </td>
          </tr>
          <tr>
            <th>会社名フリガナ</th>
            <td>
              <input type="text" class="iptW-M" />
            </td>
          </tr>
          <tr>
            <th>会社住所</th>
            <td>
              <input type="text" class="iptW-M" />
            </td>
          </tr>
          <tr>
            <th>古物商許可番号</th>
            <td>
              <input type="text" class="iptW-M" />
            </td>
          </tr>
          <tr>
            <th>古物商許可書アップロード</th>
            <td>
              <label class="upload-label">
                <span>申請必要書類を</span>
                <span>アップロード</span>
                <input
                  type="file"
                  name="file"
                  id="file-upload"
                  class="upload-input"
                />
              </label>
            </td>
          </tr>
          <tr>
            <th>氏名・担当者<em class="req">※必須</em></th>
            <td>
              <input type="text" class="iptW-M" />
            </td>
          </tr>
          <tr>
            <th>氏名・担当者フリガナ<em class="req">※必須</em></th>
            <td>
              <div class="ipt-wrap">
                <input type="text" class="iptW-M" required /><span
                  class="ipt-rule"
                  >[全角]</span
                >
              </div>
            </td>
          </tr>
          <tr>
            <th>郵便番号<em class="req">※必須</em></th>
            <td>
              <div class="ipt-wrap">
                <input
                  type="text"
                  class="ime-dis iptW-S"
                  placeholder="例）150-8512"
                  required
                /><input
                  class="ipt-btn-gray zip-search"
                  type="button"
                  value="検索"
                />
              </div>
            </td>
          </tr>
          <tr>
            <th>住所<em class="req">※必須</em></th>
            <td>
              <input
                type="text"
                class="iptW-M"
                placeholder="例）東京都渋谷区桜丘町26-1セルリアンタワー"
                required
              />
            </td>
          </tr>
          <tr>
            <th>電話番号<em class="req">※必須</em></th>
            <td>
              <div class="ipt-wrap">
                <span class="int-no">+81</span
                ><input
                  type="tel"
                  class="ime-dis iptW-MM"
                  placeholder="例）03-1111-XXXX"
                  required
                /><span class="ipt-rule">[半角数字]</span>
              </div>
            </td>
          </tr>
          <tr>
            <th>メールアドレス<em class="req">※必須</em></th>
            <td>
              <div class="ipt-wrap">
                <input type="email" class="iptW-M" required /><span
                  class="ipt-rule"
                  >[半角英数字]</span
                >
              </div>
            </td>
          </tr>
          <tr>
            <th>
              メールアドレス<br class="only_pc" />(確認用)<em class="req"
                >※必須</em
              >
            </th>
            <td>
              <input type="email" class="iptW-M" required />
            </td>
          </tr>
          <tr>
            <th>パスワード<em class="req">※必須</em></th>
            <td>
              <input type="text" class="iptW-M" required />
            </td>
          </tr>
          <tr>
            <th>パスワード(確認用)<em class="req">※必須</em></th>
            <td>
              <input type="text" class="iptW-M" required />
            </td>
          </tr>
          <tr>
            <th>
              個人情報の<br class="only_pc" />取り扱いについて<em class="req"
                >※必須</em
              >
            </th>
            <td class="agree">
              <label for="rule-chk">
                <input
                  type="checkbox"
                  id="rule-chk"
                  class="checkbox-input"
                  required
                />
                <span class="checkbox-parts">同意する</span>
              </label>
            </td>
          </tr>
          <tr class="att-use">
            <th>&nbsp;</th>
            <td>
              <p class="privacy-link">
                ※入力内容を送信いただく前に、必ず「<a href="A" target="_blank"
                  >個人情報の保護</a
                >」をご覧ください。
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </section>
    <div class="btn-form">
      <input
        type="button"
        id="sbm-login"
        class="account-edit"
        value="入力内容を確認する"
      />
    </div>
  </form>
  <div class="wrap-btn">
    <button class="withdraw">退会する</button>
  </div>
</template>
<style scoped>
  #main #entry-form table.tbl-entry input.iptW-M,
  #main #entry-form table.tbl-entry input.iptW-MM {
    background-color: #fff;
  }

  #main #entry-form table.tbl-entry input.iptW-M:disabled,
  #main #entry-form table tbody td div select:disabled,
  #main #entry-form table.tbl-entry input.iptW-MM:disabled {
    cursor: default !important;
    background-color: #d6d6d6 !important;
    opacity: 1 !important;
  }
  .work-break {
    word-break: keep-all;
    max-width: 180px;
  }
</style>
