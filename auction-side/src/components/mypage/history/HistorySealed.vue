<script setup lang="ts">
  import RowBidProductList from '@/components/common/product-list/RowBidProductList.vue'
  import useFavorite from '@/composables/favorite'
  import useHistoryItems from '@/composables/mypage/useHistoryItems'
  import useSearchResultState from '@/composables/state/useSearchResultState'
  import {CLASSIFICATION_TYPES} from '@/constants/classification'
  import {computed, onMounted} from 'vue'

  const searchState = useSearchResultState()
  const {loading, fetchHistory} = useHistoryItems()
  const {toggleFavorite} = useFavorite()

  // Filter items for sealed auctions only
  const sealedItems = computed(() => {
    return searchState.productList.all.filter(
      item => Number(item.auction_classification) === 2
    )
  })

  const totalCount = computed(() => searchState.totalCount.value)

  const handleFavoriteToggle = async (
    exhibitionItemNo: string,
    currentFavorited: boolean
  ) => {
    console.log('handleFavoriteToggle', exhibitionItemNo, currentFavorited)
    await toggleFavorite(exhibitionItemNo, currentFavorited)
    await fetchHistory(CLASSIFICATION_TYPES.SEALED)
  }

  const handlers = {
    toggleFavorite: handleFavoriteToggle,
    onRefresh: () => {
      console.log('Refresh handler called')
    },
    onBid: () => {
      console.log('Bid handler called')
    },
    onItemClick: item => {
      console.log('Item click handler called for item:', item)
    },
  }

  onMounted(async () => {
    await fetchHistory(CLASSIFICATION_TYPES.SEALED)
  })
</script>

<template>
  <div class="display-option">
    <div class="refine">
      <div class="count">
        <p>
          <span>{{ totalCount }}</span
          >件のオークション
        </p>
      </div>
    </div>
  </div>

  <div v-if="loading" class="loading">読み込み中...</div>

  <div v-else class="item-list row-bid">
    <RowBidProductList
      :items="sealedItems"
      :handlers="handlers"
      custom-classes=""
    />
  </div>

  <div class="wrap-btn pagination">
    <p>{{ totalCount }}件中 1〜{{ sealedItems.length }}件を表示</p>
    <nav class="pagination">
      <ul>
        <li class="prev"><a href="#"></a></li>
        <li><a href="#" class="active">1</a></li>
        <li><a href="#">2</a></li>
        <li><a href="#">103</a></li>
        <li class="next"><a href="#"></a></li>
      </ul>
    </nav>
  </div>
</template>
