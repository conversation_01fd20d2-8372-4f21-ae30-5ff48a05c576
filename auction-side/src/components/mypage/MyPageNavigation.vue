<script setup lang="ts">
  import {useRoute, useRouter} from 'vue-router'
  import {PATH_NAME} from '@/defined/const'

  const route = useRoute()
  const router = useRouter()

  // Tab configuration - simplified and focused only on navigation
  const tabs = [
    {
      key: 'favorite',
      label: 'お気に入り',
      icon: 'favorite',
      path: PATH_NAME.MYPAGE_FAVORITE,
    },
    {
      key: 'bidding',
      label: '入札中',
      icon: 'bidding',
      path: PATH_NAME.MYPAGE_BIDDING,
    },
    {
      key: 'history',
      label: '落札履歴',
      icon: 'winning-history',
      path: PATH_NAME.MYPAGE_BID_HISTORY,
    },
    {
      key: 'account',
      label: '会員情報編集',
      icon: 'account',
      path: PATH_NAME.MYPAGE_ACCOUNT,
      hasUploadIcon: true,
    },
  ]

  // Simple navigation logic
  const navigateToTab = (tab: (typeof tabs)[0]) => {
    if (route.path !== tab.path) {
      router.push(tab.path)
    }
  }

  // Check if tab is active
  const isTabActive = (tab: (typeof tabs)[0]) => {
    return route.path === tab.path
  }
</script>

<template>
  <section id="mypage-head">
    <div class="container">
      <div class="nav-wrap">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          class="nav-content"
          :class="{active: isTabActive(tab)}"
        >
          <a @click="navigateToTab(tab)" href="javascript:void(0)">
            <span :class="tab.icon"></span>
            <div class="label">{{ tab.label }}</div>
            <span v-if="tab.hasUploadIcon" class="upload"></span>
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
  /* Navigation specific styles */
  .nav-content a {
    cursor: pointer;
  }

  .nav-content.active a {
    background-color: #fff;
  }

  .nav-content.active a .label {
    color: #333;
  }
</style>
