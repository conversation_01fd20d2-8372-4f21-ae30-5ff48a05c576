<script setup>
  import {computed, defineAsyncComponent} from 'vue'

  const ProductList = defineAsyncComponent(
    () => import('../search-list/ProductList.vue')
  )

  const searchState = useSearchResultState()

  const productList = computed(() => searchState.productList.all)
  const exhibitionList = computed(() => searchState.productList.exhibitionList)
</script>
<template>
  <section id="list-recommend" class="list-item list-slider">
    <h2>
      <p class="ttl">新着商品</p>
    </h2>
  </section>
</template>
