<!-- <script setup>
import { computed, defineAsyncComponent } from 'vue'
import useSearchResultState from '../../composables/useSearchResultState'

const ProductList = defineAsyncComponent(() => import('../search-list/ProductList.vue'))

const searchState = useSearchResultState()

const productList = computed(() => searchState.productList.all)
const exhibitionList = computed(() => searchState.productList.exhibitionList)
</script>
<template>
  <section id="list-auction">
    <div class="container">
      <template v-for="exh in exhibitionList" :key="exh.exhibition_no">
        <ProductList
          v-if="productList.some((x) => x.exhibition_no === exh.exhibition_no)"
          :productList="productList.filter((x) => x.exhibition_no === exh.exhibition_no)"
          :exhibitionInfo="exh"
        />
      </template>
    </div>
  </section>
</template> -->
