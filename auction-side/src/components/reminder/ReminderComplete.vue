<script setup>
  import {PATH_NAME} from '@/defined/const'
  import {useRouter} from 'vue-router'
  import {useLocale} from 'vuetify'
  import BreadCrumb from '../common/BreadCrumb.vue'

  const {t: translate} = useLocale()
  const router = useRouter()

  const backToLogin = () => {
    router.push(PATH_NAME.LOGIN)
  }
</script>
<template>
  <main id="main" class="reminder">
    <BreadCrumb customTitle="パスワードをお忘れの方" />
    <h2 class="page-ttl">
      <p class="ttl">パスワードをお忘れの方</p>
      <p class="sub">Reminder</p>
    </h2>
    <div class="container">
      <div class="remind-msg-comp">
        <p>新しいパスワードをメールアドレス宛に送信いたしました。</p>
      </div>
      <div class="remind-comp-btn">
        <a class="btnBsc-Black" @click="backToLogin">ログイン画面へ戻る</a>
      </div>
    </div>
  </main>
</template>
<style scoped src="@/assets/css/home.css" />
