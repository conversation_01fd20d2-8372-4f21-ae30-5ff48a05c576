<script setup>
  import {computed, defineAsyncComponent, defineProps} from 'vue'
  import {useLocale} from 'vuetify'
  import AuctionInfo from '../parts/AuctionInfo.vue'

  const AuctionContentHistory = defineAsyncComponent(
    () => import('../parts/AuctionContentHistory.vue')
  )
  const AuctionItem = defineAsyncComponent(() => import('./AuctionItem.vue'))

  const props = defineProps(['exhibitionInfo', 'productList'])
  const isAscendingAuction = computed(
    () => Number(props.exhibitionInfo?.auction_classification) === 1
  )

  const {t} = useLocale()
</script>
<template>
  <div class="auction-conteiner">
    <AuctionInfo :exhibitionInfo="exhibitionInfo" />
    <AuctionContentHistory :productList="productList">
      <template v-slot:header>
        <tr>
          <th rowspan="1" colspan="1">{{ t('bidHistory.endDate') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.maker') }}</th>
          <th rowspan="1" colspan="1">
            {{ t('productDetail.info.productName') }}
          </th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.sim') }}</th>
          <th rowspan="1" colspan="1">
            {{ t('productDetail.info.capacity') }}
          </th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.color') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.rank') }}</th>
          <th rowspan="1" colspan="1">
            {{
              isAscendingAuction
                ? t('bidHistory.bidSuccessPrice')
                : t('bidHistory.bidSuccessUnitPrice')
            }}
          </th>
        </tr>
      </template>
      <template v-slot:item="{item}">
        <AuctionItem :item="item" :isAscendingAuction="isAscendingAuction" />
      </template>
    </AuctionContentHistory>
  </div>
</template>
