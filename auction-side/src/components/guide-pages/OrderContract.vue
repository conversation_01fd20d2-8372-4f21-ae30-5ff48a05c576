<script setup>
  import {ref, watch} from 'vue'
  import {useLocale} from 'vuetify'
  import OrderContractEN from './Locale/OrderContractEN.vue'
  import OrderContractJA from './Locale/OrderContractJA.vue'

  const {current} = useLocale()
  const currentLocale = ref(current.value)

  watch(
    () => current.value,
    () => {
      currentLocale.value = current.value
    }
  )
</script>

<template>
  <OrderContractJA v-if="currentLocale === 'ja'" />
  <OrderContractEN v-else />
</template>
