import {computed} from 'vue'
import {useRoute} from 'vue-router'
import {PATH_NAME} from '@/defined/const'

export default function useMyPageBreadcrumb() {
  const route = useRoute()

  // Map routes to breadcrumb titles
  const breadcrumbTitles = {
    [PATH_NAME.MYPAGE_FAVORITE]: 'お気に入り',
    [PATH_NAME.MYPAGE_BIDDING]: '入札中',
    [PATH_NAME.MYPAGE_BID_HISTORY]: '落札履歴',
    [PATH_NAME.MYPAGE_ACCOUNT]: '会員情報編集',
  }

  // Get breadcrumb title based on current route
  const breadcrumbTitle = computed(() => {
    return breadcrumbTitles[route.path] || 'マイページ'
  })

  return {
    breadcrumbTitle,
  }
}
