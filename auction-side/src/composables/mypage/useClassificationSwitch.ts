import {CLASSIFICATION_TYPES} from '@/constants/classification'
import {computed, ref} from 'vue'

export type ClassificationType = 'ascending' | 'sealed'

export default function useClassificationSwitch(
  defaultClassification: ClassificationType = CLASSIFICATION_TYPES.ASCENDING
) {
  const currentAuctionType = ref<ClassificationType>(defaultClassification)

  const isAscending = computed(
    () => currentAuctionType.value === CLASSIFICATION_TYPES.ASCENDING
  )
  const isSealed = computed(
    () => currentAuctionType.value === CLASSIFICATION_TYPES.SEALED
  )

  const getClassificationLabel = (
    classificationType: ClassificationType
  ): string => {
    return classificationType === CLASSIFICATION_TYPES.ASCENDING
      ? '競り上がり入札'
      : '封印入札'
  }

  const currentLabel = computed(() =>
    getClassificationLabel(currentAuctionType.value)
  )

  return {
    currentAuctionType,
    isAscending,
    isSealed,
    currentLabel,
  }
}
