import useSearchResultState from '@/composables/state/useSearchResultState'
import useApi from '@/composables/useApi'
import {ref} from 'vue'
import type {ClassificationType} from './useClassificationSwitch'

/**
 * Composable for managing successful bid history items data
 *
 * Provides functionality to fetch and manage items that the user has successfully bid on.
 * Supports both ascending and sealed auction types with filtering capabilities.
 * History items represent completed auctions where the user was the winning bidder.
 *
 * @returns {Object} Composable object with loading state and fetch functions
 * @returns {Ref<boolean>} loading - Loading state indicator
 * @returns {Function} fetchHistory - Fetch history items by classification type
 * @returns {Function} fetchAllHistory - Fetch all history items regardless of classification
 * @returns {Function} refreshHistory - Refresh history items for specific classification
 */
export default function useHistoryItems() {
  const loading = ref(false)
  const {apiExecute, parseHtmlResponseError} = useApi()
  const state = useSearchResultState()

  /**
   * Set product list data in the search result state
   * @param {Object} response - API response containing items data
   */
  const setProductList = (response: any): void => {
    if (response?.items) {
      state.productList.all = response.items.map((item: any) => ({
        ...item,
        bidPrice:
          item.bid_status?.winning_price?.toString() ||
          item.bid_status?.bid_price?.toString() ||
          '0',
        bidQuantity: item.bid_status?.bid_quantity?.toString() || '1',
      }))
      state.totalCount.value = response.total_count || response.count || 0
    }
  }

  /**
   * Fetch history items filtered by auction classification
   * @param {ClassificationType} classification - 'ascending' or 'sealed'
   */
  const fetchHistory = async (
    classification: ClassificationType
  ): Promise<void> => {
    loading.value = true

    try {
      const params = {
        classification: classification === 'ascending' ? 1 : 2,
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute(
        'private/get-successful-bid-history',
        params
      )

      // Filter items by classification after receiving response
      if (response?.items) {
        const classificationId = classification === 'ascending' ? 1 : 2
        const filteredItems = response.items.filter(
          (item: any) =>
            Number(item.auction_classification) === classificationId
        )

        setProductList({
          ...response,
          items: filteredItems,
          total_count: filteredItems.length,
        })
      }
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch all history items regardless of classification
   */
  const fetchAllHistory = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute(
        'private/get-successful-bid-history',
        params
      )
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Refresh history items for specific classification
   * @param {ClassificationType} classification - 'ascending', 'sealed', or 'all'
   */
  const refreshHistory = async (
    classification: ClassificationType
  ): Promise<void> => {
    if (classification === 'ascending') {
      await fetchHistory('ascending')
    } else if (classification === 'sealed') {
      await fetchHistory('sealed')
    } else {
      await fetchAllHistory()
    }
  }

  return {
    loading,
    fetchHistory,
    fetchAllHistory,
    refreshHistory,
  }
}
