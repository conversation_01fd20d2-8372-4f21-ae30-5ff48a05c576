import useSearchResultState from '@/composables/state/useSearchResultState'
import useApi from '@/composables/useApi'
import {ref} from 'vue'
import type {ClassificationType} from './useClassificationSwitch'

/**
 * Composable for managing bidding items data
 *
 * Provides functionality to fetch and manage items that the user is currently bidding on.
 * Supports both ascending and sealed auction types with filtering capabilities.
 *
 * @returns {Object} Composable object with loading state and fetch functions
 * @returns {Ref<boolean>} loading - Loading state indicator
 * @returns {Function} fetchBidding - Fetch bidding items by classification type
 * @returns {Function} fetchAllBidding - Fetch all bidding items regardless of classification
 * @returns {Function} refreshBidding - Refresh bidding items for specific classification
 */
export default function useBiddingItems() {
  const loading = ref(false)
  const {apiExecute, parseHtmlResponseError} = useApi()
  const state = useSearchResultState()

  /**
   * Set product list data in the search result state
   * @param {Object} response - API response containing items data
   */
  const setProductList = (response: any): void => {
    if (response?.items) {
      state.productList.all = response.items.map((item: any) => ({
        ...item,
        bidPrice: item.bid_status?.bid_price?.toString() || '0',
        bidQuantity: item.bid_status?.bid_quantity?.toString() || '1',
      }))
      state.totalCount.value = response.total_count || response.count || 0
    }
  }

  /**
   * Fetch bidding items filtered by auction classification
   * @param {ClassificationType} classification - 'ascending' or 'sealed'
   */
  const fetchBidding = async (
    classification: ClassificationType
  ): Promise<void> => {
    loading.value = true

    try {
      const params = {
        classification: classification === 'ascending' ? 1 : 2,
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute('private/get-bidding-items', params)

      // Filter items by classification after receiving response
      if (response?.items) {
        const classificationId = classification === 'ascending' ? 1 : 2
        const filteredItems = response.items.filter(
          (item: any) =>
            Number(item.auction_classification) === classificationId
        )

        setProductList({
          ...response,
          items: filteredItems,
          total_count: filteredItems.length,
        })
      }
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Fetch all bidding items regardless of classification
   */
  const fetchAllBidding = async (): Promise<void> => {
    loading.value = true

    try {
      const params = {
        languageCode: 'ja',
        limit: state.showCount.value * state.viewMore.value,
      }

      const response = await apiExecute('private/get-bidding-items', params)
      setProductList(response)
    } catch (error) {
      parseHtmlResponseError(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * Refresh bidding items for specific classification
   * @param {ClassificationType} classification - 'ascending', 'sealed', or 'all'
   */
  const refreshBidding = async (
    classification: ClassificationType
  ): Promise<void> => {
    if (classification === 'ascending') {
      await fetchBidding('ascending')
    } else if (classification === 'sealed') {
      await fetchBidding('sealed')
    } else {
      await fetchAllBidding()
    }
  }

  return {
    loading,
    fetchBidding,
    fetchAllBidding,
    refreshBidding,
  }
}
