import {noImg} from '@/defined/const'
import {reactive} from 'vue'
import {formatDateString} from './common'
import useApi from './useApi'

/**
 * Get related items
 */
export default function useGetRelatedItems() {
  const {apiExecute, parseHtmlResponseError} = useApi()

  const relatedItems = reactive([])

  const search = async (itemNo, brand) => {
    const params = {
      itemNo,
      brand,
      limit: 10,
    }
    await apiExecute('public/get-related-items', params)
      .then(response => {
        relatedItems.length = 0
        if (response.items) {
          response.items.forEach(item => {
            const {datePart: endDatePart, timePart: endTimePart} =
              formatDateString(item.end_datetime)
            const {datePart: startDatePart, timePart: startTimePart} =
              formatDateString(item.start_datetime)
            relatedItems.push({
              ...item,
              category: item.category_id,
              itemNo: item.item_no,
              productName: item.free_field.productName,
              imgSrc: item.free_field.image_url ?? noImg,
              currentPrice: item.bid_status.current_price?.toLocaleString(),
              currentPriceTaxIncluded: Math.round(
                item.bid_status.current_price +
                  item.bid_status.current_price *
                    (item.bid_status.tax_rate / 100)
              )?.toLocaleString(),
              noOfBids: item.attention_info.bid_count,
              endDatePart,
              endTimePart,
              startDatePart,
              startTimePart,
              link: `/details/${item.exhibition_item_no}`,
            })
          })
        }
      })
      .catch(error => parseHtmlResponseError(error))
  }

  return {relatedItems, search}
}
