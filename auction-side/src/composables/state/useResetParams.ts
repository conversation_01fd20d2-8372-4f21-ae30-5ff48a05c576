import useSearchResultState from './useSearchResultState'

export default function useResetParams() {
  const {
    searchKeyTop,
    searchKey,
    searchCategory,
    searchBrand,
    modelList,
    categoryList,
    brandList,
    unSoldOut,
    favorite,
    showCount,
    panelView,
    viewMore,
    sorter,
    startPrice,
    endPrice,
    showCountConstant,
  } = useSearchResultState()

  return () => {
    searchKeyTop.value = null
    searchKey.value = null
    searchCategory.value = null
    searchBrand.value = null
    modelList.length = 0
    categoryList.length = 0
    brandList.length = 0
    unSoldOut.value = false
    favorite.value = null
    showCount.value = showCountConstant
    panelView.value = 0
    viewMore.value = 1
    sorter.value = null
    startPrice.value = null
    endPrice.value = null
  }
}
