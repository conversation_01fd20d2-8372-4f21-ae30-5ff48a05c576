import {noImg, PATH_NAME} from '../../defined/const'
import {formatDateString, priceLocaleString} from '../common'
import type {RawAuctionItem} from './useSearchResultState'
import useSearchResultState from './useSearchResultState'

/**
 * Composable for setting product list data
 * Used when handling search results from [public/search-auction-items]
 * @returns {Function} Function to set product list data
 */
export default function useSetProductList() {
  const {productList, totalCount, searchKeyTop, searchKeyTopAfter} =
    useSearchResultState()

  return (searchResult: any) => {
    const itemList = searchResult?.items ?? []
    totalCount.value = searchResult?.count ?? 0
    searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map((item: RawAuctionItem) => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(
        item.end_datetime
      )
      const {datePart: startDatePart, timePart: startTimePart} =
        formatDateString(item.start_datetime)
      return {
        ...item,
        category: item.category_id,
        itemNo: item.item_no,
        link: `${PATH_NAME.DETAIL}/${item.exhibition_item_no}`,
        productName: item.free_field.product_name,
        imgSrc: item.free_field.image_url ?? noImg,
        currentPrice: item.bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          item.bid_status.current_price +
            item.bid_status.current_price * (item.bid_status.tax_rate / 100)
        )?.toLocaleString(),
        noOfBids: item.attention_info.bid_count,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
        bidPrice: priceLocaleString(item.bid_status.bid_price, 10), // input bid price value on screen will be saved here
        bidQuantity: priceLocaleString(item.bid_status.bid_quantity, 10), // input bid quantity
        bidInputError: {
          bidPrice: null,
          bidQuantity: null,
        }, // error message for bid price and quantity
      }
    })
    // Exhibition list
    productList.exhibitionList = searchResult?.exhibition_group ?? []
    // All items
    productList.all = formattedItems
  }
}

// example input( TODO : remove after create enough data)
// {
//     "items": [
//         {
//             "pdfs": null,
//             "image": null,
//             "lot_id": "134",
//             "item_no": 134,
//             "sold_out": false,
//             "manage_no": "ID00012",
//             "bid_status": {
//                 "status": 0,
//                 "can_bid": true,
//                 "started": true,
//                 "quantity": 1,
//                 "tax_rate": 10,
//                 "bid_price": null,
//                 "extending": false,
//                 "is_cancel": false,
//                 "top_price": 0,
//                 "pitch_width": 1,
//                 "bid_quantity": null,
//                 "end_datetime": "2025-08-10T14:07:00+09:00",
//                 "pitch_option": 1,
//                 "current_price": 4,
//                 "is_top_member": null,
//                 "is_more_little": false,
//                 "pitch_button_1": 1,
//                 "pitch_button_2": 10,
//                 "pitch_button_3": 50,
//                 "start_datetime": "2025-07-17T14:09:00+09:00",
//                 "is_second_member": null,
//                 "lowest_bid_price": 4,
//                 "automatic_bidding": true,
//                 "remaining_seconds": 1978865.6755,
//                 "lowest_bid_quantity": 1,
//                 "top_member_nickname": "",
//                 "is_exceeding_lowest_price": false,
//                 "is_not_exceeding_lowest_price": false
// minimum_bid_exceeded
//             },
//             "free_field": {
//                 "tax": "10",
//                 "biko": "備考 日本語 ",
//                 "rank": "B",
//                 "stock": "item-ancillary/20250717141123-3GNil96D9E/xanh-la-cay.png",
//                 "item_id": "43233",
//                 "newsprc": "NEWS価格NEWS価格NEWS価格NEWS価格",
//                 "countryName": null
//             },
//             "item_count": 1,
//             "category_id": 0,
//             "end_datetime": "2025-08-10T14:07:00+09:00",
//             "exhibition_no": 332,
//             "attention_info": {
//                 "bid_count": null,
//                 "view_count": 0,
//                 "is_favorited": false,
//                 "favorited_count": 0,
//                 "show_bid_count_flag": false
//             },
//             "start_datetime": "2025-07-17T14:09:00+09:00",
//             "create_datetime": "2025-07-17T14:11:46.786916+09:00",
//             "is_recommending": null,
//             "exhibition_item_no": 134,
//             "auction_classification": "1"
//         }
//     ],
//     "count": 10,
//     "count_init": 10,
//     "category_group": [
//         {
//             "count": 2,
//             "category_id": null
//         },
//         {
//             "count": 2,
//             "category_id": "2"
//         },
//         {
//             "count": 6,
//             "category_id": "1"
//         }
//     ],
//     "exhibition_group": [
//         {
//             "count": 3,
//             "end_datetime": "2025-07-05T10:05:00+09:00",
//             "exhibition_no": 67,
//             "start_datetime": "2025-06-24T10:05:00+09:00",
//             "exhibition_name": "競上テスト２５Jun🏓",
//             "auction_classification": "2"
//         },
//         {
//             "count": 4,
//             "end_datetime": "2025-09-20T11:14:00+09:00",
//             "exhibition_no": 100,
//             "start_datetime": "2025-06-25T11:16:00+09:00",
//             "exhibition_name": "🏖️春の特別オークション第1000",
//             "auction_classification": "2"
//         },
//         {
//             "count": 1,
//             "end_datetime": "2025-07-15T17:30:00+09:00",
//             "exhibition_no": 133,
//             "start_datetime": "2025-07-09T09:30:00+09:00",
//             "exhibition_name": "入札会_テスト",
//             "auction_classification": "2"
//         },
//         {
//             "count": 2,
//             "end_datetime": "2025-08-10T14:07:00+09:00",
//             "exhibition_no": 332,
//             "start_datetime": "2025-07-17T14:09:00+09:00",
//             "exhibition_name": "競上　テスト2👍",
//             "auction_classification": "1"
//         }
//     ],
//     "isMoreLimit": false,
//     "moreSearch": null
// }
