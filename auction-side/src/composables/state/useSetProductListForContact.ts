import useSearchResultState from './useSearchResultState'

/**
 * Composable for setting product list for contact
 * Triggered when Detail page mounts or getContact button is clicked
 * 
 * @returns {Function} Function to set product list for contact
 */
export default function useSetProductListForContact() {
  const {productDetails, productDetailsForContact} = useSearchResultState()

  return () => {
    const p = {
      image: productDetails?.images?.[0],
      sold_out: false,
      bid_status: {...productDetails.bid_status, is_top_member: null},
      free_field: productDetails.freeFields,
      category_id: productDetails.freeFields.category,
      attention_info: Object.assign({}, productDetails?.attention_info || {}),
      exhibition_item_no: productDetails.exhibition_item_no,
      category: productDetails.freeFields.category,
      link: '',
      productName: productDetails.productName,
      imgSrc: productDetails.images?.[0] ?? '',
      currentPrice: productDetails.currentPrice,
      currentPriceTaxIncluded: Math.round(
        productDetails.bid_status.current_price *
          (1 + productDetails.bid_status.tax_rate / 100)
      )?.toLocaleString(),
      noOfBids: productDetails.bid_count,
      endDatePart: productDetails.endDatePart,
      endTimePart: productDetails.endTimePart,
    }
    Object.assign(productDetailsForContact, p)
  }
}
