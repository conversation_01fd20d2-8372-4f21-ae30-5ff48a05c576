import type {Router} from 'vue-router'
import {PATH_NAME} from '../../defined/const'
import useSearchResultState from './useSearchResultState'

/**
 * Composable for updating favorite count
 * Updates favorite count for a specific item in both product list and details
 *
 * @returns {Function} Function to update favorite count
 */
export default function useUpdateFavoriteCount() {
  const {productList, productDetails} = useSearchResultState()

  return (exhibitionItemNo: string, isIncrease: boolean, router: Router) => {
    console.log('updateFavoriteCount')
    const foundItem = productList.all.find(
      x => x.exhibition_item_no === exhibitionItemNo
    )
    if (foundItem) {
      foundItem.attention_info.favorited_count += isIncrease ? 1 : -1
      if (router.currentRoute.value.path.includes(PATH_NAME.DETAIL)) {
        productDetails.favorite_count += isIncrease ? 1 : -1
      }
    }
  }
}
