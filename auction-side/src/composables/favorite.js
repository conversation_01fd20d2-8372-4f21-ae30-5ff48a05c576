import useSearchProducts from '@/composables/searchProducts'
import {useBidConfirmStore} from '@/stores/bidConfirm'
import {useLocale} from 'vuetify'
import {useAuthStore} from '../stores/auth'
import {useMessageDialogStore} from '../stores/message-dialog'
import useSearchResultState from './state/useSearchResultState'
import useApi from './useApi'

/**
 * Favorite processing
 */
export default function useFavorite() {
  const bidConfirmStore = useBidConfirmStore()
  const auth = useAuthStore()
  const msgStore = useMessageDialogStore()
  const searchState = useSearchResultState()
  const {search} = useSearchProducts()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const {t: translate} = useLocale()
  const toggleFavorite = async (exhibition_item_no, favorited) => {
    const params = {
      exhibition_item_no,
      favorited,
    }
    const productInMyPage = searchState?.productList?.all?.find(
      product => product.exhibition_item_no === exhibition_item_no
    )
    const productInDetailPage = searchState?.productDetails
    try {
      if (auth.isAuthenticated) {
        await apiExecute('private/favorite-item', params)

        // Update favorite state when user is in mypage
        // if (productInMyPage) {
        //   productInMyPage.attention_info.is_favorited = !productInMyPage.attention_info.is_favorited
        // }

        // Update favorite state when user is in product detail page
        // if (productInDetailPage) {
        //   productInDetailPage.attention_info.is_favorited
        //     = !productInDetailPage.attention_info.is_favorited
        // }

        // bidConfirmStore.isFavorited = !favorited
        // if (router.currentRoute.value.path === PATH_NAME.MYPAGE_FAVORITE) {
        //   search({favorite : true})
        // }
        // searchResultStore.updateFavoriteCount(exhibition_item_no, !favorited, router)
      } else {
        msgStore.setShowMessage(translate('favorite.loginRequiredFavorite'), {
          showOkButton: true,
        })
      }
    } catch (error) {
      parseHtmlResponseError(error)
    }
  }

  return {toggleFavorite}
}
