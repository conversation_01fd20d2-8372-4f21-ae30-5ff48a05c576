import {reactive, ref} from 'vue'
import useApi from './useApi'

/**
 * Get notice data
 */
export default function useNotice() {
  const {apiExecute} = useApi()

  const showMore = ref(1)
  const showCount = ref(3) // 1回「もっと見る」ボタンを押した後表示件数を追加する
  const totalCount = ref(0)
  const notices = reactive([
    {
      body: '',
      create_date: '',
      display_code: 1,
      file: [],
      link_url: '',
      notice_no: null,
      sub_title: '',
      title: '',
      title1: '',
    },
  ])

  const getNotices = async ({displayCodes, limit}) => {
    try {
      const normalNotice = await apiExecute('/public/get-new-notices', {
        display_code:
          typeof displayCodes === 'undefined' ? [1, 2, 3] : displayCodes,
        limit:
          typeof limit === 'undefined'
            ? showMore.value * showCount.value
            : limit,
      })
      totalCount.value = normalNotice.total_count || 0
      notices.length = 0
      normalNotice?.notices?.map(notice => notices.push(notice))
    } catch (error) {
      console.log(error)
    }
  }

  const countUpShowMore = () => {
    showMore.value++
  }

  return {notices, showMore, showCount, totalCount, countUpShowMore, getNotices}
}
