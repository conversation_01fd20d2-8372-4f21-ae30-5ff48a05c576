import {computed, ref} from 'vue'
import {useLocale} from 'vuetify'
import {useMessageDialogStore} from '../stores/message-dialog'
import {formatDateString, priceLocaleString} from './common'
import useResetParams from './state/useResetParams'
import useResetProductList from './state/useResetProductList'
import useSearchResultState from './state/useSearchResultState'
import useSetBidHistory from './state/useSetBidHistory'
import useSetConstants from './state/useSetConstants'
import useSetProductList from './state/useSetProductList'
import useApi from './useApi'

/**
 * Search products
 */
export default function useSearchProducts() {
  const {apiExecute, parseHtmlResponseError} = useApi()
  const {t, current: locale} = useLocale()
  const state = useSearchResultState()
  const setProductList = useSetProductList()
  const setBidHistory = useSetBidHistory()
  const setConstants = useSetConstants()
  const resetProductList = useResetProductList()
  const resetParams = useResetParams()
  const dialog = useMessageDialogStore()

  const today = new Date()
  const lastYear = new Date()
  lastYear.setFullYear(today.getFullYear() - 1)

  const loading = ref(false)
  const count = ref(0)

  const constants = computed(() => state.constants)
  const storedSearchKey = computed(() => state.searchKey)

  const setProductListFromResponse = searchResult => {
    const itemList = searchResult?.items ?? []
    count.value = itemList.length ?? 0
    // searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map(item => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(
        item.end_datetime
      )
      const {datePart: startDatePart, timePart: startTimePart} =
        formatDateString(item.start_datetime)

      // Current item in store
      const currentItem = state.productList.all.find(
        x => x.exhibition_item_no === item.exhibition_item_no
      )

      return {
        ...item,
        category: item.category_id,
        itemNo: item.item_no,
        link: `/details/${item.exhibition_item_no}`,
        productName: item.free_field.productName,
        currentPrice: item.bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          item.bid_status.current_price +
            item.bid_status.current_price * (item.bid_status.tax_rate / 100)
        )?.toLocaleString(),
        noOfBids: item.attention_info.bid_count,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
        bidPrice:
          currentItem?.bidPrice ||
          priceLocaleString(item.bid_status.bid_price, 10), // input bid price value on screen will be saved here
        bidQuantity:
          currentItem?.bidQuantity ||
          priceLocaleString(item.bid_status.bid_quantity, 10), // input bid quantity
        bidInputError: currentItem?.bidInputError || {
          bidPrice: null,
          bidQuantity: null,
        }, // error message for bid price and quantity
      }
    })
    // Update product list in store
    const newList = state.productList.all.filter(
      x => x.exhibition_no !== formattedItems[0].exhibition_no
    )
    state.productList.all = [...newList, ...formattedItems]
  }

  // search and sort products then save to store
  const searchAuctionItems = async inputParams => {
    loading.value = true
    const {
      category = null,
      exhibitionNos = [],
      searchKey = storedSearchKey.value,
      unSoldOut = state.unSoldOut.value,
      favorite = state.favorite.value,
      bidding = false,
      initLimit = state.showCount.value * state.viewMore.value,
      modelList = state.modelList,
      categoryList = state.categoryList,
      exhibitionItemNos = null,
      startPrice = state.startPrice.value
        ? Number(state.startPrice.value)
        : null,
      endPrice = state.endPrice.value ? Number(state.endPrice.value) : null,
      sorter = state.sorter.value,
      brandList = state.brandList,
      auction_classification = null,
    } = inputParams

    // set favorite flag
    state.favorite.value = favorite

    // categoryList をチェックするための一時変数
    const tempCategoryList = Array.isArray(categoryList) ? categoryList : []

    // brandList をチェックするための一時変数
    let tempBrandList = Array.isArray(brandList) ? brandList : []

    // 一時変数のチェック、brandList = [null] の場合は空配列にする
    if (tempBrandList.length === 1 && tempBrandList[0] === null) {
      tempBrandList = []
    }

    const params = {
      category,
      searchKey,
      unSoldOut,
      favorite,
      bidding,
      auction_classification,
      exhibitionNos,
      initLimit,
      languageCode: locale.value,
      limit: initLimit,
      showedItemNos: null,
      startPrice,
      endPrice,
      sorter,
      timeOver: null,
      modelList,
      categoryList: tempCategoryList,
      exhibitionItemNos,
      brandList: tempBrandList,
    }
    await apiExecute('public/search-auction-items', params)
      .then(response => {
        setProductList(response)
      })
      .catch(error => parseHtmlResponseError(error))
    loading.value = false
  }

  const searchScope = async inputParams => {
    loading.value = true
    const {
      category = null,
      exhibitionNos = [],
      searchKey = storedSearchKey.value,
      unSoldOut = state.unSoldOut.value,
      favorite = state.favorite.value,
      bidding = false,
      initLimit = state.showCount.value * state.viewMore.value,
      categoryList = state.categoryList,
      exhibitionItemNos = null,
      auction_classification = null,
      startPrice = null,
      endPrice = null,
      sorter = null,
      brandList = null,
    } = inputParams

    const params = {
      category: null,
      exhibitionNos: [],
      searchKey: storedSearchKey.value,
      unSoldOut: state.unSoldOut.value,
      favorite: state.favorite.value,
      bidding: false,
      initLimit: state.showCount.value * state.viewMore.value,
      categoryList: state.categoryList,
      exhibitionItemNos: null,
      startPrice: state.startPrice.value
        ? Number(state.startPrice.value)
        : null,
      endPrice: state.endPrice.value ? Number(state.endPrice.value) : null,
      sorter: state.sorter.value,
      auction_classification,
    }

    // set favorite flag
    state.favorite.value = favorite

    await apiExecute('public/search-auction-items', params)
      .then(response => {
        setProductListFromResponse(response)
      })
      .catch(error => parseHtmlResponseError(error))
    loading.value = false
  }

  const searchSuccessfulBidHistory = async (
    inputParams = {
      auctionClassification: [],
      startDatetime: '',
      endDatetime: '',
      initLimit: 0,
      searchKey: '',
      categoryList: [],
    }
  ) => {
    loading.value = true
    const classification = 1 // TODO
    const {
      auctionClassification = [classification],
      startDatetime = lastYear.toISOString().split('T')[0],
      endDatetime = today.toISOString().split('T')[0],
      initLimit = state.showCount.value * state.viewMore.value,
      searchKey = storedSearchKey.value,
      categoryList = state.categoryList,
    } = inputParams

    const params = {
      auctionClassification,
      startDatetime,
      endDatetime,
      initLimit,
      searchKey,
      categoryList,
    }

    try {
      const response = await apiExecute(
        'public/get-successful-bid-history',
        params
      )

      setBidHistory(response)
    } catch (error) {
      const err = parseHtmlResponseError(error)
      console.log({err})
      dialog.setShowMessage(err.message ?? t('common.error'), {isErr: true})
    }
    loading.value = false
  }

  const searchAllSuccessfulBidHistory = async (
    inputParams = {
      auctionClassification: [],
      startDatetime: '',
      endDatetime: '',
      initLimit: 0,
      searchKey: '',
      categoryList: [],
    }
  ) => {
    loading.value = true
    const {
      auctionClassification = [1, 2],
      startDatetime = lastYear.toISOString().split('T')[0],
      endDatetime = today.toISOString().split('T')[0],
      initLimit = state.showCount.value * state.viewMore.value,
      searchKey = storedSearchKey.value,
      categoryList = state.categoryList,
    } = inputParams

    const params = {
      auctionClassification,
      startDatetime,
      endDatetime,
      initLimit,
      searchKey,
      categoryList,
    }

    try {
      const response = await apiExecute(
        'public/get-all-successful-bid-history',
        params
      )

      setBidHistory(response)
    } catch (error) {
      const err = parseHtmlResponseError(error)
      dialog.setShowMessage(err.message)
    }
    loading.value = false
  }

  const searchWithModels = async (
    category: string,
    {checkedCategories = []}: {checkedCategories?: string[]}
  ) => {
    if (Array.isArray(state.categoryList)) {
      state.categoryList.splice(0, state.categoryList.length)
    }
    checkedCategories.map(model => state.categoryList.push(model))
    const reqParams = {
      category: category ?? null,
      unSoldOut: state.unSoldOut.value,
      searchKey: storedSearchKey.value,
      categoryList: checkedCategories,
    }
    await searchAuctionItems(reqParams)
  }

  const getConstants = async () => {
    loading.value = true
    const params = {}
    await apiExecute('public/get-item-search-constants', params).then(
      response => {
        setConstants(response)
        return Promise.resolve()
      }
    )
    loading.value = false
  }

  const countUpViewMore = () => {
    state.viewMore.value++
  }

  const resetParamsAndList = () => {
    resetProductList()
    resetParams()
  }

  return {
    loading,
    count,
    constants,
    searchAuctionItems,
    searchScope,
    searchSuccessfulBidHistory,
    searchAllSuccessfulBidHistory,
    searchWithModels,
    getConstants,
    countUpViewMore,
    resetParams: resetParamsAndList,
  }
}
