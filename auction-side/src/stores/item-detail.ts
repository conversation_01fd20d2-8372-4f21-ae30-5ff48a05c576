import {defineStore} from 'pinia'
import {reactive, ref} from 'vue'

export const useItemDetailStore = defineStore('item-detail', () => {
  const constants = reactive([])
  const pitchButton2 = ref(null)
  const pitchButton3 = ref(null)

  const setConstants = data => {
    constants.length = 0
    data.map(x => {
      constants.push(x)
    })
  }

  return {constants, setConstants, pitchButton2, pitchButton3}
})
