import {defineStore} from 'pinia'
import {reactive} from 'vue'
import useApi from '../composables/useApi'

export const useCommonConstantsStore = defineStore('common-constants', () => {
  const {apiExecute} = useApi()
  const constants = reactive([])

  const getConstants = async () => {
    try {
      const data = await apiExecute('/get-auction-common-constants', {})
      constants.length = 0
      data.map(x => constants.push(x))
    } catch (error) {
      console.log(error)
    }
  }

  return {constants, getConstants}
})
