import {defineStore} from 'pinia'
import {ref} from 'vue'

export const useMessageDialogStore = defineStore('message-dialog', () => {
  const isError = ref(false)
  const message = ref('')
  const showMessageDialog = ref(false)
  const clickedOk = ref(false)
  const clickedConfirm = ref(false)
  const clickedLogout = ref(false)
  const showCloseBtn = ref(true)
  const showTopCloseBtn = ref(false)
  const showCancelBtn = ref(false)
  const showConfirmBtn = ref(false)
  const showLogoutBtn = ref(false)
  const showOKBtn = ref(false)
  const show = ref(false)
  const dialogName = ref('')

  const setShowMessage = (
    msg = '',
    {
      name = '',
      isErr = false,
      showCloseButton = true,
      showTopCloseButton = true,
      showCancelButton = false,
      showConfirmButton = false,
      showOkButton = false,
      showLogoutButton = false,
    }
  ) => {
    dialogName.value = name
    showMessageDialog.value = true
    message.value = msg
    isError.value = isErr
    showCloseBtn.value = showCloseButton
    showTopCloseBtn.value = showTopCloseButton
    showCancelBtn.value = showCancelButton
    showConfirmBtn.value = showConfirmButton
    showOKBtn.value = showOkButton
    showLogoutBtn.value = showLogoutButton
  }

  const handleClose = () => {
    showMessageDialog.value = false
    isError.value = false
    clickedConfirm.value = false
    clickedLogout.value = false
    clickedOk.value = false
  }
  const handleConfirm = () => {
    handleClose()
    clickedConfirm.value = true
  }
  const handleLogout = () => {
    handleClose()
    clickedLogout.value = true
  }
  const handleClickedOk = () => {
    handleClose()
    clickedOk.value = true
  }

  return {
    dialogName,
    show,
    message,
    showMessageDialog,
    clickedOk,
    clickedLogout,
    clickedConfirm,
    showCloseBtn,
    showTopCloseBtn,
    showCancelBtn,
    showConfirmBtn,
    showOKBtn,
    showLogoutBtn,
    setShowMessage,
    handleClose,
    handleConfirm,
    handleLogout,
    handleClickedOk,
    isError,
  }
})
