import {defineStore} from 'pinia'

export const useMemberStore = defineStore('member-info', {
  state: () => ({
    memberInfo: {},
    memberId: null,
    memberRequestNo: null,
    memberStatus: null,
  }),
  getters: {
    fullMemberStatus: state => {
      return state.memberStatus
        ? `Status: ${state.memberStatus}`
        : 'Status not set'
    },
  },
  actions: {
    setMemberInfo(response) {
      this.memberInfo = response?.member ?? {}
      this.memberId = response?.memberId ?? null
      this.memberRequestNo = response?.memberRequestNo
      this.memberStatus = response?.memberStatus
    },
    resetParams() {
      this.$reset()
    },
  },
  // LocalStorageでデータの状態を永続化
  persist: {
    enabled: true,
    strategies: [
      {
        storage: window.localStorage,
        paths: ['memberId', 'memberStatus'],
      },
    ],
  },
})
