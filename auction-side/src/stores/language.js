import {useStorage} from '@vueuse/core'
import {defineStore} from 'pinia'
import useApi from '../composables/useApi'
import {useAuthStore} from './auth'

/**
 * Get current language from local storage or browser
 * Set current language to local storage
 */
export const useLanguageStore = defineStore('language', {
  state: () => {
    const storedLanguage = useStorage('currentLanguage', null)
    // TODO: 多言語の切り替えるデザインが待っているため、一旦日本語デフォルトで表示する
    // const browserLang = navigator.language.split('-')[0]
    // const supportedLangs = ['en', 'ja']
    // const initialLang =
    //   storedLanguage.value ||
    //   (supportedLangs.includes(browserLang) ? browserLang : 'ja')
    const initialLang = 'ja'
    return {
      currentLanguage: useStorage('currentLanguage', initialLang),
    }
  },
  actions: {
    setLanguage(lang) {
      this.currentLanguage = lang
    },
  },
  getters: {
    language: state => state.currentLanguage,
  },
})

/**
 * Change language in database when user was login
 * if user was not login, change language in local storage
 */
export default function useChangeLanguage() {
  const auth = useAuthStore()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const changeLanguage = async lang => {
    if (auth.isAuthenticated) {
      try {
        await apiExecute('private/change-language', {lang})
      } catch (error) {
        const parsedError = parseHtmlResponseError(error)
        console.error('error when change language: ', parsedError)
      }
    } else {
      useStorage('currentLanguage', lang)
    }
  }

  return {changeLanguage}
}
