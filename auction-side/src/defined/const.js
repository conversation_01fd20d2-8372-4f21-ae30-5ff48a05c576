import noImgSrc from '@/assets/no_image.svg'

export const noImg = noImgSrc

export const priceMaxLength = 6

export const PATTERN = {
  EMAIL:
    /^[a-zA-Z0-9_.+-]+@([a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.)+[a-zA-Z]{2,}$/,
  PASSWORD: /^(?=.*?[a-zA-Z])(?=.*?[0-9])[a-zA-Z0-9$@$!%*#?&_-]{8,16}$/,
  POSTCODE: /^\d{3}-?\d{4}$/,
  TEL: /^\+?\d{1,19}$/,
  ANTIQUE_DEALER_LICENSENO: /^(?:\d{0}|(?:\d{12}))$/,
  COMPANY_FURIGANA: /^[\u30A0-\u30FF]+$/,
}

export const PATH_NAME = {
  TOP: '/',
  AUCTION_LIST: '/auction-list',
  REGISTER: '/register',
  ENTRY_INFO_CONFIRM: '/entry-info_completion',
  LOGIN: '/login',
  NOTICE_LIST: '/list-notice',
  NOTICE_LIST_IMPORTANT: '/list-notice-important',
  DETAIL: '/details',
  MYPAGE_FAVORITE: '/mypage/favorite',
  MYPAGE_BIDDING: '/mypage/bidding',
  MYPAGE_BID_HISTORY: '/mypage/bid-history',
  MYPAGE_ACCOUNT: '/mypage/account',
  BID_HISTORY_ALL: '/mypage/bid-history-all',
  // MYPAGE_ACCOUNT: '/mypage',
  MYPAGE_EDIT_CONFIRM: '/mypage/confirm',
  PROFILE: '/profile',
  TERMS: '/terms',
  PRIVACY: '/privacy',
  FAQ: '/faq',
  INQUIRY: '/inquiry',
  INQUIRY_CONFIRM: '/inquiry/confirm',
  GUIDE: '/guide',
  ORDER_CONTRACT: '/order-contract',
  REMINDER: '/reminder',
}

export const API_PATH = {
  REQUEST_MEMBER: 'request-member',
  REISSUE_PASSWORD: 'reissue-password',
  LOGIN: 'login',
}

export const LOCAL_STORE_LABEL = {
  SESSION_TOKEN: 'ss_id',
  SESSION_USERNAME: 'ss_un',
}

export const defaultCookiesOptions = {
  maxAge: 24 * 3600, // Seconds
  secure: location.protocol === 'https:',
}

export const loginNextStep = {
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  OK: 'OK',
}

const classNames = {
  fontS: 'iptW-S',
  fontM: 'iptW-M',
  fontIme: 'ime-dis',
  tel: 'ime-dis iptW-M',
}
export const REGIST_FORM = [
  {
    label: t => t('register.form.country'),
    item: 'country',
    type: 'select',
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.ceoName'),
    item: 'ceoName',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.ceoNameKana'),
    append: t => t('register.form.kana'),
    item: 'ceoNameKana',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
    katakanaCheck: true,
  },
  {
    label: t => t('register.form.ceoBirthday'),
    item: 'ceoBirthday',
    type: 'date',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 10,
    required: country => country !== 'JP',
    isVisible: country => country !== 'JP',
  },
  {
    label: t => t('register.form.companyName'),
    item: 'companyName',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.companyNameKana'),
    append: t => t('register.form.kana'),
    item: 'companyNameKana',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 100,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
    katakanaCheck: true,
  },
  {
    label: t => t('register.form.companyAddress'),
    item: 'companyAddress',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: '',
    value: '',
    length: 255,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.establishmentDate'),
    item: 'establishmentDate',
    type: 'date',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: 'yyyy/mm/dd',
    value: '',
    length: 10,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.companyHp'),
    item: 'companyHp',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: '',
    value: '',
    length: 255,
    required: () => false,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.businessContent'),
    item: 'businessContent',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: '',
    value: '',
    length: 255,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.invoiceNo'),
    item: 'invoiceNo',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: '',
    value: '',
    length: 15,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.telCountryCode'),
    append: t => t('register.form.halfWidthNum'),
    item: 'telCountryCode',
    type: 'text',
    class: `${classNames.tel}`,
    wrap: true,
    placeholder: '',
    value: '',
    length: 10,
    required: country => country !== 'JP',
    isVisible: () => false, // always hidden, data will be set from Tel component
  },
  {
    label: t => t('register.form.tel'),
    append: t => t('register.form.halfWidthNum'),
    item: 'tel',
    type: 'text',
    class: `${classNames.tel}`,
    wrap: true,
    placeholder: '例）03-1111-2222',
    value: '',
    length: 20,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.antiquePermitNo'),
    item: 'antiquePermitNo',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 12,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.antiquePermitDate'),
    item: 'antiquePermitDate',
    type: 'date',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: 'yyyy/mm/dd',
    value: '',
    length: 15,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.antiquePermitCommission'),
    item: 'antiquePermitCommission',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.memberName'),
    item: 'memberName',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 20,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.memberLastName'),
    item: 'memberLastName',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 20,
    required: country => country === 'JP',
    isVisible: () => false, // always hidden, data will be set from FirstLastName component
  },
  {
    label: t => t('register.form.whatsApp'),
    item: 'whatsApp',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 255,
    required: () => false,
    isVisible: country => country !== 'JP',
  },
  {
    label: t => t('register.form.weChat'),
    item: 'weChat',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 255,
    required: () => false,
    isVisible: country => country !== 'JP',
  },
  {
    label: t => t('register.form.email'),
    item: 'email',
    type: 'text',
    wrap: true,
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 255,
    required: () => true,
    isVisible: () => true,
    /*
     * Pattern: PATTERN.EMAIL,
     * errorMsg: 'メールアドレスを確認してください。'
     */
  },
  {
    label: t => t('register.form.emailConfirm'),
    item: 'emailConfirm',
    type: 'text',
    wrap: true,
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 255,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.emailLang'),
    item: 'emailLang',
    type: 'emailLang',
    wrap: '',
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 10,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.password'),
    item: 'password',
    type: 'text',
    wrap: true,
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 14,
    required: () => true,
    isVisible: () => true,
    // Pattern: PATTERN.PASSWORD
  },
  {
    label: t => t('register.form.passwordConfirm'),
    item: 'passwordConfirm',
    type: 'text',
    wrap: true,
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 14,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.ruleCheck'),
    item: 'personalInfo',
    type: 'checkbox',
    value: false,
    required: () => true,
    isVisible: () => true,
  },
]

export const EDIT_FORM = [
  {
    label: t => t('register.form.country'),
    item: 'country',
    type: 'select',
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.ceoName'),
    item: 'ceoName',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.ceoNameKana'),
    append: t => t('register.form.kana'),
    item: 'ceoNameKana',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
    katakanaCheck: true,
  },
  {
    label: t => t('register.form.ceoBirthday'),
    item: 'ceoBirthday',
    type: 'date',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 10,
    required: country => country !== 'JP',
    isVisible: country => country !== 'JP',
  },
  {
    label: t => t('register.form.companyName'),
    item: 'companyName',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.companyNameKana'),
    append: t => t('register.form.kana'),
    item: 'companyNameKana',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 100,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
    katakanaCheck: true,
  },
  {
    label: t => t('register.form.companyAddress'),
    item: 'companyAddress',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: '',
    value: '',
    length: 255,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.establishmentDate'),
    item: 'establishmentDate',
    type: 'date',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: 'yyyy/mm/dd',
    value: '',
    length: 10,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.companyHp'),
    item: 'companyHp',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: '',
    value: '',
    length: 255,
    required: () => false,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.businessContent'),
    item: 'businessContent',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: '',
    value: '',
    length: 255,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.invoiceNo'),
    item: 'invoiceNo',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: '',
    value: '',
    length: 15,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.telCountryCode'),
    append: t => t('register.form.halfWidthNum'),
    item: 'telCountryCode',
    type: 'text',
    class: `${classNames.tel}`,
    wrap: true,
    placeholder: '',
    value: '',
    length: 10,
    required: country => country !== 'JP',
    isVisible: () => false, // always hidden, data will be set from Tel component
  },
  {
    label: t => t('register.form.tel'),
    append: t => t('register.form.halfWidthNum'),
    item: 'tel',
    type: 'text',
    class: `${classNames.tel}`,
    wrap: true,
    placeholder: '例）03-1111-2222',
    value: '',
    length: 20,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.antiquePermitNo'),
    item: 'antiquePermitNo',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 12,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.antiquePermitDate'),
    item: 'antiquePermitDate',
    type: 'date',
    wrap: true,
    class: `${classNames.fontM}`,
    placeholder: 'yyyy/mm/dd',
    value: '',
    length: 15,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.antiquePermitCommission'),
    item: 'antiquePermitCommission',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 50,
    required: country => country === 'JP',
    isVisible: country => country === 'JP',
  },
  {
    label: t => t('register.form.memberName'),
    item: 'memberName',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 20,
    required: () => true,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.memberLastName'),
    item: 'memberLastName',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 20,
    required: country => country === 'JP',
    isVisible: () => false, // always hidden, data will be set from FirstLastName component
  },
  {
    label: t => t('register.form.email'),
    item: 'email',
    type: 'text',
    wrap: true,
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 255,
    required: () => true,
    isVisible: () => true,
    disabled: true,
    /*
     * Pattern: PATTERN.EMAIL,
     * errorMsg: 'メールアドレスを確認してください。'
     */
  },
  {
    label: t => t('register.form.emailLang'),
    item: 'emailLang',
    type: 'emailLang',
    wrap: '',
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 10,
    required: () => true,
    isVisible: () => true,
    disabled: true,
  },
  {
    label: t => t('register.form.whatsApp'),
    item: 'whatsApp',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 255,
    required: () => false,
    isVisible: country => country !== 'JP',
  },
  {
    label: t => t('register.form.weChat'),
    item: 'weChat',
    type: 'text',
    wrap: true,
    class: `${classNames.fontM}`,
    value: '',
    length: 255,
    required: () => false,
    isVisible: country => country !== 'JP',
  },
  {
    label: t => t('register.form.currentPassword'),
    item: 'currentPassword',
    type: 'text',
    wrap: true,
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 14,
    required: () => false,
    isVisible: () => true,
    // Pattern: PATTERN.PASSWORD
  },
  {
    label: t => t('register.form.password'),
    item: 'password',
    type: 'text',
    wrap: true,
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 14,
    required: () => false,
    isVisible: () => true,
    // Pattern: PATTERN.PASSWORD
  },
  {
    label: t => t('register.form.passwordConfirm'),
    item: 'passwordConfirm',
    type: 'text',
    wrap: true,
    class: `${classNames.fontIme} ${classNames.fontM}`,
    value: '',
    length: 14,
    required: () => false,
    isVisible: () => true,
  },
  {
    label: t => t('register.form.ruleCheck'),
    item: 'personalInfo',
    type: 'checkbox',
    value: false,
    tdClass: 'agree',
    required: () => true,
    isVisible: () => true,
  },
]

export const ASC_BID_QUANTITY = 1

export const privacyPolicyLink =
  'https://geo-online.co.jp/information/privacy.html?td_seg=tds612141tds332476tds647896tds591581tds515926tds332475tds758634tds332477tds612033tds612049tds563650tds612252tds758598tds678273tds593812tds709589tds750547tds723958tds758578tds539694tds758602tds759330tds563644'
