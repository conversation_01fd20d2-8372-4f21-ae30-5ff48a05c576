-- =====================================================
-- Check which tables have tenant_no column
-- Run this in pgAdmin4 to identify tables with tenant_no
-- =====================================================

-- Query to find all tables with tenant_no column
SELECT 
    t.table_name,
    c.column_name,
    c.data_type,
    c.is_nullable
FROM 
    information_schema.tables t
    JOIN information_schema.columns c ON t.table_name = c.table_name
WHERE 
    t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    AND c.column_name = 'tenant_no'
ORDER BY 
    t.table_name;

-- Query to find all tables in public schema (for comparison)
SELECT 
    table_name,
    table_type
FROM 
    information_schema.tables
WHERE 
    table_schema = 'public'
    AND table_type = 'BASE TABLE'
ORDER BY 
    table_name;

-- Check specific tables mentioned in RLS policies
SELECT 
    table_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = t.table_name 
            AND column_name = 'tenant_no'
            AND table_schema = 'public'
        ) THEN 'HAS tenant_no'
        ELSE 'MISSING tenant_no'
    END as tenant_column_status
FROM (
    VALUES 
    ('m_admin'),
    ('m_field'),
    ('m_field_localized'),
    ('m_constant'),
    ('m_constant_localized'),
    ('m_member'),
    ('m_user'),
    ('t_member_request'),
    ('t_member_status_history'),
    ('t_login'),
    ('t_item'),
    ('t_item_localized'),
    ('t_item_ancillary_file'),
    ('t_stock_item_favorite'),
    ('t_stock_result'),
    ('t_lot'),
    ('t_lot_detail'),
    ('t_exhibition'),
    ('t_exhibition_localized'),
    ('t_exhibition_item'),
    ('t_exhibition_email'),
    ('t_exhibition_email_localized'),
    ('t_exhibition_item_favorite'),
    ('t_exhibition_member'),
    ('t_exhibition_result'),
    ('t_exhibition_result_detail'),
    ('t_exhibition_summary'),
    ('t_bid'),
    ('t_bid_history'),
    ('t_notice_email'),
    ('t_notice_email_localized'),
    ('t_notice'),
    ('t_notice_localized'),
    ('t_inquiry'),
    ('t_email_notification'),
    ('t_web_socket_connection'),
    ('t_batch_result'),
    ('t_exhibition_message'),
    ('t_member_search_history'),
    ('t_delivery_message')
) AS t(table_name)
ORDER BY 
    tenant_column_status, table_name;
