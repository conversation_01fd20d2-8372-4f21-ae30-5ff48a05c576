CREATE OR REPLACE FUNCTION public.f_get_member_status_for_login(
    IN in_tenant_no bigint,
    IN in_email character varying
)
RETURNS TABLE(
    tenant_no bigint,
    member_request_no bigint,
    email character varying,
    password character varying,
    status integer,
    require_password_change integer,
    language character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- Emailで会員ステータスを取得する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_email character varying
----------------------------------------------------------------------------------------------------

BEGIN

  IF EXISTS (
    SELECT 1 FROM m_member
     WHERE (m_member.free_field->>'email')::character varying = in_email
     AND m_member.delete_flag = 0
     ) THEN
    RETURN QUERY
    -- 会員と会員申請マスタ
    SELECT M.tenant_no,
          M.member_request_no,
          (M.free_field->>'email')::character varying AS email,
          U.password,
          M.status,
          U.require_password_change,
          (M.free_field->>'lang')::character varying AS language
     FROM m_member M
     JOIN m_tenant T ON T.tenant_no = M.tenant_no
                    AND T.tenant_no = in_tenant_no
     LEFT JOIN t_member_request MR ON M.member_request_no = MR.member_request_no
                                   AND MR.tenant_no = T.tenant_no
                                   AND MR.delete_flag = 0
     LEFT JOIN m_user U ON U.member_no = M.member_no
     WHERE M.delete_flag = 0
       AND (M.free_field->>'email')::character varying = in_email;
  ELSE
    RETURN QUERY
    -- 会員と会員申請マスタ
    SELECT MR.tenant_no,
          MR.member_request_no,
          (MR.free_field->>'email')::character varying AS email,
          (MR.free_field->>'password')::character varying AS password,
          MR.status,
          0 AS require_password_change,
          (MR.free_field->>'lang')::character varying AS language
     FROM t_member_request MR
     JOIN m_tenant T ON T.tenant_no = MR.tenant_no
                    AND T.tenant_no = in_tenant_no
     WHERE MR.delete_flag = 0
       AND (MR.free_field->>'email')::character varying = in_email;
  END IF;

END;

$BODY$;
