CREATE OR REPLACE FUNCTION public.f_get_new_notices (
    in_tenant_no bigint,
    in_lang_code character varying,
    in_limit integer,
    in_display_code integer[]
)
RETURNS TABLE(
    notice_no bigint,
    tenant_no bigint,
    title text,
    title1 text,
    sub_title text,
    body text,
    link_url character varying,
    file text[],
    is_new boolean,
    display_code integer,
    create_date text
)

LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 新規お知らせを取得する
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH new_constants AS(
    SELECT mcl.value1
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = in_tenant_no
      AND mcl.language_code = in_lang_code
      AND mc.key_string = 'NOTICE_LIST_NEW'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
  )

  SELECT TN.notice_no,
          TN.tenant_no,
          TNL.title,
          TNL.title1,
          TNL.sub_title,
          TNL.body,
          TNL.link_url,
          TNL.file,
          (CASE
            WHEN CAST(NC.value1 AS integer) <= 1 THEN TN.start_datetime > (now() - CAST(NC.value1 || ' day' AS interval))
            ELSE TN.start_datetime > (now() - CAST(NC.value1 || ' days' AS interval))
          END) AS is_new,
          TN.display_code,
          to_char(TN.start_datetime, 'YYYY/MM/DD')
    FROM t_notice TN
    JOIN t_notice_localized TNL
      ON TN.notice_no = TNL.notice_no
    JOIN m_tenant MT
      ON MT.tenant_no = TN.tenant_no
    LEFT JOIN new_constants NC
      ON NC.value1 IS NOT NULL
    WHERE (in_display_code IS NULL
           OR array_length(in_display_code, 1) IS NULL
           OR TN.display_code = ANY(in_display_code))
     AND now() BETWEEN TN.start_datetime AND TN.end_datetime
     AND TNL.language_code = in_lang_code
     AND TN.tenant_no = in_tenant_no
     AND TN.delete_flag = 0
     AND TNL.delete_flag = 0
     ORDER BY TN.start_datetime DESC, TN.notice_no DESC
    LIMIT in_limit
   ;
END;

$BODY$;
