CREATE OR REPLACE FUNCTION public.f_insert_inquiry_request (
    in_tenant_no bigint,
    in_classification bigint,
    in_free_field jsonb,
    in_contents character varying,
    in_file text[],
    in_status integer,
    in_member_no bigint
)
RETURNS TABLE(
    member_request_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： 問い合わせ登録
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    INSERT INTO t_inquiry (
      tenant_no,
      classification,
      free_field,
      contents,
      file,
      status,
      create_member_no,
      create_datetime
    )
    VALUES
    (
      in_tenant_no,
      in_classification,
      in_free_field,
      in_contents,
      in_file,
      in_status,
      in_member_no,
      now()
    )
    RETURNING
      t_inquiry.inquiry_no;

END;

$BODY$;
