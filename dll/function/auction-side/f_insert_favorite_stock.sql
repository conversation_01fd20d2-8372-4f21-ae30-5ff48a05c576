CREATE OR REPLACE FUNCTION public.f_insert_favorite_stock (
    in_tenant_no bigint,
    in_member_no bigint,
    in_user_no bigint,
    in_item_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- お気に入り登録
----------------------------------------------------------------------------------------------------

BEGIN

  IF NOT EXISTS (
    SELECT 1
      FROM t_stock_item_favorite
      WHERE item_no = in_item_no
        AND member_no = in_member_no
  ) THEN

    INSERT INTO t_stock_item_favorite (
      tenant_no,
      item_no,
      member_no,
      classification,
      create_user_no
    )
    VALUES
    (
      in_tenant_no,
      in_item_no,
      in_member_no,
      1,
      in_user_no
    );

    UPDATE t_item TI
       SET favorite_count = (SELECT COUNT(*) FROM t_stock_item_favorite WHERE item_no = in_item_no)
     WHERE TI.item_no = in_item_no;

  END IF;

  result := true;
  status := 200;
  message := '';

RETURN NEXT;

END;
$BODY$;
