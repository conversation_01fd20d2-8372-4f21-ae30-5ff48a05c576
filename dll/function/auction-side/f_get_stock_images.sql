CREATE OR REPLACE FUNCTION public.f_get_stock_images (
    in_item_no bigint,
    in_tenant_no bigint,
    in_lang_code character varying
)
RETURNS TABLE(
    file_path character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 商品画像を一括ダウンロード
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TIAF.file_path
    FROM t_item TI
    LEFT JOIN t_item_ancillary_file TIAF
      ON TIAF.manage_no = TI.manage_no
     AND TIAF.item_no = TI.item_no
     AND TIAF.tenant_no = in_tenant_no
     AND (TIAF.language_code = in_lang_code OR TIAF.language_code = 'common')
     AND TIAF.delete_flag = 0
     WHERE TI.item_no = in_item_no
       AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
       AND TIAF.division = 1;
END;

$BODY$;
