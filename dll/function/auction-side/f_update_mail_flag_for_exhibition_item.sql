CREATE OR REPLACE FUNCTION public.f_update_mail_flag_for_exhibition_item (
    in_tenant_no bigint,
    in_exhibition_item_nos bigint[]
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE t_exhibition_item TEI
    SET mail_flag = 1
    WHERE TEI.tenant_no = in_tenant_no
      AND in_exhibition_item_nos IS NOT NULL
      AND TEI.exhibition_item_no = ANY(in_exhibition_item_nos);

    IF FOUND THEN
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql;
