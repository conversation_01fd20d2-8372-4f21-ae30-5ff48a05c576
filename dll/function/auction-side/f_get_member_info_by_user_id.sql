CREATE OR REPLACE FUNCTION public.f_get_member_info_by_user_id (
    in_user_id character varying,
    in_tenant_no bigint
)
RETURNS TABLE(
    user_id character varying,
    member_id character varying,
    free_field jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT MU.user_id,
         MM.member_id,
         MM.free_field
       FROM m_user MU
       LEFT JOIN m_member MM
         ON MU.member_no = MM.member_no
      WHERE MU.tenant_no = in_tenant_no
        AND MU.user_id = in_user_id
        AND MU.member_no = MM.member_no
      LIMIT 1;
END;

$BODY$;
