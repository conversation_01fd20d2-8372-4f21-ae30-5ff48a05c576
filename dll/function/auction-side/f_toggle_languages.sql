CREATE OR REPLACE FUNCTION public.f_toggle_languages(
    in_tenant_no bigint,
    in_member_no bigint,
    in_user_no bigint,
    in_lang character varying
)
RETURNS TABLE (
    result character varying,
    status integer,
    message character varying
)
LANGUAGE plpgsql
AS $BODY$
BEGIN
    -- Check if the member exists
    IF NOT EXISTS (
        SELECT 1
        FROM m_member
        WHERE member_no = in_member_no
    ) THEN
        RETURN QUERY SELECT
            ''::character varying AS result,
            404 AS status,
            'Member not found.'::character varying AS message;
        RETURN;
    END IF;

    -- Update the free_field column
    UPDATE m_member
    SET free_field =
        CASE
            WHEN free_field IS NULL THEN jsonb_build_object('lang', in_lang) -- Add lang if free_field is NULL
            WHEN NOT (free_field ? 'lang') THEN jsonb_set(free_field, '{lang}', to_jsonb(in_lang::text)) -- Add lang if it does not exist
            ELSE jsonb_set(free_field, '{lang}', to_jsonb(in_lang::text)) -- Update lang if it exists
        END
    WHERE member_no = in_member_no;

    -- Check if the update was successful
    IF NOT FOUND THEN
        RETURN QUERY SELECT
            ''::character varying AS result,
            500 AS status,
            'Failed to update the free_field.'::character varying AS message;
        RETURN;
    END IF;

    -- Return updated language and status 200
    RETURN QUERY SELECT
        in_lang AS result,
        200 AS status,
        'Language updated successfully.'::character varying AS message;
END;
$BODY$;
