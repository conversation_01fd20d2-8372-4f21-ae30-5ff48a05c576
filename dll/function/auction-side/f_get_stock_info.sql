CREATE OR REPLACE FUNCTION public.f_get_stock_info (
    in_item_no bigint,
    in_tenant_no bigint,
    in_lang_code character varying,
    in_member_no bigint
)
RETURNS TABLE(
    exhibition_item_no bigint,
    manage_no character varying,
    free_field jsonb,
    image json[],
    attention_info json
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 在庫機情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TI.item_no,
         TI.manage_no,
         CASE WHEN in_member_no IS NOT NULL THEN TIL.free_field
              ELSE TIL.free_field || MV.masked
         END,
         IM.images,
         CASE WHEN in_member_no IS NOT NULL
              THEN json_build_object(
                'is_favorited', TSIF.favorite_no IS NOT NULL
              )
          ELSE NULL END
    FROM t_item TI
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
     AND TIL.tenant_no = in_tenant_no
     AND TIL.language_code = in_lang_code
     AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
    LEFT JOIN t_stock_item_favorite TSIF
      ON TSIF.item_no = TI.item_no
     AND TSIF.tenant_no = in_tenant_no
     AND TSIF.member_no = in_member_no
    LEFT JOIN t_item_ancillary_file TIAF
      ON TIAF.manage_no = TI.manage_no
     AND TIAF.tenant_no = in_tenant_no
     AND TIAF.item_no = TI.item_no
     AND (TIAF.language_code = in_lang_code OR TIAF.language_code = 'common')
     AND TIAF.delete_flag = 0
   CROSS JOIN (
       SELECT json_build_object(
               UPPER(MF.physical_name), MFL.masked_value
             )::jsonb AS masked
         FROM m_field MF
         LEFT JOIN m_field_localized MFL
           ON MF.tenant_no = MFL.tenant_no
          AND MF.field_no = MFL.field_no
          AND MFL.language_code = in_lang_code
        WHERE MFL.masked_value IS NOT NULL
          AND MF.tenant_no = in_tenant_no
          AND MF.field_division = 'item'
     ) MV
   CROSS JOIN (
       SELECT array_agg(
         json_build_object(
           'file_path', TIAF.file_path,
           'postar_file_path', TIAF.postar_file_path
         )
         ORDER BY CASE WHEN TIAF.postar_file_path IS NULL THEN 1 ELSE 0 END
       ) FILTER (WHERE TIAF.division = 1) AS images
         FROM t_item TI
         LEFT JOIN t_item_ancillary_file TIAF
           ON TIAF.manage_no = TI.manage_no
          AND TIAF.tenant_no = TI.tenant_no
          AND TIAF.item_no = TI.item_no
          AND (TIAF.language_code = in_lang_code OR TIAF.language_code = 'common')
          AND TIAF.delete_flag = 0
        WHERE TI.item_no = in_item_no
          AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
          AND TI.tenant_no = in_tenant_no
     ) IM
     WHERE TI.item_no = in_item_no
       AND (TI.delete_flag IS NULL OR TI.delete_flag = 0);
END;

$BODY$;
