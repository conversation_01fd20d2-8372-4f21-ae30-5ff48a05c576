CREATE OR REPLACE FUNCTION public.f_update_member_password (
    in_tenant_no bigint,
    in_member_no bigint,
    in_new_password character varying DEFAULT NULL
)
RETURNS TABLE(
    member_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： 会員パスワード変更
/************************************************************************/
DECLARE
BEGIN
  RETURN QUERY
  -- m_user更新
  WITH update_user AS (
    UPDATE m_user U
       SET password = COALESCE(in_new_password, U.password),
           require_password_change = 0,
           update_datetime = now()
     WHERE U.tenant_no = in_tenant_no
        AND U.member_no = in_member_no
        AND in_new_password IS NOT NULL
    RETURNING
          U.member_no
  )
  SELECT UU.member_no FROM update_user UU;
END;

$BODY$;
