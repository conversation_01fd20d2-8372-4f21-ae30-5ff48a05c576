CREATE OR REPLACE FUNCTION public.f_search_stock_items_for_csv (
    in_tenant_no bigint,
    in_search_key character varying,
    in_areas character varying[],
    in_category jsonb[],
    in_start_year numeric,
    in_end_year numeric,
    in_start_price numeric,
    in_end_price numeric,
    in_favorite boolean,
    in_bidding boolean,
    in_un_sold_out boolean,
    in_exceeding_lowest_price boolean,
    in_lang_code character varying,
    in_member_no bigint
)
RETURNS TABLE(
    lotId text,
    maker text,
    model text,
    unit text,
    model_year text,
    load_quantity text,
    outrigger text,
    awameter text,
    mileage text,
    storage_location text,
    inspection text,
    crane_inspection text,
    hook text,
    specifications text,
    cab text,
    undercarriage text,
    price text
)

LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 在庫機一覧CSV
----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  WITH filter_item AS (
    SELECT TI.item_no,
           TIL.free_field->>'MDLGRID' AS category_id
      FROM t_item TI
      JOIN t_item_localized TIL
        ON TIL.item_no = TI.item_no
       AND TIL.tenant_no = in_tenant_no
       AND TIL.language_code = in_lang_code
       AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      LEFT JOIN t_stock_item_favorite TSIF
        ON TSIF.item_no = TI.item_no
       AND TSIF.tenant_no = in_tenant_no
       AND TSIF.member_no = in_member_no
      JOIN (
              SELECT category->>'categoryNo' AS category_id,
                     NULLIF(split_part(TRIM(REPLACE(category->>'extension','t','')), '-', 1), '') AS abil_start,
                     NULLIF(split_part(TRIM(REPLACE(category->>'extension','t','')), '-', 2), '') AS abil_end
                FROM unnest(COALESCE(in_category, '{"{\"categoryNo\" : null}"}')::jsonb[]) category
           ) CF
        ON TIL.free_field IS NOT NULL
       AND (CF.category_id IS NULL OR TIL.free_field->>'MDLGRID' = CF.category_id)
       AND (CF.abil_start IS NULL OR COALESCE(REPLACE(TIL.free_field->>'ABIL', '-', '0')::numeric, 0) >= CF.abil_start::numeric)
       AND (CF.abil_end IS NULL OR COALESCE(REPLACE(TIL.free_field->>'ABIL', '-', '0')::numeric, 0) <= CF.abil_end::numeric)
     WHERE TI.status = 1
       AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
       AND (
            in_search_key IS NULL
            OR TIL.free_field->>'MAKER' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'MODEL' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'MDLGR' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'ACTSERNO' ~ f_escape_string(in_search_key)
           )
      AND (in_start_year IS NULL OR (TIL.free_field->>'YYYY' ~ '^\d+$' AND (TIL.free_field->>'YYYY')::numeric >= in_start_year::numeric))
      AND (in_end_year IS NULL OR (TIL.free_field->>'YYYY' ~ '^\d+$' AND (TIL.free_field->>'YYYY')::numeric <= in_end_year::numeric))
      AND (in_start_price IS NULL OR (TIL.free_field->>'NEWSPRC' ~ '^\d+$' AND (TIL.free_field->>'NEWSPRC')::numeric >= in_start_price::numeric))
      AND (in_end_price IS NULL OR (TIL.free_field->>'NEWSPRC' ~ '^\d+$' AND (TIL.free_field->>'NEWSPRC')::numeric <= in_end_price::numeric))
      AND (in_areas IS NULL OR TIL.free_field->>'AREAID' = ANY(in_areas))
      AND (
           in_favorite IS NULL
           OR NOT in_favorite
           OR TSIF.favorite_no IS NOT NULL
          )
      AND (in_un_sold_out IS NULL OR NOT in_un_sold_out OR NOT (TI.status = 3 AND TI.price_display_flag = 2))
      AND TI.item_no IS NOT NULL
  )
    SELECT '',
           TIL.free_field->>'MAKER',
           TIL.free_field->>'MODEL',
           TIL.free_field->>'ACTSERNO',
           TIL.free_field->>'YYYY',
           TIL.free_field->>'ABIL',
           TIL.free_field->>'OUTRIGR',
           TIL.free_field->>'OPETIM',
           TIL.free_field->>'MILEAGE',
           TIL.free_field->>'SYBSNM',
           TIL.free_field->>'DAYCARI',
           TIL.free_field->>'DAYCRAN',
           TIL.free_field->>'HOOK',
           TIL.free_field->>'OTHERSY',
           TIL.free_field->>'CAB',
           TIL.free_field->>'UDRCARR',
           CASE WHEN TI.price_display_flag = 0 THEN 'ASK'
                WHEN TI.price_display_flag = 2 THEN 'SOLD OUT'
                ELSE TIL.free_field->>'NEWSPRC' ::text 
            END
        FROM t_item TI
        JOIN t_item_localized TIL
          ON TIL.item_no = TI.item_no
         AND TIL.tenant_no = in_tenant_no
         AND TIL.language_code = in_lang_code
         AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
        JOIN filter_item FI
          ON TI.item_no = FI.item_no
          ;

RETURN NEXT;

END;

$BODY$;
