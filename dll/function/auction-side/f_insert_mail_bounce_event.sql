CREATE OR REPLACE FUNCTION public.f_insert_mail_bounce_event (
    in_tenant_no bigint,
    in_notification_type character varying,
    in_subject character varying,
    in_from_address character varying,
    in_to_address character varying,
    in_message jsonb
)
RETURNS TABLE(
    email_notification_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： バウンスメールアラーム追加
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    INSERT INTO t_email_notification (
      tenant_no,
      notification_type,
      subject,
      from_address,
      to_address,
      message,
      create_datetime
    )
    VALUES
    (
      in_tenant_no,
      in_notification_type,
      in_subject,
      in_from_address,
      in_to_address,
      in_message,
      now()
    )
    RETURNING
          t_email_notification.email_notification_no;

END;

$BODY$;
