CREATE OR REPLACE FUNCTION public.f_get_web_socket_connection (
    in_connection_id character varying,
    in_tenant_no bigint,
    in_member_no bigint
)
RETURNS TABLE(
    connection_id character varying,
    member_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： WebSocket接続取得
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    SELECT WS.connection_id,
            WS.member_no
    FROM t_web_socket_connection WS
    WHERE WS.connection_id = in_connection_id
    AND WS.tenant_no = in_tenant_no
    AND WS.member_no = in_member_no;

END;

$BODY$;
