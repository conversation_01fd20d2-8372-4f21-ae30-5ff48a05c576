CREATE OR REPLACE FUNCTION public.f_regist_payment (
    in_tenant_no bigint,
    in_exhibition_item_no bigint,
    in_payment_flag integer,
    in_payment_field jsonb
)
RETURNS TABLE(
    exhibition_result_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 支払情報登録
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  UPDATE t_exhibition_result TER
     SET payment_flag = in_payment_flag,
         payment_field = in_payment_field
   WHERE TER.tenant_no = in_tenant_no
     AND TER.exhibition_item_no = in_exhibition_item_no
  RETURNING TER.exhibition_result_no;
END;

$BODY$;
