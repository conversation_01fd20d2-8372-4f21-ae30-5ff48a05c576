CREATE OR REPLACE FUNCTION public.f_update_member_info (
    in_tenant_no bigint,
    in_member_no bigint,
    in_member_free_field jsonb,
    in_new_password character varying DEFAULT NULL,
    in_new_status integer DEFAULT NULL
)
RETURNS TABLE(
    member_no bigint,
    member_id character varying,
    free_field jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： 会員情報変更
/************************************************************************/
DECLARE
BEGIN
  RETURN QUERY
  WITH update_member AS (
    UPDATE m_member MM
       SET free_field = MM.free_field || in_member_free_field,
           status = COALESCE(in_new_status, status),
           email_delivery_flag = (CASE WHEN in_new_status IS NULL THEN email_delivery_flag ELSE (CASE WHEN in_new_status = 1 THEN 1 ELSE 0 END) END),
           bid_allow_flag = (CASE WHEN in_new_status IS NULL THEN bid_allow_flag ELSE (CASE WHEN in_new_status = 1 THEN 1 ELSE 0 END) END),
           update_datetime = now()
     WHERE MM.member_no = in_member_no
       AND MM.tenant_no = in_tenant_no
    RETURNING
          MM.member_no,
          MM.member_id,
          MM.free_field
  ),
  -- m_user更新
  update_user AS (
    UPDATE m_user U
       SET user_id = (CASE WHEN T.login_option = 1 THEN U.user_id
                           WHEN T.login_option = 2 THEN M.free_field->>'email'::character varying
                           ELSE U.user_id
                      END),
           password = COALESCE(in_new_password, U.password),
           update_datetime = now()
      FROM update_member M
      JOIN m_tenant T ON T.tenant_no = in_tenant_no
     WHERE U.member_no = in_member_no
       AND (in_new_password IS NOT NULL OR M.free_field->>'email' IS NOT NULL)
    RETURNING
          U.member_no
  )
  SELECT UM.member_no,
         UM.member_id,
         UM.free_field
    FROM update_member UM
    LEFT JOIN update_user UU
      ON UM.member_no = UM.member_no;
END;

$BODY$;
