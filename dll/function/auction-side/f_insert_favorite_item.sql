CREATE OR REPLACE FUNCTION public.f_insert_favorite_item (
    in_tenant_no bigint,
    in_member_no bigint,
    in_user_no bigint,
    in_exhibition_item_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- お気に入り登録
----------------------------------------------------------------------------------------------------

BEGIN

  IF NOT EXISTS (
    SELECT 1
      FROM t_exhibition_item_favorite
      WHERE exhibition_item_no = in_exhibition_item_no
        AND member_no = in_member_no
  ) THEN

    INSERT INTO t_exhibition_item_favorite (
      tenant_no,
      exhibition_item_no,
      member_no,
      classification,
      create_user_no
    )
    VALUES
    (
      in_tenant_no,
      in_exhibition_item_no,
      in_member_no,
      1,
      in_user_no
    );

    UPDATE t_exhibition_item TEI
       SET favorite_count = (SELECT COUNT(*)
                               FROM t_exhibition_item_favorite
                              WHERE exhibition_item_no = in_exhibition_item_no)
     WHERE TEI.exhibition_item_no = in_exhibition_item_no;

  END IF;

  result := true;
  status := 200;
  message := '';

RETURN NEXT;

END;
$BODY$;
