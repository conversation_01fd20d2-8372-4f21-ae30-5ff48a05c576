CREATE OR REPLACE FUNCTION public.f_refresh_stock_and_count_up_view (
    in_item_no bigint,
    in_tenant_no bigint,
    in_lang_code character varying,
    in_member_no bigint
)
RETURNS TABLE(
    exhibition_item_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会商品取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  UPDATE t_item TI
     SET view_count = COALESCE(TI.view_count, 0) + 1
   WHERE TI.item_no = in_item_no
  RETURNING TI.item_no;
END;

$BODY$;
