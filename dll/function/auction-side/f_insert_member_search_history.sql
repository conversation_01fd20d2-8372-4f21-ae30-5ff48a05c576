CREATE OR R<PERSON>LACE FUNCTION public.f_insert_member_search_history (
    in_tenant_no bigint,
    in_keyword character varying,
    in_member_no bigint
)
RETURNS TABLE(
    member_search_history_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 会員の検索履歴を登録する
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  INSERT INTO t_member_search_history
  (
    tenant_no,
    member_no,
    keyword
  )
  SELECT
    in_tenant_no as tenant_no,
    in_member_no as member_no,
    in_keyword as keyword
  WHERE in_keyword IS NOT NULL
    AND length(in_keyword) > 0
    AND in_member_no IS NOT NULL
    AND NOT EXISTS (
      SELECT 1
        FROM t_member_search_history TMSH2
       WHERE TMSH2.member_no = in_member_no
         AND TMSH2.keyword LIKE '%' || in_keyword || '%'
    )
  RETURNING t_member_search_history.member_search_history_no
  ;

END;
$BODY$;
