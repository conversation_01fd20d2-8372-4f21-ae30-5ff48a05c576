CREATE OR REPLACE FUNCTION public.f_insert_login (
    in_tenant_no bigint,
    in_member_no bigint,
    in_user_no bigint,
    in_user_name character varying,
    in_login_id character varying,
    in_ip character varying,
    in_user_agent character varying
)
RETURNS TABLE(
    login_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： ログインログ追加
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    INSERT INTO t_login (
      tenant_no,
      member_no,
      user_no,
      user_name,
      login_id,
      ip,
      user_agent
    )
    VALUES
    (
      in_tenant_no,
      in_member_no,
      in_user_no,
      in_user_name,
      in_login_id,
      in_ip,
      in_user_agent
    )
    RETURNING
      t_login.login_no;

END;

$BODY$;
