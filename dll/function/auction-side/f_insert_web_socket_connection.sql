CREATE OR REPLACE FUNCTION public.f_insert_web_socket_connection (
    in_connection_id character varying,
    in_tenant_no bigint,
    in_member_no bigint
)
RETURNS TABLE(
    connection_id character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： WebSocket接続追加
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    INSERT INTO t_web_socket_connection (
      connection_id,
      tenant_no,
      member_no
    )
    VALUES
    (
      in_connection_id,
      in_tenant_no,
      in_member_no
    )
    RETURNING
      t_web_socket_connection.connection_id;

END;

$BODY$;
