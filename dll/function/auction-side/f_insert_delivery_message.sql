CREATE OR REPLACE FUNCTION public.f_insert_delivery_message (
  in_tenant_no bigint,
  in_exhibition_item_no bigint,
  in_message character varying,
  in_member_no bigint
)
  RETURNS TABLE(
    delivery_message_no bigint
  )
    LANGUAGE 'plpgsql'

    COST 100
    VOLATILE
    ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： 配送チャットメッセージ追加
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    INSERT INTO t_delivery_message (
      tenant_no,
      exhibition_item_no,
      update_category_id,
      member_no,
      message,
      create_user_no,
      create_datetime
    )
    VALUES
    (
      in_tenant_no,
      in_exhibition_item_no,
      2,
      in_member_no,
      in_message,
      in_member_no,
      now()
    )
    RETURNING
      t_delivery_message.delivery_message_no;

END;

$BODY$;
