CREATE OR REPLACE FUNCTION public.f_get_successful_bid_item(
    in_tenant_no bigint,
    in_member_no bigint,
    in_exhibition_item_no bigint,
    in_lang_code character varying
)
RETURNS TABLE(
    exhibition_no bigint,
    exhibition_item_no bigint,
    manage_no character varying,
    area_id character varying,
    end_datetime timestamp with time zone,
    auction_classification_name character varying,
    lot_no bigint,
    lot_id character varying,
    item_no bigint,
    free_field jsonb,
    current_price numeric,
    bid_price numeric,
    file_path character varying,
    bid_user_name character varying,
    order_no integer,
    tax_rate numeric
)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員の落札情報を取得する
-- Parameters
-- @param in_tenant_no テナント番号
-- @params in_member_no 会員番号
-- @params in_exhibition_item_no 商品番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT te.exhibition_no
       , tei.exhibition_item_no
       , ti.manage_no
       , ti.area_id
       , tei.end_datetime
       , mcl.value2 auction_classification_name
       , tei.lot_no
       , tl.lot_id
       , ti.item_no
       , til.free_field
       , tei.current_price
       , tbh.bid_price
       , tiaf.file_path
       , tbh.bid_user_name
       , tld.order_no
       , ter.tax_rate
    FROM t_exhibition te
         INNER JOIN t_exhibition_item tei
                 ON te.exhibition_no = tei.exhibition_no
                AND top_member_no = in_member_no
                AND hummer_flag = 1
         INNER JOIN t_lot tl
                 ON tei.lot_no = tl.lot_no
         INNER JOIN t_lot_detail tld
                 ON tei.lot_no = tld.lot_no
         INNER JOIN t_item ti
                 ON tld.item_no = ti.item_no
         INNER JOIN t_item_localized til
                 ON ti.item_no = til.item_no
                AND til.language_code = in_lang_code
         INNER JOIN (
           SELECT ROW_NUMBER() OVER (PARTITION BY tbh.exhibition_item_no ORDER BY tbh.create_datetime DESC) ranking
                , tbh.bid_price
                , tbh.exhibition_item_no
                , tbh.bid_user_name
             FROM t_bid_history tbh
            WHERE tbh.member_no = in_member_no
              AND tbh.tenant_no = in_tenant_no
         ) tbh
                 ON tbh.exhibition_item_no = tei.exhibition_item_no
                AND tbh.ranking = 1
    LEFT OUTER JOIN (
      SELECT mcl.value1
           , mcl.value2
        FROM m_constant mc
             INNER JOIN m_constant_localized mcl
                     ON mcl.constant_no = mc.constant_no
       WHERE mc.tenant_no = in_tenant_no
         AND mcl.language_code = in_lang_code
         AND mc.key_string = 'AUCTION_CLASSIFICATION'
         AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    ) mcl
                 ON mcl.value1 = te.exhibition_classification_info->>'auctionClassification' :: character varying
    LEFT OUTER JOIN t_item_ancillary_file tiaf
                 ON ti.manage_no = tiaf.manage_no
                AND tiaf.division = 1
                AND tiaf.item_no = ti.item_no
                AND tiaf.serial_number = 1
                AND tiaf.delete_flag = 0
    LEFT OUTER JOIN t_exhibition_result ter
                 ON ter.exhibition_item_no = tei.exhibition_item_no
   WHERE te.tenant_no = in_tenant_no
     AND te.status = 1
     AND tei.exhibition_item_no = in_exhibition_item_no
     AND ter.bid_success_member_no = in_member_no
;
END;

$BODY$;
