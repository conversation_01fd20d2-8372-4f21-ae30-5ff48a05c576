CREATE OR REPLACE FUNCTION public.f_get_user_for_login (
    in_user_id character varying,
    in_tenant_no bigint
)
RETURNS TABLE(
    user_no bigint,
    member_no bigint,
    tenant_no bigint,
    user_id character varying,
    member_id character varying,
    free_field jsonb,
    password character varying,
    status integer,
    delete_flag integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- ログインチェック用にユーザー情報を取得する
-- Parameters
-- @param in_user_id 利用者ID
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.user_no
       , B.member_no
       , MT.tenant_no
       , A.user_id
       , B.member_id
       , B.free_field
       , A.password
       , B.status
       , A.delete_flag
  FROM m_user A
  LEFT JOIN m_member B
    ON A.member_no = B.member_no
   AND B.tenant_no = A.tenant_no
  LEFT JOIN m_tenant MT
    ON MT.tenant_no = A.tenant_no
 WHERE A.user_id = in_user_id
   AND A.delete_flag = 0
   AND MT.delete_flag = 0
   AND B.status = 1
   AND MT.tenant_no = in_tenant_no
   AND now() > MT.start_datetime
   AND now() < MT.end_datetime;
END;

$BODY$;
