CREATE OR REPLACE FUNCTION public.f_get_item_info (
    in_exhibition_item_no bigint,
    in_tenant_no bigint,
    in_lang_code character varying,
    in_member_no bigint
)
RETURNS TABLE(
    exhibition_item_no bigint,
    lot_id character varying,
    manage_no character varying,
    category_id integer,
    exhibition_name character varying,
    current_price numeric,
    lowest_bid_price numeric,
    exhibition_status integer,
    free_field jsonb,
    image json[],
    attention_info json
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会商品取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TEI.exhibition_item_no,
         TL.lot_id,
         TI.manage_no,
         TE.category_id,
         TEL.exhibition_name,
         COALESCE(TEI.current_price, TEI.lowest_bid_price),
         TEI.lowest_bid_price,
         CASE WHEN now() between TE.end_datetime and TEI.end_datetime THEN 3
              WHEN now() between TE.start_datetime and TE.end_datetime THEN 2
              WHEN now() between TE.preview_start_datetime and TE.start_datetime THEN 1
              WHEN now() between TE.end_datetime and TE.preview_end_datetime THEN 4
              ELSE NULL
         END,
         CASE WHEN in_member_no IS NOT NULL THEN TIL.free_field
              ELSE TIL.free_field || MV.masked
         END,
         IM.images,
         CASE WHEN in_member_no IS NOT NULL
              THEN json_build_object(
                'is_favorited', TEIF.favorite_no IS NOT NULL
              )
          ELSE NULL END
    FROM t_exhibition_item TEI
    JOIN t_exhibition TE
      ON TE.exhibition_no = TEI.exhibition_no
     AND TE.tenant_no = TEI.tenant_no
     AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
    JOIN t_exhibition_localized TEL
      ON TE.exhibition_no = TEL.exhibition_no
     AND TEL.language_code = in_lang_code
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
     AND TL.tenant_no = TEI.tenant_no
     AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
     AND TLD.tenant_no = TEI.tenant_no
     AND TLD.order_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
     AND TI.tenant_no = TEI.tenant_no
     AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
     AND TIL.tenant_no = TEI.tenant_no
     AND TIL.language_code = in_lang_code
     AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
    LEFT JOIN t_exhibition_item_favorite TEIF
      ON TEIF.exhibition_item_no = TEI.exhibition_item_no
     AND TEIF.tenant_no = TEI.tenant_no
     AND TEIF.member_no = in_member_no
   CROSS JOIN (
       SELECT json_build_object(
               UPPER(MF.physical_name), MFL.masked_value
             )::jsonb AS masked
         FROM m_field MF
         LEFT JOIN m_field_localized MFL
           ON MF.tenant_no = MFL.tenant_no
          AND MF.field_no = MFL.field_no
          AND MFL.language_code = in_lang_code
        WHERE MFL.masked_value IS NOT NULL
          AND MF.tenant_no = in_tenant_no
          AND MF.field_division = 'item'
     ) MV
   CROSS JOIN (
       SELECT array_agg(
         json_build_object(
           'file_path', TIAF.file_path,
           'postar_file_path', TIAF.postar_file_path
         )
         ORDER BY CASE WHEN TIAF.postar_file_path IS NULL THEN 1 ELSE 0 END
       ) FILTER (WHERE TIAF.division = 1) AS images
         FROM t_exhibition_item TEI
         JOIN t_lot TL
           ON TL.lot_no = TEI.lot_no
          AND TL.tenant_no = TEI.tenant_no
          AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
         JOIN t_lot_detail TLD
           ON TLD.lot_no = TEI.lot_no
          AND TLD.tenant_no = TEI.tenant_no
         JOIN t_item TI
           ON TLD.item_no = TI.item_no
          AND TI.tenant_no = TEI.tenant_no
          AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
         LEFT JOIN t_item_ancillary_file TIAF
           ON TIAF.manage_no = TI.manage_no
          AND TIAF.item_no = TI.item_no
          AND TIAF.tenant_no = TEI.tenant_no
          AND (TIAF.language_code = in_lang_code OR TIAF.language_code = 'common')
          AND TIAF.delete_flag = 0
        WHERE TEI.exhibition_item_no = in_exhibition_item_no
          AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
     ) IM
     WHERE TEI.exhibition_item_no = in_exhibition_item_no
       AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
       ;
END;

$BODY$;
