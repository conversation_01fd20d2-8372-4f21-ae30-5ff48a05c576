CREATE OR REPLACE FUNCTION public.f_get_tenant_info (
    in_origin character varying
)
RETURNS TABLE(
    tenant_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- テナント情報取得
-- Parameters
-- @param in_user_id 利用者ID
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT domains.tenant_no
  FROM (
    SELECT MT.tenant_no,
           unnest(regexp_split_to_array(trim(MT.domain), ',')) AS domain
      FROM m_tenant MT
      WHERE (MT.delete_flag IS NULL OR MT.delete_flag = 0)
        AND now() > MT.start_datetime
        AND now() < MT.end_datetime
  ) AS domains
 WHERE in_origin LIKE '%' || domains.domain || '%';
END;

$BODY$;
