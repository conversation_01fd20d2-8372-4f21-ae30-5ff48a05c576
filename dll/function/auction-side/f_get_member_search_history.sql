CREATE OR R<PERSON>LACE FUNCTION public.f_get_member_search_history (
    in_tenant_no bigint,
    in_member_no bigint
)
RETURNS TABLE(
    member_no bigint,
    keywords character varying[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 会員の検索履歴を登録する
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TMSH.member_no,
         array_agg(TMSH.keyword) as keywords
  FROM t_member_search_history TMSH
  WHERE TMSH.tenant_no = in_tenant_no
    AND in_member_no IS NOT NULL
    AND TMSH.member_no = in_member_no
  GROUP BY TMSH.member_no
  ;

END;
$BODY$;
