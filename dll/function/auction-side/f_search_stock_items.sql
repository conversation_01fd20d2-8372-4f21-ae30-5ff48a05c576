CREATE OR REPLACE FUNCTION public.f_search_stock_items (
    in_tenant_no bigint,
    in_search_key character varying,
    in_areas character varying[],
    in_category jsonb[],
    in_start_year numeric,
    in_end_year numeric,
    in_start_price numeric,
    in_end_price numeric,
    in_favorite boolean,
    in_bidding boolean,
    in_un_sold_out boolean,
    in_new_stock boolean,
    in_exceeding_lowest_price boolean,
    in_current_price_sort character varying,
    in_limit numeric,
    in_showed_item_nos bigint[],
    in_item_nos text[],
    in_lang_code character varying,
    in_member_no bigint
)
RETURNS TABLE(
    data jsonb
)

LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 在庫機一覧取得
----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  WITH filter_item AS (
    SELECT TI.item_no,
           TIL.free_field->>'MDLGRID' AS category_id,
           array_agg(
             json_build_object(
               'file_path', TIAF.file_path,
               'postar_file_path', TIAF.postar_file_path
             )
             ORDER BY CASE WHEN TIAF.postar_file_path IS NULL THEN TIAF.serial_number ELSE 0 END
           ) FILTER (WHERE TIAF.division = 1) AS images,
           array_agg(TIAF.file_path) FILTER (WHERE TIAF.division = 2) AS pdfs
      FROM t_item TI
      JOIN t_item_localized TIL
        ON TIL.item_no = TI.item_no
       AND TIL.tenant_no = in_tenant_no
       AND TIL.language_code = in_lang_code
       AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      LEFT JOIN t_item_ancillary_file TIAF
        ON TIAF.manage_no = TI.manage_no
       AND TIAF.tenant_no = in_tenant_no
       AND TIAF.item_no = TI.item_no
       AND (TIAF.language_code = in_lang_code OR TIAF.language_code = 'common')
       AND TIAF.delete_flag = 0
      LEFT JOIN t_stock_item_favorite TSIF
        ON TSIF.item_no = TI.item_no
       AND TSIF.tenant_no = in_tenant_no
       AND TSIF.member_no = in_member_no
      JOIN (
              SELECT category->>'categoryNo' AS category_id,
                     NULLIF(split_part(TRIM(REPLACE(category->>'extension','t','')), '-', 1), '') AS abil_start,
                     NULLIF(split_part(TRIM(REPLACE(category->>'extension','t','')), '-', 2), '') AS abil_end
                FROM unnest(COALESCE(in_category, '{"{\"categoryNo\" : null}"}')::jsonb[]) category
           ) CF
        ON TIL.free_field IS NOT NULL
       AND (CF.category_id IS NULL OR TIL.free_field->>'MDLGRID' = CF.category_id)
       AND (CF.abil_start IS NULL OR COALESCE(REPLACE(TIL.free_field->>'ABIL', '-', '0')::numeric, 0) >= CF.abil_start::numeric)
       AND (CF.abil_end IS NULL OR COALESCE(REPLACE(TIL.free_field->>'ABIL', '-', '0')::numeric, 0) <= CF.abil_end::numeric)
     WHERE (TI.status = 1 OR TI.status = 3)
       AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
       AND (
            in_search_key IS NULL
            OR TIL.free_field->>'MAKER' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'MODEL' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'MDLGR' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'ACTSERNO' ~ f_escape_string(in_search_key)
           )
      AND (in_start_year IS NULL OR (TIL.free_field->>'YYYY' ~ '^\d+$' AND (TIL.free_field->>'YYYY')::numeric >= in_start_year::numeric))
      AND (in_end_year IS NULL OR (TIL.free_field->>'YYYY' ~ '^\d+$' AND (TIL.free_field->>'YYYY')::numeric <= in_end_year::numeric))
      AND (in_start_price IS NULL OR (TIL.free_field->>'NEWSPRC' ~ '^\d+$' AND (TIL.free_field->>'NEWSPRC')::numeric >= in_start_price::numeric))
      AND (in_end_price IS NULL OR in_end_price = 0 OR (TIL.free_field->>'NEWSPRC' ~ '^\d+$' AND (TIL.free_field->>'NEWSPRC')::numeric <= in_end_price::numeric))
      AND (in_areas IS NULL OR TIL.free_field->>'AREAID' = ANY(in_areas))
      AND (
           in_favorite IS NULL
           OR NOT in_favorite
           OR TSIF.favorite_no IS NOT NULL
          )
      AND (in_new_stock IS NULL OR NOT in_new_stock OR now() BETWEEN TI.new_start_datetime AND TI.new_end_datetime)
      AND (in_un_sold_out IS NULL OR NOT in_un_sold_out OR NOT (TI.status = 3 AND TI.price_display_flag = 2))
      AND TI.item_no IS NOT NULL
      GROUP BY TI.item_no, TIL.free_field
  ),
  count_all_items AS (
    SELECT count(*) FROM filter_item
  ),
  category_group AS (
    SELECT jsonb_agg(json_build_object(
            'category_id', row.category_id,
            'count', row.count
          )) AS category_group
      FROM (
            SELECT FI.category_id, count(*) FROM filter_item FI GROUP BY FI.category_id
          ) AS row
  ),
  masked_value AS (
    SELECT json_build_object(
            UPPER(MF.physical_name), MFL.masked_value
          )::jsonb AS masked
      FROM m_field MF
      LEFT JOIN m_field_localized MFL
        ON MF.tenant_no = MFL.tenant_no
       AND MF.field_no = MFL.field_no
       AND MFL.language_code = in_lang_code
     WHERE MFL.masked_value IS NOT NULL
       AND MF.tenant_no = in_tenant_no
       AND MF.field_division = 'item'
  ),
  items AS (
      SELECT array_agg(
              json_build_object(
               'exhibition_item_no', TI.item_no,
               'free_field', CASE WHEN in_member_no IS NOT NULL THEN TIL.free_field
                    ELSE TIL.free_field || masked_value.masked
               END,
               'attention_info', CASE WHEN in_member_no IS NULL THEN NULL
                    ELSE json_build_object(
                       'favorited_count', TI.favorite_count,
                       'is_favorited', TSIF.favorite_no IS NOT NULL,
                       'view_count', TI.view_count
                    )
               END,
               'sold_out', TI.status = 3 AND TI.price_display_flag = 2,
               'image', FI.images,
               'pdfs', FI.pdfs,
               'is_new', now() BETWEEN TI.new_start_datetime AND TI.new_end_datetime,
               'price_display_flag', TI.price_display_flag
              )
              ORDER BY
               CASE WHEN in_member_no IS NOT NULL AND in_current_price_sort = 'asc' THEN (TIL.free_field->>'NEWSPRC')::numeric
                    WHEN in_member_no IS NOT NULL AND in_current_price_sort = 'desc' THEN -1 * (TIL.free_field->>'NEWSPRC')::numeric
                    ELSE TI.item_no END
            ) AS items
          FROM t_item TI
          JOIN t_item_localized TIL
            ON TIL.item_no = TI.item_no
           AND TIL.tenant_no = in_tenant_no
           AND TIL.language_code = in_lang_code
           AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
          LEFT JOIN t_stock_item_favorite TSIF
            ON TSIF.item_no = TI.item_no
           AND TSIF.tenant_no = in_tenant_no
           AND TSIF.member_no = in_member_no
          JOIN filter_item FI
            ON TI.item_no = FI.item_no
        CROSS JOIN count_all_items
        CROSS JOIN masked_value
         WHERE (TI.delete_flag IS NULL OR TI.delete_flag = 0)
           AND (in_showed_item_nos IS NULL OR NOT (TI.item_no = ANY(in_showed_item_nos)))
           AND (in_item_nos IS NULL OR TI.item_no::text = ANY(in_item_nos))
         LIMIT in_limit + 1
  )
  SELECT jsonb_build_object(
           'items', IT.items,
           'count', CAI.count,
           'category_group', CF.category_group
         )
   FROM items IT
  CROSS JOIN count_all_items CAI
  CROSS JOIN category_group CF
    ;

RETURN NEXT;

END;

$BODY$;
