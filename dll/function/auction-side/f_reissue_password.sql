CREATE OR REPLACE FUNCTION public.f_reissue_password (
    in_tenant_no bigint,
    in_user_id character varying,
    in_email character varying,
    in_hash_password character varying,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- パスワード更新
-- Parameters
-- @param in_tenant_no
-- @param in_user_id
-- @param in_email
-- @param in_hash_password
----------------------------------------------------------------------------------------------------

BEGIN

  WITH taget_user AS (
     SELECT MU.user_no
       FROM m_user MU
       LEFT JOIN m_member MM
         ON MU.member_no = MM.member_no
      WHERE MU.tenant_no = in_tenant_no
        AND (in_user_id IS NULL OR MU.user_id = in_user_id)
        AND MU.member_no = MM.member_no
        AND MM.free_field->>'email' = in_email
      LIMIT 1
  ),
  update_password AS (
    UPDATE m_user MU
       SET password        = in_hash_password,
           update_datetime = now()
      FROM taget_user TU
     WHERE MU.user_no = TU.user_no
     RETURNING
           MU.user_no
  )
  SELECT CASE WHEN count(UP.user_no) = 0 THEN FALSE
              ELSE TRUE END,
         CASE WHEN count(UP.user_no) = 0 THEN 400
              ELSE 200 END,
         CASE WHEN count(UP.user_no) = 0 THEN 'E000016'
              ELSE NULL END
    INTO result,
         status,
         message

    FROM update_password UP;


RETURN NEXT;

END;
$BODY$;
