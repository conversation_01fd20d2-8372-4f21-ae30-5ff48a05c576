CREATE OR REPLACE FUNCTION public.f_get_member_info_by_member_no (
    in_member_no bigint,
    in_tenant_no bigint
)
RETURNS TABLE(
    member_request_no bigint,
    member_status integer,
    email_delivery_flag integer,
    member_id character varying,
    free_field jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT MM.member_request_no,
        MM.status,
        MM.email_delivery_flag,
        MM.member_id,
        MM.free_field
    FROM m_member MM
  WHERE MM.member_no = in_member_no
    AND MM.tenant_no = in_tenant_no
    AND (MM.delete_flag IS NULL OR MM.delete_flag = 0)
    ;
END;

$BODY$;
