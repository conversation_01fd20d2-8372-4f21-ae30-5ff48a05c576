CREATE OR REPLACE FUNCTION public.f_search_auction_items_for_csv (
    in_tenant_no bigint,
    in_auction_classification character varying,
    in_search_key character varying,
    in_areas character varying[],
    in_category jsonb[],
    in_start_year numeric,
    in_end_year numeric,
    in_start_price numeric,
    in_end_price numeric,
    in_favorite boolean,
    in_bidding boolean,
    in_un_sold_out boolean,
    in_exceeding_lowest_price boolean,
    in_current_price_sort character varying,
    in_lang_code character varying,
    in_member_no bigint
)
RETURNS TABLE(
    lot_id character varying,
    area_id character varying,
    maker text,
    model text,
    unit text,
    model_year text,
    load_quantity text,
    outrigger text,
    awameter text,
    mileage text,
    storage_location text,
    inspection text,
    crane_inspection text,
    hook text,
    specifications text,
    cab text,
    undercarriage text,
    price text
)

LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会商品CSVデータ取得
-- Parameters
-- @param in_tenant_no テナント番号
----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  WITH filter_exhibition AS (
    SELECT TE.exhibition_no,
           TE.area_id,
           TE.pitch_width,
           TE.preview_end_datetime
      FROM t_exhibition TE
      LEFT JOIN t_exhibition_member TEM
        ON TEM.exhibition_no = TE.exhibition_no
       AND TEM.member_no = in_member_no
     WHERE TE.tenant_no = in_tenant_no
       AND (in_auction_classification IS NULL OR TE.exhibition_classification_info->>'auctionClassification' = in_auction_classification)
       AND (TE.exhibition_classification_info->>'openClassification' = '1' OR TEM.exhibition_member_no IS NOT NULL)
       AND now() > COALESCE(TE.preview_start_datetime, TE.start_datetime)
       AND (
             in_areas IS NULL
             OR array_length(in_areas, 1) IS NULL
             OR TE.area_id = ANY(in_areas)
           )
       AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
  ),
  filter_item AS (
    SELECT TEI.exhibition_item_no
      FROM t_exhibition_item TEI
      JOIN filter_exhibition FE
        ON FE.exhibition_no = TEI.exhibition_no
      JOIN t_lot TL
        ON TL.lot_no = TEI.lot_no
       AND TL.tenant_no = in_tenant_no
       AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
      JOIN t_lot_detail TLD
        ON TLD.lot_no = TEI.lot_no
       AND TLD.tenant_no = in_tenant_no
      JOIN t_item TI
        ON TLD.item_no = TI.item_no
       AND TI.tenant_no = in_tenant_no
       AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
      JOIN t_item_localized TIL
        ON TIL.item_no = TI.item_no
       AND TIL.tenant_no = in_tenant_no
       AND TIL.language_code = in_lang_code
       AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
      LEFT JOIN t_exhibition_item_favorite TEIF
        ON TEIF.exhibition_item_no = TEI.exhibition_item_no
       AND TEIF.tenant_no = in_tenant_no
      LEFT JOIN t_bid TB
        ON TB.exhibition_item_no = TEI.exhibition_item_no
       AND TB.tenant_no = in_tenant_no
       AND TB.member_no = in_member_no
     RIGHT JOIN (
              SELECT category->>'categoryNo' AS category_id,
                     NULLIF(split_part(TRIM(REPLACE(category->>'extension','t','')), '-', 1), '') AS abil_start,
                     NULLIF(split_part(TRIM(REPLACE(category->>'extension','t','')), '-', 2), '') AS abil_end
                FROM unnest(COALESCE(in_category, '{"{\"categoryNo\" : null}"}')::jsonb[]) category
           ) CF
        ON TIL.free_field IS NOT NULL
       AND (CF.category_id IS NULL OR TIL.free_field->>'MDLGRID' = CF.category_id)
       AND (CF.abil_start IS NULL OR COALESCE(REPLACE(TIL.free_field->>'ABIL', '-', '0')::numeric, 0) >= CF.abil_start::numeric)
       AND (CF.abil_end IS NULL OR COALESCE(REPLACE(TIL.free_field->>'ABIL', '-', '0')::numeric, 0) <= CF.abil_end::numeric)
     WHERE (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
       AND (
            in_favorite IS NULL
            OR NOT in_favorite
            OR TEIF.favorite_no IS NOT NULL
           )
       AND (
            in_search_key IS NULL
            OR TIL.free_field->>'MAKER' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'MODEL' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'MDLGR' ~ f_escape_string(in_search_key)
            OR TIL.free_field->>'ACTSERNO' ~ f_escape_string(in_search_key)
           )
      AND (in_start_year IS NULL OR (TIL.free_field->>'YYYY' ~ '^\d+$' AND (TIL.free_field->>'YYYY')::numeric >= in_start_year::numeric))
      AND (in_end_year IS NULL OR (TIL.free_field->>'YYYY' ~ '^\d+$' AND (TIL.free_field->>'YYYY')::numeric <= in_end_year::numeric))
      AND (in_start_price IS NULL OR COALESCE(TEI.current_price, TEI.lowest_bid_price) >= in_start_price::numeric)
      AND (in_end_price IS NULL OR COALESCE(TEI.current_price, TEI.lowest_bid_price) <= in_end_price::numeric)
      AND (in_bidding IS NULL OR NOT in_bidding OR TB.bid_price IS NOT NULL)
      AND (in_exceeding_lowest_price IS NULL OR NOT in_exceeding_lowest_price OR TEI.top_member_no IS NOT NULL)
      AND (in_un_sold_out IS NULL OR NOT in_un_sold_out OR NOT (TEI.hummer_flag = 1))
      AND now() < COALESCE(FE.preview_end_datetime, TEI.end_datetime)
      AND TEI.exhibition_item_no IS NOT NULL
      GROUP BY TEI.exhibition_item_no
  )
  SELECT TL.lot_id,
         FE.area_id,
         TIL.free_field->>'MAKER',
         TIL.free_field->>'MODEL',
         TIL.free_field->>'ACTSERNO',
         TIL.free_field->>'YYYY',
         TIL.free_field->>'ABIL',
         TIL.free_field->>'OUTRIGR',
         TIL.free_field->>'OPETIM',
         TIL.free_field->>'MILEAGE',
         TIL.free_field->>'SYBSNM',
         TIL.free_field->>'DAYCARI',
         TIL.free_field->>'DAYCRAN',
         TIL.free_field->>'HOOK',
         TIL.free_field->>'OTHERSY',
         TIL.free_field->>'CAB',
         TIL.free_field->>'UDRCARR',
         CASE WHEN TEI.hummer_flag = 1 THEN 'SOLD OUT'
              WHEN TEI.hummer_flag = 2 THEN ''
              ELSE TEI.lowest_bid_price :: text
          END
    FROM t_exhibition_item TEI
    JOIN filter_exhibition FE
      ON FE.exhibition_no = TEI.exhibition_no
    JOIN t_lot TL
      ON TL.lot_no = TEI.lot_no
     AND TL.tenant_no = in_tenant_no
     AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
    JOIN t_lot_detail TLD
      ON TLD.lot_no = TEI.lot_no
     AND TLD.tenant_no = in_tenant_no
     AND TLD.order_no = 1
    JOIN t_item TI
      ON TLD.item_no = TI.item_no
     AND TI.tenant_no = in_tenant_no
     AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    JOIN t_item_localized TIL
      ON TIL.item_no = TI.item_no
     AND TIL.tenant_no = in_tenant_no
     AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
     AND TIL.language_code = in_lang_code
    JOIN filter_item FI
      ON TEI.exhibition_item_no = FI.exhibition_item_no
    ORDER BY
     CASE WHEN in_member_no IS NOT NULL AND in_current_price_sort = 'asc' THEN COALESCE(TEI.current_price, TEI.lowest_bid_price)
          WHEN in_member_no IS NOT NULL AND in_current_price_sort = 'desc' THEN (-1 * COALESCE(TEI.current_price, TEI.lowest_bid_price))
          ELSE 1 END,
     TL.lot_id
    ;
END;

$BODY$;
