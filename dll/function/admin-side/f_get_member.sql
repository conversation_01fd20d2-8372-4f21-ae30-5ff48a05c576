CREATE OR REPLACE FUNCTION public.f_get_member (
    IN in_tenant_no bigint,
    IN in_member_request_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    member_no bigint,
    member_id character varying,
    member_request_no bigint,
    member_request_type bigint,
    currency_id character varying,
    exhibition_allow_flag integer,
    bid_allow_flag integer,
    email_delivery_flag integer,
    email_priority integer,
    free_field jsonb,
    status integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員情報を取得する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_member_request_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  IF EXISTS (
    select 1 from m_member
     where m_member.member_request_no = in_member_request_no
     and m_member.delete_flag = 0
     ) THEN
    RETURN QUERY
    SELECT M.tenant_no,
          M.member_no,
          M.member_id,
          M.member_request_no,
          MR.member_request_type,
          M.currency_id,
          M.exhibition_allow_flag,
          M.bid_allow_flag,
          M.email_delivery_flag,
          M.email_priority,
          (M.free_field-'password') as free_field,
          M.status
   FROM m_member M
   JOIN m_tenant T ON T.tenant_no = M.tenant_no
                  AND T.tenant_no = in_tenant_no
   LEFT JOIN t_member_request MR ON M.member_request_no = MR.member_request_no
                                 AND MR.tenant_no = T.tenant_no
                                 AND MR.delete_flag = 0
   WHERE M.delete_flag = 0
     AND M.member_request_no = in_member_request_no;
  ELSE
    RETURN QUERY
    SELECT MR.tenant_no,
          null::bigint as member_no,
          ''::character varying as member_id,
          MR.member_request_no,
          MR.member_request_type,
          null::character varying as currency_id,
          null::integer as exhibition_allow_flag,
          null::integer as bid_allow_flag,
          null::integer as email_delivery_flag,
          MR.email_priority,
          (MR.free_field-'password') as free_field,
          MR.status
   FROM t_member_request MR
   JOIN m_tenant T ON T.tenant_no = MR.tenant_no
                  AND T.tenant_no = in_tenant_no
   WHERE MR.delete_flag = 0
     AND MR.member_request_no = in_member_request_no;
  END IF;

END;

$BODY$;
