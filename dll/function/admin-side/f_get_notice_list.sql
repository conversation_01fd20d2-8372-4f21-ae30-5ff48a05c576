CREATE OR REPLACE FUNCTION public.f_get_notice_list (
    in_tenant_no bigint,
    in_display_code integer[],
    in_start_datetime character varying,
    in_end_datetime character varying
)
RETURNS TABLE(
    notice_no bigint,
    tenant_no bigint,
    display_code character varying,
    start_datetime text,
    end_datetime text,
    language_code character varying,
    body text,
    link_url character varying,
    file text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
  
----------------------------------------------------------------------------------------------------
-- お知らせ情報を追加する
-- Parameters
-- @param 
--   in_display_code integer,
--   in_start_datetime character varying,
--   in_end_datetime character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.notice_no,
         A.tenant_no,
         mcl.value2 as display_code,
        to_char(A.start_datetime, 'YYYY/MM/DD') as start_datetime,
        to_char(A.end_datetime, 'YYYY/MM/DD') as end_datetime,
    B.language_code, B.body, B.link_url, B.file
   FROM t_notice A
   JOIN t_notice_localized B ON A.notice_no = B.notice_no
   JOIN m_tenant T ON T.tenant_no = A.tenant_no
   LEFT OUTER JOIN (
      SELECT mcl.value1
           , mcl.value2
           , mcl.language_code
        FROM m_constant mc
        INNER JOIN m_constant_localized mcl ON mcl.constant_no = mc.constant_no
       WHERE mc.tenant_no = in_tenant_no
         AND mc.key_string = 'NOTICE_PRIORITY'
         AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    ) mcl ON mcl.value1 = A.display_code::character varying AND mcl.language_code = B.language_code
    WHERE T.tenant_no = in_tenant_no
      AND (CASE WHEN in_display_code IS NOT NULL THEN A.display_code = ANY(in_display_code) ELSE true END)
      AND (in_start_datetime IS NULL OR A.start_datetime >= to_timestamp(in_start_datetime, 'yyyy/MM/dd HH24:MI:SS'))
      AND (in_end_datetime IS NULL OR A.start_datetime <= to_timestamp(in_end_datetime, 'yyyy/MM/dd HH24:MI:SS'))
      AND A.delete_flag = 0
      AND B.delete_flag = 0
    ORDER BY A.start_datetime DESC, A.end_datetime DESC, A.notice_no, B.language_code DESC;

END;

$BODY$;
