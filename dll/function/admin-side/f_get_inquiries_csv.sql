CREATE OR REPLACE FUNCTION public.f_get_inquiries_csv(
	in_tenant_no bigint,
	in_start_datetime character varying,
	in_end_datetime character varying)
    RETURNS TABLE(tenant_no bigint, inquiry_no bigint, classification bigint, member_id character varying, contents text, file text[], create_datetime text, free_field jsonb, customer_code text)
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- お問い合わせCSVを取得する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_start_datetime character varying,
--   in_end_datetime character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  -- 会員
  SELECT  I.tenant_no,
          I.inquiry_no,
          I.classification,
          M.member_id,
          COALESCE(I.contents, '') as contents,
          I.file,
          to_char(I.create_datetime, 'YYYY/MM/DD HH24:MI') as create_datetime,
          I.free_field,
          M.free_field->>'customerCode' as customer_code
   FROM t_inquiry I
   JOIN m_tenant T ON T.tenant_no = I.tenant_no
                  AND T.tenant_no = in_tenant_no
   LEFT JOIN m_member M ON M.member_no = I.create_member_no
                        AND M.delete_flag = 0
  WHERE I.delete_flag = 0
    AND (in_start_datetime IS NULL OR I.create_datetime >= to_timestamp(in_start_datetime, 'yyyy/MM/dd HH24:MI:SS'))
    AND (in_end_datetime IS NULL OR I.create_datetime <= to_timestamp(in_end_datetime, 'yyyy/MM/dd HH24:MI:SS'))
  ORDER BY I.create_datetime;

END;

$BODY$;
