CREATE OR REPLACE FUNCTION public.f_update_items_recommend (
    in_item_nos bigint[],
    in_recommend_flags boolean[],
    in_tenant_no bigint,
    in_admin_no bigint,
    in_recommend_display_date character varying,
    OUT result boolean,
    OUT status bigint,
    OUT message character varying
)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 商品のおすすめ情報を変更する
-- Parameters
-- @param
--   in_item_nos bigint[],
--   in_recommend_flags boolean[],
--   in_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  FOR idx in 1..array_length(in_item_nos,1)
  LOOP

    -- 商品情報更新
    UPDATE t_item
       SET recommend_start_datetime = CASE WHEN in_recommend_flags[idx] THEN current_timestamp ELSE null END
         , recommend_end_datetime = CASE WHEN in_recommend_flags[idx] THEN (current_timestamp + CONCAT(in_recommend_display_date, ' day')::INTERVAL) ELSE null END
         , update_admin_no = in_admin_no
         , update_datetime = current_timestamp
    WHERE item_no = in_item_nos[idx] AND tenant_no = in_tenant_no;

  END LOOP;

  result := true;
  status := 200;
  message := '';

RETURN NEXT;

EXCEPTION

  --その他エラー
  WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
