CREATE OR REPLACE FUNCTION public.f_get_lots_xlsx (
    in_exhibition_no bigint,
    in_tenant_no bigint,
    in_serial_from character varying,
    in_serial_to character varying,
    in_language_code character varying
)
RETURNS TABLE(
    row_number bigint,
    item_no bigint,
    manage_no character varying,
    area_id character varying,
    tenant_no bigint,
    recommend_flag boolean,
    lot_no bigint,
    lot_id character varying,
    localized_json_array json[],
    lowest_bid_price numeric,
    lowest_bid_accept_price numeric,
    cancel_flag integer,
    bid_count integer,
    order_no integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 出展機情報を取得する
-- Parameters
-- @param in_exhibition_no 入札会No
-- @param in_tenant_no テナントNo
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH localized AS (
    SELECT TIL.item_no
         , array_agg(TIL.free_field ->>'ACTSERNO'::text) as serial
         , array_agg(row_to_json(row(TIL.language_code, 
            json_build_object(
              'SYBSNM', TIL.free_field ->>'SYBSNM',
              'MODEL', TIL.free_field ->>'MODEL',
              'ACTSERNO', TIL.free_field ->>'ACTSERNO',
              'MAKER', TIL.free_field ->>'MAKER',
              'AMTAREA', TIL.free_field ->>'AMTAREA',
              'NEWSPRC', TIL.free_field ->>'NEWSPRC',
              'ZIKPRC', TIL.free_field ->>'ZIKPRC',
              'YYYY', TIL.free_field ->>'YYYY',
              'ABIL', TIL.free_field ->>'ABIL',
              'MDLGRID', TIL.free_field ->>'MDLGRID'
            )::jsonb
          ))) localized_json_array
      FROM t_item TI
    INNER JOIN t_item_localized TIL
            ON TI.item_no = TIL.item_no
            AND TIL.language_code = in_language_code
   WHERE TI.status IN (0, 1, 2)
     AND TI.delete_flag = 0
     GROUP BY TIL.item_no
  ),
  SEARCH_BY_SERIAL AS(
    SELECT IL.item_no, TLD.lot_no
      FROM localized IL
      LEFT JOIN t_lot_detail TLD ON TLD.item_no = IL.item_no
     WHERE CASE WHEN in_serial_from IS NOT NULL THEN in_serial_from <= ANY(IL.serial) ELSE true END
       AND CASE WHEN in_serial_to IS NOT NULL THEN in_serial_to >= ANY(IL.serial) ELSE true END
    )
  SELECT ROW_NUMBER() OVER (ORDER BY L.lot_id, LD.order_no, I.manage_no)
       , I.item_no
       , I.manage_no
       , I.area_id
       , I.tenant_no
       , CASE WHEN I.recommend_start_datetime IS NOT NULL THEN true ELSE false END AS recommend_flag
       , L.lot_no
       , L.lot_id
       , localized.localized_json_array
       , CASE WHEN LD.order_no IS NULL THEN NULL WHEN LD.order_no = 1 THEN EI.lowest_bid_price ELSE 0 END
       , CASE WHEN LD.order_no IS NULL THEN NULL WHEN LD.order_no = 1 THEN EI.lowest_bid_accept_price ELSE 0 END
       , EI.cancel_flag
       , EI.bid_count
       , LD.order_no
    FROM t_item I
    LEFT JOIN t_lot_detail LD
      ON LD.item_no = I.item_no
    LEFT JOIN t_lot L
      ON L.lot_no = LD.lot_no
    LEFT JOIN t_exhibition_item EI
      ON EI.lot_no = L.lot_no
    LEFT JOIN t_exhibition TE
      ON TE.exhibition_no = EI.exhibition_no
    LEFT JOIN localized
      ON localized.item_no = I.item_no
    LEFT JOIN (SELECT DISTINCT(SRC.lot_no) as lot_no FROM SEARCH_BY_SERIAL SRC) SRC_LOT ON SRC_LOT.lot_no = L.lot_no
    LEFT JOIN (SELECT DISTINCT(SRC.item_no) as item_no FROM SEARCH_BY_SERIAL SRC) SRC_ITEM ON SRC_ITEM.item_no = localized.item_no
   WHERE (EI.exhibition_no = in_exhibition_no OR (I.status IN (0, 1) AND COALESCE(EI.cancel_flag, 0) = 0) )
     AND I.tenant_no = in_tenant_no
     AND CASE WHEN COALESCE(EI.hummer_flag, 999) <> 999 THEN EI.hummer_flag <> 1 ELSE true END
     AND I.delete_flag = 0
    --  AND COALESCE(TE.status, 0) = 0
     AND (SRC_ITEM.item_no IS NOT NULL OR SRC_LOT.lot_no IS NOT NULL)
   ORDER BY L.lot_id, LD.order_no, I.manage_no;

END;

$BODY$;
