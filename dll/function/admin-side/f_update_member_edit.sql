CREATE OR REPLACE FUNCTION public.f_update_member_edit (
    in_member_no bigint,
    in_bid_allow_flag integer,
    in_email_delivery_flag integer,
    in_free_field jsonb,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_member_no bigint;

----------------------------------------------------------------------------------------------------
-- 会員情報を更新する
----------------------------------------------------------------------------------------------------

BEGIN

  -- 会員
  WITH update_member AS (
  UPDATE m_member MM
     SET bid_allow_flag = (CASE WHEN in_bid_allow_flag IS NULL THEN MM.bid_allow_flag ELSE in_bid_allow_flag END),
         email_delivery_flag = (CASE WHEN in_email_delivery_flag IS NULL THEN MM.email_delivery_flag ELSE in_email_delivery_flag END),
         free_field = MM.free_field || in_free_field,
         update_admin_no = in_update_admin_no,
         update_datetime = now()
   WHERE member_no = in_member_no
   RETURNING MM.member_no
  )

  SELECT member_no INTO return_member_no FROM update_member;

  RETURN return_member_no;

END;
$BODY$;
