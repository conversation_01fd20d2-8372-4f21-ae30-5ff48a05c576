CREATE OR REPLACE FUNCTION public.f_get_members_csv (
    in_tenant_no bigint,
    in_customer_code character varying,
    in_company_name character varying,
    in_status integer[]
)
RETURNS TABLE(
    tenant_no bigint,
    member_no bigint,
    member_request_no bigint,
    member_id character varying,
    bid_allow_flag integer,
    email_delivery_flag integer,
    free_field jsonb,
    status integer,
    create_datetime text,
    update_datetime text,
    last_user_name character varying,
    last_login_datetime text,
    sort_create_datetime text
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- CSV用会員一覧を取得する
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  -- 会員
  (SELECT M.tenant_no,
         M.member_no,
         M.member_request_no,
         M.member_id,
         M.bid_allow_flag,
         M.email_delivery_flag,
         M.free_field,
         M.status,
         to_char(M.create_datetime at time zone 'Japan', 'YYYY/MM/DD HH24:MI') as create_datetime,
         to_char(M.update_datetime at time zone 'Japan', 'YYYY/MM/DD HH24:MI') as update_datetime,
         H.user_name as last_user_name,
         COALESCE(to_char(TL.create_datetime at time zone 'Japan', 'YYYY/MM/DD HH24:MI'), '')  as last_login_datetime,
         to_char(M.create_datetime at time zone 'Japan', 'YYYY/MM/DD HH24:MI:SS.MS') as sort_create_datetime
     FROM m_member M
     JOIN m_tenant T ON T.tenant_no = M.tenant_no
                    AND T.tenant_no = in_tenant_no
     LEFT JOIN (
        SELECT * FROM (
          SELECT H1.member_request_no, H1.user_name, H1.after_status, H1.create_datetime,
                 row_number() over (PARTITION BY H1.member_request_no ORDER BY H1.create_datetime DESC) as rnum
            FROM t_member_status_history H1
        ) H2 WHERE H2.rnum = 1
      ) H ON H.member_request_no = M.member_request_no
      LEFT JOIN (
        SELECT TL.member_no,
              TL.create_datetime,
              row_number() over (PARTITION BY TL.member_no ORDER BY TL.create_datetime DESC) as rnum
          FROM t_login TL
          WHERE TL.tenant_no = in_tenant_no
      ) TL
      ON TL.member_no = M.member_no
      AND TL.rnum = 1
     WHERE M.delete_flag = 0
       AND (in_customer_code IS NULL OR length(in_customer_code) = 0 OR position(LOWER(in_customer_code) in LOWER((M.free_field->>'customerCode')))>0)
       AND (in_company_name IS NULL OR length(in_company_name) = 0 OR position(LOWER(in_company_name) in LOWER((M.free_field->>'companyName')))>0)
       AND (in_status IS NULL OR array_length(in_status, 1) IS NULL OR M.status = ANY(in_status))
    ORDER BY M.member_id
  )
  UNION
  -- 会員申請
  (SELECT  MR.tenant_no,
          null::bigint as member_no,
          MR.member_request_no,
          (null::character varying) as member_id,
          0 as bid_allow_flag,
          0 as email_delivery_flag,
          MR.free_field,
          MR.status,
          to_char(MR.create_datetime at time zone 'Japan', 'YYYY/MM/DD HH24:MI') as create_datetime,
          to_char(MR.update_datetime at time zone 'Japan', 'YYYY/MM/DD HH24:MI') as update_datetime,
          H.user_name as last_user_name,
          ''  as last_login_datetime,
          to_char(MR.create_datetime at time zone 'Japan', 'YYYY/MM/DD HH24:MI:SS.MS') as sort_create_datetime
     FROM t_member_request MR
   JOIN m_tenant T ON T.tenant_no = MR.tenant_no
                  AND T.tenant_no = in_tenant_no
  LEFT JOIN (
      SELECT * FROM (
        SELECT H1.member_request_no, H1.user_name, H1.after_status, H1.create_datetime,
               row_number() over (PARTITION BY H1.member_request_no ORDER BY H1.create_datetime DESC) as rnum
          FROM t_member_status_history H1
      ) H2 WHERE H2.rnum = 1
    ) H ON H.member_request_no = MR.member_request_no
   WHERE MR.delete_flag = 0
    AND (in_customer_code IS NULL OR length(in_customer_code) = 0 OR position(LOWER(in_customer_code) in LOWER((MR.free_field->>'customerCode')))>0)
    AND (in_company_name IS NULL OR length(in_company_name) = 0 OR position(LOWER(in_company_name) in LOWER((MR.free_field->>'companyName')))>0)
    AND (in_status IS NULL OR array_length(in_status, 1) IS NULL OR MR.status = ANY(in_status))
    AND MR.member_request_no not in (
      select distinct(M.member_request_no)
      from m_member M
      where M.member_request_no is not null
    )
    ORDER BY MR.create_datetime DESC, MR.member_request_no
  );

END;

$BODY$;
