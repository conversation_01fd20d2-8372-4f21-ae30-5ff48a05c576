CREATE OR REPLACE FUNCTION public.f_get_constant_key(
)
RETURNS TABLE(
    key_string character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 定数キーを取得する
-- Parameters
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.key_string
  FROM (
    SELECT distinct B.key_string
    FROM m_constant B
    WHERE B.mainte_impossible_flag = 1
  ) A
  ORDER BY convert_to(A.key_string ,'UTF8')
;

END;
$BODY$;
