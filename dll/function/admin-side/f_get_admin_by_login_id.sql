CREATE OR REPLACE FUNCTION public.f_get_admin_by_login_id (
    in_login_id character varying,
    in_tenant_no bigint
)
RETURNS TABLE(
    admin_no bigint,
    tenant_no bigint,
    admin_name character varying,
    login_id character varying,
    role_id character varying,
    delete_flag integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
  
----------------------------------------------------------------------------------------------------
-- login_idで管理者データを取得する
-- Parameters
-- @param 
--   in_login_id character varying,
--   in_tenant_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.admin_no,
         A.tenant_no,
         A.admin_name,
         A.login_id,
         A.role_id,
         A.delete_flag
   FROM m_admin A
   JOIN m_tenant T ON T.tenant_no = A.tenant_no
   WHERE A.login_id = in_login_id
     AND T.tenant_no = in_tenant_no
  ORDER BY A.admin_no;

END;

$BODY$;
