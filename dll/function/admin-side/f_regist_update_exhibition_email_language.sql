CREATE OR REPLACE FUNCTION public.f_regist_update_exhibition_email_language(
    in_tenant_no bigint,
    in_admin_no bigint,
    in_emailLanguageReq json[],
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

-- 入札会のメール情報を更新する
-- Parameters
-- @param in_tenant_no bigint
-- @param in_exhibition_no bigint
-- @param in_emailLanguageReq json[] メール情報
-- t_exhibition_email
----------------------------------------------------------------------------------------------------

BEGIN

    WITH 
    new_exhibition_email_localized AS (
    INSERT INTO t_exhibition_email_localized (
        tenant_no,
        exhibition_email_no,
        language_code,
        title,
        body,
        footer,
        file,
        create_admin_no,
        create_datetime,
        update_admin_no,
        update_user_no,
        update_datetime,
        delete_flag
    )
    (
    SELECT 
        in_tenant_no,
        ((SELECT t_exhibition_email.exhibition_email_no FROM t_exhibition_email
         WHERE t_exhibition_email.exhibition_name = (data->>'exhibitionName')::character varying
         AND  t_exhibition_email.classification = (data->>'classification')::integer)
        ),
        (data->>'languageCode')::character varying, 
        (data->>'title')::character varying, 
        (data->>'body')::character varying, 
        (data->>'footer')::character varying, 
        translate((data->>'file'), '[]', '{}')::text[],
        in_admin_no,
        now(),
        in_admin_no,
        null,
        now(),
        0
        FROM unnest(in_emailLanguageReq::jsonb[]) AS data
        WHERE 
            (data->>'exhibitionEmailLocalizedNo') IS NULL 
    )
    RETURNING
        t_exhibition_email_localized.exhibition_email_localized_no
    ),

    exit_exhibition_email_localized AS (
    UPDATE t_exhibition_email_localized
    SET 
        title = (data->>'title')::character varying, 
        body = (data->>'body')::character varying, 
        footer = (data->>'footer')::character varying, 
        file = translate((data->>'file'), '[]', '{}')::text[],
        update_admin_no = in_admin_no,
        update_datetime = now()
        FROM unnest(in_emailLanguageReq::jsonb[]) AS data
    WHERE t_exhibition_email_localized.exhibition_email_localized_no = (data->>'exhibitionEmailLocalizedNo')::bigint
    RETURNING
        t_exhibition_email_localized.exhibition_email_localized_no
    )

    SELECT 200 INTO status FROM 
    new_exhibition_email_localized, exit_exhibition_email_localized;   
    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
