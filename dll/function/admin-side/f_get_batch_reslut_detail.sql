CREATE OR REPLACE FUNCTION public.f_get_batch_result_detail (
    in_execution_datetime character varying
)
RETURNS TABLE(
    error_details_field jsonb[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- バッチ実行結果詳細を取得する
-- Parameters
-- @param in_execution_datetime_from 実行日時
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT
    TBR.error_details_field
    FROM t_batch_result TBR
  WHERE date_trunc('minute', TBR.execution_datetime) = date_trunc('minute', to_timestamp(in_execution_datetime, 'YYYY/MM/DD HH24:MI'))
    AND TBR.delete_flag = 0;

END;

$BODY$;
