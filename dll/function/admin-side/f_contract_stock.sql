CREATE OR REPLACE FUNCTION public.f_contract_stock (
    in_item_no bigint,
    in_member_no bigint,
    in_contract_price numeric,
    in_tenant_no bigint,
    in_login_member_no bigint
)
RETURNS TABLE(
    csv_json json[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 在庫機直接成約
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH update_item AS (
    UPDATE t_item TI
       SET status = 3,
           price_display_flag = 2,
           linked_flag = 1,
           linked_datetime = now(),
           update_admin_no = in_login_member_no,
           update_datetime = now()
     WHERE TI.item_no = in_item_no
       AND TI.tenant_no = in_tenant_no
       AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
    RETURNING
        TI.item_no,
        TI.tenant_no,
        TI.manage_no
  ),
  insert_stock_result AS (
    INSERT INTO t_stock_result (
      tenant_no,
      item_no,
      manage_no,
      member_field,
      item_field,
      success_member_no,
      success_price,
      create_datetime
    )
    (
      SELECT UI.tenant_no,
             UI.item_no,
             UI.manage_no,
             MM.member_field,
             TIL.free_field,
             MM.member_no,
             in_contract_price,
             now()
        FROM update_item UI
        JOIN t_item_localized TIL
          ON TIL.item_no = UI.item_no
         AND TIL.tenant_no = UI.tenant_no
         AND TIL.language_code = 'ja'
         AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
       CROSS JOIN (
         SELECT member_no,
                free_field AS member_field
           FROM m_member
          WHERE member_no = in_member_no
       ) MM
     )
     RETURNING *
  )
  SELECT array_agg(json_build_object(
    'manageNo'         , SUBSTRING(TER.manage_no, 1, 7),
    'lotId'            , '',
    'amtarea'          , SUBSTRING(TER.item_field->>'AMTAREA', 1, 15),
    'bidSuccessPrice'  , SUBSTRING(COALESCE(TER.success_price::integer::text, ''), 1, 15),
    'hummerFlag'       , 'OK',
    'customerCd'       , SUBSTRING(COALESCE(TER.member_field->>'customerCd', ''), 1, 10),
    'companyName'      , SUBSTRING(COALESCE(TER.member_field->>'companyName', ''), 1, 35),
    'chargeSH'         , SUBSTRING(COALESCE(TER.member_field->>'shUserName', ''), 1, 40),
    'chargeCR'         , SUBSTRING(COALESCE(TER.member_field->>'crUserName', ''), 1, 40),
    'emailUserName'    , SUBSTRING(COALESCE(TER.member_field->>'emailUserName', ''), 1, 40),
    'countryCode'      , SUBSTRING(COALESCE(TER.member_field->>'countryCode', ''), 1, 3),
    'email1'           , SUBSTRING(COALESCE(TER.member_field->>'email', ''), 1, 100),
    'email2'           , SUBSTRING(COALESCE(TER.member_field->>'email2', ''), 1, 100),
    'email3'           , SUBSTRING(COALESCE(TER.member_field->>'email3', ''), 1, 100),
    'email4'           , SUBSTRING(COALESCE(TER.member_field->>'email4', ''), 1, 100),
    'email5'           , SUBSTRING(COALESCE(TER.member_field->>'email5', ''), 1, 100),
    'email6'           , SUBSTRING(COALESCE(TER.member_field->>'email6', ''), 1, 100),
    'email7'           , SUBSTRING(COALESCE(TER.member_field->>'email7', ''), 1, 100),
    'email8'           , SUBSTRING(COALESCE(TER.member_field->>'email8', ''), 1, 100),
    'email9'           , SUBSTRING(COALESCE(TER.member_field->>'email9', ''), 1, 100),
    'email10'          , SUBSTRING(COALESCE(TER.member_field->>'email10', ''), 1, 100),
    'tel'              , SUBSTRING(COALESCE(TER.member_field->>'tel', ''), 1, 30),
    'fax'              , SUBSTRING(COALESCE(TER.member_field->>'fax', ''), 1, 30),
    'postCode'         , SUBSTRING(COALESCE(TER.member_field->>'postCode', ''), 1, 10),
    'address1'         , SUBSTRING(COALESCE(TER.member_field->>'state', ''), 1, 35),
    'address2'         , SUBSTRING(COALESCE(TER.member_field->>'city', ''), 1, 35),
    'address3'         , SUBSTRING(COALESCE(TER.member_field->>'address', ''), 1, 35)
  ))
  FROM insert_stock_result TER;

END;

$BODY$;
