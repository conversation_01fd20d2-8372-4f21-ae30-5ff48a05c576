CREATE OR REPLACE FUNCTION public.f_delete_constant_localized(
    in_constant_no bigint,
    in_tenant_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- 定数(多言語化)を削除する
-- Parameters
-- @param in_constant_no bigint
-- @param in_tenant_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  DELETE FROM m_constant_localized
  WHERE constant_no = in_constant_no
  AND tenant_no = in_tenant_no;

  result := true;
  status := 200;
  message := '';

RETURN NEXT;
EXCEPTION

  --その他エラー
  WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;
$BODY$;
