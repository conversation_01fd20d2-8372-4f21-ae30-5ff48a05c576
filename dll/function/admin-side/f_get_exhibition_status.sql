CREATE OR REPLACE FUNCTION public.f_get_exhibition_status (
    in_tenant_no bigint,
    in_exhibition_no bigint
)
RETURNS TABLE(
    exhibition_status integer,
    exhibition_classification_info jsonb,
    start_datetime timestamp with time zone,
    end_datetime timestamp with time zone
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
  exhibition_status integer;
----------------------------------------------------------------------------------------------------
-- 入札会情報を削除する
-- Parameters
-- @param in_tenant_no テナントNo
-- @param in_exhibition_no 開催回番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TE.status,
        TE.exhibition_classification_info,
        TE.start_datetime,
        TE.end_datetime
    FROM t_exhibition TE
   WHERE TE.exhibition_no = in_exhibition_no
     AND TE.tenant_no = in_tenant_no
   LIMIT 1;

END;

$BODY$;
