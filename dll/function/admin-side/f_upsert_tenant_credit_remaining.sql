CREATE OR REPLACE FUNCTION public.f_upsert_tenant_credit_remaining (
    in_tenant_no bigint,
    in_bid_limit_flag integer,
    in_bid_limit numeric,
    in_reset_type integer,
    in_reset_date integer,
    in_start_datetime timestamp with time zone,
    in_end_datetime timestamp with time zone,
    in_update_admin_no bigint
)
RETURNS TABLE (
  upsert_count integer,
  insert_count integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  v_upsert_count integer := 0;
  v_insert_count integer := 0;
----------------------------------------------------------------------------------------------------
-- 入札上限額の更新
-- Parameters
-- @param in_tenant_no       テナント番号
-- @param in_bid_limit_flag  入札上限額設定フラグ
-- @param in_bid_limit       入札上限額（円）
-- @param in_reset_type      リセット形式
-- @param in_reset_date      リセット日付
-- @param in_start_datetime  適用開始日
-- @param in_end_datetime    適用終了日
-- @param in_update_admin_no 更新管理者番号
----------------------------------------------------------------------------------------------------

BEGIN

  -- t_tenant_credit_remainingにUPSERT
  INSERT INTO t_tenant_credit_remaining (
    tenant_no,
    bid_limit_flag,
    bid_limit,
    reset_type,
    reset_date,
    start_datetime,
    end_datetime,
    create_user_no,
    create_datetime,
    update_user_no,
    update_datetime
  ) VALUES (
    in_tenant_no,
    in_bid_limit_flag,
    in_bid_limit,
    in_reset_type,
    in_reset_date,
    in_start_datetime,
    in_end_datetime,
    in_update_admin_no,
    now(),
    in_update_admin_no,
    now()
  )
  ON CONFLICT (tenant_no)
  DO UPDATE SET
    bid_limit_flag = EXCLUDED.bid_limit_flag,
    bid_limit = EXCLUDED.bid_limit,
    reset_type = EXCLUDED.reset_type,
    reset_date = EXCLUDED.reset_date,
    start_datetime = EXCLUDED.start_datetime,
    end_datetime = EXCLUDED.end_datetime,
    update_user_no = in_update_admin_no,
    update_datetime = now()
  ;

  GET DIAGNOSTICS v_upsert_count = ROW_COUNT;

  -- t_tenant_credit_remaining_historyにINSERT
  INSERT INTO t_tenant_credit_remaining_history (
    tenant_no,
    bid_limit_flag,
    bid_limit,
    reset_type,
    reset_date,
    start_datetime,
    end_datetime,
    create_user_no,
    create_datetime,
    update_user_no,
    update_datetime
  ) VALUES (
    in_tenant_no,
    in_bid_limit_flag,
    in_bid_limit,
    in_reset_type,
    in_reset_date,
    in_start_datetime,
    in_end_datetime,
    in_update_admin_no,
    now(),
    in_update_admin_no,
    now()
  )
  ;

  GET DIAGNOSTICS v_insert_count = ROW_COUNT;

  RETURN QUERY SELECT v_upsert_count, v_insert_count;

END;
$BODY$;
