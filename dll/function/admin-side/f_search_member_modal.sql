CREATE OR REPLACE FUNCTION public.f_search_member_modal (
    in_tenant_no bigint,
    in_country_code character varying,
    in_company_name character varying,
    in_tanto character varying,
    in_status integer[]
)
RETURNS TABLE(
    tenant_no bigint,
    member_no bigint,
    member_request_no bigint,
    country_code character varying,
    customer_cd character varying,
    company_id character varying,
    company_name character varying,
    ceo_name character varying,
    branch_office character varying,
    sh_user_name character varying,
    cr_user_name character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員検索用会員一覧を取得する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_country_code character varying,
--   in_company_name character varying,
--   in_tanto character varying,
--   in_status integer[]
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  -- 会員
  SELECT * FROM (
    SELECT    M.tenant_no,
              M.member_no,
              M.member_request_no,
              (M.free_field->>'countryCode')::character varying as country_code,
              (M.free_field->>'customerCd')::character varying as customer_cd,
              (M.free_field->>'companyId')::character varying as company_id,
              COALESCE(((M.free_field->>'companyName')::character varying), '') as company_name,
              COALESCE(((M.free_field->>'representativeDirector')::character varying), '') as ceo_name,
              COALESCE(((M.free_field->>'branchOffice')::character varying), '') as branch_office,
              COALESCE(((M.free_field->>'shUserName')::character varying), '') as sh_user_name,
              COALESCE(((M.free_field->>'crUserName')::character varying), '') as cr_user_name
       FROM m_member M
       JOIN m_tenant T ON T.tenant_no = M.tenant_no
                      AND T.tenant_no = in_tenant_no
       WHERE M.delete_flag = 0
         AND M.status = 1
         AND (in_country_code IS NULL OR length(in_country_code) = 0 OR COALESCE(M.free_field->>'countryCode', '') = in_country_code)
         AND (in_company_name IS NULL OR length(in_company_name) = 0 OR position(LOWER(in_company_name) in LOWER((M.free_field->>'companyName')))>0)
         AND (in_status IS NULL OR array_length(in_status, 1) = 0 OR M.status = ANY(in_status))
      ORDER BY M.create_datetime DESC, M.member_no
  ) M2
  WHERE in_tanto IS NULL
     OR length(in_tanto) = 0
     OR position(LOWER(in_tanto) in LOWER(M2.sh_user_name)) > 0
  ;

END;

$BODY$;
