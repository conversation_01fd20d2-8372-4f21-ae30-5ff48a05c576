CREATE OR REPLACE FUNCTION public.f_get_items_status (
    in_tenant_no bigint,
    in_item_nos bigint[]
)
RETURNS TABLE(
    tenant_no bigint,
    item_no bigint,
    exhibition_no bigint,
    status integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- Xlsx用出展機情報を取得する
-- Parameters
-- @param in_exhibition_no 入札会No
-- @param in_tenant_no テナントNo
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TI.tenant_no
       , TI.item_no
       , EI.exhibition_no
       , TI.status
    FROM t_item TI
    LEFT JOIN t_lot_detail TLD ON TLD.item_no = TI.item_no
    LEFT JOIN t_exhibition_item EI ON EI.lot_no = TLD.lot_no and EI.delete_flag = 0
   WHERE in_item_nos IS NOT NULL AND array_length(in_item_nos, 1) > 0
     AND TI.item_no = ANY(in_item_nos)
     AND TI.tenant_no = in_tenant_no
     AND TI.delete_flag = 0
   ORDER BY TI.item_no;

END;

$BODY$;
