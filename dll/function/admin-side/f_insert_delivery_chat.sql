CREATE OR REPLACE FUNCTION public.f_insert_delivery_chat (
  in_tenant_no bigint,
  in_exhibition_item_no bigint,
  in_delivery_message_no bigint,
  in_chat_message character varying,
  in_admin_no bigint
)
  RETURNS TABLE(
    delivery_message_no bigint
  )
    LANGUAGE 'plpgsql'

    COST 100
    VOLATILE
    ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： 配送チャットメッセージ追加
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    INSERT INTO t_delivery_message (
      tenant_no,
      exhibition_item_no,
      update_category_id,
      answer_delivery_message_no,
      admin_no,
      message,
      create_admin_no,
      create_datetime
    )
    VALUES
    (
      in_tenant_no,
      in_exhibition_item_no,
      1,
      in_delivery_message_no,
      in_admin_no,
      in_chat_message,
      in_admin_no,
      now()
    )
    RETURNING
      t_delivery_message.delivery_message_no;

END;

$BODY$;
