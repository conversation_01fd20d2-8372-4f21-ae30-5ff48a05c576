CREATE OR REPLACE FUNCTION public.f_get_email_notification_list (
    in_tenant_no bigint,
    in_to_address character varying,
    in_subject character varying,
    in_start_datetime character varying,
    in_end_datetime character varying
)
RETURNS TABLE(
    email_notification_no bigint,
    create_datetime character varying,
    to_address character varying,
    subject character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 不達メールを取得する
-- Parameters
-- @param 
--   in_tenant_no bigint,
--   in_to_address character varying,
--   in_subject character varying
--   in_start_datetime character varying,
--   in_end_datetime character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT 
        A.email_notification_no AS email_notification_no,
        TO_CHAR(A.create_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying AS create_datetime,
        A.to_address AS to_address,
        A.subject AS subject
  FROM t_email_notification A
    WHERE A.tenant_no = in_tenant_no
      AND (in_to_address IS NULL OR position(in_to_address in A.to_address )>0)
      AND (in_subject IS NULL OR position(in_subject in A.subject )>0)
      AND (in_start_datetime IS NULL OR A.create_datetime >= to_timestamp(in_start_datetime, 'yyyy/MM/dd HH24:MI:SS'))
      AND (in_end_datetime IS NULL OR A.create_datetime <= to_timestamp(in_end_datetime, 'yyyy/MM/dd HH24:MI:SS'))
    ORDER BY create_datetime DESC;

END;

$BODY$;
