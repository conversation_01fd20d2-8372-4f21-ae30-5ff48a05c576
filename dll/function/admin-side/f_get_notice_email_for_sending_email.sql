CREATE OR REPLACE FUNCTION public.f_get_notice_email_for_sending_email (
    in_tenant_no bigint,
    in_notice_email_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    notice_email_no bigint,
    sent_flag integer,
    email_priority integer,
    send_datetime text,
    language_code character varying,
    title character varying,
    body text,
    footer text,
    file text[],
    members json[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- メール送信用お知らせメール情報を取得する
-- Parameters
-- @param
--   in_tenant_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH get_notice_email AS (
    SELECT  A.notice_email_no,
            A.sent_flag,
            A.email_priority,
            to_char(A.send_datetime, 'YYYY/MM/DD HH24:MI') as send_datetime,
            B.language_code,
            B.title,
            B.body_title_upper_row,
            B.body_title_lower_row,
            B.body,
            B.footer,
            B.file
     FROM t_notice_email A
     JOIN t_notice_email_localized B ON A.notice_email_no = B.notice_email_no
     JOIN m_tenant T ON T.tenant_no = A.tenant_no
      WHERE T.tenant_no = in_tenant_no
        AND A.delete_flag = 0
        AND B.delete_flag = 0
        -- AND A.sent_flag = 0
        AND A.send_datetime <= now()
        AND A.notice_email_no = in_notice_email_no
        AND (in_language_code IS NULL OR B.language_code = in_language_code)
      ORDER BY A.send_datetime ASC, A.notice_email_no, B.language_code DESC
  ),
  get_members AS (
    SELECT  M.email_priority,
            (CASE WHEN M.free_field->>'country' = 'JP' THEN 'ja'
                  ELSE 'en' END
            ) as language_code,
            array_agg(
              json_build_object(
                  'member_no', M.member_no,
                  'member_id', M.member_id,
                  'email_delivery_flag', M.email_delivery_flag,
                  'free_field', M.free_field
                )
            ) as members
      FROM m_member M
      WHERE M.delete_flag = 0
        AND M.email_delivery_flag = 1
        AND (CASE WHEN M.free_field->>'country' = 'JP' THEN 'ja'
                  ELSE 'en' END
            ) = in_language_code
      GROUP BY M.email_priority, language_code
  )

  SELECT  A.notice_email_no,
          A.sent_flag,
          A.email_priority,
          A.send_datetime,
          A.language_code,
          A.title,
          A.body,
          A.footer,
          A.file,
          M.members
    FROM get_notice_email A
    LEFT JOIN get_members M ON A.email_priority <= M.email_priority
          AND A.language_code = M.language_code;

END;

$BODY$;
