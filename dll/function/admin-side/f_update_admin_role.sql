CREATE OR REPLACE FUNCTION public.f_update_admin_role (
    in_tenant_no bigint,
    in_target_group_id integer,
    in_function_id bigint,
    in_allowed_role_id text[],
    in_update_admin_no bigint
)
RETURNS TABLE (
  update_count integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  v_update_count integer := 0;
----------------------------------------------------------------------------------------------------
-- 権限設定の更新
-- Parameters
-- @param in_tenant_no       テナント番号
-- @param in_target_group_id  対象グループID
-- @param in_function_id     機能ID
-- @param in_allowed_role_id 許可される権限
-- @param in_update_admin_no 更新管理者番号
----------------------------------------------------------------------------------------------------

BEGIN

  -- m_admin_roleにUPSERT
  UPDATE
    m_admin_role
  SET
    allowed_role_id = in_allowed_role_id
    , update_admin_no = in_update_admin_no
    , update_datetime = now()
  WHERE
    tenant_no = in_tenant_no
    AND target_group_id = in_target_group_id
    AND function_id = in_function_id
  ;

  GET DIAGNOSTICS v_update_count = ROW_COUNT;

  RETURN QUERY SELECT v_update_count;

END;
$BODY$;
