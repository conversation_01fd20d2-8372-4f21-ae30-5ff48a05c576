CREATE OR REPLACE FUNCTION public.f_get_notice_by_notice_no (
    in_tenant_no bigint,
    in_notice_no bigint
)
RETURNS TABLE(
    notice_no bigint,
    tenant_no bigint,
    display_code integer,
    display_code_name character varying,
    start_datetime text,
    end_datetime text,
    language_code character varying,
    title text,
    title1 text,
    sub_title text,
    body text,
    link_url character varying,
    file text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- notice_noでお知らせ情報を取得する
-- Parameters
-- @param 
-- 　in_notice_no integer
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.notice_no,
         A.tenant_no,
         A.display_code,
         mcl.value2 as display_code_name,
         to_char(A.start_datetime, 'YYYY/MM/DD') as start_datetime,
         to_char(A.end_datetime, 'YYYY/MM/DD') as end_datetime,
         B.language_code,
         B.title,
         B.title1,
         B.sub_title,
         B.body,
         B.link_url,
         B.file
   FROM t_notice A
   JOIN t_notice_localized B ON A.notice_no = B.notice_no
   JOIN m_tenant T ON T.tenant_no = in_tenant_no AND A.tenant_no = T.tenant_no
   LEFT OUTER JOIN (
      SELECT mcl.value1
           , mcl.value2
           , mcl.language_code
        FROM m_constant mc
        INNER JOIN m_constant_localized mcl ON mcl.constant_no = mc.constant_no
       WHERE mc.tenant_no = in_tenant_no
         AND mc.key_string = 'NOTICE_PRIORITY'
         AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    ) mcl ON mcl.value1 = A.display_code::character varying AND mcl.language_code = B.language_code
  WHERE A.notice_no = in_notice_no
    AND A.delete_flag = 0
    AND B.delete_flag = 0
  ORDER BY B.language_code DESC;

END;

$BODY$;
