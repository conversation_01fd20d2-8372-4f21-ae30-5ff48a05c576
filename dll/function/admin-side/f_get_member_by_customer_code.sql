CREATE OR REPLACE FUNCTION public.f_get_member_by_customer_code(
    IN in_tenant_no bigint,
    IN in_customer_code character varying
)
RETURNS TABLE(
    tenant_no bigint,
    member_no bigint,
    member_id character varying,
    member_request_no bigint,
    status integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 取引先コードで会員情報を取得する
----------------------------------------------------------------------------------------------------

BEGIN

  IF EXISTS (
    select 1 from m_member
     where (m_member.free_field->>'customerCode')::character varying = in_customer_code
     and m_member.delete_flag = 0
     ) THEN
    RETURN QUERY
    -- 会員と会員申請マスタ
    SELECT M.tenant_no,
          M.member_no,
          M.member_id,
          M.member_request_no,
          M.status
   FROM m_member M
   JOIN m_tenant T ON T.tenant_no = M.tenant_no
                  AND T.tenant_no = in_tenant_no
   LEFT JOIN t_member_request MR ON M.member_request_no = MR.member_request_no
                                 AND MR.tenant_no = T.tenant_no
                                 AND MR.delete_flag = 0
	                              AND MR.status > 0 -- 未採番でメールが存在した場合無視する
   WHERE M.delete_flag = 0
     AND M.status != 9 -- 退会会員は無視する
     AND (M.free_field->>'customerCode')::character varying = in_customer_code;
  ELSE
    RETURN QUERY
    -- 会員と会員申請マスタ
    SELECT MR.tenant_no,
          null::bigint as member_no,
          ''::character varying as member_id,
          MR.member_request_no,
          MR.status
   FROM t_member_request MR
   JOIN m_tenant T ON T.tenant_no = MR.tenant_no
                  AND T.tenant_no = in_tenant_no
   WHERE MR.delete_flag = 0
	  AND MR.status > 0 -- 未採番でメールが存在した場合無視する
     AND (MR.free_field->>'customerCode')::character varying = in_customer_code;
  END IF;

END;

$BODY$;
