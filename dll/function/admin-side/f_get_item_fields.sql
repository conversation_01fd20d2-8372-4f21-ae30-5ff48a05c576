CREATE OR REPLACE FUNCTION public.f_get_item_fields(
    in_tenant_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    language_code character varying,
    constant_key_strings text[],
    field_list jsonb[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- Get field list based on tenant, field division, and language code
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH fields AS (
    SELECT f.field_no AS field_no
          ,fl.language_code AS language_code
          ,fl.field_localized_no AS field_localized_no
          ,f.field_division AS field_division
          ,fl.logical_name AS logical_name
          ,f.physical_name AS physical_name
          ,f.input_type AS input_type
          ,f.required_flag AS required_flag
          ,f.data_type AS data_type
          ,f.input_data_list AS input_data_list
          ,f.max_length AS max_length
          ,f.max_value AS max_value
          ,f.regular_expressions AS regular_expressions
      FROM
          m_field f
      JOIN
          m_field_localized fl
        ON f.field_no = fl.field_no
        AND f.tenant_no = fl.tenant_no
      JOIN m_tenant mt
        ON mt.tenant_no = f.tenant_no
        AND mt.language_code_list @> ARRAY[fl.language_code]
    WHERE f.tenant_no = in_tenant_no
      AND f.field_division = 'item'
      AND (in_language_code IS NULL OR fl.language_code = in_language_code)
      AND f.delete_flag = 0
      AND fl.delete_flag = 0
    ORDER BY fl.language_code, f.order_no
  ),
  -- Get all constant keys that use in field mapping
  getConstantKeys AS (
    SELECT array_agg(DISTINCT key_string) AS key_strings
      FROM (
        SELECT DISTINCT fm.input_data_list->>'key_string' AS key_string
          FROM fields fm
          WHERE fm.input_data_list->>'key_string' IS NOT NULL
      )
  )

  SELECT f.language_code,
          c.key_strings AS constant_key_strings,
          array_agg(
            jsonb_build_object(
              'field_no', f.field_no,
              'field_localized_no', f.field_localized_no,
              'field_division', f.field_division,
              'logical_name', f.logical_name,
              'physical_name', f.physical_name,
              'input_type', f.input_type,
              'required_flag', f.required_flag,
              'data_type', f.data_type,
              'input_data_list', f.input_data_list,
              'max_length', f.max_length,
              'max_value', f.max_value,
              'regular_expressions', f.regular_expressions
            )
          ) AS field_list
    FROM fields f
    LEFT JOIN getConstantKeys c
      ON TRUE
  GROUP BY f.language_code, c.key_strings
  ORDER BY f.language_code;

END;
$BODY$;
