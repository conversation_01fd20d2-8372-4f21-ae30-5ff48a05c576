CREATE OR REPLACE FUNCTION public.f_update_csv_field_list(
    in_tenant_no bigint,
    in_csv_template_id bigint,
    in_language_code character varying,
    in_input_data_list bigint[],
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$
DECLARE

-- CSV項目マスタを更新する
-- Parameters
 -- @param in_tenant_no bigint
 -- @param in_csv_template_id bigint
 -- @param in_language_code character varying
 -- @param in_input_data_list bigint[]
----------------------------------------------------------------------------------------------------
BEGIN

    -- 更新
    UPDATE m_field_csv fc
    SET input_data_list = in_input_data_list,
        update_datetime = now()
    WHERE fc.tenant_no = in_tenant_no
      AND fc.csv_template_id = in_csv_template_id
      AND fc.language_code = in_language_code;

    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
