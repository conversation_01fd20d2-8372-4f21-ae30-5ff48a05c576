CREATE OR REPLACE FUNCTION public.f_insert_inquiry_chat_notification (
  in_tenant_no bigint,
  in_notification_category_id character varying,
  in_answer_exhibition_message_no bigint,
  in_exhibition_item_no bigint,
  in_user_no bigint,
  in_admin_no bigint
)
RETURNS TABLE(
  notification_no bigint,
  member_no bigint,
  message character varying,
  link_url character varying
)
LANGUAGE 'plpgsql'

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： お問い合わせチャット通知追加
/************************************************************************/
BEGIN
    RETURN QUERY
    WITH find_members AS (
      -- 出品者が回答する場合
      SELECT TEM.tenant_no,
              TEM.member_no,
              TEM.admin_no,
              TEM.exhibition_item_no,
              ('/chat_inquiry/' || TEM.exhibition_item_no) AS link_url
        FROM t_exhibition_message TEM
      WHERE in_answer_exhibition_message_no IS NOT NULL
        AND TEM.exhibition_message_no = in_answer_exhibition_message_no
      UNION
      -- 会員が質問する場合
      (
        SELECT TEI.tenant_no,
              CASE WHEN TEI.exhibition_division = 1 -- 自分で出品の場合はexhibition_member_noに通知とメールを送る
                    THEN TEI.exhibition_member_no
                    ELSE null -- GEO出品の場合はadminに通知とメールを送る
              END  AS member_no,
              null AS admin_no,
              TEI.exhibition_item_no,
              (CASE WHEN TEI.exhibition_division = 1 -- 自分で出品の場合は依頼一覧画面に遷移
                  THEN '/inspection'
                  ELSE '#/items/' || TEI.exhibition_item_no || '/inquiryChat'
              END) AS link_url
        FROM t_exhibition_item TEI
        WHERE in_answer_exhibition_message_no IS NULL
          AND in_exhibition_item_no IS NOT NULL
          AND TEI.exhibition_item_no = in_exhibition_item_no
      )
    ),
    category AS (
      SELECT MCL.value1,
             MCL.value2,
             MCL.value3,
             MCL.value4
        FROM m_constant MC
        INNER JOIN m_constant_localized MCL
          ON MCL.constant_no = MC.constant_no
        WHERE MC.tenant_no = in_tenant_no
          AND MC.key_string = 'NOTIFICATION_CATEGORY'
          AND MCL.value1 = in_notification_category_id
          AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
               AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    )

    INSERT INTO t_notification(
      tenant_no,
      member_no,
      notification_category_id,
      message,
      link_url,
      create_user_no,
      create_admin_no
    )
    (
      SELECT MM.tenant_no,
            COALESCE(MM.member_no, -1),
            CAT.value1,
            CAT.value3,
            MM.link_url,
            in_user_no,
            in_admin_no
      FROM find_members MM
      LEFT JOIN category CAT ON CAT.value1 = in_notification_category_id
    )
    RETURNING t_notification.notification_no,
              t_notification.member_no,
              t_notification.message,
              t_notification.link_url
              ;

END;

$BODY$;
