CREATE OR REPLACE FUNCTION public.f_delete_inquiry_chat (
    in_tenant_no bigint,
    in_exhibition_message_no bigint,
    in_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_exhibition_message_no bigint;

----------------------------------------------------------------------------------------------------
-- 問合せチャット情報を削除する
-- Parameters
-- @param
--   in_tenant_no integer,
--   in_exhibition_message_no integer
--   in_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  WITH delete_exhibition_message_no AS (
    UPDATE t_exhibition_message
       SET delete_flag = 1,
           checked_admin_no = in_admin_no
     WHERE exhibition_message_no = in_exhibition_message_no
       AND tenant_no = in_tenant_no

    RETURNING exhibition_message_no
  )

  SELECT delete_exhibition_message_no.exhibition_message_no FROM delete_exhibition_message_no
  LIMIT 1 INTO return_exhibition_message_no;

  RETURN return_exhibition_message_no;

END;

$BODY$;
