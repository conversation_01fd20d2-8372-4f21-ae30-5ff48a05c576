CREATE OR REPLACE FUNCTION public.f_get_inquiry_chat_un_read_cnt (
  in_tenant_no bigint,
  in_exhibition_item_no bigint
)
RETURNS TABLE(
  exhibition_item_no bigint,
  chat_un_read_count bigint
)
LANGUAGE 'plpgsql'

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
--  問い合わせチャットの未読カウント情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TEM.exhibition_item_no,
          COALESCE(COUNT(*), 0) as chat_un_read_count
    FROM t_exhibition_message TEM
   WHERE TEM.tenant_no = in_tenant_no
     AND TEM.exhibition_item_no = in_exhibition_item_no
     AND TEM.update_category_id = '2'
     AND TEM.checked_admin_no IS NULL
     AND TEM.delete_flag = 0
GROUP BY TEM.exhibition_item_no;

END;

$BODY$;
