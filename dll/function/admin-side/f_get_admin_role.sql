CREATE OR REPLACE FUNCTION public.f_get_admin_role(
    in_tenant_no bigint
)
RETURNS TABLE(
    target_group_id integer,
    function_id bigint,
    function_name character varying,
    allowed_role_id text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 権限設定を取得します
-- Parameters
-- @param in_tenant_no テナント番号
----------------------------------------------------------------------------------------------------
BEGIN
  RETURN QUERY
  SELECT
    m_admin_role.target_group_id,
    m_admin_role.function_id,
    m_admin_role.function_name,
    m_admin_role.allowed_role_id
  FROM
    m_admin_role
  WHERE
    m_admin_role.tenant_no = in_tenant_no
  ORDER BY
    m_admin_role.target_group_id
    , m_admin_role.function_id
  ;
END;
$BODY$;
