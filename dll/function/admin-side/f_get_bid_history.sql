CREATE OR REPLACE FUNCTION public.f_get_bid_history(
    in_tenant_no bigint,
    in_language_code character varying,
    in_exhibition_no_array bigint[]
)
RETURNS TABLE (
    product_id character varying,
    product_name character varying,
    member_id character varying,
    customer_code character varying,
    company_name character varying,
    bid_user_name character varying,
    bid_datetime character varying,
    lot_id character varying,
    bid_price numeric,
    bid_quantity numeric,
    lowest_bid_price numeric,
    after_current_price numeric,
    after_top_member_name character varying
)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 入札履歴情報を取得します
-- Parameters
-- @param in_tenant_no 運営元番号
-- @param in_language_code 言語コード
-- @param in_exhibition_no_array 開催回番号配列
----------------------------------------------------------------------------------------------------

BEGIN

RETURN QUERY

  SELECT
      TI.manage_no AS product_id
      ,(ITL.free_field->>'product_name')::character varying AS product_name
      , MM.member_id
      , (MM.free_field->>'customerCode') :: character varying AS customer_code
      , (MM.free_field->>'companyName') :: character varying AS company_name
      , TBH.bid_user_name
      , to_char(TBH.create_datetime, 'YYYY/MM/DD HH24:MI:SS') :: character varying AS bid_datetime
      , TL.lot_id
      , TBH.bid_price
      , TBH.bid_quantity
      , TEI.lowest_bid_price lowest_bid_price
      , TBH.after_current_price
      , (MMT.free_field->>'ceoName') :: character varying AS after_top_name
    FROM t_exhibition TE
        INNER JOIN t_exhibition_item TEI
                ON TE.exhibition_no = TEI.exhibition_no
        INNER JOIN t_lot TL
                ON TEI.lot_no = TL.lot_no
        INNER JOIN t_lot_detail TLD
                ON TEI.lot_no = TLD.lot_no
        INNER JOIN t_item_localized ITL
                ON TLD.item_no = ITL.item_no
                AND ITL.language_code = in_language_code
        INNER JOIN t_bid_history TBH
                ON TEI.exhibition_item_no = TBH.exhibition_item_no
        INNER JOIN t_exhibition_localized TEL
                ON TE.exhibition_no = TEL.exhibition_no
                AND TEL.language_code = in_language_code
        LEFT OUTER JOIN m_member MM
                ON MM.member_no = TBH.member_no
                AND MM.tenant_no = in_tenant_no --論理的には必要ないが名称を出力するため念のため
        LEFT OUTER JOIN m_member MMT
                ON MMT.member_no = TBH.after_top_member_no
                AND MMT.tenant_no = in_tenant_no --論理的には必要ないが名称を出力するため念のため
        JOIN t_item TI
                ON TLD.item_no = TI.item_no
  WHERE TE.tenant_no = in_tenant_no
    AND (TE.exhibition_no = ANY(in_exhibition_no_array)
          OR COALESCE(in_exhibition_no_array,'{}'::bigint[]) = '{}'::bigint[])
  ORDER BY TE.exhibition_no, TBH.create_datetime DESC
  ;
END;


$BODY$;
