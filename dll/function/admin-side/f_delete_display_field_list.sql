CREATE OR R<PERSON>LACE FUNCTION public.f_delete_display_field_list(
    in_field_mapping_no bigint,
    in_tenant_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

-- 項目表示設定一覧を削除する
-- Parameters
 -- @param in_field_mapping_no bigint
 -- @param in_tenant_no bigint
----------------------------------------------------------------------------------------------------
BEGIN

    -- 削除
    UPDATE m_field_mapping fm
    SET update_datetime = now(),
        delete_flag = 1
    WHERE fm.field_mapping_no = in_field_mapping_no
    AND fm.tenant_no = in_tenant_no;

    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
