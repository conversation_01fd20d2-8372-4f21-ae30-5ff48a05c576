CREATE OR REPLACE FUNCTION public.f_update_member_status_approve (
    in_tenant_no bigint,
    in_member_request_no bigint,
    in_tanto character varying,
    in_before_status integer,
    in_status integer,
    in_update_admin_no bigint
)
RETURNS character varying
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_member_id character varying;

----------------------------------------------------------------------------------------------------
-- 会員ステータスを変更する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_member_request_no bigint,
--   in_tanto character varying,
--   in_before_status integer,
--   in_status integer,
--   update_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  -- 承認処理
  -- 会員申請
  WITH update_member_request AS (
    UPDATE t_member_request
       SET status = in_status,
           delete_flag = (CASE WHEN in_status = 1 THEN 1 ELSE delete_flag END),
           update_admin_no = in_update_admin_no,
           update_datetime = now()
      WHERE member_request_no = in_member_request_no
      RETURNING member_request_no,
                classification,
                free_field,
                status,
                email_priority
  ),
  -- member_noを取得する
  get_member_no AS (
    SELECT nextval('m_member_no_seq'::regclass) as member_no
  ),
  -- m_memberに情報をコピー
  copy_to_member AS (
    INSERT INTO m_member(
      tenant_no,
      member_no,
      member_id,
      classification,
      currency_id,
      exhibition_allow_flag,
      bid_allow_flag,
      status,
      email_delivery_flag,
      email_priority,
      member_request_no,
      create_admin_no,
      create_datetime,
      update_admin_no,
      update_datetime,
      delete_flag,
      free_field
    )
    VALUES (
      in_tenant_no,
      (SELECT member_no FROM get_member_no),
      LPAD((SELECT member_no FROM get_member_no)::text,5,'0'),
      (SELECT classification FROM update_member_request),
      'USD',
      0,
      (CASE WHEN (SELECT status FROM update_member_request) = 1 THEN 1 ELSE 0 END),
      (SELECT status FROM update_member_request),
      (CASE WHEN (SELECT status FROM update_member_request) = 1 THEN 1 ELSE 0 END),
      (SELECT email_priority FROM update_member_request),
      (SELECT member_request_no FROM update_member_request),
      in_update_admin_no,
      now(),
      in_update_admin_no,
      now(),
      0,
      (SELECT (free_field-'password') FROM update_member_request)
    )
    RETURNING member_request_no,
              member_no,
              member_id,
              status
  ),
  -- m_userに情報をコピー
  copy_to_user AS (
    INSERT INTO m_user(
      tenant_no,
      member_no,
      user_id,
      password,
      require_password_change,
      bid_allow_flag,
      free_field,
      create_admin_no,
      create_datetime,
      update_admin_no,
      update_datetime,
      delete_flag
    )
    SELECT in_tenant_no,
            M.member_no,
            CASE WHEN T.login_option = 1 THEN M.member_id
                WHEN T.login_option = 2 THEN (MR.free_field->>'email')::character varying
                ELSE M.member_id
            END,
            (MR.free_field->>'password')::character varying,
            0,
            (CASE WHEN MR.status = 1 THEN 1 ELSE 0 END),
            null,
            in_update_admin_no,
            now(),
            in_update_admin_no,
            now(),
            0
    FROM copy_to_member M
    JOIN update_member_request MR ON MR.member_request_no = M.member_request_no
    JOIN m_tenant T ON T.tenant_no = in_tenant_no

    RETURNING user_no
  ),
  -- ステータス履歴に入れる
  insert_history AS (
    INSERT INTO t_member_status_history(
      tenant_no,
      member_request_no,
      user_name,
      before_status,
      after_status,
      create_admin_no,
      create_datetime,
      delete_flag
    ) VALUES (
      in_tenant_no,
      (SELECT member_request_no FROM update_member_request),
      in_tanto,
      in_before_status,
      (SELECT status FROM copy_to_member),
      in_update_admin_no,
      now(),
      0
    )
    RETURNING member_status_history_no
  )

  SELECT copy_to_member.member_id FROM update_member_request, get_member_no, copy_to_member, copy_to_user, insert_history
  LIMIT 1 INTO return_member_id;

  RETURN return_member_id;

END;
$BODY$;
