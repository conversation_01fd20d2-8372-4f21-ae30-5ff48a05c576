CREATE OR REPLACE FUNCTION public.f_get_items (
    in_status integer[],
    in_new_flag boolean,
    in_manage_no_from character varying,
    in_manage_no_to character varying,
    in_serial_from character varying,
    in_serial_to character varying,
    in_area_id character varying,
    in_category character varying,
    in_maker character varying,
    in_tenant_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    item_no bigint,
    manage_no character varying,
    area_id character varying,
    status integer,
    price_display_flag integer,
    new_flag boolean,
    localized_json_array json[],
    ancillary_json_array json[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 商品情報を取得する
-- Parameters
-- @param in_status 状態配列
-- @param in_new_flag 新着フラグ
-- @param in_manage_no_from 管理番号(from)
-- @param in_manage_no_to 管理番号(to)
-- @param in_serial_from SERIAL(from)
-- @param in_serial_to SERIAL(to)
-- @param in_area_id 在庫置き場ID
-- @param in_category カテゴリー
-- @param in_maker メーカー名
-- @param in_tenant_no テナントNo
----------------------------------------------------------------------------------------------------
sold_out_display_date character varying;

BEGIN

  SELECT INTO sold_out_display_date MCL.value1
    FROM m_constant MC
    JOIN m_constant_localized MCL
      ON MCL.constant_no = MC.constant_no
  WHERE MC.tenant_no = in_tenant_no
    AND MCL.language_code = in_language_code
    AND MC.key_string = 'SOLD_OUT_DISPLAY_DATE'
    AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')));

  RAISE NOTICE 'SQlログ:  %', sold_out_display_date; -- for console.log

  RETURN QUERY
  WITH Localized AS (
    SELECT L.item_no
         , array_agg(row_to_json(row(L.language_code, L.free_field))) localized_json_array
      FROM t_item_localized L
     WHERE CASE WHEN in_category IS NOT NULL THEN free_field->>'MDLGR' = any(regexp_split_to_array(in_category, ',')) ELSE true END
       AND CASE WHEN in_maker IS NOT NULL THEN free_field->>'MAKER' LIKE CONCAT('%', in_maker ,'%') ELSE true END
       AND CASE WHEN in_serial_from IS NOT NULL THEN free_field->>'ACTSERNO' >= in_serial_from ELSE true END
       AND CASE WHEN in_serial_to IS NOT NULL THEN free_field->>'ACTSERNO' <= in_serial_to ELSE true END
       AND L.language_code = in_language_code
     GROUP BY L.item_no
  ), Ancillary AS (
    SELECT AF.manage_no
         , array_agg(row_to_json(row(AF.language_code, AF.serial_number, AF.file_path))) ancillary_json_array
      FROM t_item_ancillary_file AF
     WHERE AF.division = 1
       AND AF.serial_number = 1
       AND AF.delete_flag = 0
     GROUP BY AF.manage_no
  )
  SELECT I.item_no
       , I.manage_no
       , I.area_id
       , I.status
       , I.price_display_flag
       , CASE WHEN I.new_start_datetime IS NOT NULL THEN true ELSE false END AS new_flag
       , Localized.localized_json_array
       , Ancillary.ancillary_json_array
    FROM t_item I
    LEFT JOIN Localized
      ON Localized.item_no = I.item_no
    LEFT JOIN Ancillary
      ON Ancillary.manage_no = I.manage_no
   WHERE CASE WHEN array_length(in_status, 1) = 1 AND in_status && ARRAY[3] THEN I.status = 3 AND linked_flag = 1 AND current_timestamp < (linked_datetime + CAST(sold_out_display_date || ' days' AS interval))
              WHEN array_length(in_status, 1) > 1 AND in_status && ARRAY[3] THEN (I.status = any(in_status) AND I.status <> 3) OR (I.status = 3 AND linked_flag = 1 AND current_timestamp < (linked_datetime + CAST(sold_out_display_date || ' days' AS interval)))
              WHEN array_length(in_status, 1) > 0                           THEN I.status = any(in_status)
              ELSE I.status IN (0,1) OR (I.status = 3 AND linked_flag = 1 AND current_timestamp < (linked_datetime + CAST(sold_out_display_date || ' days' AS interval))) END
     AND CASE WHEN in_new_flag IS TRUE THEN I.new_start_datetime IS NOT NULL ELSE true END
     AND CASE WHEN in_manage_no_from IS NOT NULL THEN I.manage_no >= in_manage_no_from ELSE true END
     AND CASE WHEN in_manage_no_to IS NOT NULL THEN I.manage_no <= in_manage_no_to ELSE true END
     AND CASE WHEN in_area_id IS NOT NULL THEN I.area_id = in_area_id ELSE true END
     AND Localized.localized_json_array IS NOT NULL
     AND I.tenant_no = in_tenant_no
     AND I.delete_flag = 0
   ORDER BY I.manage_no;

END;

$BODY$;
