CREATE OR REPLACE FUNCTION public.f_delete_exhibitions_email(
    in_exhibition_email_no bigint,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_exhibition_email_no bigint;

----------------------------------------------------------------------------------------------------
-- 入札会のメールを削除する
-- Parameters
-- @param in_exhibition_email_no key bigint
-- @param in_update_admin_no key bigint
----------------------------------------------------------------------------------------------------

BEGIN

  return_exhibition_email_no:=0;

  IF EXISTS ( SELECT 1 FROM t_exhibition_email WHERE exhibition_email_no = in_exhibition_email_no AND delete_flag = 0) THEN
    UPDATE t_exhibition_email
      SET delete_flag = 1,
          update_admin_no = in_update_admin_no,
          update_datetime = now()
      WHERE exhibition_email_no = in_exhibition_email_no
    RETURNING exhibition_email_no INTO return_exhibition_email_no;

    IF EXISTS ( SELECT 1 FROM t_exhibition_email_localized WHERE exhibition_email_no = in_exhibition_email_no ) THEN
      UPDATE t_exhibition_email_localized
        SET delete_flag = 1,
            update_admin_no = in_update_admin_no,
            update_datetime = now()
        WHERE exhibition_email_no = in_exhibition_email_no;
    END IF;
  END IF;

  RETURN return_exhibition_email_no;

END;

$BODY$;
