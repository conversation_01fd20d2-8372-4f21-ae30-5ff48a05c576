CREATE OR REPLACE FUNCTION public.f_update_item_price_display_flag (
    in_item_nos bigint[],
    in_price_display_flag integer,
    in_tenant_no bigint,
    in_admin_no bigint,
    OUT result boolean,
    OUT return_status bigint,
    OUT message character varying
)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 商品の価格表示フラグを変更する
-- Parameters
-- @param
--   in_item_nos bigint[],
--   in_price_display_flag integer,
--   in_tenant_no bigint,
--   in_admin_no bigint
----------------------------------------------------------------------------------------------------
BEGIN

  -- 商品情報更新
  UPDATE t_item
     SET price_display_flag = in_price_display_flag
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
  WHERE item_no = ANY(in_item_nos)
    AND tenant_no = in_tenant_no
    AND delete_flag = 0
    AND status <> 2;

  result := true;
  return_status := 200;
  message := '';

RETURN NEXT;

EXCEPTION

  --その他エラー
  WHEN OTHERS THEN
    result := false;
    return_status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
