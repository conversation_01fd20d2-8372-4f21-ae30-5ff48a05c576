CREATE OR REPLACE FUNCTION public.f_insert_or_update_admin (
    in_tenant_no bigint,
    in_admin_name character varying,
    in_login_id character varying,
    in_password character varying,
    in_role_id character varying,
    in_create_admin_no bigint,
    in_update_admin_no bigint,
    in_delete_flag integer
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_admin_no bigint;

----------------------------------------------------------------------------------------------------
-- 管理者情報を追加する
-- Parameters
-- @param
--   in_admin_no bigint,
-- 　in_tenant_no integer,
--   admin_name character varying,
--   login_id character varying,
--   password character varying,
--   role_id character varying,
--   create_admin_no bigint,
--   update_admin_no bigint,
--   in_delete_flag integer
----------------------------------------------------------------------------------------------------

BEGIN

  IF NOT EXISTS ( SELECT 1 FROM m_admin WHERE tenant_no=in_tenant_no AND login_id=in_login_id ) THEN
    INSERT INTO m_admin(
      tenant_no,
      admin_name,
      login_id,
      password,
      role_id,
      delete_flag,
      create_admin_no,
      create_datetime,
      update_admin_no,
      update_datetime
    ) VALUES (
      in_tenant_no,
      in_admin_name,
      in_login_id,
      in_password,
      in_role_id,
      in_delete_flag,
      in_create_admin_no,
      now(),
      in_update_admin_no,
      now()
    ) RETURNING admin_no INTO return_admin_no;
  ELSE
    UPDATE m_admin
      SET admin_name = in_admin_name,
          password = (CASE WHEN in_password IS NOT NULL THEN in_password ELSE password END),
          role_id = (CASE WHEN in_role_id IS NOT NULL THEN in_role_id ELSE role_id END),
          delete_flag = (CASE WHEN in_delete_flag IS NOT NULL THEN in_delete_flag ELSE delete_flag END),
          update_admin_no = in_update_admin_no,
          update_datetime = now()
      WHERE tenant_no = in_tenant_no
        AND login_id = in_login_id
    RETURNING admin_no INTO return_admin_no;
  END IF;

  RETURN return_admin_no;

END;

$BODY$;
