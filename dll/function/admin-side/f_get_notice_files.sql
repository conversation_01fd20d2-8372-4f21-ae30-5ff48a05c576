CREATE OR REPLACE FUNCTION public.f_get_notice_files (
    in_notice_no bigint
)
RETURNS TABLE(
    file text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- お知らせの添付ファイルを取得する
-- Parameters
-- @param 
-- 　in_notice_no integer
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT B.file
   FROM t_notice A
   JOIN t_notice_localized B ON A.notice_no = B.notice_no
   JOIN m_tenant T ON T.tenant_no = A.tenant_no
    WHERE A.notice_no = in_notice_no
      AND COALESCE(array_length(B.file, 1), 0) > 0
      AND A.delete_flag = 0
      AND B.delete_flag = 0;

END;

$BODY$;
