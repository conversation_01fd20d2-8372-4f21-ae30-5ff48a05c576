CREATE OR REPLACE FUNCTION public.f_get_csv_field_list(
    in_tenant_no bigint,
    in_template_id bigint,
    in_language_code character varying
)
RETURNS TABLE(
    field_no bigint,
    field_localized_no bigint,
    logical_name character varying,
    physical_name character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- CSV項目設定のリストを取得する
-- Parameters
-- @param in_tenant_no character varying - テナント番号
-- @param in_template_id bigint - テンプレートID
-- @param in_language_code character varying - 言語区分
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT f.field_no AS field_no
        ,fl.field_localized_no AS field_localized_no
        ,fl.logical_name AS logical_name
        ,f.physical_name AS physical_name
    FROM
    (
      SELECT t.field_no, t.ordinality
      FROM m_field_csv mfc
      CROSS JOIN LATERAL UNNEST(mfc.input_data_list) WITH ORDINALITY AS t(field_no, ordinality)
      WHERE mfc.tenant_no = in_tenant_no
        AND mfc.csv_template_id = in_template_id
        AND mfc.language_code = in_language_code
        AND mfc.delete_flag = 0
    ) fc
    JOIN
      m_field f
    ON fc.field_no = f.field_no
     JOIN
      m_field_localized fl
    ON f.field_no = fl.field_no
    WHERE f.tenant_no = in_tenant_no
      AND fl.tenant_no = in_tenant_no
      AND fl.language_code = in_language_code
      AND f.delete_flag = 0
      AND fl.delete_flag = 0
    ORDER BY fc.ordinality;

END;
$BODY$;
