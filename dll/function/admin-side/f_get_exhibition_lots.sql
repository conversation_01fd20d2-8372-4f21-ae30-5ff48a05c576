CREATE OR REPLACE FUNCTION public.f_get_exhibition_lots (
    in_tenant_no bigint,
    in_exhibition_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    exhibition_no bigint,
    category_id integer,
    lot_no bigint,
    lot_id character varying,
    cancel_flag integer,
    bid_count integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 出展機情報を取得する
-- Parameters
-- @param in_exhibition_no 入札会No
-- @param in_tenant_no テナントNo
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT E.tenant_no
       , E.exhibition_no
       , E.category_id
       , L.lot_no
       , L.lot_id
       , EI.cancel_flag
       , EI.bid_count
    FROM t_exhibition E
    LEFT JOIN t_exhibition_item EI ON EI.exhibition_no = E.exhibition_no and EI.delete_flag = 0
    LEFT JOIN t_lot L  ON L.lot_no = EI.lot_no AND L.delete_flag = 0
   WHERE E.exhibition_no = in_exhibition_no
     AND E.tenant_no = in_tenant_no
     AND EI.bid_count = 0
     AND CASE WHEN COALESCE(EI.hummer_flag, 999) <> 999 THEN EI.hummer_flag <> 1 ELSE true END
     AND E.delete_flag = 0
   ORDER BY E.exhibition_no, L.lot_no, L.lot_id;

END;

$BODY$;
