CREATE OR REPLACE FUNCTION public.f_update_member (
    in_tenant_no bigint,
    in_member_no bigint,
    in_bid_allow_flag integer,
    in_email_delivery_flag integer,
    in_email_priority integer,
    in_status integer,
    in_password character varying,
    in_free_field jsonb,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_member_no bigint;

----------------------------------------------------------------------------------------------------
-- 会員情報を更新する
-- Parameters
-- @param 
--   in_tenant_no bigint,
--   in_member_no bigint,
--   in_bid_allow_flag integer,
--   in_email_delivery_flag integer,
--   in_email_priority integer,
--   in_status integer,
--   in_password character varying,
--   in_free_field jsonb,
--   in_update_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  -- 会員
  WITH update_member AS (
    UPDATE m_member
       SET email_delivery_flag = (CASE WHEN in_email_delivery_flag IS NULL THEN email_delivery_flag ELSE in_email_delivery_flag END),
           email_priority = (CASE WHEN in_email_priority IS NULL THEN email_priority ELSE in_email_priority END),
           bid_allow_flag = (CASE WHEN in_bid_allow_flag IS NULL THEN bid_allow_flag ELSE in_bid_allow_flag END),
           status = (CASE WHEN in_status IS NULL THEN status ELSE in_status END),
           free_field = in_free_field,
           update_admin_no = in_update_admin_no,
           update_datetime = now()
      WHERE member_no = in_member_no
    RETURNING member_no, member_id, member_request_no, free_field
  ),
  -- m_user更新
  update_user AS (
    UPDATE m_user U
       SET user_id = (CASE WHEN T.login_option = 1 THEN U.user_id
                           WHEN T.login_option = 2 THEN M.free_field->>'email'::character varying
                           ELSE U.user_id
                      END),
           password = (CASE WHEN in_password IS NULL THEN U.password ELSE in_password END),
           bid_allow_flag = (CASE WHEN in_bid_allow_flag IS NULL THEN U.bid_allow_flag ELSE in_bid_allow_flag END),
           update_admin_no = in_update_admin_no,
           update_datetime = now()
      FROM update_member M
      JOIN m_tenant T ON T.tenant_no = in_tenant_no
     WHERE U.member_no = M.member_no
       AND (in_password IS NOT NULL OR in_bid_allow_flag IS NOT NULL)
    RETURNING user_no
  )

  SELECT update_member.member_no FROM update_member, update_user
  LIMIT 1 INTO return_member_no;

  RETURN return_member_no;

END;
$BODY$;
