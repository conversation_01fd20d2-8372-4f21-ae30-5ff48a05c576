CREATE OR REPLACE FUNCTION public.f_delete_exhibitions (
    in_tenant_no bigint,
    in_exhibition_no bigint,
    in_admin_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

----------------------------------------------------------------------------------------------------
-- 入札会情報を削除する
-- Parameters
-- @param in_exhibition_no 開催回番号
-- @param in_admin_no 管理者番号
----------------------------------------------------------------------------------------------------
DECLARE
return_exhibition_item_no bigint;
tmp_exhibition_no bigint;

BEGIN
  SELECT DISTINCT(exhibition_no) FROM t_exhibition TE
   WHERE TE.exhibition_no = in_exhibition_no AND now() > TE.start_datetime
    INTO tmp_exhibition_no;
  -- 開始した時に削除できない
  IF tmp_exhibition_no IS NOT NULL THEN
    result := false;
    status := 400;
    message := 'EXHIBITION_STARTED_EXCEPTION';
    RAISE EXCEPTION 'EXHIBITION_STARTED_EXCEPTION';
  END IF;

  WITH exhibition_info AS (
    SELECT TE.exhibition_no AS te_exhibition_no,
			    TEI.exhibition_no,
          TEI.exhibition_item_no,
          TL.lot_no,
          TI.item_no
      FROM t_exhibition TE
	    LEFT JOIN t_exhibition_item TEI ON TE.exhibition_no = TEI.exhibition_no
      LEFT JOIN t_lot TL
        ON TL.lot_no = TEI.lot_no
       AND TL.delete_flag = 0
      LEFT JOIN t_lot_detail TLD ON TLD.lot_no = TL.lot_no
      LEFT JOIN t_item TI ON TI.item_no = TLD.item_no AND TI.delete_flag = 0
    WHERE TE.tenant_no = 1
      AND TE.exhibition_no = in_exhibition_no
      AND TE.delete_flag = 0
--       AND TEI.cancel_flag = 0
      AND tmp_exhibition_no IS NULL
  ),
  delete_lot_detail AS (
    DELETE FROM t_lot_detail TLD
          WHERE TLD.lot_no IN (SELECT EI.lot_no FROM exhibition_info EI)
  ),
  delete_lot AS (
    UPDATE t_lot
     SET delete_flag = 1
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE lot_no IN (SELECT lot_no FROM exhibition_info)
  ),
  delete_item_localized AS (
    UPDATE t_item_localized TIL
     SET delete_flag = 1
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE TIL.item_no IN (SELECT EI.item_no FROM exhibition_info EI)
  ),
  delete_item_ancillary_file AS (
    UPDATE t_item_ancillary_file TIAF
     SET delete_flag = 1
       , update_datetime = current_timestamp
   WHERE TIAF.item_no IN (SELECT EI.item_no FROM exhibition_info EI)
  ),
  delete_item AS (
    UPDATE t_item TI
     SET delete_flag = 1
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE TI.item_no IN (SELECT EI.item_no FROM exhibition_info EI)
  ),
  delete_exhibition_item AS (
    UPDATE t_exhibition_item TEI
      SET delete_flag = 1
        , update_admin_no = in_admin_no
        , update_datetime = current_timestamp
    WHERE TEI.exhibition_item_no IN (SELECT EI.exhibition_item_no FROM exhibition_info EI)
  ),
  update_exhibition AS (
    UPDATE t_exhibition
      SET update_admin_no = in_admin_no
        , update_datetime = now()
        , delete_flag = 1
    WHERE exhibition_no IN (SELECT DISTINCT(te_exhibition_no) FROM exhibition_info)
  ),
  update_exhibition_localized AS (
    UPDATE t_exhibition_localized
      SET update_admin_no = in_admin_no
        , update_datetime = now()
        , delete_flag = 1
    WHERE exhibition_no IN (SELECT DISTINCT(te_exhibition_no) FROM exhibition_info)
  ),
  update_exhibition_summary AS (
    UPDATE t_exhibition_summary
      SET update_admin_no = in_admin_no
        , update_datetime = now()
        , delete_flag = 1
    WHERE exhibition_no IN (SELECT DISTINCT(te_exhibition_no) FROM exhibition_info)
    RETURNING exhibition_no
  )

  SELECT 200 INTO status FROM update_exhibition_summary;

  result := true;
  status := 200;
  message := '';

  RETURN NEXT;
  
  EXCEPTION
    --更新対象データなし
    WHEN RAISE_EXCEPTION THEN
      RETURN NEXT;
      
    WHEN OTHERS THEN
      result := false;
      message := SQLERRM;
      RETURN NEXT;

END;

$BODY$;
