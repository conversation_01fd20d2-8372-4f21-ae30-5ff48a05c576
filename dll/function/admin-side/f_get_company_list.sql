CREATE OR REPLACE FUNCTION public.f_get_company_list (
    in_tenant_no bigint,
    in_company_name character varying
)
RETURNS TABLE(
    customer_cd text,
    company_name text,
    ceo_name text
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員一覧を取得する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_company_name character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  -- 会員
  SELECT max(M2.customer_cd),
         max(M2.company_name),
         max(M2.ceo_name)
  FROM (
    SELECT  M.tenant_no,
            COALESCE(((M.free_field->>'customerCd')::character varying), '') as customer_cd,
            COALESCE(((M.free_field->>'companyName')::character varying), '') as company_name,
            COALESCE(((M.free_field->>'representativeDirector')::character varying), '') as ceo_name
     FROM m_member M
     JOIN m_tenant T ON T.tenant_no = M.tenant_no
                    AND T.tenant_no = in_tenant_no
    WHERE M.delete_flag = 0
      AND (in_company_name IS NULL OR length(in_company_name) = 0 OR position(LOWER(in_company_name) in LOWER((M.free_field->>'companyName')))>0)
  ) M2
  group by M2.customer_cd, M2.company_name, M2.ceo_name
  order by M2.customer_cd, M2.company_name, M2.ceo_name;

END;

$BODY$;
