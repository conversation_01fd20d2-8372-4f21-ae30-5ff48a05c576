CREATE OR REPLACE FUNCTION public.f_regist_update_field(
    in_field_no bigint,
    in_field_localized_no bigint,
    in_tenant_no bigint,
    in_field_division character varying,
    in_physical_name character varying,
    in_input_type character varying,
    in_input_data_list jsonb,
    in_data_type character varying,
    in_max_length integer,
    in_max_value integer,
    in_regular_expressions character varying,
    in_required_flag integer,
    in_order_no integer,
    in_language_code character varying,
    in_logical_name character varying,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

-- 定数を更新する
-- Parameters
 -- @param in_field_no bigint
 -- @param in_field_localized_no bigint
 -- @param in_tenant_no bigint
 -- @param in_field_division character varying
 -- @param in_physical_name character varying
 -- @param in_input_type character varying
 -- @param in_input_data_list jsonb
 -- @param in_data_type character varying
 -- @param in_max_length integer
 -- @param in_max_value integer
 -- @param in_regular_expressions character varying
 -- @param in_required_flag integer
 -- @param in_order_no integer
 -- @param in_language_code character varying
 -- @param in_logical_name character varying
----------------------------------------------------------------------------------------------------
BEGIN

    WITH
    -- 更新
    exist_field_data AS (
        UPDATE m_field f
        SET physical_name = in_physical_name,
            input_type = in_input_type,
            input_data_list = in_input_data_list,
            data_type = in_data_type,
            max_length = in_max_length,
            max_value = in_max_value,
            regular_expressions = in_regular_expressions,
            required_flag = in_required_flag,
            update_datetime = now()
        WHERE f.field_no = in_field_no
        AND f.tenant_no = in_tenant_no
        RETURNING
            f.field_no
    ),

    exist_field_localized_data AS (
        UPDATE m_field_localized fl
        SET logical_name = in_logical_name,
            update_datetime = now()
        WHERE fl.field_localized_no = in_field_localized_no
        AND fl.tenant_no = in_tenant_no

        RETURNING
            fl.field_localized_no
    ),

    -- 既存データの確認（最初の1件を取得）
    existing_field AS (
        SELECT field_no
        FROM m_field
        WHERE tenant_no = in_tenant_no
          AND field_division = in_field_division
          AND physical_name = in_physical_name
          AND delete_flag = 0
        LIMIT 1
    ),

    -- 新規登録
    new_field_data AS (
        INSERT INTO m_field (
            tenant_no,
            field_division,
            physical_name,
            input_type,
            input_data_list,
            data_type,
            max_length,
            max_value,
            regular_expressions,
            required_flag,
            admin_display_flag,
            admin_update_flag,
            member_display_flag,
            member_update_flag,
            order_no,
            create_datetime,
            update_datetime,
            delete_flag
        )
        (
            SELECT
                in_tenant_no,
                in_field_division,
                in_physical_name,
                in_input_type,
                in_input_data_list,
                in_data_type,
                in_max_length,
                in_max_value,
                in_regular_expressions,
                in_required_flag,
                1,
                1,
                0,
                0,
                in_order_no,
                now(),
                now(),
                0
           WHERE NOT EXISTS (
                SELECT 1
                FROM existing_field
            )
        )
        RETURNING
            field_no
    ),

    new_field_localized_data AS (
        INSERT INTO m_field_localized (
            tenant_no,
            field_no,
            language_code,
            logical_name,
            create_datetime,
            update_datetime,
            delete_flag
        )
        (
            SELECT
                in_tenant_no,
                COALESCE(
                    (SELECT field_no FROM existing_field),
                    (SELECT field_no FROM new_field_data)
                ),
                in_language_code,
                in_logical_name,
                now(),
                now(),
                0
            WHERE
                in_field_localized_no IS NULL
        )
        RETURNING
            field_localized_no
    )

    SELECT 200 INTO status FROM exist_field_data, exist_field_localized_data, new_field_data, new_field_localized_data;
    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
