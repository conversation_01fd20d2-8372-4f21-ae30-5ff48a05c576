-- FUNCTION: public.f_get_exhibition_by_name(bigint, character varying, character varying)

-- DROP FUNCTION IF EXISTS public.f_get_exhibition_by_name(bigint, character varying, character varying);

CREATE OR REPLACE FUNCTION public.f_get_exhibition_by_name(
	in_tenant_no bigint,
	in_language_code character varying,
	in_exhibition_name character varying)
    RETURNS TABLE(exhibition_no bigint, exhibition_name character varying, category_id integer, status integer, end_datetime timestamp with time zone, bid_count integer) 
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE PARALLEL UNSAFE
    ROWS 1000

AS $BODY$
DECLARE
----------------------------------------------------------------------------------------------------
-- 入札会の開催回番号を取得します
-- Parameters
-- @param in_tenant_no テナント番号
-- @param in_language_code 言語コード(管理者はja固定)
-- @param in_exhibition_name 開催名
----------------------------------------------------------------------------------------------------
BEGIN
  RETURN QUERY
  SELECT TE.exhibition_no,
  in_exhibition_name AS exhibition_name,
    TE.category_id,
    TE.status,
    TE.end_datetime,
	  COALESCE(TEI.bid_count, 0)
    FROM t_exhibition TE
          INNER JOIN t_exhibition_localized TEL
                  ON TE.exhibition_no = TEL.exhibition_no
                  AND TEL.language_code = 'ja'
                  AND TEL.exhibition_name = in_exhibition_name
			LEFT JOIN t_exhibition_item TEI ON TE.exhibition_no=TEI.exhibition_no
    WHERE TE.tenant_no = in_tenant_no
      AND TE.delete_flag = 0
    ORDER BY TE.exhibition_no;
END;
$BODY$;

ALTER FUNCTION public.f_get_exhibition_by_name(bigint, character varying, character varying)
    OWNER TO postgres;
