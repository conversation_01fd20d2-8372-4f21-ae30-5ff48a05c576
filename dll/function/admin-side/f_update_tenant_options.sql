CREATE OR REPLACE FUNCTION public.f_update_tenant_options (
    in_tenant_no bigint,
    in_function_options jsonb,
    in_bid_options jsonb
)
RETURNS TABLE(
    tenant_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- Update tenant options (function_options, bid_options)
----------------------------------------------------------------------------------------------------

BEGIN
  -- Update function_options and bid_options for the specified tenant
  RETURN QUERY
  UPDATE m_tenant MT
    SET function_options = COALESCE(function_options, '{}'::jsonb) || in_function_options,
        bid_options = COALESCE(bid_options, '{}'::jsonb) || in_bid_options,
        update_datetime = NOW()
   WHERE MT.tenant_no = in_tenant_no
     AND MT.delete_flag = 0
  RETURNING
    MT.tenant_no;

END;

$BODY$;
