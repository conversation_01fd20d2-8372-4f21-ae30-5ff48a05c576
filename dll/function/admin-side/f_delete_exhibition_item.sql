CREATE OR REPLACE FUNCTION public.f_delete_exhibition_item (
    in_tenant_no bigint,
    in_exhibition_no bigint,
    in_lot_no bigint,
    in_admin_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying
)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- ロットの出品を削除する
-- Parameters
-- @param
--   in_tenant_no integer,
--   in_exhibition_no bigint,
--   in_lot_no bigint,
--   in_admin_no bigint
----------------------------------------------------------------------------------------------------
return_exhibition_item_no bigint;
exhibition_status integer;

BEGIN
  
  SELECT TE.status FROM t_exhibition TE WHERE TE.exhibition_no = in_exhibition_no
  INTO exhibition_status;

  IF exhibition_status <> 0 THEN
    result := false;
    status := 400;
    message := 'EXHIBITION_ENDED_EXCEPTION';
    RAISE EXCEPTION 'EXHIBITION_ENDED_EXCEPTION';
  END IF;

  -- 削除
  WITH exhibition_info AS (
    SELECT TEI.exhibition_no,
          TEI.exhibition_item_no,
          TL.lot_no,
          TI.item_no
      FROM t_exhibition_item TEI
      JOIN t_lot TL
        ON TL.lot_no = TEI.lot_no
       AND TL.lot_no = in_lot_no
       AND TL.delete_flag = 0
      JOIN t_lot_detail TLD ON TLD.lot_no = TL.lot_no
      JOIN t_item TI ON TI.item_no = TLD.item_no AND TI.delete_flag = 0
    WHERE TEI.tenant_no = in_tenant_no
      AND TEI.exhibition_no = in_exhibition_no
      AND TEI.lot_no= in_lot_no
      AND TEI.delete_flag = 0
      AND TEI.cancel_flag = 0
      AND exhibition_status = 0
  ),
  delete_lot_detail AS (
    DELETE FROM t_lot_detail TLD
          WHERE TLD.lot_no IN (SELECT EI.lot_no FROM exhibition_info EI)
  ),
  delete_lot AS (
    UPDATE t_lot
     SET delete_flag = 1
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE lot_no IN (SELECT lot_no FROM exhibition_info)
  ),
  delete_item_localized AS (
    UPDATE t_item_localized TIL
     SET delete_flag = 1
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE TIL.item_no IN (SELECT EI.item_no FROM exhibition_info EI)
  ),
  delete_item_ancillary_file AS (
    UPDATE t_item_ancillary_file TIAF
     SET delete_flag = 1
       , update_datetime = current_timestamp
   WHERE TIAF.item_no IN (SELECT EI.item_no FROM exhibition_info EI)
  ),
  delete_item AS (
    UPDATE t_item TI
     SET delete_flag = 1
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE TI.item_no IN (SELECT EI.item_no FROM exhibition_info EI)
  ),
  delete_exhibition_item AS (
    UPDATE t_exhibition_item TEI
      SET delete_flag = 1
        , update_admin_no = in_admin_no
        , update_datetime = current_timestamp
    WHERE TEI.exhibition_item_no IN (SELECT EI.exhibition_item_no FROM exhibition_info EI)
  )
  SELECT 200
   INTO status
   FROM exhibition_info;

  -- 出品数の更新
  UPDATE t_exhibition_summary
     SET exhibition_item_count = (
          SELECT COUNT(*)
            FROM t_exhibition_item EI
           WHERE EI.exhibition_no = in_exhibition_no
               AND EI.delete_flag = 0
               AND EI.cancel_flag = 0
        )
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE exhibition_no = in_exhibition_no
     AND exhibition_status = 0;

  result := true;
  status := 200;
  message := '';

RETURN NEXT;

EXCEPTION

  --更新対象データなし
  WHEN RAISE_EXCEPTION THEN
    RETURN NEXT;

  --その他エラー
  WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
