CREATE OR REPLACE FUNCTION public.f_get_constants_by_keys (
    in_key_strings character varying[],
    in_tenant_no bigint,
    in_admin_language_code character varying
)
RETURNS TABLE(
    constant_no bigint,
    key_string character varying,
    value_name character varying,
    constant_localized_no bigint,
    language_code character varying,
    value1 character varying,
    value2 character varying,
    value3 character varying,
    value4 character varying,
    value5 character varying,
    file_url character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- キー項目に基づき定数情報を取得する
-- Parameters
-- @param in_key_strings 定数キー配列
-- @param in_tenant_no テナントNo
-- @param in_admin_language_code 管理者利用言語コード
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT C.constant_no
       , C.key_string
       , C.value_name
       , CL.constant_localized_no
       , CL.language_code
       , CL.value1
       , CL.value2
       , CL.value3
       , CL.value4
       , CL.value5
       , CL.file_url
    FROM m_constant C
    LEFT JOIN m_constant_localized CL
           ON C.constant_no = CL.constant_no
   WHERE C.tenant_no = in_tenant_no
     AND CL.tenant_no = in_tenant_no
     AND CASE WHEN in_admin_language_code IS NOT NULL THEN CL.language_code = in_admin_language_code OR CL.language_code = 'common' ELSE true END
     AND CASE WHEN in_key_strings IS NOT NULL THEN C.key_string = ANY(in_key_strings) ELSE true END
     AND (now() BETWEEN COALESCE(C.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD')) AND COALESCE(C.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
   ORDER BY C.sort_order, CL.constant_localized_no;

END;

$BODY$;
