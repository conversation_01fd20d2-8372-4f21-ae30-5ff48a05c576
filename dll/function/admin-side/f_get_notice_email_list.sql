CREATE OR REPLACE FUNCTION public.f_get_notice_email_list (
    in_tenant_no bigint,
    in_sent_flag integer[],
    in_title character varying,
    in_start_datetime character varying,
    in_end_datetime character varying
)
RETURNS TABLE(
    notice_email_no bigint,
    sent_flag integer,
    sent_flag_name text,
    send_datetime text,
    email_priority integer,
    email_priority_name character varying,
    language_code character varying,
    title character varying,
    body_title_upper_row character varying,
    body_title_lower_row character varying,
    body text,
    footer text,
    file text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
  
----------------------------------------------------------------------------------------------------
-- お知らせメール情報を取得する
-- Parameters
-- @param 
--   in_tenant_no bigint,
--   in_sent_flag integer[],
--   in_title character varying,
--   in_start_datetime character varying,
--   in_end_datetime character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT  A.notice_email_no,
          A.sent_flag,
          CASE WHEN A.sent_flag = 0 THEN '未送信'
              WHEN A.sent_flag = 1 THEN '送信済み'
              ELSE ''
           END as sent_flag_name,
          to_char(A.send_datetime, 'YYYY/MM/DD HH24:MI') as send_datetime,
          A.email_priority,
          mcl.value2 as email_priority_name,
          B.language_code,
          B.title,
          B.body_title_upper_row,
          B.body_title_lower_row,
          B.body,
          B.footer,
          B.file
   FROM t_notice_email A
   JOIN t_notice_email_localized B ON A.notice_email_no = B.notice_email_no
   JOIN m_tenant T ON T.tenant_no = A.tenant_no
   LEFT OUTER JOIN (
      SELECT mcl.value1
           , mcl.value2
           , mcl.language_code
        FROM m_constant mc
        INNER JOIN m_constant_localized mcl ON mcl.constant_no = mc.constant_no
       WHERE mc.tenant_no = in_tenant_no
         AND mc.key_string = 'EMAIL_NOTICE_PRIORITY'
         AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    ) mcl ON mcl.value1 = A.email_priority::character varying AND mcl.language_code = B.language_code
    WHERE T.tenant_no = in_tenant_no
      AND (CASE WHEN in_sent_flag IS NOT NULL THEN A.sent_flag = ANY(in_sent_flag) ELSE true END)
      AND (in_title IS NULL OR length(in_title) = 0 OR position(LOWER(in_title) in LOWER(B.title))>0)
      AND (in_start_datetime IS NULL OR A.send_datetime >= to_timestamp(in_start_datetime, 'YYYY/MM/DD HH24:MI:SS'))
      AND (in_end_datetime IS NULL OR A.send_datetime <= to_timestamp(in_end_datetime, 'YYYY/MM/DD HH24:MI:SS'))
      AND A.delete_flag = 0
      AND B.delete_flag = 0
    ORDER BY A.send_datetime DESC, A.notice_email_no, B.language_code DESC;

END;

$BODY$;
