CREATE OR REPLACE FUNCTION public.f_get_exhibition_result(
    in_tenant_no bigint,
    in_language_code character varying,
    in_exhibition_no_array bigint[])
RETURNS TABLE(
    manage_no character varying,    -- manage_no will use as product_id
    lot_id character varying,
    item_field jsonb,
    hummer_flag integer,
    quantity numeric,
    lowest_bid_price numeric,
    lowest_bid_quantity numeric,
    lowest_bid_accept_price numeric,
    lowest_bid_accept_quantity numeric,
    bid_price numeric,
    bid_quantity numeric,
    bid_success_price numeric,
    bid_success_quantity numeric,
    bid_success_member_id character varying,
    tax_rate numeric,
    member_field jsonb
)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$
DECLARE
----------------------------------------------------------------------------------------------------
-- 入札会の結果を取得します
-- Parameters
-- @param in_tenant_no テナント番号
-- @param in_exhibition_no_array 開催回番号配列
----------------------------------------------------------------------------------------------------
BEGIN
  RETURN QUERY
    SELECT TER.manage_no
          , TER.lot_id
          , TER.item_field
          , CASE WHEN TERD.bid_success_member_no IS NULL THEN 2 ELSE 1 END
          , TEI.quantity
          , TEI.lowest_bid_price
          , TEI.lowest_bid_quantity
          , TEI.lowest_bid_accept_price
          , TEI.lowest_bid_accept_quantity
          , TB.bid_price
          , TB.bid_quantity
          , TERD.bid_success_price
          , TERD.bid_success_quantity
          , MM.member_id AS bid_success_member_id
          , TER.tax_rate
          , MM.free_field
      FROM t_exhibition_result TER
      INNER JOIN t_exhibition_item TEI
              ON TEI.exhibition_item_no = TER.exhibition_item_no
            AND TEI.tenant_no = TER.tenant_no
      LEFT JOIN t_bid TB
              ON TB.exhibition_item_no = TEI.exhibition_item_no
      LEFT JOIN t_exhibition_result_detail TERD
              ON TERD.exhibition_result_no = TER.exhibition_result_no
			 AND TERD.bid_success_member_no = TB.member_no
      LEFT JOIN m_member MM
            ON MM.member_no = TB.member_no
      LEFT OUTER JOIN (
          SELECT MCL.value1, MCL.value2
            FROM m_constant MC
                  INNER JOIN m_constant_localized MCL
                          ON MC.constant_no = MCL.constant_no
                        AND MCL.language_code = in_language_code
            WHERE MC.key_string = 'PRODUCT_CATEGORY'
              AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
              AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
      ) MG ON TER.item_field->>'category' = MG.value1
    WHERE TER.tenant_no = in_tenant_no
      AND (
            TER.exhibition_no = ANY(in_exhibition_no_array)
            OR COALESCE(in_exhibition_no_array,'{}'::bigint[]) = '{}'::bigint[]
          )
    ORDER BY TER.manage_no, CASE WHEN TERD.bid_success_member_no IS NULL THEN 2 ELSE 1 END, MM.member_id;
END;
$BODY$;
