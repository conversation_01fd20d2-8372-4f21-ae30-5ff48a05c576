CREATE OR REPLACE FUNCTION public.f_update_member_status_others (
    in_tenant_no bigint,
    in_member_request_no bigint,
    in_tanto character varying,
    in_before_status integer,
    in_status integer,
    in_update_admin_no bigint
)
RETURNS character varying
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_member_id character varying;

----------------------------------------------------------------------------------------------------
-- 会員ステータスを変更する
-- Parameters
-- @param 
--   in_tenant_no bigint,
--   in_member_request_no bigint,
--   in_tanto character varying,
--   in_before_status integer,
--   in_status integer,
--   update_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  -- 停止・退会の処理
  -- 会員情報更新
  WITH update_member AS (
    UPDATE m_member SET
      status = in_status,
      email_delivery_flag = (CASE WHEN in_status = 1 THEN 1 ELSE 0 END),
      bid_allow_flag = (CASE WHEN in_status = 1 THEN 1 ELSE 0 END),
      update_admin_no = in_update_admin_no,
      update_datetime = now()
    WHERE member_request_no = in_member_request_no
    RETURNING member_no, member_id, member_request_no, status
  ),
  update_member_request AS (
    UPDATE t_member_request SET
      status = in_status,
      update_admin_no = in_update_admin_no,
      update_datetime = now()
    WHERE (SELECT member_request_no FROM update_member) IS NULL
      AND member_request_no = in_member_request_no
    RETURNING member_request_no, status
  ),
  update_user AS (
    UPDATE m_user SET
      bid_allow_flag = (CASE WHEN in_status = 1 THEN 1 ELSE 0 END),
      update_admin_no = in_update_admin_no,
      update_datetime = now()
    WHERE member_no = (SELECT member_no FROM update_member)
    RETURNING user_no
  ),
  -- ステータス履歴に入れる
  insert_history AS (
    INSERT INTO t_member_status_history(
      tenant_no,
      member_request_no,
      user_name,
      before_status,
      after_status,
      create_admin_no,
      create_datetime
    ) VALUES (
      in_tenant_no,
      COALESCE((SELECT member_request_no FROM update_member),(SELECT member_request_no FROM update_member_request)),
      in_tanto,
      in_before_status,
      COALESCE((SELECT status FROM update_member),(SELECT status FROM update_member_request)),
      in_update_admin_no,
      now()
    )
    RETURNING member_status_history_no
  )

  SELECT update_member.member_id FROM update_member, update_member_request, update_user, insert_history
  LIMIT 1 INTO return_member_id;

  RETURN return_member_id;

END;
$BODY$;
