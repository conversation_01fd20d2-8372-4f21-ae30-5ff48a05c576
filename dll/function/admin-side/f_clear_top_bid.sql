CREATE OR REPLACE FUNCTION public.f_clear_top_bid (
    in_tenant_no bigint,
    in_exhibition_item_no bigint,
    in_admin_no bigint
)
RETURNS json
LANGUAGE plpgsql

AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- TOP入札を取り消す
----------------------------------------------------------------------------------------------------
current_datetime timestamp without time zone;
temp_sql text = '';
error_flag boolean;
error_count integer = 0;
error_code character varying = '';

exhibition_rec record; -- 開催回情報
after_remove_top_data_rec record;
top_bid record;

-- 入札した後の終了時間
after_bid_end_datetime timestamp without time zone;
top_or_current_price_changed_flag boolean;

-- 開催回種別情報
ext_auction_classification integer;
ext_extend_flag integer;
ext_deal_flag integer;

-- 返却用
temp_text text = '';
tmp_top_text text = '';
top_changed_text text = '';
top_2nd_text text = '';
top_changed_count integer = 0;
result_data json;
result_text text = '';
status integer = 200;

-- 2nd member no
second_bidder_member_no bigint;

BEGIN

  -- 現在時刻
  SELECT CURRENT_TIMESTAMP
    INTO current_datetime;

  ----------------------------------------------------------------------------------------------------
  -- 当該商品の開催回および出品の情報を取得する
  ----------------------------------------------------------------------------------------------------
  temp_sql := 'SELECT B.tenant_no
                    , C.category_id
                    , B.exhibition_item_no
                    , C.pitch_option
                    , C.start_datetime
                    , (CASE
                        WHEN B.end_datetime > COALESCE(B.default_end_datetime, B.end_datetime) THEN B.end_datetime
                        ELSE COALESCE(B.default_end_datetime, B.end_datetime)
                      END) AS end_datetime
                    , B.default_end_datetime
                    , C.max_extend_datetime
                    , COALESCE(B.lowest_bid_price, 0) AS lowest_bid_price
                    , COALESCE(B.lowest_bid_accept_price, 0) AS lowest_bid_accept_price
                    , COALESCE(B.deal_bid_price, 0) AS deal_bid_price
                    , COALESCE(B.top_member_no, -1) AS top_member_no
                    , COALESCE(B.top_price, 0) AS top_price
                    , COALESCE(B.current_price, 0) AS current_price
                    , TBH.bid_user_name AS top_bid_user_name
                    , B.exhibition_member_no
                    , B.hummer_flag
                    , B.cancel_flag
                    , B.delete_flag exhibition_item_delete_flag
                    , C.exhibition_no
                    , C.max_extend_datetime
                    , C.extend_judge_minutes
                    , C.extend_minutes
                    , C.exhibition_classification_info
                  FROM t_exhibition_item B
                  LEFT OUTER JOIN t_exhibition C
                              ON B.exhibition_no = C.exhibition_no
                              AND C.delete_flag = 0
                  LEFT JOIN t_bid_history TBH
                          ON TBH.exhibition_item_no = B.exhibition_item_no
                        AND TBH.member_no = B.top_member_no
                        AND TBH.bid_price = B.top_price
                WHERE B.exhibition_item_no = $1
                ORDER BY TBH.create_datetime DESC
                LIMIT 1';
  EXECUTE temp_sql INTO exhibition_rec USING in_exhibition_item_no;

  ext_auction_classification := exhibition_rec.exhibition_classification_info->'auctionClassification'; -- オークション方式(1:せり上げ、2:封印入札)
  ext_deal_flag := exhibition_rec.exhibition_classification_info->'dealFlag'; -- 即決有無（0:なし、1:あり）
  ext_extend_flag := exhibition_rec.exhibition_classification_info->'extendFlag'; -- 延長有無（0:なし、1:あり）
  -- 2022/10/21: Default終了時刻 > max_extend_datetimeの場合は延長なしと同じ
  IF exhibition_rec.default_end_datetime >= exhibition_rec.max_extend_datetime THEN
    ext_extend_flag := 0;
  END IF;

  -- 開催回が存在しない
  IF exhibition_rec.exhibition_no IS NULL THEN
    error_code := 'EXHIBITION_NOT_FOUND_ERROR';

  -- 出品が存在しない
  ELSEIF exhibition_rec.exhibition_item_no IS NULL OR exhibition_rec.exhibition_item_delete_flag = 1 THEN
    error_code := 'EXHIBITION_ITEM_NOT_FOUND_ERROR';

  -- 入札停止フラグが立っていたらエラー
  ELSEIF exhibition_rec.cancel_flag = 1 THEN
    error_code := 'BID_BANNED_ERROR';

  -- 入札可能な時間内かチェック （延長発生時は商品別に終了時刻がアップデートされる）
  ELSEIF (NOT(exhibition_rec.start_datetime <= current_datetime
              AND current_datetime <= exhibition_rec.end_datetime
              AND current_datetime <= GREATEST(exhibition_rec.default_end_datetime, exhibition_rec.max_extend_datetime))
          ) THEN
    error_code := 'PERIOD_ERROR';

  -- 落札結果確定済の場合(流札、落札)はエラー
  ELSEIF exhibition_rec.hummer_flag <> 0 THEN
    error_code := 'HUMMER_ERROR';

  -- 最低落札価格未満の入札は取消できない
  ELSEIF exhibition_rec.top_price IS NOT NULL AND exhibition_rec.top_price < exhibition_rec.lowest_bid_accept_price THEN
    error_code := 'LOWER_THAN_LOWEST_BID_ACCEPT_PRICE_ERROR';

  END IF;

  top_or_current_price_changed_flag := false;
  IF error_code = '' AND exhibition_rec.top_member_no IS NOT NULL THEN
    -- Get bidded info
    SELECT TB.bid_no
          , TB.member_no
          , TB.bid_price
          , MU.user_no AS bid_user_no
          , MM.free_field->>'name' AS bid_user_name
          , MM.free_field->>'name' AS bid_nickname
      FROM t_bid TB
      INTO top_bid
      LEFT JOIN m_member MM
        ON MM.member_no = TB.member_no
      LEFT JOIN m_user MU
        ON MU.member_no = MM.member_no
    WHERE TB.tenant_no = in_tenant_no
      AND TB.exhibition_item_no = in_exhibition_item_no
      AND TB.member_no = exhibition_rec.top_member_no;

    -- その会員、商品のbid_priceを0に更新
    UPDATE t_bid
      SET bid_price = 0
        , bid_user_no = top_bid.bid_user_no
        , update_admin_no = in_admin_no
        , update_datetime = now()
    WHERE bid_no = top_bid.bid_no;

    -- Reload current price, top member after remove top bid
    SELECT *
      FROM f_get_current_price(in_exhibition_item_no)
      INTO after_remove_top_data_rec;

    -- Update current price and top member
    UPDATE t_exhibition_item TEI
      SET current_price = after_remove_top_data_rec.current_price
        , top_member_no = after_remove_top_data_rec.top_member_no
        , top_price = after_remove_top_data_rec.top_price
        , update_admin_no = in_admin_no
        , update_datetime = current_timestamp
      WHERE TEI.tenant_no = in_tenant_no
      AND TEI.exhibition_item_no = in_exhibition_item_no
      AND TEI.hummer_flag = 0
      AND TEI.delete_flag = 0
      AND TEI.cancel_flag = 0;


    -- 入札額0のレコードを追加
    INSERT INTO t_bid_history(
      tenant_no,
      exhibition_item_no,
      member_no,
      bid_price,
      after_top_member_no,
      after_current_price,
      bid_user_no,
      bid_user_name,
      bid_nickname,
      create_user_no,
      create_admin_no
    )
    VALUES(
      in_tenant_no,
      in_exhibition_item_no,
      top_bid.member_no,
      0,
      after_remove_top_data_rec.top_member_no,
      after_remove_top_data_rec.current_price,
      top_bid.bid_user_no,
      top_bid.bid_user_name,
      top_bid.bid_nickname,
      null,
      in_admin_no
    );

    ------------------------------------------------------------------------------------
    -- bid_countのカウントアップ
    ------------------------------------------------------------------------------------
    temp_sql := 'UPDATE t_exhibition_item
                    SET bid_count = COALESCE(bid_count, 0) + 1
                  WHERE exhibition_item_no = $1';
    EXECUTE temp_sql
      USING
        exhibition_rec.exhibition_item_no;

    ------------------------------------------------------------------------------------
    -- 開催回サマリーbid_countのカウントアップ
    ------------------------------------------------------------------------------------
    temp_sql := 'UPDATE t_exhibition_summary
                    SET bid_count = COALESCE(bid_count, 0) + 1
                  WHERE exhibition_no = $1';
    EXECUTE temp_sql
      USING
        exhibition_rec.exhibition_no;

    -- TOPと２番手(Socket送信用)
    temp_sql := 'SELECT BH.member_no
                  FROM (
                  SELECT TEI.exhibition_item_no, TB.member_no, TB.bid_price, TB.create_datetime,
                          ROW_NUMBER() OVER (PARTITION BY TEI.exhibition_item_no, TB.bid_price ORDER BY TB.create_datetime ASC) AS row_number
                    FROM t_exhibition_item TEI
                    LEFT JOIN t_bid TB
                      ON TB.exhibition_item_no = TEI.exhibition_item_no
                    AND TB.tenant_no = TEI.tenant_no
                    LEFT JOIN (
                    SELECT MAX(value1) AS value1, MAX(value2) AS value2, value3, value4
                      FROM m_constant_localized
                    WHERE constant_no IN (SELECT constant_no FROM m_constant WHERE key_string=''PITCH_FOLLOW_BID_PRICE'')
                      AND language_code = ''ja''
                    GROUP BY value3, value4
                    ) AUTO_PITCH_BID_PRICE
                      ON (AUTO_PITCH_BID_PRICE.value3 IS NULL OR LENGTH(AUTO_PITCH_BID_PRICE.value3) = 0 OR COALESCE(TB.bid_price, TEI.lowest_bid_price) > COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value3, '')::numeric, 0))
                    AND (AUTO_PITCH_BID_PRICE.value4 IS NULL OR LENGTH(AUTO_PITCH_BID_PRICE.value4) = 0 OR COALESCE(TB.bid_price, TEI.lowest_bid_price) <= COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value4, '')::numeric, 0))
                  WHERE TEI.exhibition_item_no=$1
                    AND COALESCE(TEI.top_member_no, -1) <> TB.member_no
                    AND TEI.lowest_bid_accept_price <= TB.bid_price
                    AND (TB.bid_price + COALESCE(NULLIF(AUTO_PITCH_BID_PRICE.value1, '''')::numeric, 0)) >= TEI.current_price
                  ) BH WHERE BH.row_number = 1';
    EXECUTE temp_sql
      INTO second_bidder_member_no
      USING exhibition_rec.exhibition_item_no;

    tmp_top_text := '{
      "exhibition_item_no":"' || exhibition_rec.exhibition_item_no || '",
      "second_member":' || COALESCE(second_bidder_member_no, -1) || ',
      "top_member":' || CASE WHEN after_remove_top_data_rec.top_price >= exhibition_rec.lowest_bid_accept_price AND after_remove_top_data_rec.top_member_no IS NOT NULL
            THEN '"' || after_remove_top_data_rec.top_member_no || '"'
            ELSE 'null' END ||
    '}';
    IF top_2nd_text <> '' THEN
      top_2nd_text := top_2nd_text || ',';
    END IF;
    top_2nd_text := top_2nd_text || tmp_top_text;

    -- TOP交代情報を戻す(メール通知用)
    IF COALESCE(exhibition_rec.top_member_no,-1) <> COALESCE(after_remove_top_data_rec.top_member_no, -1)
      AND exhibition_rec.lowest_bid_accept_price <= after_remove_top_data_rec.current_price AND exhibition_rec.cancel_flag = 0 THEN
      IF top_changed_text <> '' THEN
        top_changed_text := top_changed_text || ',';
      END IF;
      tmp_top_text := '{
        "exhibition_item_no":"' || exhibition_rec.exhibition_item_no || '",
        "before_top_member":' || CASE WHEN exhibition_rec.top_member_no IS NOT NULL
            THEN '"' || exhibition_rec.top_member_no || '"'
            ELSE 'null' END || ',
        "before_top_bid_user_name":"' || COALESCE(exhibition_rec.top_bid_user_name, '') || '",
        "before_top_member_top_price_exceeded":' || COALESCE(exhibition_rec.top_member_no IS NOT NULL
            AND COALESCE(exhibition_rec.top_price,-1) >= exhibition_rec.lowest_bid_accept_price, FALSE)::character varying || ',
        "after_top_member":' || CASE WHEN after_remove_top_data_rec.top_member_no IS NOT NULL
              THEN '"' || after_remove_top_data_rec.top_member_no || '"'
              ELSE 'null' END ||
      '}';
      top_changed_text := top_changed_text || tmp_top_text;
      top_changed_count = top_changed_count + 1;
    END IF;

    ------------------------------------------------------------------------------------
    -- 延長処理： セリ終了N分前かつセリ最大延長時刻M分前の間の入札でTOP交代または現在価格が変わった場合
    ------------------------------------------------------------------------------------
    after_bid_end_datetime := exhibition_rec.end_datetime;
    -- 2022/10/28: TOP交代か現在価格変更の場合は延長する
    IF COALESCE(exhibition_rec.top_member_no,-1) <> COALESCE(after_remove_top_data_rec.top_member_no, -1)
        OR COALESCE(exhibition_rec.current_price,-1) <> COALESCE(after_remove_top_data_rec.current_price, -1) THEN
      top_or_current_price_changed_flag := true;
    END IF;

    IF ext_extend_flag = 1 AND top_or_current_price_changed_flag = true
      AND NOT(ext_deal_flag = 1 AND exhibition_rec.deal_bid_price <= 0)
      AND current_datetime > exhibition_rec.end_datetime - CAST( exhibition_rec.extend_judge_minutes || ' minutes' AS interval ) THEN

      IF current_datetime < exhibition_rec.max_extend_datetime - CAST( exhibition_rec.extend_minutes || ' minutes' AS interval ) THEN
        RAISE NOTICE '延長処理実行==%' , current_datetime + CAST( exhibition_rec.extend_minutes || ' minutes' AS interval );

        temp_sql := 'UPDATE t_exhibition_item
                        SET end_datetime = $1
                      WHERE exhibition_item_no = $2
                      RETURNING end_datetime';
        EXECUTE temp_sql
          INTO after_bid_end_datetime
          USING
            CASE WHEN current_datetime + CAST( exhibition_rec.extend_minutes || ' minutes' AS interval ) > exhibition_rec.end_datetime
                  THEN to_timestamp(to_char((current_datetime + CAST( exhibition_rec.extend_minutes || ' minutes' AS interval )), 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'), 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
                  ELSE exhibition_rec.end_datetime
              END
            ,
            exhibition_rec.exhibition_item_no;
      ELSE
        -- 最大延長時刻到達
        RAISE NOTICE '延長処理実行（最大延長時刻）==%' , exhibition_rec.max_extend_datetime;

        temp_sql := 'UPDATE t_exhibition_item
                        SET end_datetime = $1
                      WHERE exhibition_item_no = $2
                      RETURNING end_datetime';
        EXECUTE temp_sql
          INTO after_bid_end_datetime
          USING
            exhibition_rec.max_extend_datetime,
            exhibition_rec.exhibition_item_no;
      END IF;

    END IF;

  END IF;

  IF error_code = '' THEN
    temp_text := '{
      "exhibition_item_no":"' || exhibition_rec.exhibition_item_no || '",
      "auction_classification":"' || ext_auction_classification || '",
      "current_price":"' || COALESCE(after_remove_top_data_rec.current_price::character varying, exhibition_rec.lowest_bid_price::character varying) || '"
    }';
  ELSE
    temp_text := '{
      "exhibition_item_no":"' || COALESCE(exhibition_rec.exhibition_item_no::character varying, in_exhibition_item_no::character varying, '') || '",
      "errorMessage":"' || error_code || '"
    }';
    error_count := error_count + 1;
    status := 200;
  END IF;

  IF result_text <> '' THEN
    result_text := COALESCE(result_text, '') || ',';
  END IF;

  result_text := COALESCE(result_text, '') || temp_text;

  ----------------------------------------------------------------------------------------------------
  -- return OK
  ----------------------------------------------------------------------------------------------------

  temp_text := format('{"result":{"statusCode":%s, "errorCount":%I, "bidList": %s, "topChangeCount":%I, "topChangeList": %s, "topAndSecondList": %s}}',
                 status, error_count, '[' || COALESCE(result_text, '') || ']', top_changed_count, '[' || top_changed_text || ']', '[' || top_2nd_text || ']');

  result_data := temp_text::json;

  RETURN result_data;

END;

$BODY$;
