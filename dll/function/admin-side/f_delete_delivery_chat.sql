CREATE OR REPLACE FUNCTION public.f_delete_delivery_chat (
    in_tenant_no bigint,
    in_delivery_message_no bigint,
    in_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_delivery_message_no bigint;

----------------------------------------------------------------------------------------------------
-- 配送チャット情報を削除する
-- Parameters
-- @param
--   in_tenant_no integer,
--   in_delivery_message_no integer
--   in_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  WITH delete_delivery_message_no AS (
    UPDATE t_delivery_message
       SET delete_flag = 1,
           checked_admin_no = in_admin_no
     WHERE delivery_message_no = in_delivery_message_no
       AND tenant_no = in_tenant_no

    RETURNING delivery_message_no
  )

  SELECT delete_delivery_message_no.delivery_message_no FROM delete_delivery_message_no
  LIMIT 1 INTO return_delivery_message_no;

  RETURN return_delivery_message_no;

END;

$BODY$;
