CREATE OR REPLACE FUNCTION public.f_get_delivery_chat (
  in_tenant_no bigint,
  in_exhibition_item_no bigint
)
RETURNS TABLE(
  delivery_message_no bigint,
  update_category_id character varying,
  answer_delivery_message_no bigint,
  message character varying,
  member_no bigint,
  member_name text,
  checked_admin_no bigint,
  checked_admin_name character varying,
  create_admin_no bigint,
  create_admin_name character varying,
  create_datetime character varying,
  answer_flag integer,
  delete_flag integer,
  no_answer_flag integer
)
LANGUAGE 'plpgsql'

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
--  配送チャット情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TDM.delivery_message_no,
         TDM.update_category_id,
         TDM.answer_delivery_message_no,
         TDM.message,
         TDM.member_no,
         MM.free_field->>'memberName' member_name,
         TDM.checked_admin_no,
         MA.admin_name as checked_admin_name,
         TDM.create_admin_no,
         MA2.admin_name as create_admin_name,
         to_char(TDM.create_datetime, 'YYYY/MM/DD HH24:MI') :: character varying create_datetime,
         CASE WHEN TDM.update_category_id = '2' AND TDM.checked_admin_no IS NOT NULL AND TDM.delete_flag = 0 AND TDM.no_answer_flag = 0 THEN 1
              ELSE 0
              END as answer_flag,
         TDM.delete_flag,
         TDM.no_answer_flag
    FROM t_delivery_message TDM
    LEFT OUTER JOIN m_member MM
	    ON MM.member_no = TDM.member_no
     AND MM.tenant_no = in_tenant_no
    LEFT OUTER JOIN m_admin MA
	    ON MA.admin_no = TDM.checked_admin_no
     AND MA.tenant_no = in_tenant_no
    LEFT OUTER JOIN m_admin MA2
	    ON MA2.admin_no = TDM.create_admin_no
     AND MA2.tenant_no = in_tenant_no
   WHERE TDM.tenant_no = in_tenant_no
     AND TDM.exhibition_item_no = in_exhibition_item_no
ORDER BY COALESCE(TDM.answer_delivery_message_no, TDM.delivery_message_no), TDM.create_datetime;

END;

$BODY$;
