CREATE OR R<PERSON>LACE FUNCTION public.f_insert_or_update_static_page (
    in_tenant_no bigint,
    in_static_page_no bigint,
    in_page_path character varying,
    in_localized jsonb[],
    in_create_admin_no bigint,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$
DECLARE
  v_static_page_no bigint;
  v_localized jsonb;
  v_language_code character varying;
  v_page_name character varying;
  v_file_url character varying;

BEGIN

  -- Check uniqueness of (tenant_no, page_path)
  IF EXISTS (
    SELECT 1 FROM t_static_page
    WHERE tenant_no = in_tenant_no AND page_path = in_page_path
      AND (in_static_page_no IS NULL OR static_page_no <> in_static_page_no)
  ) THEN
    RAISE EXCEPTION '同じページパスが既に存在します (tenant_no=%, page_path=%)', in_tenant_no, in_page_path;
  END IF;

  -- Insert or update t_static_page
  IF in_static_page_no IS NULL OR NOT EXISTS (
    SELECT 1 FROM t_static_page WHERE static_page_no = in_static_page_no AND tenant_no = in_tenant_no
  ) THEN
    INSERT INTO t_static_page (
      tenant_no, page_path, create_admin_no, create_datetime, update_admin_no, update_datetime
    ) VALUES (
      in_tenant_no, in_page_path, in_create_admin_no, now(), in_update_admin_no, now()
    ) RETURNING static_page_no INTO v_static_page_no;
  ELSE
    UPDATE t_static_page
      SET page_path = in_page_path,
          update_admin_no = in_update_admin_no,
          update_datetime = now()
    WHERE static_page_no = in_static_page_no AND tenant_no = in_tenant_no
    RETURNING static_page_no INTO v_static_page_no;
  END IF;

  -- Upsert for each localized entry
  FOREACH v_localized IN ARRAY in_localized LOOP
    v_language_code := v_localized->>'language_code';
    v_page_name := v_localized->>'page_name';
    v_file_url := v_localized->>'file_url';

    IF NOT EXISTS (
      SELECT 1 FROM t_static_page_localized
      WHERE static_page_no = v_static_page_no AND tenant_no = in_tenant_no AND language_code = v_language_code
    ) THEN
      INSERT INTO t_static_page_localized (
        static_page_no, tenant_no, language_code, page_name, file_url,
        create_admin_no, create_datetime, update_admin_no, update_datetime
      ) VALUES (
        v_static_page_no, in_tenant_no, v_language_code, v_page_name, v_file_url,
        in_create_admin_no, now(), in_update_admin_no, now()
      );
    ELSE
      UPDATE t_static_page_localized
        SET page_name = v_page_name,
            file_url = v_file_url,
            update_admin_no = in_update_admin_no,
            update_datetime = now()
      WHERE static_page_no = v_static_page_no AND tenant_no = in_tenant_no AND language_code = v_language_code;
    END IF;
  END LOOP;

  RETURN v_static_page_no;

END;
$BODY$;
