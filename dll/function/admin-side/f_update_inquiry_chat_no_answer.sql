CREATE OR REPLACE FUNCTION public.f_update_inquiry_chat_no_answer (
    in_tenant_no bigint,
    in_exhibition_message_no bigint,
    in_admin_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- 商品チャットの無回答更新
-- Parameters
-- @param in_tenant_no
-- @param in_exhibition_message_no
-- @param in_admin_no
----------------------------------------------------------------------------------------------------

BEGIN

  UPDATE t_exhibition_message
     SET no_answer_flag = 1,
         checked_admin_no = in_admin_no
   WHERE exhibition_message_no = in_exhibition_message_no
     AND tenant_no = in_tenant_no;

  SELECT TRUE, 200, NULL
  INTO result,
         status,
         message;
  RETURN NEXT;

END;
$BODY$;
