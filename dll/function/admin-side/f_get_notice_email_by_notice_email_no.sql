CREATE OR REPLACE FUNCTION public.f_get_notice_email_by_notice_email_no (
    in_tenant_no bigint,
    in_notice_email_no bigint
)
RETURNS TABLE(
    notice_email_no bigint,
    tenant_no bigint,
    sent_flag integer,
    sent_flag_name text,
    send_datetime text,
    send_date text,
    send_time text,
    email_priority integer,
    email_priority_name character varying,
    language_code character varying,
    title character varying,
    body_title_upper_row character varying,
    body_title_lower_row character varying,
    body text,
    footer text,
    file text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- notice_email_noでお知らせメール情報を取得する
-- Parameters
-- @param 
--   in_tenant_no bigint,
-- 　in_notice_email_no integer
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.notice_email_no,
         A.tenant_no,
         A.sent_flag,
         CASE WHEN A.sent_flag = 0 THEN '未送信'
              WHEN A.sent_flag = 1 THEN '送信済み'
              ELSE ''
          END as sent_flag_name,
        to_char(A.send_datetime, 'YYYY/MM/DD HH24:MI') as send_datetime,
        to_char(A.send_datetime, 'YYYY/MM/DD') as send_date,
        to_char(A.send_datetime, 'HH24:MI') as send_time,
        A.email_priority,
        mcl.value2 as email_priority_name,
        B.language_code,
        B.title,
        B.body_title_upper_row,
        B.body_title_lower_row,
        B.body,
        B.footer,
        B.file
   FROM t_notice_email A
   JOIN t_notice_email_localized B ON A.notice_email_no = B.notice_email_no
   JOIN m_tenant T ON T.tenant_no = A.tenant_no AND T.tenant_no = in_tenant_no
   LEFT OUTER JOIN (
      SELECT mcl.value1
           , mcl.value2
           , mcl.language_code
        FROM m_constant mc
        INNER JOIN m_constant_localized mcl ON mcl.constant_no = mc.constant_no
       WHERE mc.tenant_no = in_tenant_no
         AND mc.key_string = 'EMAIL_NOTICE_PRIORITY'
         AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
    ) mcl ON mcl.value1 = A.email_priority::character varying AND mcl.language_code = B.language_code
  WHERE A.notice_email_no = in_notice_email_no
    AND A.delete_flag = 0
    AND B.delete_flag = 0
  ORDER BY B.language_code DESC;

END;

$BODY$;
