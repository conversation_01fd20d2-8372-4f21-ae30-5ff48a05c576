CREATE OR REPLACE FUNCTION public.f_update_items_status_new (
    in_item_nos bigint[],
    in_status integer[],
    in_tenant_no bigint,
    in_admin_no bigint,
    in_new_mark_display_date character varying,
    OUT result boolean,
    OUT return_status bigint,
    OUT message character varying
)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 商品の表示・非表示、新着ステータスを変更する
-- Parameters
-- @param
--   in_item_nos bigint[],
--   in_status integer[],
--   in_tenant_no bigint,
--   in_admin_no bigint
----------------------------------------------------------------------------------------------------
BEGIN
  -- in_status = 0 :　在庫表示
  -- in_status = 1 :　在庫非表示
  -- in_status = 2 :　新着マークをつける
  -- in_status = 3 :　新着マークを外す
  FOR idx in 1..array_length(in_item_nos,1)
  LOOP
    -- 商品情報更新
    UPDATE t_item
       SET status = CASE WHEN in_status[idx] IN (0,1) THEN in_status[idx] ELSE status END
         , new_start_datetime = CASE WHEN in_status[idx] = 2 THEN current_timestamp
                                    WHEN in_status[idx] = 3 THEN NULL
                                    ELSE new_start_datetime END
         , new_end_datetime = CASE WHEN in_status[idx] = 2 THEN (current_timestamp + CONCAT(in_new_mark_display_date, ' day')::INTERVAL)
                                    WHEN in_status[idx] = 3 THEN NULL
                                    ELSE new_end_datetime END
         , update_admin_no = in_admin_no
         , update_datetime = current_timestamp
    WHERE item_no = in_item_nos[idx]
      AND tenant_no = in_tenant_no
      AND delete_flag = 0
      AND status <> 2 AND status <> 3; -- 出品していないと成約済みでない商品のみ
  END LOOP;

  result := true;
  return_status := 200;
  message := '';

RETURN NEXT;

EXCEPTION

  --その他エラー
  WHEN OTHERS THEN
    result := false;
    return_status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
