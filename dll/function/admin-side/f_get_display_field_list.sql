CREATE OR REPLACE FUNCTION public.f_get_display_field_list(
    in_tenant_no bigint,
    in_window_id character varying,
    in_language_code character varying
)
RETURNS TABLE(
    field_mapping_no bigint,
    field_no bigint,
    field_localized_no bigint,
    field_division character varying,
    logical_name character varying,
    physical_name character varying,
    input_type character varying,
    input_data_list jsonb,
    data_type character varying,
    max_length integer,
    max_value integer,
    required_flag integer,
    regular_expressions character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 項目表示設定のリストを取得する
-- Parameters
-- @param in_tenant_no character varying - テナント番号
-- @param in_window_id character varying - 画面ID
-- @param in_language_code character varying - 言語区分
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT fm.field_mapping_no AS field_mapping_no
        ,f.field_no AS field_no
        ,fl.field_localized_no AS field_localized_no
        ,f.field_division AS field_division
        ,fl.logical_name AS logical_name
        ,f.physical_name AS physical_name
        ,f.input_type AS input_type
        ,f.input_data_list AS input_data_list
        ,f.data_type AS data_type
        ,f.max_length AS max_length
        ,f.max_value AS max_value
        ,f.required_flag AS required_flag
        ,f.regular_expressions AS regular_expressions
    FROM
    (
      SELECT mfp.field_mapping_no, t.field_no, t.ordinality
      FROM m_field_mapping mfp
      CROSS JOIN LATERAL UNNEST(mfp.field_no) WITH ORDINALITY AS t(field_no, ordinality)
      WHERE mfp.tenant_no = in_tenant_no
        AND mfp.window_id = in_window_id
        AND mfp.language_code = in_language_code
        AND mfp.delete_flag = 0
    ) fm
    JOIN
      m_field f
    ON fm.field_no = f.field_no
     JOIN
      m_field_localized fl
    ON f.field_no = fl.field_no
    WHERE f.tenant_no = in_tenant_no
      AND fl.tenant_no = in_tenant_no
      AND fl.language_code = in_language_code
      AND f.delete_flag = 0
      AND fl.delete_flag = 0
    ORDER BY fm.ordinality;

END;
$BODY$;
