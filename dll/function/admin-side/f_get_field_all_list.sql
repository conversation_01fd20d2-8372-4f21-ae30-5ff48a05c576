CREATE OR REPLACE FUNCTION public.f_get_field_all_list(
    in_tenant_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    field_no bigint,
    field_localized_no bigint,
    field_division character varying,
    logical_name character varying,
    physical_name character varying,
    input_type character varying,
    required_flag integer,
    data_type character varying,
    input_data_list jsonb,
    max_length integer,
    max_value integer,
    regular_expressions character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 特定の言語のすべての項目設定リストを取得する
-- Parameters
-- @param in_tenant_no character varying - テナント番号
-- @param in_language_code character varying - 言語区分
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT f.field_no AS field_no
        ,fl.field_localized_no AS field_localized_no
        ,f.field_division AS field_division
        ,fl.logical_name AS logical_name
        ,f.physical_name AS physical_name
        ,f.input_type AS input_type
        ,f.required_flag AS required_flag
        ,f.data_type AS data_type
        ,f.input_data_list AS input_data_list
        ,f.max_length AS max_length
        ,f.max_value AS max_value
        ,f.regular_expressions AS regular_expressions
    FROM
      m_field f
    JOIN
      m_field_localized fl
    ON f.field_no = fl.field_no
  WHERE f.tenant_no = in_tenant_no
    AND f.tenant_no = in_tenant_no
    AND fl.language_code = in_language_code
    AND f.delete_flag = 0
    AND fl.delete_flag = 0
  ORDER BY f.order_no;

END;
$BODY$;
