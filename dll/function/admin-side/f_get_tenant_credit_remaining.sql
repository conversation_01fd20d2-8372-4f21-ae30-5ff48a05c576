CREATE OR REPLACE FUNCTION public.f_get_tenant_credit_remaining(
    in_tenant_no bigint
)
RETURNS TABLE(
    tenant_credit_remaining_history_no bigint,
    bid_limit_flag integer,
    start_datetime timestamp with time zone,
    end_datetime timestamp with time zone,
    bid_limit numeric,
    reset_type integer,
    reset_date integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 入札上限額履歴を取得します
-- Parameters
-- @param in_tenant_no テナント番号
----------------------------------------------------------------------------------------------------
BEGIN
  RETURN QUERY
  SELECT
    T.tenant_credit_remaining_history_no,
    T.bid_limit_flag,
    T.start_datetime,
    T.end_datetime,
    T.bid_limit,
    T.reset_type,
    T.reset_date
  FROM
    t_tenant_credit_remaining_history T
  WHERE
    T.tenant_no = in_tenant_no
  ORDER BY
    T.start_datetime DESC
    , T.end_datetime
	  , T.update_datetime DESC
    , T.tenant_credit_remaining_history_no DESC
  ;
END;
$BODY$;
