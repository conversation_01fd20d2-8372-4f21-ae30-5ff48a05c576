/**
 * @function f_get_exhibition_pulldown
 * @description PostgreSQL function to retrieve exhibition pulldown data based on tenant and role(role_id 30(一般) is excluded).
 *
 * @example input
 *  SELECT * FROM f_get_exhibition_pulldown(1, 10);
 *
 * @example output
 * [
 *   {
 *     exhibition_name: 'TTT入札会',
 *     category_id: 101
 *   }
 * ]
 */

CREATE OR REPLACE FUNCTION public.f_get_exhibition_pulldown (
    in_tenant_no bigint,
    in_role_id bigint
)
RETURNS TABLE(
    exhibition_name character varying,
    category_id integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会プルダウンを取得する
-- Parameters
-- @param in_tenant_no テナントNo
-- @param in_role_id Role
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH EXHIBITIONS AS (
    SELECT TEL.exhibition_name, TE.category_id, TE.start_datetime
      FROM t_exhibition TE
    INNER JOIN t_exhibition_localized TEL
            ON TE.exhibition_no = TEL.exhibition_no
            AND TEL.language_code = 'ja'
    INNER JOIN t_exhibition_summary TES
            ON TE.exhibition_no = TES.exhibition_no
    WHERE TE.tenant_no = in_tenant_no
      AND TE.delete_flag = 0
      -- AND TES.exhibition_item_count > 0  -- 出展機数が0の場合は表示しない(Fixed 2020/05/20: 出展機数が0の場合でも表示する)
      AND TE.start_datetime <= now()
      AND (in_role_id <> 30 OR TE.status = 0)
    ORDER BY TE.start_datetime DESC
  )
  SELECT EXH.exhibition_name, EXH.category_id FROM (
    SELECT DISTINCT(EXHIBITIONS.exhibition_name) AS exhibition_name,
        MAX(EXHIBITIONS.start_datetime) AS start_datetime,
        EXHIBITIONS.category_id AS category_id
    FROM EXHIBITIONS
    GROUP BY EXHIBITIONS.exhibition_name, EXHIBITIONS.category_id
  ) EXH
  ORDER BY EXH.start_datetime DESC;

END;

$BODY$;
