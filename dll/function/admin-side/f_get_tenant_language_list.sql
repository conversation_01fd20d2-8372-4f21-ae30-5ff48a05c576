CREATE OR REPLACE FUNCTION public.f_get_tenant_language_list(
    in_tenant_no bigint
)
RETURNS TABLE(
    language_code_list character varying[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- テナントマスタから対応言語リストを取得する
-- Parameters
-- @param in_tenant_no character varying - テナント番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT t.language_code_list AS language_code_list
    FROM
      m_tenant t
  WHERE t.tenant_no = in_tenant_no
    AND t.delete_flag = 0;

END;

$BODY$;
