CREATE OR REPLACE FUNCTION public.f_get_bid_order(
    in_tenant_no bigint,
    in_language_code character varying,
    in_exhibition_no_array bigint[]
)
RETURNS TABLE(
    product_id character varying,
    exhibition_item_no bigint,
    exhibition_no bigint,
    quantity numeric,
    lowest_bid_quantity numeric,
    lowest_bid_price numeric,
    lowest_bid_accept_quantity numeric,
    lowest_bid_accept_price numeric,
    current_price numeric,
    lot_no bigint,
    item_no bigint,
    product_name character varying,
    ranking_list json
    )

LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000
AS $BODY$
DECLARE
----------------------------------------------------------------------------------------------------
-- 入札履歴情報を取得します
-- Parameters
-- @param in_tenant_no 運営元番号
-- @param in_language_code 言語コード
-- @param in_exhibition_no_array 開催回番号配列
----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  SELECT
      TI.manage_no as product_id,
      RW.exhibition_item_no,
      TEI.exhibition_no,
      TEI.quantity,
      TEI.lowest_bid_quantity,
      TEI.lowest_bid_price,
      TEI.lowest_bid_accept_quantity,
      TEI.lowest_bid_accept_price,
      COALESCE(TEI.current_price, TEI.lowest_bid_price) AS current_price,
      TEI.lot_no,
      TLD.item_no,
      (TIL.free_field->>'product_name')::character varying AS product_name,
      COALESCE(
          json_agg(
              CASE
                  WHEN RW.exhibition_item_no IS NOT NULL THEN json_build_object(
                      'ranking_no', RW.ranking_no,
                      'member_id', RW.member_id,
                      'member_no', RW.member_no,
                      'customer_code', RW.customer_code,
                      'company_name', RW.company_name,
                      'user_name', TBH.bid_user_name,
                      'bid_price', RW.bid_price,
                      'bid_quantity', RW.bid_quantity
                  )
                  ELSE NULL
              END
              ORDER BY RW.ranking_no
          ) FILTER (WHERE RW.exhibition_item_no IS NOT NULL),
          '[]'
      ) AS ranking_list
  FROM t_exhibition_item TEI
  LEFT JOIN (
      SELECT TB.exhibition_item_no
            , RANK() OVER (
                PARTITION BY TB.exhibition_item_no
                    ORDER BY
                      CASE WHEN TB.bid_price >= TEI.lowest_bid_accept_price AND TB.bid_quantity >= TEI.lowest_bid_accept_quantity THEN 0 ELSE 1 END,
                      TB.bid_price DESC, TB.bid_quantity DESC, TB.update_datetime, TB.member_no
              ) ranking_no
            , MM.member_id
            , MM.member_no
            , MM.free_field->>'customerCode' customer_code
            , MM.free_field->>'companyName' company_name
            , TB.bid_price
            , TB.bid_quantity
      FROM t_exhibition_item TEI
              LEFT OUTER JOIN t_bid TB
                          ON TEI.exhibition_item_no = TB.exhibition_item_no
                        AND TB.bid_price <> 0
              LEFT OUTER JOIN m_member MM
                            ON MM.member_no = TB.member_no
  ) RW
    ON TEI.exhibition_item_no = RW.exhibition_item_no
  LEFT JOIN (
      SELECT TBH.exhibition_item_no, TBH.member_no, TBH.bid_user_name, TBH.bid_nickname
            FROM (
              SELECT TEI.exhibition_item_no
                  , TBH.member_no
                  , TBH.bid_user_name
                  , TBH.bid_nickname
                  , row_number() OVER(PARTITION BY TEI.exhibition_item_no, TBH.member_no ORDER BY TBH.create_datetime desc) order_seq
                  FROM t_exhibition_item TEI
                    INNER JOIN t_bid_history TBH
                      ON TEI.exhibition_item_no = TBH.exhibition_item_no) TBH
        WHERE order_seq=1
  ) TBH
    ON TBH.exhibition_item_no = RW.exhibition_item_no
      AND TBH.member_no = RW.member_no
  JOIN t_lot_detail TLD
    ON TLD.lot_no = TEI.lot_no
  JOIN t_item_localized TIL
    ON TIL.item_no = TLD.item_no
    AND TIL.language_code = in_language_code
  JOIN t_item TI
    ON TI.item_no = TLD.item_no
  WHERE TEI.tenant_no = in_tenant_no
    AND (TEI.exhibition_no = ANY(in_exhibition_no_array)
        OR COALESCE(in_exhibition_no_array,'{}'::bigint[]) = '{}'::bigint[])

  GROUP BY
    TI.manage_no,
    RW.exhibition_item_no,
    TEI.exhibition_no,
    TEI.current_price,
    TEI.lot_no,
    TEI.quantity,
    TEI.lowest_bid_quantity,
    TEI.lowest_bid_price,
    TEI.lowest_bid_accept_quantity,
    TEI.lowest_bid_accept_price,
    TLD.item_no,
    TIL.free_field

  ORDER BY
    TI.manage_no ASC
  ;

END;

$BODY$;
