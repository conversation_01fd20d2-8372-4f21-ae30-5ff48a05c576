CREATE OR REPLACE FUNCTION public.f_get_constants_language_list(
    in_tenant_no bigint,
    in_constant_no bigint
)
RETURNS TABLE(
    out_language_value1 character varying,
    out_language_value2 character varying,
    out_constant_localized_no bigint,
    out_constant_no bigint,
    out_value1 character varying,
    out_value2 character varying,
    out_value3 character varying,
    out_value4 character varying,
    out_value5 character varying,
    out_file_url character varying,
    out_key_string character varying,
    out_value_name character varying,
    out_sort_order integer,
    out_description character varying,
    out_start_datetime character varying,
    out_end_datetime character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 全て言語の定数リストを取得する
-- Parameters
-- @param in_tenant_no bigint
-- @param in_constant_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
    SELECT
    cl.value1 AS out_language_value1, 
    cl.value2 AS out_language_value2, 
    rjcl.constant_localized_no AS out_constant_localized_no, 
    rjcl.constant_no AS out_constant_no, 
    rjcl.value1 AS out_value1, 
    rjcl.value2 AS out_value2, 
    rjcl.value3 AS out_value3, 
    rjcl.value4 AS out_value4, 
    rjcl.value5 AS out_value5, 
    rjcl.file_url  AS out_file_url, 
    rjc.key_string  AS out_key_string,
    rjc.value_name  AS out_value_name,
    rjc.sort_order  AS out_sort_order,
    rjc.description  AS out_description,  
    TO_CHAR(rjc.start_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying AS out_start_datetime,
    TO_CHAR(rjc.end_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying AS out_end_datetime
    FROM m_constant c
      LEFT JOIN m_constant_localized cl
      ON cl.tenant_no = c.tenant_no
      AND cl.constant_no = c.constant_no

      LEFT JOIN m_constant_localized rjcl
      ON rjcl.tenant_no = c.tenant_no
      AND rjcl.constant_no = in_constant_no
      AND cl.value1 = rjcl.language_code

      LEFT JOIN m_constant rjc
      ON rjc.tenant_no = c.tenant_no
      AND rjc.constant_no = in_constant_no
    WHERE c.key_string = 'LANGUAGE_CODE'
    AND c.tenant_no = in_tenant_no
    ORDER BY c.sort_order;

END;
$BODY$;
