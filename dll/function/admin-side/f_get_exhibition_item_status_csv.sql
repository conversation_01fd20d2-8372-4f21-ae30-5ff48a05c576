CREATE OR REPLACE FUNCTION public.f_get_exhibition_item_status_csv(
    in_tenant_no bigint,
    in_language_code character varying,
    in_endDataFrom character varying,
    in_endDataTo character varying
)
RETURNS TABLE(
    exhibition_name character varying,
    category character varying,
    exhibition_no bigint,
    exhibition_item_no bigint,
    exhibition_status integer,
    lot_id character varying,
    manage_no character varying,
    product_name character varying,
    ubrand_code character varying,
    lowest_bid_price numeric,
    lowest_bid_accept_price numeric,
    current_price numeric,
    bid_count integer,
    end_datetime character varying,
    extend_remaining_time bigint,
    hummer_flag integer
)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$
DECLARE
----------------------------------------------------------------------------------------------------
-- 入札会の状況を取得します
-- Parameters
-- @param in_tenant_no テナント番号
-- @param in_language_code 言語コード(管理者はja固定)
-- @param in_endDataFrom 商品終了日時 FROM
-- @param in_endDataTo 商品終了日時 TO
----------------------------------------------------------------------------------------------------
BEGIN
  RETURN QUERY
  WITH RANKING_WORK AS(
    SELECT TB.exhibition_item_no
          , RANK() OVER (
              PARTITION BY TB.exhibition_item_no
                  ORDER BY TB.bid_price DESC, TB.update_datetime, TB.member_no
            ) ranking_no
          , MM.member_id
          , MM.member_no
          , MM.free_field->>'nickname' member_nickname
          , MM.free_field->>'memberName' member_name
          , TB.bid_price
    FROM t_exhibition_item TEI
            LEFT OUTER JOIN t_bid TB
                        ON TEI.exhibition_item_no = TB.exhibition_item_no
                       AND TB.bid_price <> 0
            LEFT OUTER JOIN m_member MM
                          ON MM.member_no = TB.member_no
                          AND MM.tenant_no = in_tenant_no --論理的には必要ないが名称を出力するため念のため
      WHERE TEI.tenant_no = in_tenant_no
          -- AND (
          --   TEI.exhibition_no = ANY(in_exhibition_no_array)
          --   OR COALESCE(in_exhibition_no_array,'{}'::bigint[]) = '{}'::bigint[]
          -- )
      ORDER BY TEI.exhibition_item_no, ranking_no
    ),
  ITEM_WORK AS (
    SELECT TEI.exhibition_no, TEI.exhibition_item_no, TI.manage_no, TIL.free_field
      FROM t_exhibition_item TEI
            INNER JOIN t_lot_detail TLD
                    ON TEI.lot_no = TLD.lot_no
            INNER JOIN t_item TI
                    ON TLD.item_no = TI.item_no
            INNER JOIN t_item_localized TIL
                    ON TI.item_no = TIL.item_no
                    AND TIL.language_code = in_language_code
      WHERE TEI.tenant_no = in_tenant_no
      ORDER BY TEI.exhibition_no, TEI.exhibition_item_no, TI.manage_no, TIL.free_field
    ),
  SEARCH_BY_SERIAL AS(
    SELECT IW.exhibition_item_no
      FROM ITEM_WORK IW
      -- WHERE in_nickname IS NULL
      --    OR IW.free_field->>'productName' ~* f_escape_string(in_nickname)
      GROUP BY IW.exhibition_item_no
    ),
  ITEM_LIST AS(
    SELECT IW.exhibition_item_no
          , IW.manage_no
          , (IW.free_field->>'productName')::character varying as product_name
          , (IW.free_field->>'ubrand_code')::character varying as ubrand_code
      FROM ITEM_WORK IW
      JOIN SEARCH_BY_SERIAL SRC ON SRC.exhibition_item_no = IW.exhibition_item_no
    )
  SELECT TEL.exhibition_name
        , MCL.value2 category
        , TE.exhibition_no
        , TEI.exhibition_item_no
        , TE.status
        , TL.lot_id --画面に表示するロット番号
        , IL.manage_no
        , IL.product_name
        , IL.ubrand_code
        , TEI.lowest_bid_price lowest_bid_price --最低入札価格
        , TEI.lowest_bid_accept_price lowest_bid_accept_price --最低落札価格
        , CASE WHEN TE.exhibition_classification_info->'auctionClassification' = '1'
               THEN COALESCE(TEI.current_price, TEI.lowest_bid_price)
               ELSE GREATEST((SELECT TB.bid_price
                               FROM t_bid TB
                              WHERE TB.exhibition_item_no = TEI.exhibition_item_no
                              ORDER BY TB.bid_price DESC, TB.update_datetime
                              LIMIT 1), TEI.lowest_bid_price)
                END current_price --現在価格
        , TEI.bid_count --入札件数
        , to_char((CASE
            WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
            ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
          END) at time zone 'Japan', 'YYYY/MM/DD') :: character varying AS end_datetime --商品の終了時間(延長フラグが1の場合に延長残り時間の表示に使用)
        , (EXTRACT(epoch FROM (CASE
            WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
            ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
          END) - COALESCE(TEI.default_end_datetime, TEI.end_datetime)))::bigint extend_remaining_time --延長残り時間(画面では使用しない)
        , TEI.hummer_flag
    FROM t_exhibition TE
          INNER JOIN t_exhibition_item TEI
                  ON TE.exhibition_no = TEI.exhibition_no
                  AND TEI.cancel_flag = 0
                  AND TEI.delete_flag = 0
          INNER JOIN ITEM_LIST IL
                  ON TEI.exhibition_item_no = IL.exhibition_item_no
          INNER JOIN t_lot TL
                  ON TEI.lot_no = TL.lot_no
          INNER JOIN t_exhibition_localized TEL
                  ON TE.exhibition_no = TEL.exhibition_no
                  AND TEL.language_code = in_language_code
          LEFT OUTER JOIN (
            SELECT MCL.value1
                , MCL.value2
              FROM m_constant MC
                  INNER JOIN m_constant_localized MCL
                          ON MCL.constant_no = MC.constant_no
            WHERE MC.tenant_no = in_tenant_no
              AND MCL.language_code = in_language_code
              AND MC.key_string = 'PRODUCT_CATEGORY' -- 定数キー変更
              AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
          ) MCL ON MCL.value1 = TE.category_id::character varying
          LEFT OUTER JOIN m_member TMM
                      ON TMM.member_no = TEI.top_member_no
                      AND TMM.tenant_no = in_tenant_no --論理的には必要ないが名称を出力するため念のため
          LEFT OUTER JOIN t_exhibition_result TER
                      ON TER.exhibition_item_no = TEI.exhibition_item_no
    WHERE TE.tenant_no = in_tenant_no
      AND (in_endDataFrom IS NULL OR TEI.end_datetime >= to_timestamp(in_endDataFrom, 'yyyy/MM/dd HH24:MI:SS'))
      AND (in_endDataTo IS NULL OR TEI.end_datetime <= to_timestamp(in_endDataTo, 'yyyy/MM/dd HH24:MI:SS'))
      AND TEI.hummer_flag = 0
      AND TE.delete_flag = 0
    ORDER BY
        end_datetime ASC
        , IL.ubrand_code ASC
        , IL.manage_no ASC;
END;
$BODY$;
