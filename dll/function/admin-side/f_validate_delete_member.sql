CREATE OR REPLACE FUNCTION public.f_validate_delete_member(
    IN in_tenant_no bigint,
    IN in_member_id character varying
)
RETURNS TABLE(
    exhibition_no bigint,
    status integer,
    hummer_flag integer,
    payment_flag integer,
    is_end_auction boolean
)
LANGUAGE plpgsql
AS $BODY$
BEGIN
  RETURN QUERY
  SELECT te.exhibition_no, te.status, tei.hummer_flag, ter.payment_flag,
      CASE
          WHEN NOW() > tei.end_datetime THEN TRUE
          ELSE FALSE
      END AS is_end_auction
  FROM public.m_member mm
  JOIN public.t_bid tb ON mm.member_no = tb.member_no
  JOIN public.t_exhibition_item tei ON tb.exhibition_item_no = tei.exhibition_item_no
  JOIN public.t_exhibition te ON tei.exhibition_no = te.exhibition_no
  LEFT JOIN public.t_exhibition_result ter ON te.exhibition_no = ter.exhibition_no
  WHERE mm.member_id = in_member_id
    AND mm.tenant_no = in_tenant_no;
END;
$BODY$;
