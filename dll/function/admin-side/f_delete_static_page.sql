CREATE OR R<PERSON>LACE FUNCTION public.f_delete_static_page (
    in_tenant_no bigint,
    in_static_page_no bigint,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_static_page_no bigint;

----------------------------------------------------------------------------------------------------
-- Delete static page
----------------------------------------------------------------------------------------------------

B<PERSON><PERSON>

  return_static_page_no:=0;

  IF EXISTS ( SELECT 1
    FROM t_static_page
    WHERE tenant_no = in_tenant_no
    AND static_page_no=in_static_page_no
    AND delete_flag=0
  ) THEN
    UPDATE t_static_page
       SET delete_flag = 1,
           update_admin_no = in_update_admin_no,
           update_datetime = now()
      WHERE tenant_no = in_tenant_no
        AND static_page_no = in_static_page_no
    RETURNING static_page_no INTO return_static_page_no;

    IF EXISTS ( SELECT 1 FROM t_static_page_localized WHERE static_page_no=in_static_page_no ) THEN
      UPDATE t_static_page_localized
         SET delete_flag = 1,
             update_admin_no = in_update_admin_no,
             update_datetime = now()
        WHERE tenant_no = in_tenant_no
          AND static_page_no = in_static_page_no;
    END IF;
  END IF;

  RETURN return_static_page_no;

END;

$BODY$;
