CREATE OR REPLACE FUNCTION public.f_get_delivery_chat_un_read_cnt (
  in_tenant_no bigint,
  in_exhibition_item_no bigint
)
RETURNS TABLE(
  exhibition_item_no bigint,
  chat_un_read_count bigint
)
LANGUAGE 'plpgsql'

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
--  配送チャットの未読カウント情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TDM.exhibition_item_no,
          COALESCE(COUNT(*), 0) as chat_un_read_count
    FROM t_delivery_message TDM
   WHERE TDM.tenant_no = in_tenant_no
     AND TDM.exhibition_item_no = in_exhibition_item_no
     AND TDM.update_category_id = '2'
     AND TDM.checked_admin_no IS NULL
     AND TDM.delete_flag = 0
GROUP BY TDM.exhibition_item_no;

END;

$BODY$;
