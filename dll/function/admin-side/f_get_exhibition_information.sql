CREATE OR REPLACE FUNCTION public.f_get_exhibition_information (
    in_tenant_no bigint,
    in_exhibition_name character varying,
    in_language_code character varying
)
RETURNS TABLE(
    preview_start_datetime timestamp with time zone,
    start_datetime timestamp with time zone,
    end_datetime timestamp with time zone,
    preview_end_datetime timestamp with time zone,
    exhibition_item_count bigint,
    recommended_number numeric
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会情報を取得する
-- Parameters
-- @param in_tenant_no テナントNo
-- @param in_exhibition_name 入札会名
-- @param in_language_code 言語
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
    SELECT MIN(TE.preview_start_datetime) preview_start_datetime,  --下見開始日時
        MIN(TE.start_datetime) start_datetime,  --開始日時
        MAX(TE.end_datetime) end_datetime,  --終了日時
        MAX(TE.preview_end_datetime) preview_end_datetime,  --閲覧終了日時
        SUM(TES.exhibition_item_count) exhibition_item_count,  --出展機数
        SUM((SELECT COUNT(*) 
          FROM t_exhibition_item TEI
            INNER JOIN t_lot_detail TLD
            ON TEI.lot_no = TLD.lot_no
            INNER JOIN t_item_localized TIL
            ON TLD.item_no = TIL.item_no
            AND (TIL.free_field->>'hasRecommendation')::boolean = true
          WHERE TEI.cancel_flag = 0
          AND TEI.exhibition_no = TE.exhibition_no)) --おすすめ数
    FROM t_exhibition TE
        INNER JOIN t_exhibition_summary TES
        ON TE.exhibition_no = TES.exhibition_no
        INNER JOIN t_exhibition_localized TEL
        ON TE.exhibition_no = TEL.exhibition_no
        AND TEL.exhibition_name = in_exhibition_name --引数(画面の入札会名)
        AND TEL.language_code = in_language_code --引数(管理者の言語)
    WHERE TE.delete_flag = 0
        AND TE.tenant_no = in_tenant_no;
END;

$BODY$;
