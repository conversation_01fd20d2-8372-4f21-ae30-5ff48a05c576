CREATE OR REPLACE FUNCTION public.f_get_exhibition_item_status(
    in_tenant_no bigint,
    in_language_code character varying,
    in_exhibition_no_array bigint[],
    in_manage_no character varying,
    in_product_name character varying,
    in_status_array bigint[],
    in_unread_array bigint[]
)
RETURNS TABLE(
    exhibition_name character varying,
    category character varying,
    exhibition_no bigint,
    exhibition_item_no bigint,
    exhibition_status integer,
    auction_classification text,
    lot_id character varying,
    item_list json,
    manage_no character varying,
    ranking_list json,
    quantity numeric,
    lowest_bid_quantity numeric,
    lowest_bid_accept_quantity numeric,
    lowest_bid_price numeric,
    lowest_bid_accept_price numeric,
    current_price numeric,
    exhibition_item_status integer,
    bid_count integer,
    bid_member_count integer,
    extend_flag integer,
    end_datetime timestamp with time zone,
    extend_remaining_time bigint,
    inquiry_message_count bigint,
    delivery_message_count bigint,
    inquiry_un_checked_count bigint,
    delivery_un_checked_count bigint,
    hummer_flag integer
)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$
DECLARE
----------------------------------------------------------------------------------------------------
-- 入札会の状況を取得します
-- Parameters
-- @param in_tenant_no テナント番号
-- @param in_language_code 言語コード(管理者はja固定)
-- @param in_manage_no 商品ID
-- @param in_product_name 商品名
-- @param in_status_array ステータス配列(0:未入札、1:入札あり、2:落札、3:流札)
----------------------------------------------------------------------------------------------------
BEGIN
  RETURN QUERY
  WITH RANKING_WORK AS(
    SELECT TB.exhibition_item_no
          , RANK() OVER (
              PARTITION BY TB.exhibition_item_no
                  ORDER BY
                    CASE WHEN TB.bid_price >= TEI.lowest_bid_accept_price AND TB.bid_quantity >= TEI.lowest_bid_accept_quantity THEN 0 ELSE 1 END,
                    TB.bid_price DESC, TB.bid_quantity DESC, TB.update_datetime, TB.member_no
            ) ranking_no
          , MM.member_id
          , MM.member_no
          , MM.free_field->>'nickname' member_nickname
          , MM.free_field->>'ceoName' member_name
          , MM.free_field->>'companyName' company_name
          , TB.bid_price
          , TB.bid_quantity
          , TEI.quantity AS total_quantity
          , SUM(TB.bid_quantity) OVER (
            PARTITION BY TEI.exhibition_item_no
            ORDER BY TB.bid_price DESC, TB.bid_quantity DESC, TB.update_datetime, TB.member_no
          ) AS cumulative_quantity
          , TEI.quantity - SUM(TB.bid_quantity) OVER (
            PARTITION BY TEI.exhibition_item_no
            ORDER BY TB.bid_price DESC, TB.bid_quantity DESC, TB.update_datetime, TB.member_no
          ) AS remain_quantity
    FROM t_exhibition_item TEI
            LEFT OUTER JOIN t_bid TB
                        ON TEI.exhibition_item_no = TB.exhibition_item_no
                       AND TB.bid_price <> 0
            LEFT OUTER JOIN m_member MM
                          ON MM.member_no = TB.member_no
                          AND MM.tenant_no = in_tenant_no
      WHERE TEI.tenant_no = in_tenant_no
        AND (
          TEI.exhibition_no = ANY(in_exhibition_no_array)
          OR COALESCE(in_exhibition_no_array,'{}'::bigint[]) = '{}'::bigint[]
        )
      ORDER BY TEI.exhibition_item_no, ranking_no
    ),
  RANKING_LIST AS (
    SELECT RW.exhibition_item_no
          , json_agg (
              json_build_object (
                'ranking_no', RW.ranking_no
              , 'member_id', RW.member_id
              , 'member_no', RW.member_no
              , 'member_nickname', RW.member_nickname
              , 'member_name', RW.member_name
              , 'company_name', RW.company_name
              , 'user_name', TBH.bid_user_name
              , 'bid_price', RW.bid_price
              , 'bid_quantity', RW.bid_quantity
              , 'bid_success_quantity', TERD.bid_success_quantity
              , 'allocated_quantity', GREATEST(0, LEAST(RW.bid_quantity, RW.total_quantity - RW.cumulative_quantity + RW.bid_quantity))
              )
              ORDER BY RW.ranking_no
            ) ranking_list
      FROM RANKING_WORK RW
          LEFT OUTER JOIN (
            SELECT TBH.exhibition_item_no, TBH.member_no, TBH.bid_user_name, TBH.bid_nickname
              FROM (
                SELECT TEI.exhibition_item_no
                     , TBH.member_no
                     , TBH.bid_user_name
                     , TBH.bid_nickname
                     , row_number() OVER(PARTITION BY TEI.exhibition_item_no, TBH.member_no ORDER BY TBH.create_datetime desc) order_seq
                    FROM t_exhibition_item TEI
                       INNER JOIN t_bid_history TBH
                         ON TEI.exhibition_item_no = TBH.exhibition_item_no
                 WHERE TEI.tenant_no = in_tenant_no
                    AND (
                      TEI.exhibition_no = ANY(in_exhibition_no_array)
                      OR COALESCE(in_exhibition_no_array,'{}'::bigint[]) = '{}'::bigint[]
                    )
              ) TBH
            WHERE order_seq=1
            ORDER BY exhibition_item_no
          ) TBH
            ON TBH.exhibition_item_no = RW.exhibition_item_no
            AND TBH.member_no = RW.member_no

          LEFT OUTER JOIN (
            SELECT TER.exhibition_item_no
                 , TERD.bid_success_member_no
                 , TERD.bid_success_quantity
              FROM t_exhibition_result TER
              LEFT JOIN t_exhibition_result_detail TERD
                ON TER.exhibition_result_no = TERD.exhibition_result_no
             WHERE TER.tenant_no = in_tenant_no
          ) TERD
            ON TERD.exhibition_item_no = RW.exhibition_item_no
            AND TERD.bid_success_member_no = RW.member_no
      GROUP BY RW.exhibition_item_no
  ),
  ITEM_WORK AS (
    SELECT TEI.exhibition_no, TEI.exhibition_item_no, TI.manage_no, TIL.free_field
      FROM t_exhibition_item TEI
            INNER JOIN t_lot_detail TLD
                    ON TEI.lot_no = TLD.lot_no
            INNER JOIN t_item TI
                    ON TLD.item_no = TI.item_no
            INNER JOIN t_item_localized TIL
                    ON TI.item_no = TIL.item_no
                    AND TIL.language_code = in_language_code
      WHERE TEI.tenant_no = in_tenant_no
        AND (
              TEI.exhibition_no = ANY(in_exhibition_no_array) OR COALESCE(in_exhibition_no_array,'{}'::bigint[]) = '{}'::bigint[]
            )
      ORDER BY TEI.exhibition_no, TEI.exhibition_item_no, TI.manage_no, TIL.free_field
    ),
  SEARCH_BY_SERIAL AS(
    SELECT IW.exhibition_item_no
      FROM ITEM_WORK IW
      WHERE in_product_name IS NULL
         OR IW.free_field->>'product_name' ~* f_escape_string(in_product_name)
      GROUP BY IW.exhibition_item_no
    ),
  ITEM_LIST AS(
    SELECT IW.exhibition_item_no
          , IW.manage_no
          , IW.free_field->>'product_name'::character varying as product_name
          , json_agg (
              json_build_object (
                'exhibition_no', IW.exhibition_no
              , 'manage_no', IW.manage_no
              , 'free_field', IW.free_field
              )
              ORDER BY IW.exhibition_item_no
            ) item_list
      FROM ITEM_WORK IW
      JOIN SEARCH_BY_SERIAL SRC ON SRC.exhibition_item_no = IW.exhibition_item_no
      GROUP BY IW.exhibition_item_no, IW.manage_no, product_name
    )
  SELECT TEL.exhibition_name
        , MCL.value2 category
        , TE.exhibition_no
        , TEI.exhibition_item_no
        , TE.status
        , TE.exhibition_classification_info->>'auctionClassification' auction_classification
        , TL.lot_id --画面に表示するロット番号
        , IL.item_list --画面に表示する商品情報
        , IL.manage_no
        , CASE WHEN RL.ranking_list IS NULL THEN '[]'::json ELSE RL.ranking_list END
        , TEI.quantity --数量
        , TEI.lowest_bid_quantity --最低入札数量
        , TEI.lowest_bid_accept_quantity --最低落札数量
        , TEI.lowest_bid_price lowest_bid_price --最低入札価格
        , TEI.lowest_bid_accept_price lowest_bid_accept_price  --最低落札価格
        , CASE WHEN TE.exhibition_classification_info->'auctionClassification' = '1'
               THEN COALESCE(TEI.current_price, TEI.lowest_bid_price)
               ELSE GREATEST((SELECT TB.bid_price
                               FROM t_bid TB
                              WHERE TB.exhibition_item_no = TEI.exhibition_item_no
                              ORDER BY TB.bid_price DESC, TB.update_datetime
                              LIMIT 1), TEI.lowest_bid_price)
                END current_price --現在価格
        , CASE
            WHEN TEI.bid_count = 0 THEN 0 --未入札
            WHEN (
              TEI.lowest_bid_accept_price <= COALESCE(RW_TOP.bid_price, 0)
              AND TEI.lowest_bid_accept_quantity <= COALESCE(RW_TOP.bid_quantity, 0)
              ) THEN 2 --最低落札越え
            WHEN TEI.bid_count > 0 THEN 1 --入札あり
            ELSE 9
          END exhibition_item_status
        , TEI.bid_count --入札件数
        , json_array_length(COALESCE(RL.ranking_list, '[]'::json)) bid_member_count --入札会員数
        , CASE WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN 1
              ELSE 0
          END extend_flag --延長フラグ
        , (CASE
            WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
            ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
          END) AS end_datetime --商品の終了時間(延長フラグが1の場合に延長残り時間の表示に使用)
        , (EXTRACT(epoch FROM (CASE
            WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
            ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
          END) - COALESCE(TEI.default_end_datetime, TEI.end_datetime)))::bigint extend_remaining_time --延長残り時間(画面では使用しない)
        , COALESCE(TEM.cnt, 0) as inquiry_message_count
        , COALESCE(TDM.cnt, 0) as delivery_message_count
        , COALESCE(TEM.un_checked_count, 0) as inquiry_un_checked_count
        , COALESCE(TDM.un_checked_count, 0) as delivery_un_checked_count
        , TEI.hummer_flag
    FROM t_exhibition TE
          INNER JOIN t_exhibition_item TEI
                  ON TE.exhibition_no = TEI.exhibition_no
                  AND TEI.cancel_flag = 0
                  AND TEI.delete_flag = 0
          INNER JOIN ITEM_LIST IL
                  ON TEI.exhibition_item_no = IL.exhibition_item_no
          INNER JOIN t_lot TL
                  ON TEI.lot_no = TL.lot_no
          INNER JOIN t_exhibition_localized TEL
                  ON TE.exhibition_no = TEL.exhibition_no
                  AND TEL.language_code = in_language_code
          LEFT OUTER JOIN (
            SELECT MCL.value1
                , MCL.value2
              FROM m_constant MC
                  INNER JOIN m_constant_localized MCL
                          ON MCL.constant_no = MC.constant_no
            WHERE MC.tenant_no = in_tenant_no
              AND MCL.language_code = in_language_code
              AND MC.key_string = 'PRODUCT_CATEGORY' -- 定数キー変更
              AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
          ) MCL ON MCL.value1 = TE.category_id::character varying
          LEFT OUTER JOIN RANKING_LIST RL
                      ON TEI.exhibition_item_no = RL.exhibition_item_no
          LEFT OUTER JOIN m_member TMM
                      ON TMM.member_no = TEI.top_member_no
                      AND TMM.tenant_no = in_tenant_no --論理的には必要ないが名称を出力するため念のため
          LEFT OUTER JOIN t_exhibition_result TER
                      ON TER.exhibition_item_no = TEI.exhibition_item_no
          LEFT JOIN (
            -- 商品チャット数
            SELECT TEM.exhibition_item_no
                   , COUNT(*) as cnt
                   , COUNT((TEM.update_category_id = '2' AND TEM.checked_admin_no IS NULL) OR NULL) as un_checked_count
              FROM t_exhibition_message TEM
             WHERE TEM.delete_flag = 0
             GROUP BY TEM.exhibition_item_no
          ) TEM ON TEI.exhibition_item_no = TEM.exhibition_item_no
          LEFT JOIN (
            -- 配送チャット数
            SELECT TDM.exhibition_item_no
                   , COUNT(*) as cnt
                   , COUNT((TDM.update_category_id = '2' AND TDM.checked_admin_no IS NULL) OR NULL) as un_checked_count
              FROM t_delivery_message TDM
             WHERE TDM.delete_flag = 0
             GROUP BY TDM.exhibition_item_no
          ) TDM ON TEI.exhibition_item_no = TDM.exhibition_item_no

          -- Get bid exceed lowest bid accept price and lowest bid accept quantity from ranking_list
          LEFT JOIN (
            SELECT RW.exhibition_item_no
                 , RW.bid_price
                 , RW.bid_quantity
              FROM RANKING_WORK RW
             WHERE RW.ranking_no = 1
          ) RW_TOP ON TEI.exhibition_item_no = RW_TOP.exhibition_item_no

    WHERE TE.tenant_no = in_tenant_no
      AND CASE WHEN in_manage_no IS NOT NULL THEN IL.manage_no LIKE '%' || in_manage_no || '%' ELSE true END
      AND CASE WHEN f_escape_string(in_product_name) IS NOT NULL THEN
          position(LOWER(f_escape_string(in_product_name)) IN LOWER(IL.product_name)) > 0
          ELSE true END
      AND (
            (
              CASE
                WHEN TEI.bid_count = 0 THEN 0 --未入札
                WHEN (
                  TEI.lowest_bid_accept_price <= COALESCE(RW_TOP.bid_price, 0)
                  AND TEI.lowest_bid_accept_quantity <= COALESCE(RW_TOP.bid_quantity, 0)
                 ) THEN 2 --最低落札越え
                WHEN TEI.bid_count > 0 THEN 1 --入札あり
                ELSE 9
              END
            ) = ANY(in_status_array) OR COALESCE(in_status_array,'{}'::bigint[]) = '{}'::bigint[]
          )
      AND (
            (
              CASE WHEN ((TEM.cnt > 0 AND TEM.un_checked_count = 0) AND (TDM.cnt > 0 AND TDM.un_checked_count = 0))
                        OR ((TEM.cnt > 0 AND TEM.un_checked_count = 0) AND (TDM.cnt IS NULL))
                        OR ((TEM.cnt IS NULL) AND (TDM.cnt > 0 AND TDM.un_checked_count = 0))   THEN 0 --未読メッセージ無
                   WHEN (TEM.cnt > 0 AND TEM.un_checked_count > 0) OR (TDM.cnt > 0 AND TDM.un_checked_count > 0) THEN 1 --未読メッセージ有
              END
            ) = ANY(in_unread_array) OR COALESCE(in_unread_array,'{}'::bigint[]) = '{}'::bigint[]
          )
      AND TE.delete_flag = 0
    ORDER BY TE.exhibition_no
            , TL.lot_id
            , TEI.exhibition_item_no;
END;
$BODY$;
