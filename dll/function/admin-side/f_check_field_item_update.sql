CREATE OR REPLACE FUNCTION public.f_check_field_item_update(
    in_tenant_no bigint,
    in_field_division character varying,
    in_physical_name character varying,
    in_field_no bigint
)
RETURNS TABLE(
    is_exists boolean
)
LANGUAGE plpgsql
AS $BODY$
----------------------------------------------------------------------------------------------------
-- 更新時の項目設定（テナント番号、項目区分、物理名、項目番号で）の重複チェックを行う
-- Parameters
-- @param in_tenant_no bigint - テナント番号
-- @param in_field_division character varying - 項目区分
-- @param in_physical_name character varying - 物理名
-- @param in_field_no bigint - 項目番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT EXISTS (
    SELECT 1
    FROM m_field f
    JOIN m_field_localized fl
      ON f.field_no = fl.field_no
    WHERE f.tenant_no = in_tenant_no
    AND f.field_division = in_field_division
    AND f.physical_name = in_physical_name
    AND f.field_no <> in_field_no
    AND f.delete_flag = 0
    AND fl.delete_flag = 0
  ) AS is_exists;

END;
$BODY$;
