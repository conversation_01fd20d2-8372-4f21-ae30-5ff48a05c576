CREATE OR REPLACE FUNCTION public.f_delete_field_item(
    in_field_no bigint,
    in_field_localized_no bigint,
    in_tenant_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE
    has_other_localizations boolean;

-- 項目設定を削除する
-- Parameters
 -- @param in_field_no bigint
 -- @param in_field_localized_no bigint
 -- @param in_tenant_no bigint
----------------------------------------------------------------------------------------------------
BEGIN

    -- 同じfield_noに関連する他の多言語項目があるか確認
    SELECT EXISTS (
        SELECT 1
        FROM m_field_localized fl
        WHERE fl.field_no = in_field_no
        AND fl.field_localized_no != in_field_localized_no
        AND fl.tenant_no = in_tenant_no
        AND fl.delete_flag = 0
    ) INTO has_other_localizations;

    -- 削除
    UPDATE m_field_localized fl
    SET update_datetime = now(),
        delete_flag = 1
    WHERE fl.field_localized_no = in_field_localized_no
    AND fl.tenant_no = in_tenant_no;

     -- 条件に基づいてm_fieldを更新
    IF NOT has_other_localizations THEN
        -- 他の言語版がない場合は項目自体も削除
        UPDATE m_field f
        SET delete_flag = 1,
            update_datetime = now()
        WHERE f.field_no = in_field_no
        AND f.tenant_no = in_tenant_no;
    ELSE
        -- 他の言語版がある場合は更新日時のみ更新
        UPDATE m_field f
        SET update_datetime = now()
        WHERE f.field_no = in_field_no
        AND f.tenant_no = in_tenant_no;
    END IF;

    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
