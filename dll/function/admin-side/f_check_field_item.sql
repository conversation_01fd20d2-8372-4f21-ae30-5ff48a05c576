CREATE OR REPLACE FUNCTION public.f_check_field_item(
    in_tenant_no bigint,
    in_field_division character varying,
    in_physical_name character varying,
    in_language_code character varying
)
RETURNS TABLE(
    is_exists boolean
)
LANGUAGE plpgsql
AS $BODY$
----------------------------------------------------------------------------------------------------
-- 項目設定（テナント番号、項目区分、物理名、言語コードで）の重複チェックを行う
-- Parameters
-- @param in_tenant_no bigint - テナント番号
-- @param in_field_division character varying - 項目区分
-- @param in_physical_name character varying - 物理名
-- @param in_language_code character varying - 言語コード
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT EXISTS (
    SELECT 1
    FROM m_field f
    JOIN m_field_localized fl
      ON f.field_no = fl.field_no
    WHERE f.tenant_no = in_tenant_no
    AND f.field_division = in_field_division
    AND f.physical_name = in_physical_name
    AND fl.language_code = in_language_code
    AND f.delete_flag = 0
    AND fl.delete_flag = 0
  ) AS is_exists;

END;
$BODY$;
