CREATE OR REPLACE FUNCTION public.f_get_tenant_options (
    in_tenant_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    function_options jsonb,
    bid_options jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- Get tenant options (function_options, bid_options)
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT T.tenant_no,
          T.function_options,
          T.bid_options
   FROM m_tenant T
   WHERE T.tenant_no = in_tenant_no
     AND T.delete_flag = 0
   ;

END;

$BODY$;
