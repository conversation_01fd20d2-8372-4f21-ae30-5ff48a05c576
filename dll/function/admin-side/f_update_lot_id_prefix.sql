CREATE OR REPLACE FUNCTION public.f_update_lot_id_prefix (
    in_tenant_no bigint,
    in_exhibition_no bigint,
    in_new_prefix character varying,
    in_admin_no bigint
)
RETURNS TABLE(
    lot_no bigint,
    lot_id character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- ロットNoの頭文字を更新する
-- Parameters
-- @param
--   in_tenant_no integer,
--   in_exhibition_no bigint,
--   in_new_prefix character varying,
--   admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  UPDATE t_lot L
     SET lot_id = (CASE WHEN in_new_prefix IS NULL THEN L.lot_id
                       ELSE CONCAT(in_new_prefix, SUBSTRING(L.lot_id,2))
                  END),
         update_admin_no = in_admin_no,
         update_datetime = current_timestamp
  WHERE tenant_no = in_tenant_no
    AND delete_flag = 0
    AND L.lot_no IN (
          SELECT L.lot_no
            FROM t_exhibition E
            LEFT JOIN t_exhibition_item EI ON EI.exhibition_no = E.exhibition_no and EI.delete_flag = 0
            LEFT JOIN t_lot L  ON L.lot_no = EI.lot_no AND L.delete_flag = 0
           WHERE E.exhibition_no = in_exhibition_no
             AND E.tenant_no = in_tenant_no
             AND EI.bid_count = 0
             AND CASE WHEN COALESCE(EI.hummer_flag, 999) <> 999 THEN EI.hummer_flag <> 1 ELSE true END
             AND E.delete_flag = 0
        )
  RETURNING L.lot_no, L.lot_id;

END;

$BODY$;
