CREATE OR REPLACE FUNCTION public.f_insert_or_update_notice_email (
    in_tenant_no bigint,
    in_notice_email_no bigint,
    in_send_datetime character varying,
    in_email_priority integer,
    in_language_code character varying,
    in_title character varying,
    in_body_title_upper_row character varying,
    in_body_title_lower_row character varying,
    in_body text,
    in_footer text,
    in_file text[],
    in_create_admin_no bigint,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_notice_email_no bigint;

----------------------------------------------------------------------------------------------------
-- お知らせメール情報を更新・追加する
-- Parameters
-- @param 
-- 　in_tenant_no bigint,
--   in_notice_email_no bigint,
--   in_send_datetime character varying,
--   in_email_priority integer,
--   in_language_code character varying,
--   in_title character varying,
--   in_body_title_upper_row character varying,
--   in_body_title_lower_row character varying,
--   in_body text,
--   in_footer text,
--   in_file text[],
--   in_create_admin_no bigint,
--   in_update_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  
  IF in_notice_email_no = 0
   OR NOT EXISTS ( SELECT 1 FROM t_notice_email WHERE notice_email_no=in_notice_email_no AND delete_flag=0 ) THEN
    INSERT INTO t_notice_email(
      tenant_no,
      send_datetime,
      email_priority,
      create_admin_no,
      create_datetime,
      update_admin_no,
      update_datetime
    ) VALUES (
      in_tenant_no,
      to_timestamp(in_send_datetime,'yyyy/MM/dd HH24:MI:SS'),
      in_email_priority,
      in_create_admin_no,
      now(),
      in_update_admin_no,
      now()
    ) RETURNING notice_email_no INTO return_notice_email_no;
  ELSE
    UPDATE t_notice_email
       SET send_datetime = to_timestamp(in_send_datetime,'yyyy/MM/dd HH24:MI:SS'),
           email_priority = in_email_priority,
           update_admin_no = in_update_admin_no,
           update_datetime = now()
      WHERE notice_email_no = in_notice_email_no
    RETURNING notice_email_no INTO return_notice_email_no;
  END IF;

  IF in_notice_email_no = 0
   OR NOT EXISTS ( SELECT 1 FROM t_notice_email_localized
    WHERE notice_email_no=in_notice_email_no AND language_code=in_language_code AND delete_flag=0 ) THEN
    INSERT INTO t_notice_email_localized(
      tenant_no,
      notice_email_no,
      language_code,
      title,
      body_title_upper_row,
      body_title_lower_row,
      body,
      footer,
      file,
      create_admin_no,
      create_datetime,
      update_admin_no,
      update_datetime
    ) VALUES (
      in_tenant_no,
      return_notice_email_no,
      in_language_code,
      in_title,
      in_body_title_upper_row,
      in_body_title_lower_row,
      in_body,
      in_footer,
      in_file,
      in_create_admin_no,
      now(),
      in_update_admin_no,
      now()
    );
  ELSE
    UPDATE  t_notice_email_localized
       SET  title = in_title,
            body_title_upper_row = in_body_title_upper_row,
            body_title_lower_row = in_body_title_lower_row,
            body = in_body,
            footer = in_footer,
            file = in_file,
            update_admin_no = in_update_admin_no,
            update_datetime = now()
      WHERE notice_email_no = in_notice_email_no
        AND language_code = in_language_code;
  END IF;

  RETURN return_notice_email_no;
  
END;

$BODY$;
