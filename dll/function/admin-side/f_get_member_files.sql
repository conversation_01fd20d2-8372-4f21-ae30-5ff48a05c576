CREATE OR REPLACE FUNCTION public.f_get_member_files (
    in_tenant_no bigint,
    in_member_request_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    member_request_no bigint,
    antique_file jsonb,
    export_file jsonb,
    name_card_file jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員添付ファイル一覧を取得する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_member_request_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  -- 会員
  (SELECT M.tenant_no,
            M.member_request_no,
            (M.free_field->'antiqueFilePath') as antique_file,
            (M.free_field->'exportFilePath') as export_file,
            (M.free_field->'nameCardFilePath') as name_card_file
     FROM m_member M
     JOIN m_tenant T ON T.tenant_no = M.tenant_no
                    AND T.tenant_no = in_tenant_no
     WHERE M.delete_flag = 0
       AND M.member_request_no = in_member_request_no
    ORDER BY M.create_datetime DESC, M.member_no)
  UNION
  -- 会員申請
  (SELECT  MR.tenant_no,
          MR.member_request_no,
          (MR.free_field->'antiqueFilePath') as antique_file,
          (MR.free_field->'exportFilePath') as export_file,
          (MR.free_field->'nameCardFilePath') as name_card_file
   FROM t_member_request MR
   JOIN m_tenant T ON T.tenant_no = MR.tenant_no
                  AND T.tenant_no = in_tenant_no
   WHERE MR.delete_flag = 0
    AND MR.member_request_no = in_member_request_no
    AND MR.member_request_no not in (
      SELECT DISTINCT(M.member_request_no)
      FROM m_member M
      WHERE M.member_request_no is not null
    )
  ORDER BY MR.create_datetime DESC, MR.member_request_no)
  ;

END;

$BODY$;
