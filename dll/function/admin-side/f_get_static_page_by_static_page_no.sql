CREATE OR REPLACE FUNCTION public.f_get_static_page_by_no (
    in_tenant_no bigint,
    in_static_page_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    static_page_no bigint,
    page_path character varying,
    localized jsonb[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- Get static page information by static_page_no
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.tenant_no,
          A.static_page_no,
          A.page_path,
          array_agg(
            jsonb_build_object(
              'language_code', B.language_code,
              'page_name', B.page_name,
              'file_url', B.file_url
            )
            ORDER BY B.language_code DESC
          ) AS localized
   FROM t_static_page A
   LEFT JOIN t_static_page_localized B ON A.static_page_no = B.static_page_no
   JOIN m_tenant T ON T.tenant_no = A.tenant_no
    WHERE A.tenant_no = in_tenant_no
      AND A.delete_flag = 0
      AND B.delete_flag = 0
      AND A.static_page_no = in_static_page_no
    GROUP BY A.tenant_no, A.static_page_no, A.page_path
    ORDER BY A.page_path, A.static_page_no;

END;

$BODY$;
