CREATE OR REPLACE FUNCTION public.f_delete_all_lots_xlsx (
    in_exhibition_no bigint,
    in_tenant_no bigint,
    in_admin_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    lot_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 出展機情報を取得する
-- Parameters
-- @param in_exhibition_no 入札会No
-- @param in_tenant_no テナントNo
-- @param in_admin_no 管理者番号
----------------------------------------------------------------------------------------------------

BEGIN
  
  RETURN QUERY
  -- Delete lot_no
  WITH getAllLots AS (
    SELECT TE.exhibition_no ,TEI.exhibition_item_no , TL.lot_no, TL.lot_id, TLD.item_no
      FROM t_exhibition TE
    LEFT JOIN t_exhibition_item TEI
           ON TE.exhibition_no = TEI.exhibition_no
          AND TEI.delete_flag = 0
    LEFT JOIN t_lot TL
           ON TEI.lot_no = TL.lot_no
          AND TL.delete_flag = 0
    LEFT JOIN t_lot_detail TLD
           ON TLD.lot_no = TL.lot_no
    LEFT JOIN t_item_localized TIL
           ON TIL.item_no = TLD.item_no
          AND TIL.delete_flag = 0
          AND TIL.language_code = in_language_code
     WHERE TE.exhibition_no = in_exhibition_no
       AND TE.tenant_no = in_tenant_no
       AND TE.delete_flag = 0
  ),
  DeleteLotDetail AS (
    DELETE FROM t_lot_detail TLD
          WHERE TLD.lot_no IN (SELECT AL.lot_no FROM getAllLots AL)
    RETURNING TLD.lot_no
  ),
  DeleteLot AS (
    UPDATE t_lot AS TL
       SET delete_flag = 1,
           update_admin_no = in_admin_no,
           update_datetime = current_timestamp
    WHERE TL.lot_no IN (SELECT AL.lot_no FROM getAllLots AL)
    RETURNING TL.lot_no
  ),
  DeleteExhibitionItem AS (
    UPDATE t_exhibition_item AS TEI
       SET delete_flag = 1,
           update_admin_no = in_admin_no,
           update_datetime = current_timestamp
    WHERE TEI.lot_no IN (SELECT AL.lot_no FROM getAllLots AL)
    RETURNING TEI.lot_no
  ),
  UpdateItemStatus AS (
    -- 商品情報更新
    UPDATE t_item AS TI
       SET status = 0,
           update_admin_no = in_admin_no,
           update_datetime = current_timestamp
    WHERE TI.item_no IN (SELECT AL.item_no FROM getAllLots AL)
      AND TI.tenant_no = in_tenant_no
  ),
  UpdateItemCount AS (
    -- 出品数カウントダウン
    UPDATE t_exhibition_summary
       SET exhibition_item_count = (
            SELECT COUNT(*) 
              FROM t_exhibition_item EI
             WHERE EI.exhibition_no = in_exhibition_no
                 AND EI.delete_flag = 0
                 AND EI.cancel_flag = 0
          ),
           update_admin_no = in_admin_no,
           update_datetime = current_timestamp
     WHERE exhibition_no = in_exhibition_no
  )

  SELECT AL.lot_no FROM getAllLots AL;

END;

$BODY$;
