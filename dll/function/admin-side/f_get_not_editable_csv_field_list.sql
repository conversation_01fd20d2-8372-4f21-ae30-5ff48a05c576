CREATE OR REPLACE FUNCTION public.f_get_not_editable_csv_field_list(
    in_tenant_no bigint,
    in_template_id bigint,
    in_language_code character varying
)
RETURNS TABLE(
    not_editable_input_data_list bigint[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- CSV項目設定の編集不可入力リストを取得する
-- Parameters
-- @param in_tenant_no character varying - テナント番号
-- @param in_template_id bigint - テンプレートID
-- @param in_language_code character varying - 言語区分
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT fc.not_editable_input_data_list AS not_editable_input_data_list
    FROM m_field_csv fc
    WHERE fc.tenant_no = in_tenant_no
      AND fc.csv_template_id = in_template_id
      AND fc.language_code = in_language_code
      AND fc.delete_flag = 0;

END;
$BODY$;
