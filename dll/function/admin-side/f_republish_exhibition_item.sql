CREATE OR REPLACE FUNCTION public.f_republish_exhibition_item (
    in_tenant_no bigint,
    in_exhibition_no bigint,
    in_lot_no bigint,
    in_admin_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying
)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 出品停止を取り消す
-- Parameters
-- @param
--   in_tenant_no integer,
--   in_exhibition_no bigint,
--   in_lot_no bigint,
--   in_admin_no bigint
----------------------------------------------------------------------------------------------------
return_exhibition_item_no bigint;
exhibition_status integer;

BEGIN
  
  SELECT TE.status FROM t_exhibition TE WHERE TE.exhibition_no = in_exhibition_no
  INTO exhibition_status;

  IF exhibition_status <> 0 THEN
    result := false;
    status := 400;
    message := 'EXHIBITION_ENDED_EXCEPTION';
    RAISE EXCEPTION 'EXHIBITION_ENDED_EXCEPTION';
  END IF;

  UPDATE t_exhibition_item ei
     SET cancel_flag = 0
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
    FROM t_lot l
   WHERE ei.exhibition_no = in_exhibition_no
     AND exhibition_status = 0
     AND l.lot_no = ei.lot_no
     AND l.lot_no=in_lot_no
     AND ei.tenant_no=in_tenant_no
     AND ei.delete_flag = 0
     AND ei.cancel_flag = 1;

  -- 出品数の更新
  UPDATE t_exhibition_summary
     SET exhibition_item_count = (
          SELECT COUNT(*)
            FROM t_exhibition_item EI
           WHERE EI.exhibition_no = in_exhibition_no
               AND EI.delete_flag = 0
               AND EI.cancel_flag = 0
        )
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE exhibition_no = in_exhibition_no
     AND exhibition_status = 0;

  result := true;
  status := 200;
  message := '';

RETURN NEXT;

EXCEPTION

  --更新対象データなし
  WHEN RAISE_EXCEPTION THEN
    RETURN NEXT;

  --その他エラー
  WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
