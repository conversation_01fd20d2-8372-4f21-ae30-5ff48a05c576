CREATE OR REPLACE FUNCTION public.f_get_exhibition_name_pulldown (
    in_tenant_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    exhibition_name character varying,
    category_id integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- お知らせメールの入札会名を取得する
-- Parameters
-- @param in_tenant_no テナントNo
-- @param in_language_code 管理者利用言語コード
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH EXHIBITIONS AS (
    SELECT TEL.exhibition_name, TE.category_id, TE.start_datetime
      FROM t_exhibition TE
    INNER JOIN t_exhibition_localized TEL
            ON TE.exhibition_no = TEL.exhibition_no
            AND TEL.language_code = in_language_code
    WHERE TE.tenant_no = in_tenant_no
      AND TE.delete_flag = 0
    ORDER BY TE.start_datetime DESC
  )
  SELECT E.exhibition_name, E.category_id FROM (
    SELECT DISTINCT(EXHIBITIONS.exhibition_name) AS exhibition_name,
        MAX(EXHIBITIONS.start_datetime) AS start_datetime,
        EXHIBITIONS.category_id AS category_id
    FROM EXHIBITIONS
    GROUP BY EXHIBITIONS.exhibition_name, EXHIBITIONS.category_id
  ) E
  ORDER BY E.start_datetime DESC;

END;

$BODY$;
