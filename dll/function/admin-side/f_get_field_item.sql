CREATE OR REPLACE FUNCTION public.f_get_field_item(
    in_tenant_no bigint,
    in_field_localized_no bigint
)
RETURNS TABLE(
    field_no bigint,
    field_localized_no bigint,
    physical_name character varying,
    input_type character varying,
    input_data_list jsonb,
    data_type character varying,
    max_length integer,
    max_value integer,
    regular_expressions character varying,
    required_flag integer,
    logical_name character varying,
    field_division character varying,
    language_code character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 特定の項目設定の値を取得する
-- Parameters
-- @param in_tenant_no character varying - テナント番号
-- @param in_field_no bigint - 項目番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT f.field_no AS field_no,
          fl.field_localized_no AS field_localized_no,
          f.physical_name AS physical_name,
          f.input_type AS input_type,
          f.input_data_list AS input_data_list,
          f.data_type AS data_type,
          f.max_length AS max_length,
          f.max_value AS max_value,
          f.regular_expressions AS regular_expressions,
          f.required_flag AS required_flag,
          fl.logical_name AS logical_name,
          f.field_division AS field_division,
          fl.language_code AS language_code
    FROM
      m_field f
    JOIN
      m_field_localized fl
    ON f.field_no = fl.field_no
  WHERE f.tenant_no = in_tenant_no
    AND f.tenant_no = fl.tenant_no
    AND fl.field_localized_no = in_field_localized_no
    AND fl.delete_flag = 0
    AND f.delete_flag = 0;

END;

$BODY$;
