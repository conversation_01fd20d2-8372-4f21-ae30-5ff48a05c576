CREATE OR REPLACE FUNCTION public.f_get_admin_for_login (
    in_admin_id character varying,
    in_origin character varying
)
RETURNS TABLE(
    admin_no bigint,
    tenant_no bigint,
    admin_name character varying,
    password character varying,
    role_id character varying,
    delete_flag integer,
    admin_language_code character varying,
    language_code_list character varying[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- ログインチェック用に管理者情報を取得する
-- Parameters
-- @param in_admin_id 利用者ID
-- @param in_origin アクセス元ドメイン
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.admin_no
       , MT.tenant_no
       , A.admin_name
       , A.password
       , A.role_id
       , A.delete_flag
       , MT.admin_language_code
       , MT.language_code_list
  FROM m_admin A
  LEFT JOIN m_tenant MT
    ON MT.tenant_no = A.tenant_no
 WHERE A.login_id = in_admin_id
   AND A.delete_flag = 0
   AND MT.delete_flag = 0
   AND (in_origin IS NULL OR in_origin LIKE '%' || MT.domain)
   AND now() > MT.start_datetime
   AND now() < MT.end_datetime;

END;

$BODY$;
