CREATE OR REPLACE FUNCTION public.f_delete_lot_detail (
    in_exhibition_no bigint,
    in_tenant_no bigint,
    in_item_nos bigint[],
    in_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- ロット内訳情報の削除をする
-- Parameters
-- @param
--   in_exhibition_no bigint
--   in_tenant_no integer
--   in_item_nos bigint[]
--   admin_no bigint
----------------------------------------------------------------------------------------------------
return_lot_no bigint;

BEGIN

  FOR idx in 1..array_length(in_item_nos,1)
  LOOP

    -- 登録先以外のロットにある内訳データを削除する
    DELETE FROM t_lot_detail ld
     USING t_exhibition_item ei
     WHERE ld.lot_no = ei.lot_no
       AND ld.item_no = in_item_nos[idx]
       AND ei.exhibition_no = in_exhibition_no
       AND ei.tenant_no = in_tenant_no
       AND ei.delete_flag = 0
    RETURNING ld.lot_no INTO return_lot_no;

    -- 商品情報更新
    UPDATE t_item
       SET status = 0
         , update_admin_no = in_admin_no
         , update_datetime = current_timestamp
    WHERE item_no = in_item_nos[idx] AND tenant_no = in_tenant_no;

    -- 削除したロットが空になった場合はt_lotとt_exhibition_itemを無効化する
    IF NOT EXISTS (SELECT 1 FROM t_lot_detail WHERE lot_no = return_lot_no AND tenant_no = in_tenant_no) THEN

      UPDATE t_lot l
        SET delete_flag = 1
          , update_admin_no = in_admin_no
          , update_datetime = current_timestamp
      WHERE lot_no = return_lot_no
        AND tenant_no = in_tenant_no
        AND delete_flag = 0;

      UPDATE t_exhibition_item
        SET delete_flag = 1
          , update_admin_no = in_admin_no
          , update_datetime = current_timestamp
      WHERE exhibition_no = in_exhibition_no
        AND lot_no = return_lot_no
        AND tenant_no = in_tenant_no
        AND delete_flag = 0;

    END IF;

  END LOOP;

  -- 出品数カウントダウン
  UPDATE t_exhibition_summary
     SET exhibition_item_count = (
          SELECT COUNT(*) 
            FROM t_exhibition_item EI
           WHERE EI.exhibition_no = in_exhibition_no
               AND EI.delete_flag = 0
               AND EI.cancel_flag = 0
        )
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE exhibition_no = in_exhibition_no;

  RETURN return_lot_no;

END;

$BODY$;
