CREATE OR REPLACE FUNCTION public.f_get_exhibitions (
    in_exhibition_no bigint,
    in_tenant_no bigint,
    in_exhibition_name text,
    in_preview_start_datetime_from character varying,
    in_preview_start_datetime_to character varying,
    in_start_datetime_from character varying,
    in_start_datetime_to character varying,
    in_category_id integer DEFAULT NULL::integer
)
RETURNS TABLE(
    row_number bigint,
    exhibition_no bigint,
    tenant_no bigint,
    category_id integer,
    pitch_option integer,
    end_option integer,
    localized_json_array json[],
    preview_start_datetime text,
    preview_end_datetime text,
    start_datetime text,
    end_datetime text,
    status text,
    max_extend_datetime text,
    extend_judge_minutes integer,
    extend_minutes integer,
    currency_id character varying,
    pitch_width numeric,
    more_little_judge_pitch integer,
    bid_count integer,
    exhibition_item_count integer,
    all_exhibition_item_count integer,
    contract_count integer,
    exhibition_classification_info jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 入札会情報を取得する
-- Parameters
-- @param in_exhibition_no 入札会No
-- @param in_tenant_no テナントNo
-- @param in_exhibition_name 入札会名
-- @param in_preview_start_datetime_from 下見開始日時(from)
-- @param in_preview_start_datetime_to 下見開始日時(to)
-- @param in_start_datetime_from 開催回開始日時(from)
-- @param in_start_datetime_to 開催回開始日時(to)
-- @param in_category_id 入札会カテゴリ
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH localized AS (
    SELECT TEL.exhibition_no
         , array_agg(row_to_json(row(TEL.language_code, TEL.exhibition_name))) localized_json_array
      FROM t_exhibition_localized TEL
      WHERE in_exhibition_name IS NULL OR TEL.exhibition_name ~ f_escape_string(in_exhibition_name)
     GROUP BY TEL.exhibition_no
  ),
  realtime_summary AS (
    SELECT TE.exhibition_no,
           COALESCE(SUM(
           CASE WHEN TE.exhibition_classification_info->'auctionClassification' = '1'
                THEN (CASE WHEN TEI.top_price >= TEI.lowest_bid_accept_price AND TEI.cancel_flag = 0 THEN 1 ELSE 0 END)
                ELSE (CASE WHEN (SELECT top_price FROM "f_get_current_price" (TEI.exhibition_item_no)) >= TEI.lowest_bid_accept_price AND TEI.cancel_flag = 0 THEN 1 ELSE 0 END)
            END),0) contract_count,
           SUM(CASE WHEN TEI.exhibition_item_no IS NOT NULL THEN 1 ELSE 0 END) item_count
    FROM t_exhibition TE
    LEFT JOIN t_exhibition_item TEI
    ON TE.exhibition_no = TEI.exhibition_no
    WHERE TE.delete_flag = 0
    AND TE.status = 0
    GROUP BY TE.exhibition_no
  )
  SELECT  ROW_NUMBER() OVER (ORDER BY A.start_datetime DESC)
       , A.exhibition_no
       , A.tenant_no
       , A.category_id
       , A.pitch_option
       , A.end_option
       , L.localized_json_array
       , to_char(A.preview_start_datetime, 'yyyy-MM-dd HH24:MI') preview_start_datetime
       , to_char(A.preview_end_datetime, 'yyyy-MM-dd HH24:MI') preview_end_datetime
       , to_char(A.start_datetime, 'yyyy-MM-dd HH24:MI') start_datetime
       , to_char(A.end_datetime, 'yyyy-MM-dd HH24:MI') end_datetime
       , CASE A.status WHEN 0 THEN '落札結果未確定'
                       WHEN 1 THEN '落札結果確定済'
                       WHEN 2 THEN 'インボイス作成済'
                       WHEN 3 THEN 'インボイス仮完了'
                       WHEN 4 THEN 'インボイス完了済'
                       ELSE ''
         END
       , to_char(A.max_extend_datetime, 'yyyy-MM-dd HH24:MI') max_extend_datetime
       , A.extend_judge_minutes
       , A.extend_minutes
       , A.currency_id
       , A.pitch_width
       , A.more_little_judge_pitch
       , S.bid_count
       , S.exhibition_item_count
       , R.item_count::integer AS all_exhibition_item_count
       , (CASE WHEN A.status = 0 THEN R.contract_count ELSE S.contract_count END) ::integer
       , A.exhibition_classification_info
  FROM t_exhibition A
  LEFT JOIN m_tenant T
    ON T.tenant_no = A.tenant_no
  JOIN localized L
    ON L.exhibition_no = A.exhibition_no
  LEFT JOIN t_exhibition_summary S
    ON S.exhibition_no = A.exhibition_no
  LEFT JOIN realtime_summary R
    ON A.exhibition_no = R.exhibition_no
 WHERE CASE WHEN in_exhibition_no IS NOT NULL THEN A.exhibition_no = in_exhibition_no ELSE true END
  --  AND CASE WHEN in_exhibition_name IS NOT NULL THEN L.exhibition_name ~ f_escape_string(in_exhibition_name) ELSE true END
   AND CASE WHEN in_preview_start_datetime_from IS NOT NULL THEN A.preview_start_datetime >= in_preview_start_datetime_from::timestamp with time zone ELSE true END
   AND CASE WHEN in_preview_start_datetime_to IS NOT NULL THEN A.preview_start_datetime <= in_preview_start_datetime_to::timestamp with time zone ELSE true END
   AND CASE WHEN in_start_datetime_from IS NOT NULL THEN A.start_datetime >= in_start_datetime_from::timestamp with time zone ELSE true END
   AND CASE WHEN in_start_datetime_to IS NOT NULL THEN A.start_datetime <= in_start_datetime_to::timestamp with time zone ELSE true END
   AND CASE WHEN in_category_id IS NOT NULL AND in_category_id != 99 THEN A.category_id = in_category_id WHEN in_category_id = 99 THEN A.category_id = ANY('{1,2}') ELSE A.category_id >= 0 END
   AND A.tenant_no = in_tenant_no
   AND A.delete_flag = 0
   AND T.delete_flag = 0
 ORDER BY start_datetime DESC;

END;

$BODY$;
