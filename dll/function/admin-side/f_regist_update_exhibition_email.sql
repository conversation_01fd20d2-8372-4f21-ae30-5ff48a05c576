CREATE OR REPLACE FUNCTION public.f_regist_update_exhibition_email(
    in_tenant_no bigint,
    in_admin_no bigint,
    emailReq json[],
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

-- 入札会のメール情報を更新する
-- Parameters
-- @param in_tenant_no bigint
-- @param in_exhibition_no bigint
-- @param emailReq json[] メール情報
-- t_exhibition_email
----------------------------------------------------------------------------------------------------

BEGIN

    WITH 
    new_exhibition_email AS (
        INSERT INTO t_exhibition_email (
        tenant_no,
        exhibition_name,
        classification,
        send_datetime,
        send_flag,
        create_admin_no,
        create_datetime,
        update_admin_no,
        update_datetime,
        sent_flag
    )
    (
        SELECT 
            in_tenant_no,
            (data->>'exhibitionName')::character varying, 
            (data->>'classification')::integer, 
            (data->>'dateTime')::timestamp with time zone, 
            (data->>'sendFlag')::bigint,
            in_admin_no,
            now(),
            null,
            now(),
            0
            FROM unnest(emailReq::jsonb[]) AS data
        WHERE
            ( SELECT COUNT(*)
                FROM t_exhibition_email EE
                WHERE EE.exhibition_name = (data->>'exhibitionName')::character varying
                    AND EE.classification = (data->>'classification')::integer
                    ) = 0
    )
	RETURNING
        exhibition_email_no,
        exhibition_name,
        classification
    ),

    exit_exhibition_email AS (
    UPDATE t_exhibition_email EER
    SET 
        send_datetime = (data->>'dateTime')::timestamp with time zone, 
        send_flag = (data->>'sendFlag')::bigint,
        update_admin_no = in_admin_no,
        update_datetime = now()
        FROM unnest(emailReq::jsonb[]) AS data
    WHERE 
        (   SELECT COUNT(*)
                FROM t_exhibition_email EE
                WHERE EE.exhibition_name = (data->>'exhibitionName')::character varying
                    AND EE.classification = (data->>'classification')::integer
                    ) > 0
        AND EER.exhibition_name = (data->>'exhibitionName')::character varying
        AND EER.classification = (data->>'classification')::integer
    RETURNING
        exhibition_email_no,
        exhibition_name,
        classification
    )

    SELECT 200 INTO status FROM new_exhibition_email, 
    exit_exhibition_email;   
    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
