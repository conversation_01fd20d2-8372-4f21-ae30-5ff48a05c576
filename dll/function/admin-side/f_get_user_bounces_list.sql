CREATE OR REPLACE FUNCTION public.f_get_user_bounces_list(
    in_tenant_no bigint,
    in_to_address text)
RETURNS TABLE(
    member_request_no bigint,
    nickname text,
    username text,
    email text,
    tel text,
    telCountryCode text
)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 利用会員を取得する
-- Parameters
-- @param in_to_address character varying
----------------------------------------------------------------------------------------------------

BEGIN
    RETURN QUERY
    WITH get_member AS (
        SELECT
            m_member.member_request_no AS member_request_no,
            free_field::jsonb->>'email'  AS email,
            free_field::jsonb->>'companyName' AS nickname,
            free_field::jsonb->>'memberName' AS username,
            free_field::jsonb->>'tel' AS tel,
            free_field::jsonb->>'telCountryCode' AS telCountryCode
        FROM m_member
        WHERE m_member.tenant_no = in_tenant_no
    ),
    get_member_request AS (
        SELECT
            MR.member_request_no AS member_request_no,
            MR.free_field::jsonb->>'email'  AS email,
            MR.free_field::jsonb->>'companyName' AS nickname,
            MR.free_field::jsonb->>'memberName'  AS username,
            MR.free_field::jsonb->>'tel' AS tel,
            free_field::jsonb->>'telCountryCode' AS telCountryCode
        FROM t_member_request MR
        WHERE MR.tenant_no = in_tenant_no
          AND MR.member_request_no NOT IN (
              SELECT DISTINCT(M.member_request_no)
                FROM get_member M
               WHERE M.member_request_no IS NOT NULL
            )
    )

    SELECT data.member_request_no,
        data.nickname,
        data.username,
        data.email,
        data.tel,
        data.telCountryCode
    FROM (SELECT * FROM get_member UNION SELECT * FROM get_member_request) AS data
    WHERE data.email = in_to_address
    ORDER BY data.nickname;

END;
$BODY$;

ALTER FUNCTION public.f_get_user_bounces_list(bigint, text)
OWNER TO postgres;
