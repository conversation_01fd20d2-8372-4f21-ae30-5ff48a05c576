CREATE OR REPLACE FUNCTION public.f_delete_notice (
    in_notice_no bigint,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_notice_no bigint;

----------------------------------------------------------------------------------------------------
-- お知らせ情報を削除する
-- Parameters
-- @param 
-- 　in_notice_no integer,
--   in_update_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  
  return_notice_no:=0;

  IF EXISTS ( SELECT 1 FROM t_notice WHERE notice_no=in_notice_no AND delete_flag=0) THEN
    UPDATE t_notice 
       SET delete_flag = 1,
           update_admin_no = in_update_admin_no,
           updated_datetime = now()
      WHERE notice_no = in_notice_no
    RETURNING notice_no INTO return_notice_no;

    IF EXISTS ( SELECT 1 FROM t_notice_localized WHERE notice_no=in_notice_no ) THEN
      UPDATE t_notice_localized 
         SET delete_flag = 1,
             update_admin_no = in_update_admin_no,
             updated_datetime = now()
        WHERE notice_no = in_notice_no;
    END IF;
  END IF;

  RETURN return_notice_no;
  
END;

$BODY$;
