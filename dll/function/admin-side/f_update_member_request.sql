CREATE OR REPLACE FUNCTION public.f_update_member_request (
    in_tenant_no bigint,
    in_member_request_no bigint,
    in_email_priority integer,
    in_free_field jsonb,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_member_request_no bigint;

----------------------------------------------------------------------------------------------------
-- 会員申請情報を更新する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_member_request_no bigint,
--   in_email_priority integer,
--   in_free_field jsonb,
--   in_update_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  -- 会員申請
  WITH update_member_request AS (
    UPDATE t_member_request
       SET email_priority = (CASE WHEN in_email_priority IS NULL THEN email_priority ELSE in_email_priority END),
           free_field = free_field || in_free_field,
           update_admin_no = in_update_admin_no,
           update_datetime = now()
      WHERE member_request_no = in_member_request_no
    RETURNING member_request_no
  )

  SELECT update_member_request.member_request_no FROM update_member_request
  LIMIT 1 INTO return_member_request_no;

  RETURN return_member_request_no;

END;
$BODY$;
