CREATE OR REPLACE FUNCTION public.f_get_ip_address_for_access_check (
    in_tenant_no bigint
)
RETURNS TABLE(
    allowed_ip character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- アクセスチェック用のIPアドレス一覧を取得する
-- Parameters
-- @param in_tenant_no テナント番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT A.allowed_ip
    FROM m_tenant A
   WHERE A.tenant_no = in_tenant_no;

END;

$BODY$;
