CREATE OR REPLACE FUNCTION public.f_get_notice_email_for_validation (
    in_tenant_no bigint,
    in_notice_email_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    notice_email_no bigint,
    sent_flag integer,
    send_datetime timestamp with time zone,
    delete_flag integer,
    langs json[]
)

LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- お知らせメール用の情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT NE.tenant_no,
         NE.notice_email_no,
         NE.sent_flag,
         NE.send_datetime,
         NE.delete_flag,
         array_agg(json_build_object(
           'title', NEL.title,
           'body_title_upper_row', NEL.body_title_upper_row,
           'body_title_lower_row', NEL.body_title_lower_row,
           'body', NEL.body,
           'footer', NEL.footer,
           'file', NEL.file,
           'language_code', NEL.language_code
         ))
    FROM t_notice_email NE
    JOIN m_tenant T ON T.tenant_no = NE.tenant_no
    LEFT JOIN t_notice_email_localized NEL
      ON NEL.notice_email_no = NE.notice_email_no
     AND (NEL.delete_flag IS NULL OR NEL.delete_flag = 0)
    WHERE T.tenant_no = in_tenant_no
      AND NE.notice_email_no = in_notice_email_no
    GROUP BY NE.tenant_no, NE.notice_email_no, NE.sent_flag, NE.send_datetime, NE.delete_flag
   ;
END;

$BODY$;
