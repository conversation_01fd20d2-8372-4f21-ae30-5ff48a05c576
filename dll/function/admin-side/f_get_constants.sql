CREATE OR REPLACE FUNCTION public.f_get_constants(
    in_tenant_no bigint
)
RETURNS TABLE(
    key_string character varying,
    value_name character varying,
    sort_order integer,
    start_datetime character varying,
    end_datetime character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 定数リストを取得する
-- Parameters
-- @param tenant_no character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT c.key_string AS key_string
        ,c.value_name AS value_name
        ,c.sort_order AS sort_order
        ,TO_CHAR(c.start_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying  AS start_datetime 
        ,TO_CHAR(c.end_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying  AS end_datetime
    FROM m_constant c
  WHERE c.tenant_no = in_tenant_no
    AND c.mainte_impossible_flag = 1
  ORDER BY c.constant_no, c.sort_order;

END;
$BODY$;
