CREATE OR REPLACE FUNCTION public.f_get_member_status_history (
    in_tenant_no bigint,
    in_member_request_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    member_request_no bigint,
    before_status integer,
    after_status integer,
    user_name character varying,
    create_admin_no bigint,
    create_datetime text
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員一覧を取得する
-- Parameters
-- @param 
--   in_tenant_no bigint,
--   in_country_code character varying,
--   in_company_name character varying,
--   in_tanto character varying,
--   in_status integer[]
----------------------------------------------------------------------------------------------------

BEGIN
  
  RETURN QUERY
  -- 会員ステータス変更履歴
  SELECT H.tenant_no,
          H.member_request_no,
          H.before_status,
          H.after_status,
          H.user_name as last_user_name,
          H.create_admin_no,
          to_char(H.create_datetime at time zone 'Japan', 'YYYY/MM/DD HH24:MI') as create_datetime
   FROM t_member_status_history H
   JOIN m_tenant T ON T.tenant_no = H.tenant_no
                    AND T.tenant_no = in_tenant_no
   WHERE H.delete_flag = 0
     AND in_member_request_no IS NOT NULL
     AND H.member_request_no = in_member_request_no
  ORDER BY H.create_datetime DESC;

END;

$BODY$;
