CREATE OR REPLACE FUNCTION public.f_get_batch_result_list (
  in_execution_datetime_from character varying,
  in_execution_datetime_to character varying,
  in_error_exists character varying
)
RETURNS TABLE(
  row_number bigint,
  execution_datetime timestamp with time zone,
  api_type character varying,
  all_data_count bigint,
  success_data_count bigint,
  error_data_count bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- バッチ実行結果を取得する
-- Parameters
-- @param in_execution_datetime_from 実行日時(from)
-- @param in_execution_datetime_to 実行日時(to)
-- @param in_error_exists エラー有無
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT
    ROW_NUMBER() OVER (ORDER BY TBR.execution_datetime DESC) AS row_number,
    TBR.execution_datetime,
    TBR.api_type,
    TBR.all_data_count,
    TBR.success_data_count,
    TBR.error_data_count
    FROM t_batch_result TBR
  WHERE (TBR.execution_datetime >= to_timestamp(in_execution_datetime_from, 'yyyy/MM/dd HH24:MI:SS') OR in_execution_datetime_from IS NULL)
    AND (TBR.execution_datetime <= to_timestamp(in_execution_datetime_to, 'yyyy/MM/dd HH24:MI:SS') OR in_execution_datetime_to IS NULL)
    AND (
      in_error_exists = '1' OR
      (in_error_exists = '2' AND TBR.error_data_count > 0) OR
      (in_error_exists = '3' AND TBR.error_data_count = 0))
    AND TBR.delete_flag = 0
  ORDER BY TBR.execution_datetime DESC;

END;

$BODY$;
