CREATE OR REPLACE FUNCTION public.f_insert_or_update_notice (
    in_notice_no bigint,
    in_tenant_no bigint,
    in_display_code integer,
    in_language_code character varying,
    in_start_datetime character varying,
    in_end_datetime character varying,
    in_title text,
    in_title1 text,
    in_sub_title text,
    in_body text,
    in_link_url character varying,
    in_file text[],
    in_create_admin_no bigint,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_notice_no bigint;

----------------------------------------------------------------------------------------------------
-- お知らせ情報を更新・追加する
-- Parameters
-- @param 
-- 　in_notice_no integer,
-- 　in_tenant_no integer,
--   in_display_code character varying,
--   in_language_code character varying,
--   in_start_datetime character varying,
--   in_end_datetime character varying,
--   in_title text,
--   in_title1 text,
--   in_sub_title text,
--   in_body text,
--   in_link_url character varying,
--   in_file text[],
--   in_create_admin_no bigint,
--   in_update_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  
  IF in_notice_no = 0
   OR NOT EXISTS ( SELECT 1 FROM t_notice WHERE notice_no=in_notice_no AND delete_flag=0 ) THEN
    INSERT INTO t_notice(
      tenant_no,
      display_code,
      start_datetime,
      end_datetime,
      create_admin_no,
      create_datetime,
      update_admin_no,
      updated_datetime
    ) VALUES (
      in_tenant_no,
      in_display_code,
      to_timestamp(in_start_datetime,'yyyy/MM/dd HH24:MI:SS'),
      to_timestamp(in_end_datetime,'yyyy/MM/dd HH24:MI:SS'),
      in_create_admin_no,
      now(),
      in_update_admin_no,
      now()
    ) RETURNING notice_no INTO return_notice_no;
  ELSE
    UPDATE t_notice 
       SET display_code = in_display_code,
           start_datetime = to_timestamp(in_start_datetime,'yyyy/MM/dd HH24:MI:SS'),
           end_datetime = to_timestamp(in_end_datetime,'yyyy/MM/dd HH24:MI:SS'),
           update_admin_no = in_update_admin_no,
           updated_datetime = now()
      WHERE notice_no = in_notice_no
    RETURNING notice_no INTO return_notice_no;
  END IF;

  IF in_notice_no = 0
   OR NOT EXISTS ( SELECT 1 FROM t_notice_localized
    WHERE notice_no=in_notice_no AND language_code=in_language_code AND delete_flag=0 ) THEN
    INSERT INTO t_notice_localized(
      tenant_no,
      notice_no,
      language_code,
      title,
      title1,
      sub_title,
      body,
      link_url,
      file,
      create_admin_no,
      create_datetime,
      update_admin_no,
      updated_datetime
    ) VALUES (
      in_tenant_no,
      return_notice_no,
      in_language_code,
      in_title,
      in_title1,
      in_sub_title,
      in_body,
      in_link_url,
      in_file,
      in_create_admin_no,
      now(),
      in_update_admin_no,
      now()
    );
  ELSE
    UPDATE t_notice_localized
       SET title = in_title,
           title1 = in_title1,
           sub_title = in_sub_title,
           body = in_body,
           link_url = in_link_url,
           file = in_file,
           update_admin_no = in_update_admin_no,
           updated_datetime = now()
      WHERE notice_no = in_notice_no
        AND language_code = in_language_code;
  END IF;

  RETURN return_notice_no;
  
END;

$BODY$;
