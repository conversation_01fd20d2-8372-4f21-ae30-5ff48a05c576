CREATE OR REPLACE FUNCTION public.f_insert_item_files(
    in_tenant_no bigint,
    in_item_no bigint,
    in_manage_no character varying,
    in_files json[],
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql
COST 100
VOLATILE
AS $BODY$
/************************************************************************/
--  処理内容： 商品付随ファイル連携
--  引数   : in_tenant_no　テナント番号
--  引数   : in_files データ(json[]形)
/************************************************************************/
DECLARE
BEGIN
  WITH files AS (
    -- Manage_no 不要のためitem_noと同じ値を設定する
     SELECT LOWER((REGEXP_MATCHES(data->>'fileName', '[^.]+$'))[1]) AS extend,
            LOWER((REGEXP_MATCHES(data->>'fileName', '([^/]*)\.'))[1]) AS name,
            COALESCE(in_manage_no, in_item_no::character varying) AS manage_no,
            (CASE WHEN data->>'division' IS NOT NULL
             THEN (data->>'division')::integer
             ELSE 0 END) AS division,
            data->>'key' AS key
       FROM unnest(in_files::jsonb[]) AS data
  ),
  common_files AS (
    SELECT in_tenant_no AS tenant_no,
           in_item_no AS item_no,
           F.manage_no,
           'common' AS language_code,
           1 AS division,
           ROW_NUMBER() OVER (PARTITION BY manage_no) AS serial_number,
           F.key AS file_path
      FROM files F
     WHERE F.extend IS NOT NULL
       AND F.extend IN ('jpg', 'png', 'mp4')
       AND F.division = 1
       -- AND F.name != F.manage_no
  ),
  bt_files AS (
    SELECT in_tenant_no AS tenant_no,
           in_item_no AS item_no,
           F.manage_no,
           LANG.language_code AS language_code,
           2 AS division,
           ROW_NUMBER() OVER (PARTITION BY manage_no, LANG.language_code) AS serial_number,
           F.key AS file_path
      FROM files F
      LEFT JOIN (
        SELECT DISTINCT MCL.value1 AS language_code
        FROM m_constant MC
          LEFT JOIN m_constant_localized MCL
          ON MCL.tenant_no = MC.tenant_no
          AND MCL.constant_no = MC.constant_no
       WHERE MC.key_string = 'LANGUAGE_CODE'
         AND MC.tenant_no = in_tenant_no
         AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
     ) LANG
      ON LANG.language_code = 'ja'

     WHERE F.extend IS NOT NULL
       AND F.extend IN ('jpg', 'png', 'mp4')
       AND F.name != F.manage_no
       AND F.division = 2
  ),
  pdf_files AS (
    SELECT in_tenant_no AS tenant_no,
           in_item_no AS item_no,
           F.manage_no,
           LANG.language_code AS language_code,
           3 AS division,
           ROW_NUMBER() OVER (PARTITION BY manage_no, LANG.language_code) AS serial_number,
           F.key AS file_path
      FROM files F
      LEFT JOIN (
        SELECT DISTINCT MCL.value1 AS language_code
        FROM m_constant MC
          LEFT JOIN m_constant_localized MCL
          ON MCL.tenant_no = MC.tenant_no
          AND MCL.constant_no = MC.constant_no
       WHERE MC.key_string = 'LANGUAGE_CODE'
         AND MC.tenant_no = in_tenant_no
         AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
     ) LANG
      ON LANG.language_code = 'ja'
     WHERE F.extend IS NOT NULL
       AND F.extend IN ('pdf')
       AND F.division = 3
       -- AND F.name != F.manage_no
  ),
  delete_existed AS (
    UPDATE t_item_ancillary_file TIAF
       SET delete_flag = 1,
           update_datetime = now()
     WHERE TIAF.item_no = in_item_no
      AND NOT EXISTS (
         SELECT 1 FROM t_item I
          WHERE I.item_no = in_item_no
            AND I.linked_flag = 1 
      )
    RETURNING
          TIAF.item_ancillary_file_no
  ),
  insert_new AS (
    INSERT INTO t_item_ancillary_file (
      tenant_no,
      item_no,
      manage_no,
      language_code,
      division,
      serial_number,
      file_path
    )
    (
      (SELECT * FROM common_files)
         UNION ALL
      (SELECT * FROM pdf_files)
         UNION ALL
      (SELECT * FROM bt_files)
    )
    RETURNING
          item_ancillary_file_no
  )

  SELECT 200 INTO status FROM delete_existed, insert_new;

  result := true;
  status := 200;
  message := '';
RETURN NEXT;
EXCEPTION
  --更新対象データなし
  WHEN RAISE_EXCEPTION THEN
    result := false;
    status := 500;
    message := 'data not found';
    RETURN NEXT;
  --その他エラー
  WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;
$BODY$;
