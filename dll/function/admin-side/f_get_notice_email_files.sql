CREATE OR REPLACE FUNCTION public.f_get_notice_email_files (
    in_tenant_no bigint,
    in_notice_email_no bigint
)
RETURNS TABLE(
    file text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- お知らせメールの添付ファイルを取得する
-- Parameters
-- @param 
--  in_notice_email_no integer
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT B.file
   FROM t_notice_email A
   JOIN t_notice_email_localized B ON A.notice_email_no = B.notice_email_no
   JOIN m_tenant T ON T.tenant_no = A.tenant_no AND T.tenant_no = in_tenant_no
    WHERE A.notice_email_no = in_notice_email_no
      AND COALESCE(array_length(B.file, 1), 0) > 0
      AND A.delete_flag = 0
      AND B.delete_flag = 0;

END;

$BODY$;
