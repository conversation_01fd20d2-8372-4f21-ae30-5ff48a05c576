CREATE OR REPLACE FUNCTION public.f_get_member_by_id(
    IN in_tenant_no bigint,
    IN in_member_id character varying
)
RETURNS TABLE(
    tenant_no bigint,
    member_no bigint,
    member_id character varying,
    member_request_no bigint,
    currency_id character varying,
    exhibition_allow_flag integer,
    bid_allow_flag integer,
    email_delivery_flag integer,
    free_field jsonb,
    status integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- MemberIDで会員情報を取得する
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  -- 会員と会員申請マスタ
  SELECT M.tenant_no,
        M.member_no,
        M.member_id,
        M.member_request_no,
        M.currency_id,
        M.exhibition_allow_flag,
        M.bid_allow_flag,
        M.email_delivery_flag,
        M.free_field,
        M.status
  FROM m_member M
  JOIN m_tenant T ON T.tenant_no = M.tenant_no
                AND T.tenant_no = in_tenant_no
  WHERE M.delete_flag = 0
    AND M.member_id = in_member_id;

END;

$BODY$;
