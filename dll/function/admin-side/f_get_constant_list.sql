CREATE OR REPLACE FUNCTION public.f_get_constant_list(
    in_tenant_no bigint,
    in_key_string character varying,
    in_get_mente_impossible_data_flag boolean
)
RETURNS TABLE(
    constant_no bigint,
    key_string character varying,
    value_name character varying,
    sort_order integer,
    start_datetime character varying,
    end_datetime character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 定数リストを取得する
-- Parameters
-- @param tenant_no character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT c.constant_no AS constant_no
        ,c.key_string AS key_string
        ,c.value_name AS value_name
        ,c.sort_order AS sort_order
        ,TO_CHAR(c.start_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying  AS start_datetime 
        ,TO_CHAR(c.end_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying  AS end_datetime
    FROM m_constant c
  WHERE c.tenant_no = in_tenant_no
    AND CASE WHEN length(in_key_string) > 0 THEN c.key_string = in_key_string ELSE TRUE END
    AND CASE WHEN in_get_mente_impossible_data_flag = TRUE THEN c.mainte_impossible_flag = 1 ELSE c.mainte_impossible_flag = 0 END
  ORDER BY c.key_string, c.sort_order;

END;
$BODY$;
