CREATE OR REPLACE FUNCTION public.f_delete_notice_email (
    in_tenant_no bigint,
    in_notice_email_no bigint,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_notice_email_no bigint;

----------------------------------------------------------------------------------------------------
-- お知らせメール情報を削除する
-- Parameters
-- @param 
--   in_tenant_no integer,
--   in_notice_email_no integer,
--   in_update_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  
  return_notice_email_no:=0;

  IF EXISTS ( SELECT 1 FROM t_notice_email WHERE notice_email_no=in_notice_email_no AND delete_flag=0) THEN
    UPDATE t_notice_email
       SET delete_flag = 1,
           update_admin_no = in_update_admin_no,
           update_datetime = now()
      WHERE notice_email_no = in_notice_email_no
        AND tenant_no = in_tenant_no
    RETURNING notice_email_no INTO return_notice_email_no;

    IF EXISTS ( SELECT 1 FROM t_notice_email_localized WHERE notice_email_no=in_notice_email_no ) THEN
      UPDATE t_notice_email_localized 
         SET delete_flag = 1,
             update_admin_no = in_update_admin_no,
             update_datetime = now()
        WHERE notice_email_no = in_notice_email_no
          AND tenant_no = in_tenant_no;
    END IF;
  END IF;

  RETURN return_notice_email_no;
  
END;

$BODY$;
