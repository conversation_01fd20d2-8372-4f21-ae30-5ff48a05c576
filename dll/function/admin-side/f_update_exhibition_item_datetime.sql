CREATE OR REPLACE FUNCTION public.f_update_exhibition_item_datetime (
    in_exhibition_no bigint,
    in_start_datetime character varying,
    in_end_datetime character varying,
    in_admin_no bigint,
    OUT result boolean,
    OUT status bigint,
    OUT message character varying
)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

----------------------------------------------------------------------------------------------------
-- 出品情報の開始・完了時間を更新する
-- Parameters
-- @param
--   in_exhibition_no bigint,
--   in_start_datetime character varying,
--   in_end_datetime character varying,
--   in_admin_no bigint
----------------------------------------------------------------------------------------------------

BEGIN

  result := true;
  status := 200;
  message := '';

  WITH exhibition AS (
    SELECT exhibition_no
      FROM t_exhibition
     WHERE exhibition_no = in_exhibition_no
       AND start_datetime <> in_start_datetime::timestamp with time zone
  )
  UPDATE t_exhibition_item
     SET start_datetime = in_start_datetime::timestamp with time zone,
         update_admin_no = in_admin_no,
         update_datetime = now()
    FROM exhibition
   WHERE t_exhibition_item.exhibition_no = exhibition.exhibition_no;


  WITH exhibition AS (
    SELECT exhibition_no
         , end_datetime
      FROM t_exhibition
     WHERE exhibition_no = in_exhibition_no
       AND end_datetime <> in_end_datetime::timestamp with time zone
  )
  UPDATE t_exhibition_item
     SET end_datetime = in_end_datetime::timestamp with time zone,
         default_end_datetime = in_end_datetime::timestamp with time zone,
         update_admin_no = in_admin_no,
         update_datetime = now()
    FROM exhibition
   WHERE t_exhibition_item.exhibition_no = exhibition.exhibition_no
     AND (t_exhibition_item.end_datetime = exhibition.end_datetime  --延長が行われていない商品
      OR (t_exhibition_item.end_datetime <> exhibition.end_datetime
        AND t_exhibition_item.end_datetime < in_end_datetime::timestamp with time zone)); --延長しているが、延長後の終了時間よりも今回入力した入札会の終了時間の方が大きい場合

RETURN NEXT;

EXCEPTION

  --その他エラー
  WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
