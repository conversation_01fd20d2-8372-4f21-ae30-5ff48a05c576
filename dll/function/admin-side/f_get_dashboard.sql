CREATE OR REPLACE FUNCTION public.f_get_dashboard(
    in_tenant_no bigint,
    in_language_code character varying,
    in_exhibition_name character varying,
    in_category_id integer
)
RETURNS TABLE(
    sum_exhibition_item_count bigint,
    sum_bid_count bigint,
    sum_bid numeric,
    sum_bid_member_count integer,
    sum_current_price numeric,
    sum_lowest_bid numeric,
    sum_contract_count integer,
    sum_hummer_bid numeric,
    min_start_datetime timestamp with time zone,
    max_start_datetime timestamp with time zone,
    sum_login bigint,
    datetime_bid json,
    bid_ranking json,
    bid_member_ranking json,
    category_bid_summary json
)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$
DECLARE

BEGIN
  RETURN QUERY
  WITH CURRENT_AUCTION AS(
    SELECT  TE.exhibition_no
            , TE.category_id
            , TE.exhibition_classification_info
            , TE.preview_start_datetime
            , TE.preview_end_datetime
            , TE.start_datetime
            , TE.end_datetime
            , TEL.exhibition_name
            , TES.bid_count --入札数
            , TES.exhibition_item_count -- 出展機数
            , TE.status
      FROM t_exhibition_localized TEL
          INNER JOIN t_exhibition TE
                  ON TEL.exhibition_no = TE.exhibition_no
          INNER JOIN t_exhibition_summary TES
                  ON TEL.exhibition_no = TES.exhibition_no
                AND TE.delete_flag = 0
    WHERE TEL.tenant_no = in_tenant_no
      AND TEL.language_code = in_language_code
      AND (in_exhibition_name IS NOT NULL AND TEL.exhibition_name = in_exhibition_name)
      AND (in_category_id IS NULL OR TE.category_id = in_category_id)
  ),
  CURRENT_AUCTION_ITEM AS (
    SELECT CA.exhibition_no
        , CA.exhibition_classification_info
        , TEI.exhibition_item_no
        , TL.lot_no
        , TL.lot_id
        , TEI.bid_count
        , TEI.quantity
        , TEI.lowest_bid_price
        , TEI.lowest_bid_quantity
        , COALESCE(TEI.lowest_bid_accept_price,0) lowest_bid_accept_price
        , TEI.lowest_bid_accept_quantity
        , TEI.hummer_flag
        , COALESCE((
            CASE WHEN CA.status = 0
                THEN
                  (
                    CASE WHEN CA.exhibition_classification_info->'auctionClassification' = '1'
                        THEN TEI.current_price
                        ELSE (
                          SELECT SUM(top_price * allocated_quantity)
                            FROM "f_get_top_bid_order" (TEI.exhibition_item_no)
                        )
                    END
                  )
                ELSE
                  TEI.current_price
            END), 0) current_price
        , TEI.start_datetime
        , TEI.end_datetime
      FROM CURRENT_AUCTION CA
          LEFT OUTER JOIN t_exhibition_item TEI
                        ON CA.exhibition_no = TEI.exhibition_no
                      AND TEI.cancel_flag = 0
                      AND TEI.delete_flag = 0
          LEFT OUTER JOIN t_lot TL
                      ON TEI.lot_no = TL.lot_no
  ),
  CURRENT_AUCTION_ITEM_BID AS (
    SELECT CAI.exhibition_no
        , TB.member_no
        , CAI.exhibition_item_no
        , CAI.lot_id
        , CAI.bid_count
        , COALESCE(TB.bid_price,0) bid_price
        , COALESCE(TB.bid_quantity,0) bid_quantity
        , COALESCE(CAI.lowest_bid_accept_price,0) lowest_bid_accept_price
        , CAI.hummer_flag
         , (COALESCE(TB.bid_price,0) * COALESCE(TB.bid_quantity,0)) current_price
      FROM CURRENT_AUCTION_ITEM CAI
          LEFT OUTER JOIN t_bid TB
                        ON CAI.exhibition_item_no = TB.exhibition_item_no
      WHERE TB.member_no IS NOT NULL
  ),
  CURRENT_AUCTION_ITEM_SUMMARY AS (
    SELECT CAI.exhibition_no
        , COALESCE(SUM(BID_RESULT_DETAIL.bid_success_price),0) sum_hummer_bid -- 総落札金額
        , SUM(CAIB.current_price) sum_current_price -- 現在価格合計
        , SUM(CAIB_EXCEED_LOWEST.current_price) sum_lowest_bid --うち最低落札超え(うち最落越え)
        , COALESCE(SUM(CASE WHEN CAI.lowest_bid_accept_price <= COALESCE(CAI.current_price,0)
                            THEN 1
                            ELSE 0
                        END
          ),0) sum_contract_count --成約台数
    FROM CURRENT_AUCTION_ITEM CAI

    LEFT JOIN (
      SELECT CAIB.exhibition_item_no,
            SUM(CAIB.current_price) AS current_price
        FROM CURRENT_AUCTION_ITEM_BID CAIB
        GROUP BY CAIB.exhibition_item_no
    ) CAIB
        ON CAIB.exhibition_item_no = CAI.exhibition_item_no

    LEFT JOIN (
      SELECT CAIB.exhibition_item_no,
          SUM(CAIB.current_price) AS current_price
        FROM CURRENT_AUCTION_ITEM_BID CAIB
        WHERE COALESCE(CAIB.bid_price,0) >= CAIB.lowest_bid_accept_price
        GROUP BY CAIB.exhibition_item_no
    ) CAIB_EXCEED_LOWEST
        ON CAIB_EXCEED_LOWEST.exhibition_item_no = CAI.exhibition_item_no

    LEFT JOIN (
      SELECT TER.exhibition_item_no,
          SUM(COALESCE(TERD.bid_success_quantity, 0) * COALESCE(TERD.bid_success_price, 0)) AS bid_success_price
        FROM t_exhibition_result TER
        LEFT JOIN t_exhibition_result_detail TERD
          ON TER.exhibition_result_no = TERD.exhibition_result_no
        WHERE TERD.exhibition_result_detail_no IS NOT NULL
        GROUP BY TER.exhibition_item_no
    ) BID_RESULT_DETAIL
        ON BID_RESULT_DETAIL.exhibition_item_no = CAI.exhibition_item_no

    GROUP BY CAI.exhibition_no
  ),
  CURRENT_AUCTION_BID_SUMMARY AS (
    SELECT CAIB.exhibition_no
      , COALESCE(SUM(COALESCE(CAIB.bid_price, 0) * COALESCE(CAIB.bid_quantity, 0)), 0) AS sum_bid --総入札金額
    FROM CURRENT_AUCTION_ITEM_BID CAIB
    GROUP BY CAIB.exhibition_no
  ),
  BID_HISTORY AS(
    SELECT COUNT(TBH.bid_history_no) bid_count, DT.datetime
    FROM generate_series((SELECT MIN(CAI.start_datetime) FROM CURRENT_AUCTION_ITEM CAI), (SELECT MAX(CAI.end_datetime) FROM CURRENT_AUCTION_ITEM CAI), '60 minute') AS DT(datetime)
          LEFT OUTER JOIN t_bid_history TBH
                    ON TBH.create_datetime >= DT.datetime and TBH.create_datetime < (DT.datetime + INTERVAL '60 minute')
                    AND EXISTS (SELECT CAI.exhibition_item_no FROM CURRENT_AUCTION_ITEM CAI WHERE TBH.exhibition_item_no = CAI.exhibition_item_no)
    GROUP BY DT.datetime
    ORDER BY DT.datetime
  ),
  BID_COUNT_RANKING AS (
    SELECT CAI.bid_count,
          CAI.lot_id,
          (TIL.free_field ->>'product_name'::character varying) as productName -- 商品名追加
      FROM CURRENT_AUCTION_ITEM CAI
          INNER JOIN t_lot TL
                  ON TL.lot_no = CAI.lot_no
          INNER JOIN t_lot_detail TLD
                  ON TLD.lot_no = TL.lot_no
          INNER JOIN t_item TI
                  ON TI.item_no = TLD.item_no
          INNER JOIN t_item_localized TIL
                  ON TI.item_no = TIL.item_no
                  AND language_code = in_language_code
    WHERE CAI.bid_count > 0
    ORDER BY CAI.bid_count DESC
    LIMIT 10
  ),
  BID_MEMBER_RANKING AS (
    SELECT CAIB.member_no,
          MAX(MM.free_field->>'companyName') :: character varying company_name,
          SUM(CAIB.bid_price * CAIB.bid_quantity) sum_bid_price
      FROM CURRENT_AUCTION_ITEM_BID CAIB
          LEFT OUTER JOIN m_member MM
                        ON MM.member_no = CAIB.member_no
                      AND MM.tenant_no = in_tenant_no
    GROUP BY CAIB.member_no
    ORDER BY SUM(CAIB.bid_price * CAIB.bid_quantity) DESC
  ),
  CATEGORY AS(
    SELECT MCL.value1, MCL.value2, MCL.value5, MC.sort_order
      FROM m_constant MC
           INNER JOIN m_constant_localized MCL
                   ON MC.constant_no = MCL.constant_no
                  AND MCL.language_code = in_language_code
     WHERE MC.key_string = 'PRODUCT_CATEGORY' -- 定数キー変更
       AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
     GROUP BY MCL.value1, MCL.value2, MCL.value5, MC.sort_order
  ),
  CATEGORY_WORK AS(
    SELECT TEI.exhibition_no
         , TEI.exhibition_item_no
         , TL.lot_no
         , TLD.item_no
         , TB.bid_price
         , TB.member_no
         , COUNT(*) OVER(PARTITION BY TEI.exhibition_item_no, TB.member_no) AS lot_detail_count
         , row_number() OVER(PARTITION BY TEI.exhibition_item_no, TB.member_no ORDER BY TLD.item_no) order_seq
         , TIL.free_field
      FROM t_bid TB
           INNER JOIN t_exhibition_item TEI
                   ON TB.exhibition_item_no = TEI.exhibition_item_no
                  AND TEI.cancel_flag = 0
                  AND TEI.delete_flag = 0
           INNER JOIN CURRENT_AUCTION CA
                   ON TEI.exhibition_no = CA.exhibition_no
           INNER JOIN t_lot TL
                   ON TL.lot_no = TEI.lot_no
           INNER JOIN t_lot_detail TLD
                   ON TL.lot_no = TLD.lot_no
           INNER JOIN t_item TI
                   ON TI.item_no = TLD.item_no
           INNER JOIN t_item_localized TIL
                   ON TI.item_no = TIL.item_no
                  AND language_code = in_language_code
  ),
  CATEGORY_CALC AS(
    SELECT CATEGORY_WORK.exhibition_no
         , CATEGORY_WORK.exhibition_item_no
         , CATEGORY_WORK.lot_no
         , CATEGORY_WORK.item_no
         , CATEGORY_WORK.lot_detail_count
         , CATEGORY_WORK.order_seq
         , CATEGORY_WORK.free_field
         , CASE WHEN order_seq = 1 THEN trunc(trunc(bid_price/lot_detail_count , 0) + mod(bid_price, lot_detail_count),0) ELSE trunc(bid_price/lot_detail_count , 0) END AS bid_price
      FROM CATEGORY_WORK
  ),
  CATEGORY_SUMMARY AS(
    SELECT (CATEGORY_CALC.free_field->>'category') CATEGORY_ID
         , SUM(CATEGORY_CALC.bid_price) bid_summary
      FROM CATEGORY_CALC
     GROUP BY (CATEGORY_CALC.free_field->>'category')
  ),
  CATEGORY_BID_SUMMARY AS(
    SELECT CATEGORY.value1
         , CATEGORY.value2
         , CATEGORY.value5
         , CATEGORY.sort_order
         , CATEGORY_SUMMARY.bid_summary
      FROM CATEGORY
           LEFT OUTER JOIN CATEGORY_SUMMARY
                      ON CATEGORY.value1 = CATEGORY_SUMMARY.CATEGORY_ID
    ORDER BY CATEGORY.sort_order
  )
  SELECT SUM(CA.exhibition_item_count) sum_exhibition_item_count --出展機数
      , SUM(COALESCE(CA.bid_count, 0)) sum_bid_count --総入札数
      , SUM(COALESCE(CABS.sum_bid,0)) sum_bid --総入札金額
      , (SELECT COALESCE(COUNT(DISTINCT CAIB.member_no),0) sum_bid_member_count FROM CURRENT_AUCTION_ITEM_BID CAIB) :: integer sum_bid_member_count --総入札会員数
      , SUM(COALESCE(CAIS.sum_current_price,0)) sum_current_price --現在価格合計
      , SUM(COALESCE(CAIS.sum_lowest_bid, 0)) sum_lowest_bid -- うち最低落札超え
      , SUM(COALESCE(CAIS.sum_contract_count, 0)) :: integer sum_contract_count -- 成約台数
      , SUM(COALESCE(CAIS.sum_hummer_bid,0)) sum_hummer_bid -- 総落札金額
      , MIN(CA.start_datetime) min_start_datetime --開始時刻(グラフの左端に使用)
      , MAX(CA.end_datetime) max_start_datetime --終了時刻(グラフの右端に使用)
      , (SELECT COUNT(*) FROM (
            SELECT DISTINCT(member_no)
              FROM t_login
            WHERE create_datetime BETWEEN COALESCE(MIN(CA.preview_start_datetime), MIN(CA.start_datetime)) AND COALESCE(MAX(CA.preview_end_datetime), MAX(CA.end_datetime))
            ) TL
          ) sum_login --ログイン数
      , (SELECT json_agg (
                json_build_object (
                  'datetime', BH.datetime
                , 'bid_count', COALESCE(BH.bid_count, 0)
                )
          ORDER BY BH.datetime, BH.bid_count
          ) FROM BID_HISTORY BH) datetime_bid -- 入札数
      , (SELECT json_agg (
                json_build_object (
                  'lot_id', BCR.lot_id
                , 'productName', COALESCE(BCR.productName, '') -- 商品名追加
                , 'bid_count', COALESCE(BCR.bid_count, 0)
                )
            ORDER BY BCR.bid_count desc, BCR.lot_id
          ) FROM BID_COUNT_RANKING BCR) bid_ranking --入札数上位
      , (SELECT json_agg (
                json_build_object (
                  'member_no', BMR.member_no,
                  'company_name', CASE WHEN BMR.company_name IS NULL OR BMR.company_name = '' THEN '会社名なし' ELSE BMR.company_name END
                , 'sum_bid_price', COALESCE(BMR.sum_bid_price, 0)
                )
          ORDER BY COALESCE(BMR.sum_bid_price, 0) desc, BMR.company_name
          ) FROM BID_MEMBER_RANKING BMR) bid_member_ranking --会員別入札金額
      , (SELECT json_agg (
                json_build_object (
                  'category_id', CBS.value1
                , 'category_name', CBS.value2
                , 'category_color', CBS.value5
                , 'bid_summary', COALESCE(CBS.bid_summary, 0)
                )
          ORDER BY CBS.sort_order, COALESCE(CBS.bid_summary, 0)
          ) FROM CATEGORY_BID_SUMMARY CBS) category_bid_summary --カテゴリー別入札割合
  FROM CURRENT_AUCTION CA
        INNER JOIN CURRENT_AUCTION_BID_SUMMARY CABS
                ON CA.exhibition_no = CABS.exhibition_no
        INNER JOIN CURRENT_AUCTION_ITEM_SUMMARY CAIS
                ON CA.exhibition_no = CAIS.exhibition_no;
END;
$BODY$;
