-- =====================================================
-- Row-Level Security (RLS) Implementation
-- Enable RLS on all tables with tenant_no column
-- =====================================================

-- Enable RLS on Master Tables with tenant_no
ALTER TABLE m_admin ENABLE ROW LEVEL SECURITY;
ALTER TABLE m_field ENABLE ROW LEVEL SECURITY;
ALTER TABLE m_field_localized ENABLE ROW LEVEL SECURITY;
ALTER TABLE m_constant ENABLE ROW LEVEL SECURITY;
ALTER TABLE m_constant_localized ENABLE ROW LEVEL SECURITY;
ALTER TABLE m_member ENABLE ROW LEVEL SECURITY;
ALTER TABLE m_user ENABLE ROW LEVEL SECURITY;

-- Enable RLS on Transaction Tables
ALTER TABLE t_member_request ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_member_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_login ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_item ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_item_localized ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_item_ancillary_file ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_stock_item_favorite ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_stock_result ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_lot ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_lot_detail ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_localized ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_item ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_email ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_email_localized ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_item_favorite ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_member ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_result ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_result_detail ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_bid ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_bid_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_notice_email ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_notice_email_localized ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_notice ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_notice_localized ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_inquiry ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_email_notification ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_web_socket_connection ENABLE ROW LEVEL SECURITY;
-- NOTE: t_batch_result is EXCLUDED because it doesn't have tenant_no column
ALTER TABLE t_exhibition_message ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_member_search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE t_delivery_message ENABLE ROW LEVEL SECURITY;

-- Force RLS for table owners (prevents superuser bypass)
-- This ensures even database owners must follow RLS policies
ALTER TABLE m_admin FORCE ROW LEVEL SECURITY;
ALTER TABLE m_field FORCE ROW LEVEL SECURITY;
ALTER TABLE m_field_localized FORCE ROW LEVEL SECURITY;
ALTER TABLE m_constant FORCE ROW LEVEL SECURITY;
ALTER TABLE m_constant_localized FORCE ROW LEVEL SECURITY;
ALTER TABLE m_member FORCE ROW LEVEL SECURITY;
ALTER TABLE m_user FORCE ROW LEVEL SECURITY;

ALTER TABLE t_member_request FORCE ROW LEVEL SECURITY;
ALTER TABLE t_member_status_history FORCE ROW LEVEL SECURITY;
ALTER TABLE t_login FORCE ROW LEVEL SECURITY;
ALTER TABLE t_item FORCE ROW LEVEL SECURITY;
ALTER TABLE t_item_localized FORCE ROW LEVEL SECURITY;
ALTER TABLE t_item_ancillary_file FORCE ROW LEVEL SECURITY;
ALTER TABLE t_stock_item_favorite FORCE ROW LEVEL SECURITY;
ALTER TABLE t_stock_result FORCE ROW LEVEL SECURITY;
ALTER TABLE t_lot FORCE ROW LEVEL SECURITY;
ALTER TABLE t_lot_detail FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_localized FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_item FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_email FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_email_localized FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_item_favorite FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_member FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_result FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_result_detail FORCE ROW LEVEL SECURITY;
ALTER TABLE t_exhibition_summary FORCE ROW LEVEL SECURITY;
ALTER TABLE t_bid FORCE ROW LEVEL SECURITY;
ALTER TABLE t_bid_history FORCE ROW LEVEL SECURITY;
ALTER TABLE t_notice_email FORCE ROW LEVEL SECURITY;
ALTER TABLE t_notice_email_localized FORCE ROW LEVEL SECURITY;
ALTER TABLE t_notice FORCE ROW LEVEL SECURITY;
ALTER TABLE t_notice_localized FORCE ROW LEVEL SECURITY;
ALTER TABLE t_inquiry FORCE ROW LEVEL SECURITY;
ALTER TABLE t_email_notification FORCE ROW LEVEL SECURITY;
ALTER TABLE t_web_socket_connection FORCE ROW LEVEL SECURITY;
-- NOTE: t_batch_result is EXCLUDED because it doesn't have tenant_no column
ALTER TABLE t_exhibition_message FORCE ROW LEVEL SECURITY;
ALTER TABLE t_member_search_history FORCE ROW LEVEL SECURITY;
ALTER TABLE t_delivery_message FORCE ROW LEVEL SECURITY;

-- =====================================================
-- TABLES EXCLUDED FROM RLS:
-- 1. m_tenant - tenant master table (needed for tenant resolution)
-- 2. t_batch_result - system-wide table without tenant_no column
-- =====================================================
