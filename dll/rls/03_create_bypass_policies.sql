-- =====================================================
-- BYPASS RLS POLICIES (Continued)
-- Transaction Tables Bypass Policies
-- =====================================================

CREATE POLICY bypass_rls_policy ON t_member_request
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_member_status_history
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_login
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_item
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_item_localized
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_item_ancillary_file
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_stock_item_favorite
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_stock_result
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_lot
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_lot_detail
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_localized
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_item
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_email
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_email_localized
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_item_favorite
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_member
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_result
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_result_detail
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_exhibition_summary
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_bid
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_bid_history
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_notice_email
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_notice_email_localized
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_notice
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_notice_localized
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_inquiry
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_email_notification
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_web_socket_connection
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

-- NOTE: t_batch_result is EXCLUDED from bypass policies as well
-- since it doesn't have tenant_no and doesn't need RLS

CREATE POLICY bypass_rls_policy ON t_exhibition_message
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_member_search_history
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON t_delivery_message
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

-- =====================================================
-- NOTES:
-- 1. RLS policies are evaluated with OR logic
-- 2. If either tenant_isolation_policy OR bypass_rls_policy returns TRUE, access is granted
-- 3. Use bypass_rls for system operations, migrations, and administrative tasks
-- 4. Always set app.current_tenant for normal application operations
-- 5. Tables excluded from RLS: t_batch_result (system-wide table without tenant_no)
-- =====================================================
