-- =====================================================
-- Row-Level Security (RLS) Policies (CORRECTED VERSION)
-- Create tenant isolation policies for tables WITH tenant_no column
-- =====================================================

-- =====================================================
-- TENANT ISOLATION POLICIES
-- These policies restrict access to rows where tenant_no matches app.current_tenant
-- =====================================================

-- Master Tables Policies
CREATE POLICY tenant_isolation_policy ON m_admin
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON m_field
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON m_field_localized
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON m_constant
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON m_constant_localized
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON m_member
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON m_user
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

-- Transaction Tables Policies
CREATE POLICY tenant_isolation_policy ON t_member_request
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_member_status_history
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_login
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_item
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_item_localized
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_item_ancillary_file
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_stock_item_favorite
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_stock_result
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_lot
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_lot_detail
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_localized
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_item
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_email
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_email_localized
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_item_favorite
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_member
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_result
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_result_detail
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_exhibition_summary
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_bid
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_bid_history
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_notice_email
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_notice_email_localized
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_notice
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_notice_localized
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_inquiry
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_email_notification
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_web_socket_connection
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

-- NOTE: t_batch_result is EXCLUDED because it doesn't have tenant_no column
-- This is a system-wide table that doesn't need tenant isolation

CREATE POLICY tenant_isolation_policy ON t_exhibition_message
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_member_search_history
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

CREATE POLICY tenant_isolation_policy ON t_delivery_message
    USING (tenant_no = current_setting('app.current_tenant', TRUE)::bigint);

-- =====================================================
-- BYPASS RLS POLICIES
-- These policies allow bypassing RLS when app.bypass_rls is set to 'on'
-- Useful for system operations, migrations, and cross-tenant operations
-- =====================================================

-- Master Tables Bypass Policies
CREATE POLICY bypass_rls_policy ON m_admin
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON m_field
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON m_field_localized
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON m_constant
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON m_constant_localized
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON m_member
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');

CREATE POLICY bypass_rls_policy ON m_user
    USING (current_setting('app.bypass_rls', TRUE)::text = 'on');
