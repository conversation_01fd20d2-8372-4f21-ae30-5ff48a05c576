CREATE TABLE t_static_page_localized (
    static_page_localized_no bigint, --静的ページLocalized番号
    static_page_no bigint NOT NULL, --静的ページ番号
    tenant_no bigint NOT NULL, --テナント番号
    language_code character varying NOT NULL, --言語コード
    page_name character varying NOT NULL, --ページ名
    file_url character varying NOT NULL, --ファイルURL
    create_admin_no bigint NOT NULL, --作成者
    create_datetime timestamp with time zone NOT NULL DEFAULT now(), --作成日時
    update_admin_no bigint NOT NULL, --更新者
    update_datetime timestamp with time zone NOT NULL DEFAULT now(), --更新日
    delete_flag smallint NOT NULL DEFAULT 0, --削除フラグ
    PRIMARY KEY (static_page_localized_no));

COMMENT ON TABLE t_static_page_localized IS '静的ページLocalizedテーブル';
COMMENT ON COLUMN t_static_page_localized.static_page_localized_no IS '静的ページLocalized番号';
COMMENT ON COLUMN t_static_page_localized.static_page_no IS '静的ページ番号';
COMMENT ON COLUMN t_static_page_localized.tenant_no IS 'テナント番号';
COMMENT ON COLUMN t_static_page_localized.language_code IS '言語コード';
COMMENT ON COLUMN t_static_page_localized.page_name IS 'ページ名';
COMMENT ON COLUMN t_static_page_localized.file_url IS 'ファイルURL';
COMMENT ON COLUMN t_static_page_localized.create_admin_no IS '作成者番号';
COMMENT ON COLUMN t_static_page_localized.create_datetime IS '作成日時';
COMMENT ON COLUMN t_static_page_localized.update_admin_no IS '更新者番号';
COMMENT ON COLUMN t_static_page_localized.update_datetime IS '更新日';
COMMENT ON COLUMN t_static_page_localized.delete_flag IS '削除フラグ';

CREATE SEQUENCE t_static_page_localized_static_page_localized_no_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;

ALTER TABLE ONLY t_static_page_localized ALTER COLUMN static_page_localized_no
SET DEFAULT nextval(
    't_static_page_localized_static_page_localized_no_seq'::regclass
);
