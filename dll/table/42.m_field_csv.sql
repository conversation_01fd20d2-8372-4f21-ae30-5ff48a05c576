CREATE TABLE IF NOT EXISTS public.m_field_csv (
    tenant_no bigint NOT NULL,
    csv_template_id bigint NOT NULL,
    language_code character varying NOT NULL,
    input_data_list bigint[],
    not_editable_input_data_list bigint[],
    admin_display_flag integer NOT NULL,
    member_display_flag integer NOT NULL,
    create_datetime timestamp with time zone DEFAULT now(),
    update_datetime timestamp with time zone DEFAULT now(),
    delete_flag integer,
    CONSTRAINT m_field_csv_pkey PRIMARY KEY (tenant_no, csv_template_id, language_code),
    CONSTRAINT m_field_csv_tenant_no_fkey FOREIGN KEY (tenant_no)
    REFERENCES public.m_tenant (tenant_no) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE RESTRICT
)
WITH (
    OIDS = FALSE
)
TABLESPACE pg_default;

ALTER TABLE public.m_field_csv
OWNER TO postgres;

COMMENT ON TABLE public.m_field_csv
IS 'CSV項目マスタ';

COMMENT ON COLUMN public.m_field_csv.tenant_no
IS 'テナント番号';

COMMENT ON COLUMN public.m_field_csv.csv_template_id
IS 'CSVテンプレート番号';

COMMENT ON COLUMN public.m_field_csv.language_code
IS '言語区分';

COMMENT ON COLUMN public.m_field_csv.input_data_list
IS '入力リスト';

COMMENT ON COLUMN public.m_field_csv.not_editable_input_data_list
IS '編集不可入力リスト';

COMMENT ON COLUMN public.m_field_csv.admin_display_flag
IS '管理者表示フラグ';

COMMENT ON COLUMN public.m_field_csv.member_display_flag
IS '会員表示フラグ';

COMMENT ON COLUMN public.m_field_csv.create_datetime
IS '作成日時';

COMMENT ON COLUMN public.m_field_csv.update_datetime
IS '更新日時';

COMMENT ON COLUMN public.m_field_csv.delete_flag
IS '削除フラグ';
