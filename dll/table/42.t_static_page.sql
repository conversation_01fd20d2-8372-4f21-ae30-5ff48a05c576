CREATE TABLE t_static_page (
    static_page_no bigint, --静的ページ番号
    tenant_no bigint NOT NULL, --テナント番号
    page_path character varying NOT NULL, --ページパス
    create_admin_no bigint NOT NULL, --作成者
    create_datetime timestamp with time zone NOT NULL DEFAULT now(), --作成日時
    update_admin_no bigint NOT NULL, --更新者
    update_datetime timestamp with time zone NOT NULL DEFAULT now(), --更新日
    delete_flag smallint NOT NULL DEFAULT 0, --削除フラグ
    PRIMARY KEY (static_page_no),
    UNIQUE (tenant_no, page_path));

COMMENT ON TABLE t_static_page IS '静的ページテーブル';
COMMENT ON COLUMN t_static_page.static_page_no IS '静的ページ番号';
COMMENT ON COLUMN t_static_page.tenant_no IS 'テナント番号';
COMMENT ON COLUMN t_static_page.page_path IS 'ページパス';
COMMENT ON COLUMN t_static_page.create_admin_no IS '作成者番号';
COMMENT ON COLUMN t_static_page.create_datetime IS '作成日時';
COMMENT ON COLUMN t_static_page.update_admin_no IS '更新者番号';
COMMENT ON COLUMN t_static_page.update_datetime IS '更新日';
COMMENT ON COLUMN t_static_page.delete_flag IS '削除フラグ';

CREATE SEQUENCE t_static_page_static_page_no_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;

ALTER TABLE ONLY t_static_page ALTER COLUMN static_page_no
SET DEFAULT nextval(
    't_static_page_static_page_no_seq'::regclass
);
