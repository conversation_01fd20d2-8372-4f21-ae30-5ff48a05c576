{"name": "gmo-saas-auction-system-admin-side", "version": "5.0.0", "description": "GMOのSaas入札システムの管理サイト", "bugs": {"url": "https://github.com/coreui/coreui-free-vue-admin-template/issues"}, "repository": {"type": "git", "url": "**************:coreui/coreui-free-vue-admin-template.git"}, "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people)", "scripts": {"dev": "vite --mode development --port 3000", "demo": "vite --mode demo --port 3000", "format": "prettier --write \"src/**/*.{js,vue}\"", "format:check": "prettier --check \"src/**/*.{js,vue}\"", "build-demo": "vite build --mode demo", "deploy-demo": "npm run build-demo && aws --profile saas-demo s3 sync ./dist s3://gmo-demo-saas-auction-bucket/client/admin --delete --cache-control max-age=86400,public,no-store", "build-demo2": "vite build --mode demo2", "deploy-demo2": "npm run build-demo2 && aws --profile saas-demo s3 sync ./dist s3://gmo-demo2-saas-auction-bucket/client/admin --delete --cache-control max-age=86400,public,no-store", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@aws-sdk/client-s3": "^3.600.0", "@coreui/chartjs": "^4.0.0", "@coreui/coreui": "^5.2.0", "@coreui/icons": "^3.0.1", "@coreui/icons-vue": "2.2.0", "@coreui/utils": "^2.0.2", "@coreui/vue": "^5.4.1", "@coreui/vue-chartjs": "^3.0.0", "@imengyu/vue3-context-menu": "^1.4.1", "@popperjs/core": "^2.11.8", "@tanstack/vue-form": "^1.12.3", "@vue-layout/countdown": "^5.0.1", "@vueuse/core": "^13.1.0", "aws-amplify": "^6.15.0", "axios": "^1.7.2", "chart.js": "^4.4.6", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "marked": "^13.0.0", "moment": "^2.30.1", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "simplebar-vue": "^2.3.5", "swiper": "^8.4.7", "vue": "^3.5.13", "vue-awesome-swiper": "^5.0.1", "vue-cookies": "^1.8.6", "vue-router": "^4.4.5", "vue3-cookies": "^1.0.6", "vue3-editor": "^0.1.1", "vue3-popper": "^1.5.0", "vue3-spinner": "^0.0.17", "vuex": "^4.1.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@testing-library/vue": "^8.1.0", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.20", "eslint-plugin-jsdoc": "^50.4.3", "globals": "^15.11.0", "jsdom": "^26.1.0", "postcss": "^8.4.49", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.81.0", "vite": "^5.4.11", "vite-plugin-compression": "^0.5.1", "vitest": "^3.2.2"}}