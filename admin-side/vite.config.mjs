import vue from '@vitejs/plugin-vue'
import autoprefixer from 'autoprefixer'
import path from 'node:path'
import {visualizer} from 'rollup-plugin-visualizer'
import {defineConfig, loadEnv} from 'vite'

export default defineConfig(({mode}) => {
  // Load .env
  const env = loadEnv(mode, process.cwd(), '')
  process.env = {...process.env, ...env}

  return {
    plugins: [vue(), visualizer()],
    base: './',
    css: {
      postcss: {
        plugins: [autoprefixer({})],
      },
      preprocessorOptions: {
        scss: {
          quietDeps: true,
          silenceDeprecations: ['import', 'legacy-js-api'],
        },
      },
    },
    resolve: {
      alias: [
        // Webpack path resolve to vitejs
        {
          find: /^~(.*)$/,
          replacement: '$1',
        },
        {
          find: '@/',
          replacement: `${path.resolve(__dirname, 'src')}/`,
        },
        {
          find: '@',
          replacement: path.resolve(__dirname, '/src'),
        },
      ],
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue', '.scss'],
    },
    server: {
      port: 3000,
      proxy: {
        // https://vitejs.dev/config/server-options.html
      },
    },
    define: {
      // Vitejs does not support process.env so we have to redefine it
      'process.env': process.env,
    },
  }
})
