import pluginJs from '@eslint/js'
import jsdoc from 'eslint-plugin-jsdoc'
import pluginVue from 'eslint-plugin-vue'
import commonRules from '../.eslintRules.js'

export default [
  {
    files: ['**/*.{js,mjs,cjs,vue}'],
  },
  {
    ignores: ['node_modules/*', '/dist/**'],
  },
  pluginJs.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  jsdoc.configs['flat/recommended'],
  commonRules,
  {
    rules: {
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      // Turn off formatting-related rules to let <PERSON><PERSON><PERSON> handle formatting
      'vue/max-attributes-per-line': 'off',
      'vue/singleline-html-element-content-newline': 'off',
      'vue/multiline-html-element-content-newline': 'off',
      'vue/html-self-closing': 'off',
    },
  },
]
