const https = require('https')
const fs = require('fs')

const data = JSON.stringify({
  username: 'test-user2',
  email: '<EMAIL>',
  password: 'Test123!',
  group: 'system-admin'
})

const endpoints = ['https://do268epc5wmsb.cloudfront.net/api/register']
const logStream = fs.createWriteStream('api-test-results.log', {flags: 'a'})
const log = message => {
  const timestamp = new Date().toISOString()
  const logMessage = `${timestamp} - ${message}\n`
  console.log(message)
  logStream.write(logMessage)
}

endpoints.forEach((url, index) => {
  log(`\n[TEST ${index + 1}] Testing endpoint: ${url}`)

  const urlObj = new URL(url)

  const options = {
    hostname: urlObj.hostname,
    port: 443,
    path: urlObj.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'User-Agent': 'Node.js API Test Script'
    }
  }

  const req = https.request(options, res => {
    log(`Status Code: ${res.statusCode}`)
    log(`Headers: ${JSON.stringify(res.headers)}`)

    let responseData = ''

    res.on('data', chunk => {
      responseData += chunk
    })

    res.on('end', () => {
      log(`Response Body: ${responseData}`)
      log(`Test ${index + 1} completed`)
    })
  })

  req.on('error', error => {
    log(`Error: ${error.message}`)
    log(`Test ${index + 1} failed`)
  })

  req.write(data)
  req.end()
})

log('Tests initiated. Results will be logged to api-test-results.log')
