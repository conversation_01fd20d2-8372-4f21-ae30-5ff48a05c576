import Methods from '@/api/methods';
import {ref} from 'vue';

/**
 * Custom composable to fetch and manage tenant options.
 * This includes function options and bid options for a specific tenant.
 */
export const useTenantOptions = () => {
  const functionOptions = ref({});
  const bidOptions = ref({});

  const getTenantOptions = async () => {
    try {
      const result = await Methods.apiExecute('/get-tenant-options', {});
      const tenantOption =
        result?.data && result.data.length > 0 ? result.data[0] : {};

      // Initialize functionOptions and bidOptions with the fetched data
      functionOptions.value = {};
      if (tenantOption.function_options) {
        Object.keys(tenantOption.function_options).forEach(key => {
          // Convert string values to boolean
          functionOptions.value[key] = tenantOption.function_options[key] === 1;
        });
      }
      // Initialize bid options
      bidOptions.value = {};
      if (tenantOption.bid_options) {
        const checkBoxOptions = [
          'sankaseigen_seri',
          'sankaseigen_fuin',
          'minimum_price_seri',
          'minimum_price_fuin',
          'instant_win_seri',
          'instant_win_fuin',
          'bid_cancel_seri',
          'bid_cancel_fuin',
          'extension_seri',
          'extension_fuin',
        ];
        Object.keys(tenantOption.bid_options).forEach(key => {
          // Convert string values to boolean
          if (checkBoxOptions.includes(key)) {
            bidOptions.value[key] = tenantOption.bid_options[key] === 1;
          }
          // For select options, directly assign the value
          else {
            bidOptions.value[key] = tenantOption.bid_options[key];
          }
        });
      }
    } catch (error) {
      console.error('Error fetching tenant options:', error);
      throw error;
    }
  };

  const saveTenantOptions = async options => {
    try {
      // Prepare parameters for API call
      const params = {
        function_options: options.functionOptions || {},
        bid_options: options.bidOptions || {},
      };
      const response = await Methods.apiExecute(
        '/update-tenant-options',
        params
      );
      console.log('Tenant options saved successfully:', response);
    } catch (error) {
      console.error('Error saving tenant options:', error);
      throw error;
    }
  };

  return {
    functionOptions,
    bidOptions,
    getTenantOptions,
    saveTenantOptions,
  };
};
