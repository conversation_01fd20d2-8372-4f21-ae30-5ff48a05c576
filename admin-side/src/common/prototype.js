String.prototype.toJst = function () {
  const date = new Date(this);
  date.setTime(date.getTime() + 1000 * 60 * 60 * 9); // JSTに変換
  return date.toString();
};

String.prototype.fromJst = function () {
  const date = new Date(this);
  date.setTime(date.getTime() - 1000 * 60 * 60 * 9); // JSTに変換
  return date.toString();
};

Number.prototype.priceString = function () {
  return `${this.toFixed(0).replace(/(\d)(?=(\d{3})+$)/g, '$1,')}円`;
};

String.prototype.priceString = function () {
  return `${parseInt(this, 10)
    .toFixed(0)
    .replace(/(\d)(?=(\d{3})+$)/g, '$1,')}円`;
};

String.prototype.remainTime = function () {
  const remainTime = parseInt((new Date(this) - Date.now()) / 1000, 10);
  if (remainTime > 0) {
    return remainTime;
  }
  return '';
};
