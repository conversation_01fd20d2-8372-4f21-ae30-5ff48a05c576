import axios from 'axios';
import Base from './base';
/**
 * Download file from url
 */
export default function useFileDownload() {
  const download = async (uri, fileName) => {
    await axios
      .get(uri, {
        responseType: 'blob',
      })
      .then(({headers, data}) => {
        const fname = decodeURI(fileName || Base.getFileName(uri));
        const downloadUrl = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.setAttribute('download', fname);
        document.body.appendChild(link);
        link.click();
        link.remove();
      });
  };

  return {
    download,
  };
}
