import('./prototype');

export default {
  // Check if objects are identical
  objectsAreIdentical(obj1, obj2) {
    if (
      (typeof obj1 === 'undefined' || obj1 === null) &&
      (typeof obj2 === 'undefined' || obj2 === null)
    ) {
      return true;
    }
    if (
      (typeof obj1 === 'undefined' || obj1 === null) &&
      typeof obj2 !== 'undefined' &&
      obj2 !== null
    ) {
      return false;
    }
    if (
      (typeof obj2 === 'undefined' || obj2 === null) &&
      typeof obj1 !== 'undefined' &&
      obj1 !== null
    ) {
      return false;
    }
    return (
      obj1.length === obj2.length &&
      JSON.stringify(obj1) === JSON.stringify(obj2)
    );
  },
  getFileName(item) {
    if (item) {
      const actualFileName = item.substring(
        item.lastIndexOf('/') + 1,
        item.length
      );
      return actualFileName;
    }
    return '';
  },
  getFileNameShorten(fileInfo) {
    const maxLength = 12;
    const fname = fileInfo?.name ? String(fileInfo.name) : null;
    if (fname) {
      const fnames = fname.split('.');
      const extension = fnames.pop();
      const tmpFname = fnames.join('.');
      if (tmpFname.length <= maxLength) {
        return ` (${fname})`;
      }
      return tmpFname
        ? ` (${tmpFname.substring(0, maxLength)}...${extension})`
        : '';
    }
    return '';
  },
  hidePassword(password) {
    let ret = '';
    if (password) {
      for (let i = 0; i < password.length; i++) {
        ret += '*';
      }
    }
    return ret;
  },
  scrollToTop() {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'auto',
    });
  },

  /**
   * Formats a numeric value into a localized string representation with optional length restriction.
   * @param {string|number} value - The input value to format as a localized string. Can be a number or a numeric string.
   * @param {number} [maxLength] - Optional. If provided, limits the maximum length of the numeric value.
   * @returns {string} The formatted localized string representation of the number.
   * @example
   * priceLocaleString(1234567)    // "1,234,567"
   * priceLocaleString(1234567, 4) // "1,234"
   * priceLocaleString("abc,123")  // ""
   */
  priceLocaleString(value, maxLength) {
    const arr = String(value).split(',');
    const out = arr.filter(x => !isNaN(x));
    const num = out.join('');
    if (num === '') {
      return num;
    }
    return maxLength
      ? Math.floor(Number(num.substring(0, maxLength))).toLocaleString()
      : Math.floor(Number(num)).toLocaleString();
  },

  /**
   * Converts a localized string representation of a number into a numeric value.
   * @param {string|number} value - The input value to convert to a number. Can be a localized string or a numeric value.
   * @returns {number} The numeric value converted from the localized string.
   * @example
   * localeString2Number("1,234,567") // 1234567
   * localeString2Number("abc,123")   // 123
   * localeString2Number("")          // 0
   */
  localeString2Number(value) {
    const arr = String(value).split(',');
    const out = arr.filter(x => !isNaN(x));
    return Number(out.join(''));
  },
  // Value to string or -
  number2string(val) {
    if (typeof val === 'undefined' || val === null) {
      return '―';
    }
    return isNaN(val) ? String(val) : val.toLocaleString('ja-JP');
  },
  checkInput(input, length = 200, options) {
    const {type = 'string', required = true, isCompany = false} = options;
    if (required && (!input || input.length === 0) && !isCompany) {
      return {
        valid: false,
        errorMsg: '入力が必要です。',
      };
    }
    if (required && (!input || input.length === 0) && isCompany) {
      return {
        valid: false,
        errorMsg:
          '会社名、会社名フリガナ、会社郵便番号、会社住所を登録する場合は、全ての項目を入力してください。',
      };
    }
    if (input.length > length) {
      return {
        valid: false,
        errorMsg:
          type === 'antique'
            ? '古物商許可番号は12桁の数字で入力してください。'
            : `${length}文字以内で入力してください。`,
      };
    }

    if (
      type === 'furigana' &&
      !/^[\u30A0-\u30FF]+$/.test(input.replace(/\s/g, ''))
    ) {
      return {
        valid: false,
        errorMsg: '全角カタカナで入力してください。',
      };
    }
    if (type === 'antique' && !/^(?:\d{0}|(?:\d{12}))$/.test(input)) {
      return {
        valid: false,
        errorMsg: '古物商許可番号は12桁の数字で入力してください。',
      };
    }
    if (type === 'postCode' && !/^\d{3}-?\d{4}$/.test(input)) {
      return {
        valid: false,
        errorMsg: '郵便番号が間違ってます。',
      };
    }
    if (
      type === 'tel' &&
      !/^(\d{3}-\d{4}-\d{4}|\d{2}-\d{4}-\d{4}|\d{4}-\d{3}-\d{4}|\d{10}|\d{11})$/.test(
        input
      )
    ) {
      return {
        valid: false,
        errorMsg: '電話番号が間違ってます。',
      };
    }
    if (
      type === 'password' &&
      !/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9$@$!%*#?&_-]{8,}$/.test(
        input
      )
    ) {
      return {
        valid: false,
        errorMsg: '入力規則が満たされていません。',
      };
    }
    if (
      type === 'email' &&
      !/^[a-zA-Z0-9_.+-]+@([a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.)+[a-zA-Z]{2,}$/.test(
        input
      )
    ) {
      return {
        valid: false,
        errorMsg: 'メールの値が間違ってます。',
      };
    }
    return {
      valid: true,
    };
  },
  convertFullWidthToHalfWidth(input) {
    const replace = input.replace(/[Ａ-Ｚａ-ｚ０-９！-～ー]/g, char => {
      const fullWidth =
        'ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ０１２３４５６７８９！＂＃＄％＆＇（）＊＋，－．／：；＜＝＞？＠［＼］＾＿｀｛｜｝～ー';
      const halfWidth =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~-';
      const index = fullWidth.indexOf(char);
      return index === -1 ? char : halfWidth.charAt(index);
    });
    // Console.log({ replace });
    return replace;
  },
};
