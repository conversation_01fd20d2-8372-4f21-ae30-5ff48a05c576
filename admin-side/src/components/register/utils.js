export const tenantOptions = [
  {value: '1', label: 'テナント1'},
  {value: '2', label: 'テナント2'},
  {value: '3', label: 'テナント3'},
  {value: '4', label: 'テナント4'},
];

export const roleOptions = [
  {value: '10', label: '管理者'},
  {value: '20', label: '運用担当者'},
  {value: '30', label: '一般'},
];

/**
 * @param {*} tenantId : string, 1:テナント1,2:テナント2,3:テナント3,4:テナント4
 * @returns string : 1,2,3,4
 */
export function getTenantName(tenantId) {
  const id = String(tenantId);
  if (!id) throw new Error(`テナントID :${id} が空です`);
  const found = tenantOptions.find(t => t.value === id);
  if (!found) throw new Error(`テナントID :${id} が見つかりません`);
  return found.label;
}

/**
 * @param {*} roleId : string, 10:管理者,20:運用担当者,30:一般
 * @returns 管理者,運用担当者,一般
 */
export function getRoleName(roleId) {
  const id = String(roleId);
  if (!id) throw new Error(`権限ID :${id} が空です`);
  const found = roleOptions.find(r => r.value === id);
  if (!found) throw new Error(`権限ID :${id} が見つかりません`);
  return found.label;
}
