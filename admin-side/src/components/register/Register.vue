<template>
  <div class="bg-body-tertiary min-vh-100 d-flex flex-row align-items-center">
    <CContainer>
      <CRow class="justify-content-center">
        <CCol md="6">
          <!-- Success Message Component -->
          <CCard v-if="registrationSuccess" class="mx-4">
            <CCardBody class="p-4 text-center">
              <div class="mb-4">
                <CIcon
                  icon="cil-check-circle"
                  size="3xl"
                  class="text-success"
                />
                <h3 class="mt-3">アカウント登録が完了しました</h3>
                <p class="text-medium-emphasis">
                  以下の情報でアカウントが作成されました
                </p>
              </div>

              <CCard class="mb-4">
                <CCardBody class="text-start">
                  <div class="mb-3">
                    <strong>テナント:</strong>
                    {{ getTenantName(createdUser.tenantId) }}
                  </div>
                  <div class="mb-3">
                    <strong>権限:</strong> {{ getRoleName(createdUser.role) }}
                  </div>
                  <div class="mb-3">
                    <strong>管理者氏名:</strong> {{ createdUser.username }}
                  </div>
                  <div>
                    <strong>メールアドレス:</strong> {{ createdUser.email }}
                  </div>
                </CCardBody>
              </CCard>

              <div class="d-flex justify-content-between">
                <CButton color="secondary" @click="backToRegister">
                  戻る
                </CButton>
                <CButton color="primary" @click="navigateToLogin">
                  ログイン画面へ
                </CButton>
              </div>
            </CCardBody>
          </CCard>

          <!-- Registration Form -->
          <CCard v-else class="mx-4">
            <CCardBody class="p-4">
              <form @submit="onSubmit">
                <h1 class="text-center">アカウント登録</h1>
                <p class="text-medium-emphasis text-center">
                  テナント管理者アカウントを作成
                </p>

                <!-- Tenant Selection -->
                <form.Field name="tenantId" v-slot="{field, state}">
                  <FormField
                    :field="field"
                    :state="state"
                    type="select"
                    :options="tenantOptions"
                    placeholder="テナントを選択"
                    icon="cil-user"
                    wrapper-class="mb-3"
                  />
                </form.Field>

                <!-- 権限ID(10:管理者,20:運用担当者,30:一般) -->
                <form.Field name="role" v-slot="{field, state}">
                  <FormField
                    :field="field"
                    :state="state"
                    type="select"
                    :options="roleOptions"
                    placeholder="権限を選択"
                    icon="cil-shield-alt"
                    wrapper-class="mb-3"
                  />
                </form.Field>

                <!-- 管理者氏名 -->
                <form.Field name="username" v-slot="{field, state}">
                  <FormField
                    :field="field"
                    :state="state"
                    type="text"
                    placeholder="管理者氏名"
                    icon="cil-user"
                    wrapper-class="mb-3"
                  />
                </form.Field>

                <!-- メールアドレス -->
                <form.Field name="email" v-slot="{field, state}">
                  <FormField
                    :field="field"
                    :state="state"
                    type="email"
                    placeholder="メールアドレス"
                    icon="cil-user"
                    wrapper-class="mb-3"
                  />
                </form.Field>

                <!-- パスワード -->
                <form.Field name="password" v-slot="{field, state}">
                  <FormField
                    :field="field"
                    :state="state"
                    type="password"
                    placeholder="パスワード"
                    icon="cil-lock-locked"
                    wrapper-class="mb-3"
                  />
                </form.Field>

                <!-- パスワード（確認用） -->
                <form.Field name="passwordConfirmation" v-slot="{field, state}">
                  <FormField
                    :field="field"
                    :state="state"
                    type="password"
                    placeholder="パスワード（確認用）"
                    icon="cil-lock-locked"
                    wrapper-class="mb-4"
                  />
                </form.Field>

                <!-- エラーメッセージ -->
                <div v-if="registerMsg.length > 0" class="mb-3">
                  <div
                    v-for="(msg, index) in registerMsg"
                    :key="index"
                    class="text-danger"
                  >
                    {{ msg }}
                  </div>
                </div>

                <!-- 登録ボタン -->
                <div class="d-grid">
                  <CButton
                    id="registerBtn"
                    color="success"
                    type="submit"
                    :disabled="loading"
                  >
                    <CSpinner
                      v-if="loading"
                      component="span"
                      size="sm"
                      aria-hidden="true"
                    />
                    <span v-else>アカウント作成</span>
                  </CButton>
                </div>

                <!-- ログインリンク -->
                <div class="text-center mt-3">
                  <CLink
                    @click="navigateToLogin"
                    href="javascript:void(0)"
                    class="text-primary"
                  >
                    既存アカウントでログイン
                  </CLink>
                </div>
              </form>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
    </CContainer>
  </div>
</template>

<script setup>
  defineOptions({name: 'RegisterPage'});
  import FormField from '@/components/common/FormField.vue';
  import {useForm} from '@tanstack/vue-form';
  import axios from 'axios';
  import {ref} from 'vue';
  import {useRouter} from 'vue-router';
  import {
    getRoleName,
    getTenantName,
    roleOptions,
    tenantOptions,
  } from './utils';

  const router = useRouter();

  const registerMsg = ref([]);
  const loading = ref(false);
  const registrationSuccess = ref(false);
  const createdUser = ref({
    tenantId: '',
    role: '',
    username: '',
    email: '',
  });

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Password validation regex - requires uppercase, lowercase, number, and special character
  const passwordRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;

  // Validation functions
  const validateTenantId = value => {
    if (!value) return 'テナントを選択してください';
    return undefined;
  };

  const validateRole = value => {
    if (!value) return '権限を選択してください';
    return undefined;
  };

  const validateUsername = value => {
    if (!value) return '管理者氏名を入力してください';
    if (value.length < 2) return '管理者氏名は2文字以上で入力してください';
    if (value.length > 50) return '管理者氏名は50文字以下で入力してください';
    return undefined;
  };

  const validateEmail = value => {
    if (!value) return 'メールアドレスを入力してください';
    if (!emailRegex.test(value))
      return '有効なメールアドレスを入力してください';
    return undefined;
  };

  const validatePassword = value => {
    if (!value) return 'パスワードを入力してください';
    if (value.length < 8) return 'パスワードは8文字以上で入力してください';
    if (value.length > 50) return 'パスワードは50文字以下で入力してください';
    if (!passwordRegex.test(value)) {
      return 'パスワードは大文字、小文字、数字、特殊文字を含む必要があります';
    }
    return undefined;
  };

  const validatePasswordConfirmation = (value, fieldApi) => {
    if (!value) return 'パスワード確認を入力してください';
    const password = fieldApi.form.getFieldValue('password');
    if (value !== password) return 'パスワードが一致しません';
    return undefined;
  };

  // TanStack Form setup
  const form = useForm({
    defaultValues: {
      tenantId: '',
      role: '',
      username: '',
      email: '',
      password: '',
      passwordConfirmation: '',
    },
    onSubmit: async ({value}) => {
      await register(value);
    },
  });

  form.useField({
    name: 'tenantId',
    validators: {onChange: ({value}) => validateTenantId(value)},
  });

  form.useField({
    name: 'role',
    validators: {onChange: ({value}) => validateRole(value)},
  });

  form.useField({
    name: 'username',
    validators: {onChange: ({value}) => validateUsername(value)},
  });

  form.useField({
    name: 'email',
    validators: {onChange: ({value}) => validateEmail(value)},
  });

  form.useField({
    name: 'password',
    validators: {onChange: ({value}) => validatePassword(value)},
  });

  form.useField({
    name: 'passwordConfirmation',
    validators: {
      onChange: ({value, fieldApi}) =>
        validatePasswordConfirmation(value, fieldApi),
    },
  });

  const register = async formData => {
    registerMsg.value = [];
    loading.value = true;

    try {
      const apiEndpoint = `${import.meta.env.VITE_API_ENDPOINT}register`;
      const response = await axios.post(apiEndpoint, {
        tenantId: formData.tenantId,
        role: formData.role,
        username: formData.username,
        email: formData.email,
        password: formData.password,
        languageCode: 'ja', // Default language
      });
      console.log('response: ', response);
      if (response.status === 200) {
        createdUser.value = {
          tenantId: response.data.tenantId,
          role: response.data.role,
          username: response.data.adminName,
          email: response.data.email,
        };
        registrationSuccess.value = true;
      }
      loading.value = false;
    } catch (error) {
      console.error('Full error:', error);
      loading.value = false;
      if (error.response) {
        if (error.response.data.error) {
          switch (error.response.data.error) {
            case 'UsernameExistsException':
              registerMsg.value = ['このユーザーネームは既に使用されています'];
              break;
            case 'EmailExistsException':
              registerMsg.value = ['このメールアドレスは既に使用されています'];
              break;
            case 'InvalidPasswordException':
              registerMsg.value = [
                'パスワードは大文字、小文字、数字、特殊文字を含む必要があります',
              ];
              break;
            case 'InvalidParameterException':
              registerMsg.value = ['入力パラメータが無効です'];
              break;
            default:
              registerMsg.value = [
                error.response.data.message || error.response.data.error,
              ];
          }
        } else {
          registerMsg.value = ['登録中にエラーが発生しました'];
        }
      } else {
        registerMsg.value = [
          'ネットワークエラーが発生しました。もう一度お試しください。',
        ];
      }
    }
  };

  const onSubmit = e => {
    e.preventDefault();
    form.handleSubmit();
  };

  const backToRegister = () => {
    registrationSuccess.value = false;
    form.reset();
  };

  const navigateToLogin = () => {
    router.push('/pages/login');
  };
</script>
