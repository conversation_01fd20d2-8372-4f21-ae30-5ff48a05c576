<script setup>
  import {ref} from 'vue';
  const open = ref(false);
  const selectedMembers = ref([
    {name: '会員１'},
    {name: '会員２'},
    {name: '会員３'},
    {name: '会員４'},
    {name: '会員５'},
    {name: '会員６'},
    {name: '会員７'},
    {name: '会員８'},
    {name: '会員９'},
    {name: '会員１０'},
  ]);
</script>
<template>
  <CButton
    color="primary"
    @click="
      () => {
        open = true;
      }
    "
  >
    会員選択
  </CButton>
  <CModal
    alignment="center"
    scrollable
    :visible="open"
    @close="
      () => {
        open = false;
      }
    "
    size="lg"
  >
    <CModalHeader>
      <CModalTitle id="VerticallyCenteredExample2">会員選択</CModalTitle>
    </CModalHeader>
    <CModalBody>
      <div class="d-flex">
        <CCard style="width: 30rem">
          <CCardBody>
            <CVirtualScroller :visibleItems="5">
              <div v-for="mem in selectedMembers" class="d-flex gap-2">
                <CIcon icon="cil-user" />
                <span>{{ mem.name }}</span>
              </div>
            </CVirtualScroller>
          </CCardBody>
        </CCard>
        <div
          style="width: 8rem"
          class="mx-1 d-flex flex-column justify-content-center align-items-center gap-2"
        >
          <div class="w-100 d-grid">
            <CButton color="success" @click="() => {}">
              <CIcon icon="cil-caret-left" />追加
            </CButton>
          </div>
          <div class="w-100 d-grid">
            <CButton color="danger" @click="() => {}"
              >削除
              <CIcon icon="cil-caret-right" />
            </CButton>
          </div>
        </div>
        <div style="width: 30rem" class="d-flex flex-column gap-2">
          <div class="d-flex gap-1">
            <CFormInput placeholder="会員名..." />
            <CButton
              type="submit"
              color="primary"
              class="col-auto"
              @click="() => {}"
              >検索</CButton
            >
          </div>
          <CCard>
            <CCardBody>
              <CVirtualScroller :visibleItems="5">
                <div v-for="mem in selectedMembers" class="d-flex gap-2">
                  <CIcon icon="cil-user" />
                  <span>{{ mem.name }}</span>
                </div>
              </CVirtualScroller>
            </CCardBody>
          </CCard>
        </div>
      </div>
    </CModalBody>
    <CModalFooter>
      <CButton
        color="secondary"
        @click="
          () => {
            open = false;
          }
        "
      >
        閉じる
      </CButton>
      <CButton
        color="primary"
        @click="
          () => {
            open = false;
          }
        "
        >確認</CButton
      >
    </CModalFooter>
  </CModal>
</template>
