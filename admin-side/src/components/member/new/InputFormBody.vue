<script setup>
  import Methods from '@/api/methods'
  import {defineProps, onMounted, ref} from 'vue'
  import FileSelection from './fileSelection.vue'

  const props = defineProps({
    memberEditData: {
      type: Object,
      default: () => ({}),
    },
    countryOptions: {
      type: Array,
      default: () => [],
    },
    emailLangOptions: {
      type: Array,
      default: () => [],
    },
    handleCountryChange: {
      type: Function,
      default: () => {},
    },
    checkDateTimeValid: {
      type: Function,
      default: () => {},
    },
    dateTimeValid: {
      type: Object,
      default: () => ({}),
    },
  })

  const selectedEmailLangRef = ref('ja')
  let fieldList = ref([])
  let constants = ref({})
  onMounted(async () => {
    selectedEmailLangRef.value = 'ja'
    props.memberEditData.freeField.emailLang = 'ja'
    const response = await Methods.apiExecute('get-field-list', {
      language_code: 'ja',
      resource_type: 'member',
    })
    fieldList.value = response.data.data
    const constantsRes = await Methods.apiExecute('get-constants-by-keys', {
      key_strings: fieldList.value
        .filter(row => row.input_data_list && row.input_data_list.key_string)
        .map(row => row.input_data_list.key_string),
    })
    constantsRes.data.forEach(element => {
      if (!constants.value[element.key_string])
        constants.value[element.key_string] = [
          {
            value: null,
            label: '',
          },
        ]
      constants.value[element.key_string].push({
        value: element.value1,
        label: element.value2,
      })
    })
  })
</script>

<template>
  <CForm onsubmit="return false;">
    <CRow class="mb-3" v-for="field in fieldList">
      <CCol sm="3" class="d-flex align-items-start" style="line-height: 40px">
        <label>{{ field.logical_name }}</label>
        <CBadge
          v-if="field.required_flag"
          color="danger"
          class="ms-auto"
          style="margin: 10px 0"
          >必須</CBadge
        >
      </CCol>
      <CCol sm="4">
        <CFormInput
          v-if="field.input_type == 'text'"
          :name="field.physical_name"
          :maxlength="field.max_length"
          v-model="memberEditData.freeField[field.physical_name]"
        />
        <CFormSelect
          v-if="field.input_type == 'pulldown'"
          :name="field.physical_name"
          :options="constants[field.input_data_list.key_string]"
          v-model="memberEditData.freeField[field.physical_name]"
        />
        <CFormCheck
          v-if="field.input_type == 'checkbox'"
          v-model="memberEditData.freeField[field.physical_name]"
          :name="field.physical_name"
        />
        <FileSelection
          v-if="field.input_type == 'file'"
          @onFilesChange="
            memberEditData.freeField[field.physical_name] = $event.map(
              row => row.fileName
            )
          "
        />
      </CCol>
    </CRow>
    <!-- 共通情報 -->
    <CRow class="mb-3">
      <CCol sm="3">
        <label>入札</label>
      </CCol>
      <CCol sm="2" class="form-group col-sm-3">
        <CFormCheck
          name="bidAllowFlag"
          type="radio"
          inline
          v-for="option in [
            {value: '0', label: '否'},
            {value: '1', label: '可'},
          ]"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :custom="true"
          v-model="memberEditData.bidAllowFlag"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>入札上限額</label>
      </CCol>
      <CCol sm="2">
        <CFormInput name="bidLimit" v-model="memberEditData.bidLimitAmount" />
      </CCol>
      <div class="col-auto">
        <div class="flex-row align-center">{{ currencyId }}</div>
      </div>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>メール配信</label>
      </CCol>
      <CCol sm="2">
        <CFormCheck
          type="radio"
          inline
          v-for="option in [
            {value: '0', label: '配信しない'},
            {value: '1', label: '配信する'},
          ]"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :custom="true"
          name="emailDeliveryFlag"
          v-model="memberEditData.emailDeliveryFlag"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">備考</CCol>
      <CCol :sm="8">
        <CFormTextarea
          name="memo"
          v-model="memberEditData.freeField.memo"
          rows="3"
        />
      </CCol>
    </CRow>
  </CForm>
</template>
