<script setup>
import {computed, defineProps} from 'vue';

  const props = defineProps({
    memberEditData: {
      type: Object,
      default: {},
    },
    confirmMode: {
      type: Boolean,
      default: false,
    }
  });
  const confirmMode = computed(() => props.confirmMode)

</script>

<template>
  <CCardBody v-if="confirmMode">
    <CRow class="mb-3">
    <CCol sm="2">
      <label>入札</label>
    </CCol>
    <CCol sm="2">
      <label class="word-break">{{
        memberEditData.bidAllowFlag === '1' ? '可' : '否'
      }}</label>
    </CCol>
  </CRow>
  <CRow class="mb-3">
    <CCol sm="2">
      <label>入札上限額</label>
    </CCol>
    <CCol sm="4">
      <label class="word-break"
        >{{ memberEditData.bidLimitAmount }} 円</label
      >
    </CCol>
  </CRow>
  <CRow class="mb-3">
    <CCol sm="2">
      <label>メール配信</label>
    </CCol>
    <CCol sm="2">
      <label class="word-break">{{
        memberEditData.emailDeliveryFlag === '1' ? '配信する' : '配信しない'
      }}</label>
    </CCol>
  </CRow>
  <CRow class="mb-3">
    <CCol sm="2"> 備考 </CCol>
    <CCol sm="8">
      <p
        style="white-space: pre-line"
        v-html="memberEditData.freeField.memo"
      ></p>
    </CCol>
  </CRow>
  </CCardBody>
  <CCardBody v-else>
    <!-- 共通情報 -->
    <CRow class="mb-3">
      <CCol sm="3">
        <label>入札</label>
      </CCol>
      <CCol sm="2" class="form-group col-sm-3">
        <CFormCheck
          name="bidAllowFlag"
          type="radio"
          inline
          v-for="option in [
            {value: '0', label: '否'},
            {value: '1', label: '可'},
          ]"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :custom="true"
          v-model="memberEditData.bidAllowFlag"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>入札上限額</label>
      </CCol>
      <CCol sm="2">
        <CFormInput name="bidLimit" v-model="memberEditData.bidLimitAmount" />
      </CCol>
      <div class="col-auto">
        <div class="flex-row align-center">{{ currencyId }}</div>
      </div>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>メール配信</label>
      </CCol>
      <CCol sm="2">
        <CFormCheck
          type="radio"
          inline
          v-for="option in [
            {value: '0', label: '配信しない'},
            {value: '1', label: '配信する'},
          ]"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :custom="true"
          name="emailDeliveryFlag"
          v-model="memberEditData.emailDeliveryFlag"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">備考</CCol>
      <CCol :sm="8">
        <CFormTextarea
          name="memo"
          v-model="memberEditData.freeField.memo"
          rows="3"
        />
      </CCol>
    </CRow>
  </CCardBody>
</template>
