<template>
  <div name="file-list">
    <div v-if="fileInfos">
      <div v-for="(fileInfo, index) in fileInfos" :key="index">
        <div class="row mb-2" v-if="true">
          <div class="col-sm-6">
            <a
              @contextmenu.prevent="onContextMenuOpen($event, fileInfo.s3url)"
              @click="fileNameClickEvent(fileInfo.s3url)"
              href="javascript:void(0);"
              :style="
                fileInfo.s3url
                  ? ''
                  : 'color: inherit; text-decoration: inherit;'
              "
            >
              {{ fileInfo.fileName }}
            </a>
          </div>
          <div class="col-sm-4">
            <CButton
              name="btn-delete"
              :disabled="isBtnDisabled"
              :class="isBtnDisabled ? 'pt-not-allow' : ''"
              @click="openFileDeleteModal(fileInfo)"
              color="danger"
              style="padding-left: 5px; padding-right: 5px"
              block
              >削除する</CButton
            >
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-2">
      <div class="col-sm-6">
        <label
          name="btn-select"
          class="btn btn-block"
          :class="isBtnDisabled ? 'btn-disabled' : 'btn-secondary'"
        >
          参照...
          <input
            :disabled="isBtnDisabled"
            type="file"
            ref="fileupload"
            multiple
            data-max="10"
            @change="selectFile"
            hidden
          />
        </label>
      </div>
      <div class="col-sm-4"></div>
    </div>

    <div class="row mb-2">
      <div class="col-sm-11">
        <div v-if="message" class="alert alert-light" role="alert">
          <ul>
            <li v-for="(ms, i) in message.split('\n')" :key="i">
              {{ ms }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="fileDeleteModal"
      @close="
        () => {
          fileDeleteModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>添付ファイルを削除してもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="fileDeleteModal = false" color="light"
          >キャンセル</CButton
        >
        <CButton @click="deleteFile" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="fileUploadModal"
      @close="
        () => {
          fileUploadModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle>アップロード確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>添付ファイルをアップロードしてもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="fileUploadModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入力エラー</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>{{ errorMsg }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="errorModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <ContextMenu v-model:show="contextMenu.show" :options="contextMenu.options">
      <context-menu-item label="ファイルを開く" @click="getFileViewUrl" />
      <context-menu-item label="ダウンロード" @click="getFileDownloadUrl" />
    </ContextMenu>
  </div>
</template>

<script>
  import UploadFile from '@/api/uploadFileToS3'
  import useFileDownload from '@/common/useFileDownload'
  import {CButton, CModal} from '@coreui/vue'
  import {ContextMenu, ContextMenuItem} from '@imengyu/vue3-context-menu'

  export default {
    name: 'fileSelection',
    components: {
      ContextMenu,
      ContextMenuItem,
      CButton,
      CModal,
    },
    setup() {
      const {download} = useFileDownload()
      return {download}
    },
    props: {
      items: Array,
      isBtnDisabled: {
        type: Boolean,
        default: false,
      },
      btnRegist: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        // Select file
        selectedFiles: undefined,
        message: '',
        // Delete file
        fileDeleteModal: false,
        deleteFileTemp: null,
        // Upload file
        fileUploadModal: false,

        // Data
        fileInfos: [],
        deleteFiles: [],

        // エラーmodal
        errorModal: false,
        errorMsg: '',

        // Context menu
        contextMenu: {
          show: false,
          options: {
            zIndex: 9999,
            minWidth: 230,
            x: 500,
            y: 500,
          },
          data: null,
        },
      }
    },
    watch: {
      items(newVal, oldVal) {
        console.log('items changed: ', newVal, ' | was: ', oldVal)

        if (newVal && oldVal && this.objectsAreIdentical(newVal, oldVal)) {
          return
        }

        if (typeof this.items !== 'undefined' && this.items !== null) {
          const tmpItems = this.items
          if (typeof tmpItems !== 'undefined' && tmpItems !== null) {
            this.fileInfos = []
            for (let i = 0; i < tmpItems.length; i++) {
              const fileUrl = tmpItems[i]
              let actualFileName = ''
              if (typeof fileUrl !== 'undefined' && fileUrl !== null) {
                actualFileName = fileUrl.substring(
                  fileUrl.lastIndexOf('/') + 1,
                  fileUrl.length
                )
              }
              this.fileInfos.push({
                id: `local${this.fileInfos.length}`,
                fileName: actualFileName,
                file: null,
                s3url: tmpItems[i],
              })
            }
          }
        }
      },
    },
    mounted() {
      this.getFiles(response => {
        console.log(`response. ${JSON.stringify(response)}`)
      })
    },
    methods: {
      getFiles(callback) {
        callback(this.items)
      },
      selectFile() {
        if (typeof this.fileInfos === 'undefined' || this.fileInfos === null) {
          this.fileInfos = []
        }

        // 5個まで登録できる
        if (this.fileInfos.length >= 5) {
          this.errorModal = true
          this.errorMsg = '添付ファイルは5個までしか登録できません。'
          this.$refs.fileupload.value = null
          return
        }

        this.selectedFiles = event.target.files
        for (let i = 0; i < this.selectedFiles.length; i++) {
          if (this.checkFileSize(this.selectedFiles[i])) {
            this.errorModal = true
            this.errorMsg = '5MB以内のファイルをアップロードしてください。'
            this.$refs.fileupload.value = null
          } else {
            this.fileInfos.push({
              id: `local${this.fileInfos.length}`,
              fileName: this.selectedFiles[i].name,
              file: this.selectedFiles[i],
              s3url: null,
            })
          }
        }
        this.$refs.fileupload.value = null
        this.onChange()
        this.uploadFiles(null, () => {})
      },
      uploadFiles(event, callback) {
        if (event) {
          event.preventDefault()
        }

        this.message = ''
        this.upload(0, this.fileInfos, null, s3response => {
          console.log(`s3response = ${JSON.stringify(s3response)}`)
          if (callback) {
            return callback(this.fileInfos)
          }
        })
      },
      upload(idx, fileList, s3response, callback) {
        console.log(`idx = ${idx}`)
        if (
          typeof fileList === 'undefined' ||
          fileList === null ||
          idx >= fileList.length
        ) {
          return callback(s3response)
        }

        if (s3response === null) {
          s3response = []
        }

        const fileInfo = fileList[idx]
        // Upload s3 unlinked items only
        if (fileInfo.s3url !== null && fileInfo.s3url !== '') {
          return this.upload(idx + 1, fileList, s3response, callback)
        }

        const api_type = 'notice-email'
        UploadFile.upload(api_type, fileInfo.file, data => {
          console.log(`data. ${JSON.stringify(data)}`)

          if (data.status === 200) {
            if (
              typeof this.fileInfos !== 'undefined' &&
              this.fileInfos !== null
            ) {
              const fItem = this.fileInfos.find(item => item === fileInfo)
              if (typeof fItem !== 'undefined' && fItem !== null) {
                fItem.s3url = data.message
              }
            }
          } else {
            const prevMessage = this.message ? `${this.message}\n` : ''
            this.message = prevMessage + data.message
          }

          s3response.push(data.message)
          this.upload(idx + 1, fileList, s3response, callback)
        })
      },
      deleteFile() {
        const file = this.deleteFileTemp
        console.log(`file. ${JSON.stringify(file)}`)
        if (file.s3url === null) {
          this.fileInfos = this.fileInfos.filter(item => {
            return item !== file
          })
        } else {
          const fItem = this.fileInfos.find(item => item === file)
          if (typeof fItem === 'undefined' || fItem === null) {
            if (
              typeof this.deleteFiles === 'undefined' ||
              this.deleteFiles === null
            ) {
              this.deleteFiles = []
            }
            this.deleteFiles.push(file)
          } else {
            this.fileInfos = this.fileInfos.filter(item => item !== file)
          }
        }
        this.fileDeleteModal = false
        this.deleteFileTemp = null
        this.onChange()
      },
      onChange() {
        console.log(`fileInfos: ${JSON.stringify(this.fileInfos)}`)
        this.$emit('onFilesChange', this.fileInfos)
      },
      onUploaded() {
        console.log('onUploaded: emitted')
        this.$emit('onUploaded', this.fileInfos)
      },
      openFileDeleteModal(file) {
        console.log('openFileDeleteModal')
        this.fileDeleteModal = true
        this.deleteFileTemp = file
      },
      objectsAreIdentical(obj1, obj2) {
        return (
          obj1.length === obj2.length &&
          JSON.stringify(obj1) === JSON.stringify(obj2)
        )
      },
      getFileDownloadUrl() {
        console.log('getFileDownloadUrl')
        // Get download url from file server
        const fileUrl = this.contextMenu.data
        if (fileUrl !== null && fileUrl !== '') {
          UploadFile.getDownloadUrl(fileUrl).then(res => {
            console.log(`res: ${JSON.stringify(res)}`)
            this.download(res, Base.getFileName(fileUrl))
          })
        }
      },
      getFileViewUrl() {
        console.log('getFileViewUrl')
        // Get file viewing url from file server
        const fileUrl = this.contextMenu.data
        if (fileUrl !== null && fileUrl !== '') {
          UploadFile.getFileViewUrl(fileUrl).then(res => {
            console.log(`res: ${JSON.stringify(res)}`)
            window.open(res, '_blank')
          })
        }
      },
      fileNameClickEvent(fileUrl) {
        console.log('fileNameClickEvent')
        // Filename click event handler
        if (fileUrl !== null && fileUrl !== '') {
          UploadFile.getFile(fileUrl).then(res => {
            console.log(`res: ${JSON.stringify(res)}`)
            window.open(res, '_blank')
          })
        }
      },
      checkFileSize(file) {
        if (file?.size) {
          if (file.size > 5 * 1024 * 1024) {
            return true
          }
        }
        return null
      },
      onContextMenuOpen(e, item) {
        // Show context menu
        this.contextMenu.show = true
        this.contextMenu.options.x = e.x
        this.contextMenu.options.y = e.y
        this.contextMenu.data = item
      },
    },
  }
</script>
<style type="text/css">
  .btn-disabled {
    opacity: 0.65;
    cursor: not-allowed !important;
    color: #fff;
    background-color: #636f83;
    border-color: #636f83;
  }
  .btn-disabled:hover {
    color: #fff;
    background-color: #636f83;
    border-color: #636f83;
    text-decoration: none;
  }
  .btn-disabled:focus {
    color: #fff !important;
    background-color: #636f83 !important;
    border-color: #636f83 !important;
    text-decoration: none;
  }
</style>
