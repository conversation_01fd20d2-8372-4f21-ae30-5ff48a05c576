<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>ShareThis</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              スクリプト入力
            </CCol>
            <CCol sm="12">
              <CFormTextarea name="url" rows="4" v-model="shareThis.script" />
            </CCol>
          </CRow>
        </CForm>
        <CRow>
          <CCol sm="5" />
          <CCol sm="2" class="d-grid">
            <CButton color="primary" @click="update">更新</CButton>
          </CCol>
          <CCol sm="5" />
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script setup>
  import {ref} from 'vue';

  const shareThis = ref({
    script: '',
  });

  const update = () => {
    console.log('update');
  };
</script>
