<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>SFTP</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> SFTPのURL </CCol>
            <CCol sm="10">
              <CFormInput name="url" v-model="gmoShop.url" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> ユーザーID </CCol>
            <CCol sm="10">
              <CFormInput name="id" v-model="gmoShop.id" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> パスワード </CCol>
            <CCol sm="10">
              <CFormInput name="secret_access_key" v-model="gmoShop.password" />
            </CCol>
          </CRow>
        </CForm>
        <CRow>
          <CCol sm="5" />
          <CCol sm="2" class="d-grid">
            <CButton color="primary" @click="update">更新</CButton>
          </CCol>
          <CCol sm="5" />
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script setup>
  import {ref} from 'vue';

  const gmoShop = ref({
    url: '',
    id: '',
    password: '',
  });

  const update = () => {
    console.log('update');
  };
</script>
