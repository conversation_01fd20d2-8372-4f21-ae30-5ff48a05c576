<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>GMO クラウドEC</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left"> APIのURL </CCol>
            <CCol sm="12">
              <CFormInput name="url" v-model="gmoShop.url" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              アクセスキー
            </CCol>
            <CCol sm="12">
              <CFormInput name="access_key" v-model="gmoShop.access_key" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              シークレットアクセスキー
            </CCol>
            <CCol sm="12">
              <CFormInput
                name="secret_access_key"
                v-model="gmoShop.secret_access_key"
              />
            </CCol>
          </CRow>
        </CForm>
        <CRow class="mt-3">
          <CCol sm="5" />
          <CCol sm="2" class="d-grid">
            <CButton color="primary" @click="update">更新</CButton>
          </CCol>
          <CCol sm="5" />
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script setup>
  import {ref} from 'vue';

  const gmoShop = ref({
    url: '',
    access_key: '',
    secret_access_key: '',
  });

  const update = () => {
    console.log('update');
  };
</script>
