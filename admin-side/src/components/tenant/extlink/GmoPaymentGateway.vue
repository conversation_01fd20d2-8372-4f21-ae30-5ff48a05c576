<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>GMO Payment Gateway</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              ショップID
            </CCol>
            <CCol sm="12">
              <CFormInput name="shop_id" v-model="gmoShop.shop_id" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              ショップパスワード
            </CCol>
            <CCol sm="12">
              <CFormInput
                name="shop_password"
                v-model="gmoShop.shop_password"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left"> サイトID </CCol>
            <CCol sm="12">
              <CFormInput name="site_id" v-model="gmoShop.site_id" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              サイトパスワード
            </CCol>
            <CCol sm="12">
              <CFormInput
                name="site_password"
                v-model="gmoShop.site_password"
              />
            </CCol>
          </CRow>
        </CForm>
        <CRow class="mt-3">
          <CCol sm="5" />
          <CCol sm="2" class="d-grid">
            <CButton color="primary" @click="update">更新</CButton>
          </CCol>
          <CCol sm="5" />
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script setup>
  import {ref} from 'vue';

  const gmoShop = ref({
    shop_id: '',
    shop_password: '',
    site_id: '',
    site_password: '',
  });

  const update = () => {
    console.log('update');
  };
</script>
