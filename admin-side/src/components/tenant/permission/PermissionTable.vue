<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" />{{ caption }} </slot>
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :loading="loading"
        :items="items"
        :fields="fields"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #object="{item}">
          <td v-if="item.rowspan" style="width: 100px" :rowspan="item.rowspan">
            <div
              class="full-height d-flex justify-content-center align-items-center"
            >
              {{
                item.target_group_id === 1
                  ? '会員'
                  : item.target_group_id === 2
                    ? '商品'
                    : ''
              }}
            </div>
          </td>
        </template>
        <template #permission="{item}">
          <td class="text-center" style="width: 100px">
            {{ item.function_name }}
          </td>
        </template>
        <template #admin="{item}">
          <td class="text-center" style="width: 100px">
            <CFormCheck
              :checked="item.allowed_role_id.includes('10')"
              @change="e => onRoleChange(item, '10', e.target.checked)"
            />
          </td>
        </template>
        <template #operator="{item}">
          <td class="text-center" style="width: 100px">
            <CFormCheck
              :checked="item.allowed_role_id.includes('20')"
              @change="e => onRoleChange(item, '20', e.target.checked)"
            />
          </td>
        </template>
        <template #general_admin="{item}">
          <td class="text-center" style="width: 100px">
            <CFormCheck
              :checked="item.allowed_role_id.includes('30')"
              @change="e => onRoleChange(item, '30', e.target.checked)"
            />
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CRow>
        <CCol sm="5" />
        <CCol sm="2" class="d-grid">
          <CButton color="primary" @click="update"> 更新 </CButton>
        </CCol>
        <CCol sm="5" />
      </CRow>
    </CCardBody>
  </CCard>
</template>

<script setup>
  import {CDataTable} from '@/components/Table'
  import {defineEmits, defineProps} from 'vue'

  const emit = defineEmits([
    'page-change',
    'sorter-change',
    'pagination-change',
    'update-permission',
  ])
  const props = defineProps({
    items: Array,
    current_count: {
      type: String,
      default: '0',
    },
    total_count: {
      type: String,
      default: '0',
    },
    fields: {
      type: Array,
      default() {
        return [
          {
            key: 'object',
            label: '対象',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'permission',
            label: '権限',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'admin',
            label: '管理者',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'operator',
            label: '運用担当者',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'general_admin',
            label: '一般',
            _classes: 'text-center',
            sorter: false,
          },
        ]
      },
    },
    caption: {
      type: String,
      default: '',
    },
    loading: Boolean,
    activePage: Number,
    itemsPerPage: Number,
    pages: Number,
    itemsSorter: Object,
  })

  const pageChange = val => {
    if (props.items.length > 0) {
      emit('page-change', val)
    }
  }

  const sorterChange = val => {
    emit('sorter-change', val)
  }

  const paginationChange = val => {
    emit('pagination-change', val)
  }

  const update = () => {
    console.log('update')
    emit('update-permission')
  }

  const onRoleChange = (item, roleId, checked) => {
    const idx = item.allowed_role_id.indexOf(roleId)
    if (checked) {
      if (idx === -1) item.allowed_role_id.push(roleId)
    } else {
      if (idx !== -1) item.allowed_role_id.splice(idx, 1)
    }
  }
</script>
