<script setup>
  import {useTenantOptions} from '@/composables/useTenantOptions'
  import {
    CButton,
    CFormSwitch,
    CListGroup,
    CListGroupItem,
    CModal,
    CModalBody,
    CModalFooter,
    CModalHeader,
  } from '@coreui/vue'
  import {onMounted, ref, watch} from 'vue'

  const emit = defineEmits(['refresh'])
  const props = defineProps({
    options: {
      type: Object,
      default: () => ({}),
    },
    triggerSaveBtn: {
      type: Number,
      default: 0,
    },
  })

  const {saveTenantOptions} = useTenantOptions()

  const inputOptions = ref({
    satei: false,
    shuppin: false,
    shuppinDaiKou: false,
    haisouDaiKou: false,
    topNicknameDisplay: false,
    csvDownload: false,
    memberLinkage: false,
    stockLinkage: false,
    productLinkage: false,
    htmlMail: false,
    creditCardPayment: false,
  })
  const showConfirm = ref(false)
  const isLoading = ref(false)
  const saveError = ref('')
  const toasts = ref([])

  const createToast = ({color, textColor, title, message}) => {
    toasts.value.push({
      color: color || 'info',
      textColor: textColor || 'white',
      title: title || '',
      content: message || '',
    })
  }

  const doSaveOptions = async () => {
    isLoading.value = true
    saveError.value = ''
    try {
      // Functions options
      const functionOptions = {}
      Object.keys(inputOptions.value).forEach(key => {
        functionOptions[key] = inputOptions.value[key] ? 1 : 0
      })
      await saveTenantOptions({functionOptions})
      showConfirm.value = false
      createToast({
        color: 'success',
        textColor: 'white',
        title: '保存成功',
        message: '設定が正常に保存されました',
      })
      emit('refresh')
    } catch (error) {
      console.error('Error saving options:', error)
      saveError.value = '設定の保存に失敗しました'
      createToast({
        color: 'danger',
        textColor: 'white',
        title: '保存失敗',
        message: '設定の保存に失敗しました',
      })
    } finally {
      isLoading.value = false
    }
  }

  onMounted(() => {
    // Initialize inputOptions with props if provided
    if (props.options) {
      inputOptions.value = {
        ...inputOptions.value,
        ...props.options,
      }
    }
  })

  watch(
    () => props.options,
    newOptions => {
      if (newOptions) {
        inputOptions.value = {
          ...inputOptions.value,
          ...newOptions,
        }
      }
    },
    {deep: true}
  )

  watch(
    () => props.triggerSaveBtn,
    () => {
      if (props.triggerSaveBtn > 0) {
        showConfirm.value = true
      }
    }
  )
</script>
<template>
  <div>
    <CToaster class="p-3" placement="top-end">
      <CToast
        v-for="(toast, index) in toasts"
        visible
        :key="index"
        :autohide="true"
        :delay="5000"
        :color="toast.color"
        class="align-items-center"
        :class="`text-${toast.textColor}`"
      >
        <div class="d-flex">
          <CToastBody>{{ toast.content }}</CToastBody>
          <CToastClose class="me-2 m-auto" white />
        </div>
      </CToast>
    </CToaster>
    <CListGroup class="mb-3">
      <CListGroupItem active class="d-flex justify-content-left"
        >依頼</CListGroupItem
      >
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>査定</span>
        <CFormSwitch
          v-model="inputOptions.satei"
          size="lg"
          label=""
          id="satei"
        />
      </CListGroupItem>
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>出品</span>
        <CFormSwitch
          v-model="inputOptions.shuppin"
          size="lg"
          label=""
          id="shuppin"
        />
      </CListGroupItem>
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>出品代行</span>
        <CFormSwitch
          v-model="inputOptions.shuppinDaiKou"
          size="lg"
          label=""
          id="shuppinDaiKou"
        />
      </CListGroupItem>
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>配送代行</span>
        <CFormSwitch
          v-model="inputOptions.haisouDaiKou"
          size="lg"
          label=""
          id="haisouDaiKou"
        />
      </CListGroupItem>
    </CListGroup>

    <CListGroup class="mb-3">
      <CListGroupItem active class="d-flex justify-content-left"
        >入札画面</CListGroupItem
      >
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>TOP者のニックネーム表示</span>
        <CFormSwitch
          v-model="inputOptions.topNicknameDisplay"
          size="lg"
          label=""
          id="topNicknameDisplay"
        />
      </CListGroupItem>
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>出品CSVダウンロード</span>
        <CFormSwitch
          v-model="inputOptions.csvDownload"
          size="lg"
          label=""
          id="csvDownload"
        />
      </CListGroupItem>
    </CListGroup>

    <CListGroup class="mb-3">
      <CListGroupItem active class="d-flex justify-content-left"
        >外部連携</CListGroupItem
      >
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>会員連携(クラウドEC)</span>
        <CFormSwitch
          v-model="inputOptions.memberLinkage"
          size="lg"
          label=""
          id="memberLinkage"
        />
      </CListGroupItem>
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>在庫連携(SFTP)</span>
        <CFormSwitch
          v-model="inputOptions.stockLinkage"
          size="lg"
          label=""
          id="stockLinkage"
        />
      </CListGroupItem>
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>出品商品連携(クラウドEC)</span>
        <CFormSwitch
          v-model="inputOptions.productLinkage"
          size="lg"
          label=""
          id="productLinkage"
        />
      </CListGroupItem>
    </CListGroup>

    <CListGroup class="mb-3">
      <CListGroupItem active class="d-flex justify-content-left"
        >メール</CListGroupItem
      >
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>HTMLメール</span>
        <CFormSwitch
          v-model="inputOptions.htmlMail"
          size="lg"
          label=""
          id="htmlMail"
        />
      </CListGroupItem>
    </CListGroup>

    <CListGroup>
      <CListGroupItem active class="d-flex justify-content-left"
        >決済</CListGroupItem
      >
      <CListGroupItem class="d-flex justify-content-between align-items-center">
        <span>クレジットカード決済(GMOPG)</span>
        <CFormSwitch
          v-model="inputOptions.creditCardPayment"
          size="lg"
          label=""
          id="creditCardPayment"
        />
      </CListGroupItem>
    </CListGroup>

    <div class="mt-4 text-end">
      <CButton color="primary" @click="showConfirm = true">保存</CButton>
    </div>
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="showConfirm"
      @close="
        () => {
          showConfirm = false
        }
      "
    >
      <CModalHeader>保存の確認</CModalHeader>
      <CModalBody>
        <div v-if="isLoading" class="text-center py-3">
          <span
            class="spinner-border spinner-border-sm"
            role="status"
            aria-hidden="true"
          ></span>
          <span class="ms-2">保存中...</span>
        </div>
        <div v-else>
          <div v-if="saveError" class="text-danger mb-2">{{ saveError }}</div>
          この内容で保存してもよろしいですか？
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          color="secondary"
          @click="showConfirm = false"
          :disabled="isLoading"
          >キャンセル</CButton
        >
        <CButton color="primary" @click="doSaveOptions" :disabled="isLoading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>
