<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" />{{ caption }} </slot>
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :loading="loading"
        :items="items"
        :fields="fields"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #auction_item_name="{item}">
          <td class="text-left">
            <CFormSelect
              name="auction_item_name"
              :options="auctionItemList"
              v-model="item.auction_item_name"
              addInputClasses="w-100"
            />
          </td>
        </template>
        <template #export_column_name="{item}">
          <td class="text-left">
            <CFormInput
              name="export_column_name"
              v-model="item.export_column_name"
              addInputClasses="w-100"
            />
          </td>
        </template>

        <template #action="{item}">
          <td style="width: 100px">
            <div class="d-flex justify-content-center align-items-center gap-1">
              <CButton size="sm" color="danger" @click="deleteItem(item)"
                >削除</CButton
              >
            </div>
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />

      <div>
        <CRow>
          <CCol sm="4" />
          <CCol md="2" class="text-right d-grid">
            <CButton color="primary" @click="addNewItem" block
              >行を追加</CButton
            >
          </CCol>
          <CCol col="2" class="text-right d-grid">
            <CButton color="primary" @click="update" block>更新</CButton>
          </CCol>
          <CCol sm="4" />
        </CRow>
      </div>
    </CCardBody>
  </CCard>
</template>

<script setup>
  import {CDataTable} from '@/components/Table';
  import {defineEmits, defineProps, ref} from 'vue';
  import {useRouter} from 'vue-router';

  const emit = defineEmits([
    'page-change',
    'sorter-change',
    'pagination-change',
    'add-new-item',
    'delete-item',
  ]);
  const props = defineProps({
    items: Array,
    current_count: {
      type: String,
      default: '0',
    },
    total_count: {
      type: String,
      default: '0',
    },
    fields: {
      type: Array,
      default() {
        return [
          {
            key: 'auction_item_name',
            label: 'オークション項目名',
            _classes: 'text-center',
            sorter: true,
          },
          {
            key: 'export_column_name',
            label: '外部システム物理名',
            _classes: 'text-center',
          },
          {key: 'action', label: '操作', _classes: 'text-center'},
        ];
      },
    },
    caption: {
      type: String,
      default: '',
    },
    loading: Boolean,
    activePage: Number,
    itemsPerPage: Number,
    pages: Number,
    itemsSorter: Object,
  });

  const router = useRouter();

  const auctionItemList = ref([
    {value: 1, label: '商品名(item_name)'},
    {value: 2, label: '色(color)'},
    {value: 3, label: 'サイズ(size)'},
    {value: 4, label: '説明文(description)'},
  ]);

  const pageChange = val => {
    if (props.items.length > 0) {
      emit('page-change', val);
    }
  };

  const sorterChange = val => {
    emit('sorter-change', val);
  };

  const paginationChange = val => {
    emit('pagination-change', val);
  };

  const addNewItem = () => {
    console.log('addNewItem');
    emit('add-new-item');
  };

  const update = () => {
    console.log('update');
  };

  const deleteItem = item => {
    console.log('delete-item', item);
    emit('delete-item', item.id);
  };
</script>
