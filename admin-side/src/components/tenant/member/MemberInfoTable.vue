<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header">
        <CIcon name="cil-grid" />{{ caption }}
        <span style="float: right">総件数: {{ current_count }}件</span>
      </slot>
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :loading="loading"
        :items="items"
        :fields="fields"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        :itemsPerPageSelect="itemsPerPageSelect"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #name="{item}">
          <td class="text-left" style="width: 200px">
            {{ item.name }}
          </td>
        </template>
        <template #column_name="{item}">
          <td class="text-left" style="width: 150px">
            {{ item.column_name }}
          </td>
        </template>
        <template #input_method="{item}">
          <td class="text-left" style="width: 150px">
            <CFormSelect
              name="input_method"
              :options="inputMethodOptions"
              v-model="item.input_method"
              @update:modelValue="() => {}"
            />
          </td>
        </template>
        <template #is_required="{item}">
          <td class="text-center" style="width: 100px">
            <CFormCheck :checked="item.is_required === 1" />
          </td>
        </template>
        <template #input_validation="{item}">
          <td>
            <CFormSelect
              name="input_validation"
              :options="inputValidationOptions"
              v-model="item.input_validation"
              @update:modelValue="() => {}"
            />
          </td>
        </template>

        <template #action="{item}">
          <td style="width: 150px">
            <div class="d-flex justify-content-center align-items-center gap-1">
              <CButton size="sm" color="success" @click="() => {}"
                >編集</CButton
              >
              <CButton size="sm" color="danger" @click="() => {}">削除</CButton>
            </div>
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
    </CCardBody>
  </CCard>
</template>

<script setup>
  import {CDataTable} from '@/components/Table';
  import {itemsPerPageSelect} from '@/views/common/customTableView';
  import {defineEmits, defineProps, ref} from 'vue';

  const emit = defineEmits([
    'page-change',
    'sorter-change',
    'pagination-change',
  ]);
  const props = defineProps({
    items: Array,
    current_count: {
      type: String,
      default: '0',
    },
    total_count: {
      type: String,
      default: '0',
    },
    fields: {
      type: Array,
      default() {
        return [
          {key: 'name', label: '項目名', _classes: 'text-center', sorter: true},
          {key: 'column_name', label: '物理名', _classes: 'text-center'},
          {key: 'input_method', label: '入力方法', _classes: 'text-center'},
          {key: 'is_required', label: '必須', _classes: 'text-center'},
          {key: 'input_validation', label: '入力制限', _classes: 'text-center'},
          {key: 'action', label: 'アクション', _classes: 'text-center'},
        ];
      },
    },
    caption: {
      type: String,
      default: '',
    },
    loading: Boolean,
    activePage: Number,
    itemsPerPage: Number,
    pages: Number,
    itemsSorter: Object,
  });

  const inputValidationOptions = ref([
    {value: 1, label: '定数(メーカー)'},
    {value: 2, label: '最大文字数：100'},
    {value: 3, label: '数値'},
  ]);

  const inputMethodOptions = ref([
    {value: 1, label: 'プルダウン'},
    {value: 2, label: 'テキスト'},
    {value: 3, label: 'チェックボックス'},
  ]);

  const pageChange = val => {
    if (props.items.length > 0) {
      emit('page-change', val);
    }
  };

  const sorterChange = val => {
    emit('sorter-change', val);
  };

  const paginationChange = val => {
    emit('pagination-change', val);
  };
</script>
