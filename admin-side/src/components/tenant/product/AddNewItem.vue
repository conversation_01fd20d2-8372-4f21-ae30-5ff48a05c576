<script setup>
  import {
    defineProps,
    defineEmits,
    defineExpose,
    ref,
    computed,
    watch,
  } from 'vue';

  const emit = defineEmits(['add-new-item']);

  const props = defineProps({
    parentInputValidationOptions: Array,
    parentInputMethodOptions: Array,
    parentConstantKey: Array,
  });

  const itemData = ref({
    logical_name: '',
    physical_name: '',
    input_type: 'pulldown',
    required_flag: false,
    data_type: 'constant',
  });

  const inputValidationOptions = ref(props.parentInputValidationOptions);
  const inputMethodOptions = ref(props.parentInputMethodOptions);

  const addNewItem = () => {
    emit('add-new-item', {
      ...itemData.value,
    });
  };

  // 入力値のリセット
  const resetItem = () => {
    itemData.value = {
      logical_name: '',
      physical_name: '',
      input_type: 'pulldown',
      required_flag: false,
      data_type: 'constant',
    };
  };
  defineExpose({resetItem});

  // 入力制限の制御
  const controlDataTypeOptions = computed(() => {
    switch (itemData.value.input_type) {
      case 'pulldown':
      case 'checkbox':
        return inputValidationOptions.value.filter(
          item => item.value === 'constant'
        );
      case 'text':
        return inputValidationOptions.value.filter(
          item => item.value !== 'constant'
        );
      case 'file':
        return inputValidationOptions.value.filter(
          item => item.value === 'reg'
        );
      default:
        return [];
    }
  });

  watch(
    () => itemData.value.input_type,
    newValue => {
      const options = controlDataTypeOptions.value;
      itemData.value.data_type = options[0] ? options[0].value : '';
    }
  );

  // 表示の制御
  const isConstant = computed(() => itemData.value.data_type === 'constant');
  const isCharacter = computed(() => itemData.value.data_type === 'character');
  const isNumeric = computed(
    () =>
      itemData.value.data_type === 'integer' ||
      itemData.value.data_type === 'float'
  );
  const isReg = computed(() => itemData.value.data_type === 'reg');
</script>

<template>
  <div>
    <div>
      <CForm onsubmit="return false;">
        <CRow class="mb-3">
          <CCol sm="3">
            <label>項目名</label>
            <CBadge color="danger">必須</CBadge>
          </CCol>
          <CCol sm="3">
            <CFormInput name="logical_name" v-model="itemData.logical_name" />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>物理名</label>
            <CBadge color="danger">必須</CBadge>
          </CCol>
          <CCol sm="3">
            <CFormInput name="physical_name" v-model="itemData.physical_name" />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>入力方法</label>
            <CBadge color="danger">必須</CBadge>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="input_type"
              :options="inputMethodOptions"
              v-model="itemData.input_type"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>必須項目</label>
          </CCol>
          <CCol sm="3">
            <CFormCheck name="required_flag" v-model="itemData.required_flag" />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>入力制限</label>
            <CBadge color="danger">必須</CBadge>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="data_type"
              :options="controlDataTypeOptions"
              v-model="itemData.data_type"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3" v-if="isConstant">
          <CCol sm="3">
            <label>定数キー</label>
            <CBadge color="danger">必須</CBadge>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="country"
              :options="props.parentConstantKey"
              v-model="itemData.input_data_list"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3" v-if="isCharacter">
          <CCol sm="3">
            <label>最大文字数</label>
          </CCol>
          <CCol sm="3">
            <CFormInput name="max_length" v-model="itemData.max_length" />
          </CCol>
        </CRow>
        <CRow class="mb-3" v-if="isNumeric">
          <CCol sm="3">
            <label>最大値</label>
          </CCol>
          <CCol sm="3">
            <CFormInput name="max_value" v-model="itemData.max_value" />
          </CCol>
        </CRow>
        <CRow class="mb-3" v-if="isReg">
          <CCol sm="3">
            <label>正規表現</label>
          </CCol>
          <CCol sm="3">
            <CFormInput
              name="regular_expressions"
              v-model="itemData.regular_expressions"
            />
          </CCol>
        </CRow>
      </CForm>
    </div>
    <div class="mt-4">
      <CRow>
        <CCol sm="2" class="d-grid">
          <CButton size="sm" color="primary" @click="addNewItem">追加</CButton>
        </CCol>
      </CRow>
    </div>
  </div>
</template>
