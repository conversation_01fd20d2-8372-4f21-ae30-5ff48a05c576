<script setup>
  import {defineProps, onMounted, ref, computed, watch} from 'vue';

  const props = defineProps({
    itemData: {
      type: Object,
      default: () => ({}),
    },
    inputMethodOptions: {
      type: Array,
      default: () => [],
    },
    inputValidationOptions: {
      type: Array,
      default: () => [],
    },
    constantKeyOptions: {
      type: Array,
      default: () => [],
    },
  });

  // 入力制限の制御
  const controlDataTypeOptions = computed(() => {
    switch (props.itemData.input_type) {
      case 'pulldown':
      case 'checkbox':
        return props.inputValidationOptions.filter(
          item => item.value === 'constant'
        );
      case 'text':
        return props.inputValidationOptions.filter(
          item => item.value !== 'constant'
        );
      case 'file':
        return props.inputValidationOptions.filter(
          item => item.value === 'reg'
        );
      default:
        return [];
    }
  });

  watch(
    () => props.itemData.input_type,
    newValue => {
      const options = controlDataTypeOptions.value;
      props.itemData.data_type = options[0] ? options[0].value : '';
    }
  );

  // 表示の制御
  const isConstant = computed(() => props.itemData.data_type === 'constant');
  const isCharacter = computed(() => props.itemData.data_type === 'character');
  const isNumeric = computed(
    () =>
      props.itemData.data_type === 'integer' ||
      props.itemData.data_type === 'float'
  );
  const isReg = computed(() => props.itemData.data_type === 'reg');
</script>

<template>
  <CForm onsubmit="return false;">
    <CRow class="mb-3">
      <CCol sm="3">
        <label>項目名</label>
        <CBadge color="danger">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput name="logical_name" v-model="itemData.logical_name" />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>物理名</label>
        <CBadge color="danger">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput name="physical_name" v-model="itemData.physical_name" />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>入力方法</label>
        <CBadge color="danger">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormSelect
          name="input_type"
          :options="inputMethodOptions"
          v-model="itemData.input_type"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>必須項目</label>
      </CCol>
      <CCol sm="3">
        <CFormCheck name="required_flag" v-model="itemData.required_flag" />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>入力制限</label>
        <CBadge color="danger">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormSelect
          name="data_type"
          :options="controlDataTypeOptions"
          v-model="itemData.data_type"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3" v-if="isConstant">
      <CCol sm="3">
        <label>定数キー</label>
        <CBadge color="danger">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormSelect
          name="country"
          :options="props.constantKeyOptions"
          v-model="itemData.input_data_list"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3" v-if="isCharacter">
      <CCol sm="3">
        <label>最大文字数</label>
      </CCol>
      <CCol sm="3">
        <CFormInput name="max_length" v-model="itemData.max_length" />
      </CCol>
    </CRow>
    <CRow class="mb-3" v-if="isNumeric">
      <CCol sm="3">
        <label>最大値</label>
      </CCol>
      <CCol sm="3">
        <CFormInput name="max_value" v-model="itemData.max_value" />
      </CCol>
    </CRow>
    <CRow class="mb-3" v-if="isReg">
      <CCol sm="3">
        <label>正規表現</label>
      </CCol>
      <CCol sm="3">
        <CFormInput
          type="text"
          name="regular_expressions"
          v-model="itemData.regular_expressions"
        />
      </CCol>
    </CRow>
  </CForm>
</template>
