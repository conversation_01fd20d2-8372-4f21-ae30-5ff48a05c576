<template>
  <CCardBody>
    <CForm onsubmit="return false;">
      <CRow class="mb-3">
        <CCol sm="2">
          <label>項目名</label>
        </CCol>
        <CCol sm="2">
          {{ itemData.logical_name }}
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>物理名</label>
        </CCol>
        <CCol sm="2">
          {{ itemData.physical_name }}
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>入力方法</label>
        </CCol>
        <CCol sm="4">
          {{ getMethodName(itemData.input_type) }}
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>必須項目</label>
        </CCol>
        <CCol sm="4">
          <label>{{ itemData.required_flag === 1 ? '必須' : '任意' }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>入力制限</label>
        </CCol>
        <CCol sm="4">
          <label>{{ getValidationName(itemData.data_type) }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3" v-if="itemData.data_type === 'constant'">
        <CCol sm="2">
          <label>定数キー</label>
        </CCol>
        <CCol sm="4">
          <label>{{ getConstantKeyName(itemData.input_data_list) }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3" v-if="itemData.data_type === 'character'">
        <CCol sm="2">
          <label>最大文字数</label>
        </CCol>
        <CCol sm="4">
          <label>{{ itemData.max_length }} 文字</label>
        </CCol>
      </CRow>
      <CRow
        class="mb-3"
        v-if="
          itemData.data_type === 'integer' || itemData.data_type === 'float'
        "
      >
        <CCol sm="2">
          <label>最大値</label>
        </CCol>
        <CCol sm="4">
          <label>{{ itemData.max_value }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3" v-if="itemData.data_type === 'reg'">
        <CCol sm="3">
          <label>正規表現</label>
        </CCol>
        <CCol sm="3">
          <CFormInput
            type="text"
            name="regular_expressions"
            v-model="itemData.regular_expressions"
          />
        </CCol>
      </CRow>
    </CForm>
  </CCardBody>
</template>

<script setup>
  import {defineProps, onMounted} from 'vue';

  const props = defineProps({
    inputMethodOptions: {
      type: Array,
      default: () => [],
    },
    inputValidationOptions: {
      type: Array,
      default: () => [],
    },
    languageOptions: {
      type: Array,
      default: () => [],
    },
    itemData: {
      type: Object,
      default: () => ({}),
    },
    constantKeyOptions: {
      type: Array,
      default: () => [],
    },
  });

  onMounted(() => {
    console.log(`itemData = ${JSON.stringify(props.itemData)}`);
  });

  const getMethodName = id => {
    const label = props.inputMethodOptions.find(
      item => String(item.value) === String(id)
    )?.label;
    return label || '';
  };

  const getValidationName = id => {
    const label = props.inputValidationOptions.find(
      item => String(item.value) === String(id)
    )?.label;
    return label || '';
  };

  const getConstantKeyName = id => {
    const label = props.constantKeyOptions.find(
      item => String(item.value) === String(id)
    )?.label;
    return label || '';
  };
</script>
<style type="text/css">
  .word-break {
    word-break: break-all;
  }
</style>
