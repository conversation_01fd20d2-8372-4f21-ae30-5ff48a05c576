<script setup>
  import {defineEmits, ref, watch} from 'vue'

  const emit = defineEmits(['add'])
  const props = defineProps({
    resourceType: Number,
    languageCode: String,
    initialItem: {
      type: Object,
      default: () => ({}),
    },
  })

  const resetTypeOptions = [
    {value: 'auction', label: '入札会ごとにリセット'},
    {value: 'period', label: '期間でリセット'},
  ]

  const defaultItemData = {
    is_bid_limit_flag: false,
    limit_amount: null,
    reset_type: resetTypeOptions[0].value, // デフォルトは「入札会ごとにリセット」
    reset_date: '0',
    start_date: null,
    end_date: null,
  }

  const itemData = ref({...defaultItemData})

  // 0〜28の数値をプルダウン用に生成
  const resetDateOptions = Array.from({length: 29}, (_, index) => ({
    value: index,
    label: index === 0 ? '月末' : `毎月 ${index.toString()} 日`,
  }))

  const addNewItem = () => {
    console.log('add')
    emit('add', {
      ...itemData.value,
    })
  }

  // initialItemの変化を監視して反映
  watch(
    () => props.initialItem,
    val => {
      if (val && val.bid_limit_flag) {
        // bid_limit_flagがtrueならinitialItemで上書き
        itemData.value = {
          is_bid_limit_flag: !!val.bid_limit_flag,
          limit_amount: val.bid_limit,
          reset_type: val.reset_type ?? defaultItemData.reset_type,
          reset_date: val.reset_date ?? defaultItemData.reset_date,
          start_date: val.start_datetime ?? defaultItemData.start_date,
          end_date: val.end_datetime ?? defaultItemData.end_date,
        }
      } else {
        // それ以外はデフォルト値
        itemData.value = {...defaultItemData}
      }
    },
    {immediate: true}
  )
</script>

<template>
  <div>
    <div>
      <CForm onsubmit="return false;">
        <CRow class="mb-3">
          <CCol sm="3">
            <CFormCheck
              id="is_bid_limit_flag"
              v-model="itemData.is_bid_limit_flag"
              label="入札上限額を利用する"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>入札上限額(円)</label>
          </CCol>
          <CCol sm="3">
            <CFormInput
              type="number"
              name="amount"
              v-model="itemData.limit_amount"
              :disabled="!itemData.is_bid_limit_flag"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>リセット型式</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="tax"
              :options="resetTypeOptions"
              v-model="itemData.reset_type"
              :disabled="!itemData.is_bid_limit_flag"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3" v-if="itemData.reset_type === 'period'">
          <CCol sm="3">
            <label>リセット日付</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="reset_date"
              :options="resetDateOptions"
              v-model="itemData.reset_date"
              :disabled="!itemData.is_bid_limit_flag"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>適用開始日</label>
          </CCol>
          <CCol sm="3">
            <CFormInput
              type="date"
              name="start_date"
              v-model="itemData.start_date"
              :disabled="!itemData.is_bid_limit_flag"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>適用終了日</label>
          </CCol>
          <CCol sm="3">
            <CFormInput
              type="date"
              name="start_date"
              v-model="itemData.end_date"
              :disabled="!itemData.is_bid_limit_flag"
            />
          </CCol>
        </CRow>
      </CForm>
    </div>
    <div class="mt-4">
      <CRow>
        <CCol sm="5"></CCol>
        <CCol sm="2" class="d-grid">
          <CButton color="primary" @click="addNewItem">保存</CButton>
        </CCol>
        <CCol sm="5"></CCol>
      </CRow>
    </div>
  </div>
</template>
