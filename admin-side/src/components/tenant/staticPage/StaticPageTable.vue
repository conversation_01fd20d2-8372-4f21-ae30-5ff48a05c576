<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" /> {{ caption }} </slot>
    </CCardHeader>
    <CCardBody>
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :items="items"
        :fields="fields"
        :loading="loading"
        @update:sorter-value="sorterChange"
      >
        <template #no="{item, index}">
          <td style="text-align: center; width: 30px">{{ index + 1 }}</td>
        </template>
        <template #path="{item}">
          <td style="min-width: 80px">{{ item.path }}</td>
        </template>
        <template #pageName="{item}">
          <td>
            {{ item.pageName }}
          </td>
        </template>
        <template #files="{item}">
          <td v-if="item.files && item.files.length > 0" class="text-center">
            <CButton
              size="sm"
              block
              color="primary"
              @click="() => openViewFileModal(item)"
              >表示</CButton
            >
          </td>
          <td v-else></td>
        </template>
        <template #btn_edit="{item}">
          <td style="width: 70px" class="text-center">
            <CButton size="sm" block color="success" @click="editPage(item)"
              >編集</CButton
            >
          </td>
        </template>
        <template #btn_delete="{item}">
          <td style="width: 70px" class="text-center">
            <CButton
              v-if="!item.sent_flag"
              size="sm"
              block
              color="danger"
              @click="openDeleteModal(item)"
              >削除</CButton
            >
          </td>
        </template>
      </CDataTable>
    </CCardBody>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="fileViewModal"
      @close="closeViewFileModal"
    >
      <CModalHeader>
        <CModalTitle>テンプレート一覧</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div class="mb-2">
          <CRow>
            <CCol
              v-for="template in templateFileList"
              :key="template.language_code"
            >
              <div class="ml-1">
                {{ languageOptions[template.language_code] }}
              </div>
              <CDataTable
                :items="[template.file_url]"
                :fields="[
                  {
                    key: 'fname',
                    label: languageOptions[template.language_code],
                  },
                ]"
                :header="false"
              >
                <template #fname="{item}">
                  <a
                    style="line-height: 2"
                    @contextmenu.prevent="onContextMenuOpen($event, item)"
                    @click="fileNameClickEvent(item)"
                    href="javascript:void(0);"
                  >
                    {{ item.substring(item.lastIndexOf('/') + 1, item.length) }}
                  </a>
                </template>
              </CDataTable>
            </CCol>
          </CRow>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="closeViewFileModal" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <ContextMenu v-model:show="contextMenu.show" :options="contextMenu.options">
      <context-menu-item label="ファイルを開く" @click="getFileViewUrl" />
      <context-menu-item label="ダウンロード" @click="getFileDownloadUrl" />
    </ContextMenu>
  </CCard>
</template>

<script setup>
  import DownloadFile from '@/api/uploadFileToS3'
  import {CDataTable} from '@/components/Table'
  import {CIcon} from '@coreui/icons-vue'
  import {ContextMenu, ContextMenuItem} from '@imengyu/vue3-context-menu'
  import {defineProps, ref} from 'vue'
  import {useRouter} from 'vue-router'

  const emit = defineEmits([
    'delete',
    'onOpenDeleteModal',
    'onCloseDeleteModal',
    'sorter-change',
  ])
  const props = defineProps({
    languageOptions: Object,
    items: Array,
    fields: {
      type: Array,
      default: () => {
        return [
          {key: 'no', label: 'No', _classes: 'text-center', width: '20%'},
          {key: 'path', label: 'パース', _classes: 'text-center', width: '30%'},
          {
            key: 'pageName',
            label: 'ページ名',
            _classes: 'text-center',
            width: '30%',
          },
          {
            key: 'files',
            label: 'テンプレート',
            _classes: 'text-center',
            width: '10%',
            sorter: false,
          },
          {
            key: 'btn_edit',
            label: '詳細',
            _classes: 'text-center',
            width: '10%',
          },
          {
            key: 'btn_delete',
            label: '削除',
            _classes: 'text-center',
            width: '10%',
          },
        ]
      },
    },
    caption: {
      type: String,
      default: 'noticeTable',
    },
    hover: Boolean,
    striped: Boolean,
    border: Boolean,
    small: Boolean,
    fixed: Boolean,
    dark: Boolean,
    loading: Boolean,
    itemsSorter: Object,
  })

  const router = useRouter()

  const btnClicked = ref(false)
  const fileViewModal = ref(false)
  const templateFileList = ref([])
  const contextMenu = ref({
    show: false,
    options: {
      zIndex: 9999,
      minWidth: 230,
      x: 500,
      y: 500,
    },
    data: null,
  })

  const editPage = item => {
    router.push({path: `/tenant/static-page/${item.staticPageNo}`})
  }

  const openDeleteModal = item => {
    if (btnClicked.value) {
      return
    }
    btnClicked.value = true
    emit('onOpenDeleteModal', item)
  }

  const sorterChange = val => {
    emit('sorter-change', val)
  }

  const openViewFileModal = item => {
    console.log('openViewFileModal')
    if (btnClicked.value) {
      return
    }
    btnClicked.value = true
    templateFileList.value = item.files
    fileViewModal.value = true
  }

  const fileNameClickEvent = fileUrl => {
    if (fileUrl !== null && fileUrl !== '') {
      DownloadFile.getFile(fileUrl).then(res => {
        console.log(`res: ${JSON.stringify(res)}`)
        window.open(res, '_blank')
      })
    }
  }
  const closeViewFileModal = () => {
    console.log('closeViewFileModal')
    fileViewModal.value = false
    btnClicked.value = false
  }

  const onContextMenuOpen = (e, item) => {
    // Show context menu
    contextMenu.value.show = true
    contextMenu.value.options.x = e.x
    contextMenu.value.options.y = e.y
    contextMenu.value.data = item
  }
</script>
