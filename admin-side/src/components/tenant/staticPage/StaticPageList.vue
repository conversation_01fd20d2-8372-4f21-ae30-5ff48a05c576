<template>
  <div class="mb-3">
    <CRow class="">
      <CCol sm="12">
        <CButton color="primary" @click="addNewPage">新規登録</CButton>
      </CCol>
    </CRow>
    <CRow class="mt-3">
      <CCol sm="12">
        <StaticPageTable
          :languageOptions="languageOptions"
          :items="staticPageList"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :itemsSorter="itemsSorter"
          caption="静的ページ一覧"
          @onOpenDeleteModal="openDeleteModal"
          @sorter-change="sorterChange"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="validateModal"
      @close="
        () => {
          validateModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle>エラー</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="validateModal = false" color="dark" :disabled="loading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="deleteModal"
      @close="closeDeleteModal"
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loadingDelete">ページを削除してもよろしいですか？</div>
        <div style="margin-left: 10px">
          <ScaleLoader
            :loading="loadingDelete"
            color="#5dc596"
            height="10px"
            width="4px"
          ></ScaleLoader>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="closeDeleteModal"
          color="dark"
          :disabled="loadingDelete"
          >キャンセル</CButton
        >
        <CButton @click="deletePage" color="primary" :disabled="loadingDelete"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods'
  import ScaleLoader from '@/components/Table/ScaleLoader.vue'
  import {useAuthStore} from '@/store/auth'
  import {useCommonStore} from '@/store/common'
  import {onBeforeMount, onMounted, ref} from 'vue'
  import {useRouter} from 'vue-router'
  import StaticPageTable from './StaticPageTable.vue'

  const router = useRouter()
  const authStore = useAuthStore()
  const store = useCommonStore()

  const languageOptions = ref({})
  const loading = ref(true)
  const staticPageList = ref([])
  const itemsSorter = ref({asc: false, column: 'path'})
  const validateModal = ref(false)
  const validateResult = ref([])
  const prevRoute = ref(null)
  const btnClicked = ref(false)

  const loadingDelete = ref(false)
  const deleteModal = ref(false)
  const deleteItem = ref(null)

  onBeforeMount(() => {
    prevRoute.value = router.options.history?.state?.back
    console.log(prevRoute.value)
    store.set(['itemsSorter', {asc: false, column: 'path'}])
  })

  const getNoticesFromServer = async () => {
    // Request to server
    return Methods.apiExecute('get-static-page-list', {})
      .then(response => {
        if (response.status === 200) {
          loading.value = false
          // CompModal.value = true
          const pages = response.data
          if (!pages) {
            return Promise.resolve()
          }
          // Processing data
          const langCode = authStore.user.language_code
          const finalList = pages.map((item, index) => {
            const tmp = (item.localized || []).find(
              x => x.language_code === langCode
            )
            const pageName = tmp ? tmp.page_name : ''
            const files = (item.localized || [])
              .map(x => {
                if (x.file_url) {
                  return {
                    file_url: x.file_url,
                    language_code: x.language_code,
                  }
                }
                return null
              })
              .filter(Boolean)
            return {
              staticPageNo: item.static_page_no,
              path: item.page_path,
              pageName,
              files,
            }
          })
          staticPageList.value = finalList
          return Promise.resolve()
        }
        return Promise.resolve()
      })
      .catch(error => {
        console.log('error: ', error)
        loading.value = false
        validateResult.value = Methods.parseHtmlResponseError(router, error)
        openValidateModal()
        return Promise.resolve()
      })
  }

  const getStaticPageList = async () => {
    console.log('getStaticPageList')
    loading.value = true
    validateResult.value = []
    staticPageList.value = []
    await getNoticesFromServer().then(() => {
      // 登録・編集画面からの遷移時は閲覧してたページに戻す
      if (
        prevRoute.value &&
        (prevRoute.value.includes('edit') || prevRoute.value.includes('new'))
      ) {
        itemsSorter.value = store.itemsSorter
      }
      return Promise.resolve()
    })
  }

  const search = async () => {
    console.log('search')
    loading.value = true
    validateResult.value = []
    staticPageList.value = []
    await getNoticesFromServer().then(() => {
      sorterChange({asc: false, column: 'path'})
    })
  }

  const getConstantsData = async () => {
    languageOptions.value = {}
    // Request to server
    await Methods.apiExecute('get-constants-by-keys-language', {
      key_strings: ['LANGUAGE_CODE'],
    }).then(response => {
      if (response.status === 200) {
        languageOptions.value = {}
        for (const constant of response.data) {
          languageOptions.value[constant.value1] = constant.value2
        }
      }
    })
  }
  const addNewPage = () => {
    router.push({path: '/tenant/static-page/new'})
  }

  const deletePage = () => {
    console.log('deletePage:', deleteItem.value)
    if (!deleteItem.value.staticPageNo) {
      return
    }
    btnClicked.value = false
    loadingDelete.value = true
    validateModal.value = false
    // Request to server
    Methods.apiExecute('delete-static-page', {
      static_page_no: deleteItem.value.staticPageNo,
    })
      .then(response => {
        console.log(`response = ${JSON.stringify(response)}`)
        loadingDelete.value = false
        deleteModal.value = false
        if (response.status === 200) {
          loading.value = false
          search()
        }
      })
      .catch(error => {
        console.log(error)
        loading.value = false
        loadingDelete.value = false
        deleteModal.value = false
        validateResult.value = Methods.parseHtmlResponseError(router, error)
        validateModal.value = true
      })
  }
  const openValidateModal = () => {
    validateModal.value = true
  }
  const sorterChange = val => {
    itemsSorter.value = val
    store.set(['itemsSorter', val])
  }
  const openDeleteModal = item => {
    console.log('openDeleteModal', item)
    deleteItem.value = item
    deleteModal.value = true
  }
  const closeDeleteModal = () => {
    deleteModal.value = false
  }

  onMounted(async () => {
    console.log('mounted')
    try {
      await getConstantsData()
      await getStaticPageList()
    } catch (error) {
      console.log(JSON.stringify(error))
      loading.value = false
      Methods.parseHtmlResponseError(router, error)
    }
  })
</script>
