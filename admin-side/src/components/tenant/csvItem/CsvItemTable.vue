<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" />{{ caption }} </slot>
    </CCardHeader>
    <CCardBody>
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :loading="loading"
        :items="items"
        :fields="fields"
      >
        <template #logical_name="{item}">
          <td class="text-left">
            {{ item.logical_name }}
          </td>
        </template>
        <template #physical_name="{item}">
          <td class="text-left">
            {{ item.physical_name }}
          </td>
        </template>
        <template #action="{item}">
          <td style="width: 150px">
            <div class="d-flex justify-content-center align-items-center gap-1">
              <CButton size="sm" @click="moveUp(item)">
                <CIcon icon="cilArrowTop" />
              </CButton>
              <CButton size="sm" @click="moveDown(item)">
                <CIcon icon="cilArrowBottom" />
              </CButton>
              <div class="delete-button-container">
                <CButton
                  v-if="!notEditableStatus(item)"
                  size="sm"
                  color="danger"
                  @click="deleteItem(item)"
                  >削除</CButton
                >
              </div>
            </div>
          </td>
        </template>
      </CDataTable>
    </CCardBody>

    <!-- Add new item -->
    <CCardHeader
      style="border-top: 1px solid; border-bottom: none; border-color: #d8dbe0"
    >
      <slot name="header"><strong>追加項目選択</strong></slot>
    </CCardHeader>
    <CCardBody>
      <AddNewItem
        :resourceType="'item'"
        :fieldList="fieldList"
        :resourceList="constResourceList"
        @add-new-item="addNewItem"
      />
    </CCardBody>
  </CCard>
</template>

<script setup>
  import {CDataTable} from '@/components/Table';
  import {defineEmits, defineProps, ref} from 'vue';
  import AddNewItem from './AddNewItem.vue';

  const emit = defineEmits([
    'page-change',
    'sorter-change',
    'pagination-change',
    'add-new-item',
    'delete-item',
    'move-up',
    'move-down',
  ]);
  const props = defineProps({
    items: Array,
    current_count: {
      type: String,
      default: '0',
    },
    total_count: {
      type: String,
      default: '0',
    },
    fieldList: Array,
    notEditableList: Array,
    fields: {
      type: Array,
      default() {
        return [
          {
            key: 'logical_name',
            label: '項目名',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'physical_name',
            label: '物理名',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'action',
            label: 'アクション',
            _classes: 'text-center',
            sorter: false,
          },
        ];
      },
    },
    caption: {
      type: String,
      default: '',
    },
    loading: Boolean,
  });

  const constResourceList = ref([
    {value: 'item', label: '商品'},
    {value: 'member', label: '会員'},
  ]);

  const notEditableStatus = item => {
    return props.notEditableList.some(no => Number(no) === item.field_no);
  };

  const addNewItem = item => {
    emit('add-new-item', item);
  };

  const deleteItem = item => {
    emit('delete-item', item);
  };

  const moveUp = item => {
    emit('move-up', item);
  };

  const moveDown = item => {
    emit('move-down', item);
  };
</script>

<style scoped>
  .delete-button-container {
    width: 50px; /* 削除ボタンの幅を固定 */
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
