<script setup>
  import {defineEmits, ref, onMounted, computed, watch} from 'vue';

  const emit = defineEmits(['add-new-item']);
  const props = defineProps({
    fieldList: Array,
    resourceType: String,
    resourceList: Array,
  });

  const itemData = ref({});

  const selectField = ref({
    resource: '',
    field_no: '',
  });

  const itemNameList = computed(() => {
    return props.fieldList
      .filter(item => item.field_division === selectField.value.resource)
      .map(item => ({
        value: String(item.field_no),
        label: item.logical_name,
      }));
  });

  onMounted(() => {
    selectField.value.resource = props.resourceType;
  });

  watch(
    () => props.fieldList,
    newFieldList => {
      if (newFieldList && newFieldList.length > 0) {
        selectPulldownValue();
      }
    }
  );

  watch(
    () => selectField.value.resource,
    newValue => {
      selectPulldownValue();
    }
  );

  const addNewItem = () => {
    const selectItem = props.fieldList.filter(
      item => String(item.field_no) === selectField.value.field_no
    );
    itemData.value = selectItem[0] ? selectItem[0] : {};
    emit('add-new-item', {
      ...itemData.value,
    });
  };

  // プルダウンの初期値設定
  const selectPulldownValue = () => {
    const options = itemNameList.value;
    selectField.value.field_no = options[0] ? String(options[0].value) : '';
  };
</script>

<template>
  <div>
    <div>
      <CForm onsubmit="return false;">
        <CRow class="mb-3">
          <CCol sm="3">
            <label>リソース</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="resource_type"
              :options="resourceList"
              v-model="selectField.resource"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>項目名</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="item_name"
              :options="itemNameList"
              v-model="selectField.field_no"
            />
          </CCol>
        </CRow>
      </CForm>
    </div>
    <div class="mt-4">
      <CRow>
        <CCol sm="2" class="d-grid">
          <CButton size="sm" color="primary" @click="addNewItem">追加</CButton>
        </CCol>
      </CRow>
    </div>
  </div>
</template>
