<script setup>
  import {defineEmits, ref} from 'vue';

  const emit = defineEmits(['add']);
  const props = defineProps({
    resourceType: Number,
    languageCode: String,
  });

  const itemData = ref({
    start_date: null,
    amount: null,
    weight: null,
    tax: null,
  });
  const taxOptions = [
    {value: 1, label: '内税'},
    {value: 2, label: '外税'},
    {value: 3, label: '非課税'},
  ];

  const addNewItem = () => {
    console.log('add');
    emit('add', {
      ...itemData.value,
    });
  };
</script>

<template>
  <div>
    <div>
      <CForm onsubmit="return false;">
        <CRow class="mb-3">
          <CCol sm="3">
            <label>全国一律送料(円)</label>
          </CCol>
          <CCol sm="3">
            <CFormInput type="number" name="amount" v-model="itemData.amount" />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>重量</label>
          </CCol>
          <CCol sm="3">
            <CFormInput name="weight" v-model="itemData.weight" />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>サイズ</label>
          </CCol>
          <CCol sm="3">
            <CFormInput
              name="size"
              v-model="itemData.size"
              placeholder="サイズを入力（例：M、L）"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>税区分</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="tax"
              :options="taxOptions"
              v-model="itemData.tax"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>適用開始日</label>
          </CCol>
          <CCol sm="3">
            <CFormInput
              type="date"
              name="start_date"
              v-model="itemData.start_date"
            />
          </CCol>
        </CRow>
      </CForm>
    </div>
    <div class="mt-4">
      <CRow>
        <CCol sm="5"></CCol>
        <CCol sm="2" class="d-grid">
          <CButton color="primary" @click="addNewItem">保存</CButton>
        </CCol>
        <CCol sm="5"></CCol>
      </CRow>
    </div>
  </div>
</template>
