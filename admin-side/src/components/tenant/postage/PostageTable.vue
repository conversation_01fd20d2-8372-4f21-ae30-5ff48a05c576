<template>
  <CCard>
    <CCardHeader>
      <strong>送料設定履歴</strong>
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :loading="loading"
        :items="items"
        :fields="fields"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #start_date="{item}">
          <td class="text-center">
            {{ item.start_date }}
          </td>
        </template>
        <template #amount="{item}">
          <td class="text-right">{{ item.amount | addComma }} 円</td>
        </template>
        <template #weight="{item}">
          <td class="text-center">
            {{ item.weight }}
          </td>
        </template>
        <template #tax="{item}">
          <td class="text-center">
            {{ getTaxLabel(item.tax) }}
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
    </CCardBody>
  </CCard>
</template>

<script setup>
  import {CDataTable} from '@/components/Table';
  import {defineEmits, defineProps} from 'vue';

  const emit = defineEmits([
    'page-change',
    'sorter-change',
    'pagination-change',
  ]);
  const props = defineProps({
    items: Array,
    fields: {
      type: Array,
      default() {
        return [
          {
            key: 'start_date',
            label: '適用開始日',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'amount',
            label: '全国一律送料(円)',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'weight',
            label: '適用基準',
            _classes: 'text-center',
            sorter: false,
          },
          {key: 'tax', label: '税区分', _classes: 'text-center', sorter: false},
        ];
      },
    },
    caption: {
      type: String,
      default: 'postageTable',
    },
    loading: Boolean,
    activePage: Number,
    itemsPerPage: Number,
    pages: Number,
    itemsSorter: Object,
  });

  const taxOptions = [
    {value: 1, label: '内税'},
    {value: 2, label: '外税'},
    {value: 3, label: '非課税'},
  ];

  const getTaxLabel = tax => {
    const label = taxOptions.find(item => item.value === tax)?.label;
    return label || '';
  };

  const pageChange = val => {
    if (props.items.length > 0) {
      emit('page-change', val);
    }
  };

  const sorterChange = val => {
    emit('sorter-change', val);
  };

  const paginationChange = val => {
    emit('pagination-change', val);
  };
</script>
