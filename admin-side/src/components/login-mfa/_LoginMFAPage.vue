<template>
  <div class="bg-body-tertiary min-vh-100 d-flex flex-row align-items-center">
    <CContainer>
      <CRow class="justify-content-center">
        <CCol md="5">
          <CCardGroup>
            <CCard class="p-4">
              <CCardBody>
                <CForm>
                  <h1 class="text-center">ログイン(MFA)</h1>
                  <div cols="10">
                    <div v-if="loginMsg">
                      <div
                        v-for="(msg, key, index) in loginMsg"
                        :key="index"
                        class="text-danger"
                      >
                        {{ msg }}
                      </div>
                    </div>
                  </div>

                  <!--TODO Email Verification Form (shown when email verification is required) -->
                  <div v-if="showEmailVerification" class="mt-4">
                    <h4>メール確認</h4>
                    <p>
                      あなたのメールアドレスに確認コードを送信しました。以下の手順で認証を完了してください：
                    </p>
                    <ol>
                      <li>メールボックスを確認してください</li>
                      <li>メールに記載された確認コードをコピーする</li>
                      <li>下のフィールドにコードを入力して確認</li>
                    </ol>

                    <CInputGroup class="mb-4">
                      <CInputGroupText>
                        <CIcon icon="cil-asterisk" />
                      </CInputGroupText>
                      <CFormInput
                        placeholder="メールの確認コード"
                        v-model="verificationCode"
                        inputmode="numeric"
                        pattern="[0-9]*"
                        maxlength="6"
                      />
                    </CInputGroup>

                    <CButton
                      color="success"
                      class="px-4 w-100 mb-3"
                      @click="verifyEmail"
                      :disabled="
                        loading ||
                        !verificationCode ||
                        verificationCode.length < 4
                      "
                    >
                      確認して完了
                    </CButton>

                    <div class="text-center">
                      <CLink
                        @click="resendVerificationEmail"
                        href="javascript:void(0)"
                        class="text-primary"
                        :class="{disabled: loading}"
                      >
                        コードを再送信する
                      </CLink>
                    </div>
                  </div>

                  <!-- New Password Form (shown when password change is required) -->
                  <div v-if="showNewPasswordForm" class="mt-4">
                    <h4>パスワード変更</h4>
                    <p>初期パスワードを変更してください</p>
                    <CInputGroup class="mb-3">
                      <CInputGroupText>
                        <CIcon icon="cil-lock-locked" />
                      </CInputGroupText>
                      <CFormInput
                        type="password"
                        placeholder="新しいパスワード"
                        v-model="newPassword"
                      />
                    </CInputGroup>

                    <CInputGroup class="mb-4">
                      <CInputGroupText>
                        <CIcon icon="cil-lock-locked" />
                      </CInputGroupText>
                      <CFormInput
                        type="password"
                        placeholder="新しいパスワード（確認）"
                        v-model="confirmNewPassword"
                        @keypress.enter="changePassword"
                      />
                    </CInputGroup>

                    <CButton
                      color="success"
                      class="px-4 w-100 mb-3"
                      @click="changePassword"
                      :disabled="
                        loading ||
                        !newPassword ||
                        !confirmNewPassword ||
                        newPassword !== confirmNewPassword
                      "
                    >
                      パスワードを変更する
                    </CButton>
                  </div>

                  <!-- Success message after verification -->
                  <div
                    v-if="verificationComplete"
                    class="alert alert-success mt-3 mb-3"
                  >
                    メール認証が完了しました。ログイン情報を入力してログインしてください。
                  </div>
                  <!-- Login Form (hidden when email verification or password change is shown) -->
                  <div v-if="!showEmailVerification && !showNewPasswordForm">
                    <CInputGroup class="mb-3">
                      <CInputGroupText>
                        <CIcon icon="cil-user" />
                      </CInputGroupText>
                      <CFormInput
                        id="loginId"
                        placeholder="ログインID"
                        autocomplete="loginid email"
                        v-model="loginId"
                        v-on:keypress.enter="handleLogin"
                      />
                    </CInputGroup>
                    <CInputGroup class="mb-4">
                      <CInputGroupText>
                        <CIcon icon="cil-lock-locked" />
                      </CInputGroupText>
                      <CFormInput
                        type="password"
                        placeholder="パスワード"
                        autocomplete="current-password"
                        v-model="password"
                        v-on:keypress.enter="handleLogin"
                      />
                    </CInputGroup>
                    <CRow>
                      <CCol md="4"></CCol>
                      <CCol md="4" class="text-center d-grid">
                        <CButton
                          id="loginBtn"
                          color="primary"
                          class="px-4"
                          @click="handleLogin"
                          :disabled="loading"
                        >
                          <span v-if="loading">処理中...</span>
                          <span v-else>ログイン</span>
                        </CButton>
                      </CCol>
                      <CCol md="4"></CCol>
                    </CRow>
                  </div>
                  <CRow class="mt-4">
                    <CCol class="text-center">
                      <CLink
                        @click="navigateToRegister"
                        href="javascript:void(0)"
                        class="text-primary"
                      >
                        新規登録はこちら
                      </CLink>
                    </CCol>
                  </CRow>
                  <!-- TODO -->
                  <CRow class="mt-4">
                    <CCol class="text-center">
                      <CLink
                        @click="authStore.logout"
                        href="javascript:void(0)"
                        class="text-primary"
                      >
                        Logout for testing
                      </CLink>
                    </CCol>
                  </CRow>
                </CForm>
              </CCardBody>
            </CCard>
          </CCardGroup>
        </CCol>
      </CRow>
    </CContainer>
  </div>
</template>

<script setup>
  defineOptions({name: 'LoginComponent'});
  import {onMounted, ref} from 'vue';
  import {useRouter} from 'vue-router';
  import {useAuthStore} from '@/store/auth';
  import {cognitoRequest} from './utils/login';
  const router = useRouter();
  const loginId = ref('');
  const password = ref('');
  const loading = ref(false);
  const loginMsg = ref([]);
  const authStore = useAuthStore();
  const showEmailVerification = ref(false);
  const verificationCode = ref('');
  const verificationComplete = ref(false);

  const showNewPasswordForm = ref(false);
  const newPassword = ref('');
  const confirmNewPassword = ref('');

  const handleLogin = async () => {
    loading.value = true;
    if (!loginId.value || !password.value) {
      loginMsg.value = ['ログインIDとパスワードを入力してください。'];
      loading.value = false;
      return;
    }

    const result = await cognitoRequest(loginId.value, password.value);
    console.log(result);
    loading.value = false;
  };

  const focusLoginID = () => {
    document.getElementById('loginId')?.focus();
  };

  const navigateToRegister = () => {
    router.push('/pages/register');
  };

  onMounted(() => {
    focusLoginID();
  });
</script>
