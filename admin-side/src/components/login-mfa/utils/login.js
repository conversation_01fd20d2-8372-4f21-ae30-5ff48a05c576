import {
  signIn,
  resendSignUpCode,
  confirmSignUp,
  confirmSignIn,
  fetchAuthSession,
} from 'aws-amplify/auth';
import {jwtDecode} from 'jwt-decode';

// TODO : use in auth.js
const errorToMessage = error => {
  if (error === null) return undefined;
  console.log('You got error: ', error.name);
  switch (error.name) {
    case 'UserNotConfirmedException':
      return 'このアカウントは有効性が検証されていません';
    case 'UserNotFoundException':
      return 'メール、またはパスワードが間違っています';
    case 'UserAlreadyAuthenticatedException':
      return '既にログインしています';
    case 'NotAuthorizedException':
      return 'メール、またはパスワードが間違っています';
    case 'LimitExceededException':
      return 'パスワード変更のリクエストが制限を超えました。しばらくしてから再度お試しください。';
    case 'InvalidPasswordException':
      return 'パスワードはポリシーの要件を満たしていません';
    default:
      return 'アカウントを認証できませんでした';
  }
};

export const cognitoRequest = async (username, password) => {
  const signInResponse = await signIn({
    username,
    password,
    options: {authFlowType: 'USER_PASSWORD_AUTH'},
  });
  console.log(
    '%c 🗒️: cognitoRequest -> signInResponse ',
    'font-size:16px;background-color:#1b81f2;color:white;',
    signInResponse
  );

  if (signInResponse.error) {
    return {
      type: 'ERROR',
      message: errorToMessage(signInResponse.error),
      error: signInResponse.error,
    };
  }

  if (signInResponse.isSignedIn) {
    return {type: 'SUCCESS', data: signInResponse};
  }

  const {signInStep} = signInResponse.nextStep;
  switch (signInStep) {
    case 'CONFIRM_SIGN_UP':
    case 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE':
      return {
        type: 'EMAIL_VERIFICATION_REQUIRED',
        data: signInResponse,
        username,
      };
    case 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED':
      return {
        type: 'NEW_PASSWORD_REQUIRED',
        data: signInResponse,
        username,
        message:
          '初めてのログイン時はパスワード変更が必要です。新しいパスワードを設定してください。',
      };
    case 'DONE':
      return {type: 'SUCCESS', data: signInResponse};
    default:
      throw new Error(`認証ステップが不明です: ${signInStep}`);
  }
};
