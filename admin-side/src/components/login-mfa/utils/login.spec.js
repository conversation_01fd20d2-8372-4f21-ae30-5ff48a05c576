import {describe, it, expect, vi, beforeEach} from 'vitest';
import {login, sendVerificationEmail} from './login';
import {signIn, resendSignUpCode} from 'aws-amplify/auth';

vi.mock('aws-amplify/auth', () => ({
  signIn: vi.fn(),
  confirmSignIn: vi.fn(),
  resendSignUpCode: vi.fn(),
}));

vi.mock('jwt-decode', () => ({
  jwtDecode: vi.fn(),
}));

describe('Authentication Functions', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('login', () => {
    it('should successfully authenticate a user with direct login flow', async () => {
      const username = 'testUser';
      const password = 'Password123';
      const mockSignInResponse = {
        isSignedIn: true,
        nextStep: {signInStep: 'DONE'},
      };
      signIn.mockResolvedValue(mockSignInResponse);
      const result = await login(username, password);
      expect(signIn).toHaveBeenCalledWith({
        username,
        password,
        options: {authFlowType: 'USER_PASSWORD_AUTH'},
      });
      expect(result).toEqual({
        type: 'SUCCESS',
        data: mockSignInResponse,
      });
    });

    it('should handle email verification requirement', async () => {
      const username = 'testUser';
      const password = 'Password123';
      const mockSignInResponse = {
        isSignedIn: false,
        nextStep: {
          signInStep: 'CONFIRM_SIGN_UP',
        },
      };
      signIn.mockResolvedValue(mockSignInResponse);
      const result = await login(username, password);
      expect(result).toEqual({
        type: 'EMAIL_VERIFICATION_REQUIRED',
        username: username,
        data: mockSignInResponse,
      });
    });
  });

  describe('sendVerificationEmail', () => {
    it('should successfully send a verification email', async () => {
      const username = 'testUser';
      resendSignUpCode.mockResolvedValue({});
      const result = await sendVerificationEmail(username);
      expect(resendSignUpCode).toHaveBeenCalledWith({username});
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    it('should handle errors when sending verification email', async () => {
      const username = 'testUser';
      const errorMessage = 'アカウントを認証できませんでした';
      resendSignUpCode.mockRejectedValue(new Error(errorMessage));
      const result = await sendVerificationEmail(username);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toBe(errorMessage);
    });
  });
});
