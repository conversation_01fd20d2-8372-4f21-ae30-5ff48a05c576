<template>
  <div class="bg-body-tertiary min-vh-100 d-flex flex-row align-items-center">
    <CContainer>
      <CRow class="justify-content-center">
        <CCol md="5">
          <CCard class="text-center">
            <CCardBody>
              <CCardTitle>メールでコードを確認する</CCardTitle>
              <CCardText class="mt-3"
                ><EMAIL>.jp宛に６文字のコードを送信しました。このコードの有効期間は短いため、すぐに入力してください。</CCardText
              >

              <!-- Input OTP: only input 1 character-->
              <div class="my-5 d-flex justify-content-center gap-2">
                <CFormInput
                  v-for="(x, i) in otp"
                  :ref="`otp${i}`"
                  :key="`otp${i}`"
                  style="
                    width: 50px;
                    height: 50px;
                    text-align: center;
                    font-size: 24px;
                  "
                  class="otp-input"
                  v-model="otp[i]"
                  @keypress.enter="
                    e => {
                      // focus next input
                      if (i < otp.length - 1) {
                        // get the next input element
                        $refs[`otp${i + 1}`]?.focus();
                      }
                    }
                  "
                  @input="
                    e => {
                      if (e.target.value.length > 1) {
                        e.target.value = e.target.value.slice(0, 1);
                      }
                      if (i < otp.length - 1 && e.target.value) {
                        // get the next input element
                        console.log('ref', $refs[`otp${i + 1}`]);
                        $refs[`otp${i + 1}`]?.[0]?.focus();
                      }
                    }
                  "
                  @keydown="
                    e => {
                      if (e.key === 'Backspace' && !otp[i]) {
                        if (i > 0) {
                          // get the previous input element
                          $refs[`otp${i - 1}`]?.focus();
                        }
                      }
                    }
                  "
                  :disabled="false"
                  :maxlength="1"
                  autocomplete="off"
                  :autofocus="i === 0"
                  type="text"
                  :pattern="'[0-9]*'"
                  inputmode="numeric"
                  :aria-invalid="false"
                  :aria-required="true"
                />
              </div>

              <!-- <CCardText class="text-danger">Invalid OTP. Please try again.</CCardText> -->

              <CCardText>
                <CRow class="d-flex justify-center align-items-center">
                  <CCol sm="4"> </CCol>
                  <CCol sm="4" class="d-grid">
                    <CButton color="primary" @click="verifyCode">確認</CButton>
                  </CCol>
                  <CCol sm="4"></CCol>
                </CRow>
              </CCardText>

              <!-- Hint message indicating this is a mock screen -->
              <CCardText class="mt-3 text-muted font-italic">
                <small
                  >※これはモック画面です。コードを入力せずに「確認」をクリックするだけで先に進めます。</small
                >
              </CCardText>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
    </CContainer>
  </div>
</template>
<script setup>
  defineOptions({name: 'MailOtpInput'});
  import {ref} from 'vue';
  import {useRouter} from 'vue-router';

  const router = useRouter();
  const otp = ref(['', '', '', '', '', '']); // Array to hold OTP digits

  const gotoDashboard = () => {
    // Navigate to the dashboard
    router.push({path: '/dashboard'});
  };

  const verifyCode = () => {
    // Simulate verification process
    if (otp.value.length === 6) {
      // Assuming the OTP is valid
      gotoDashboard();
    } else {
      alert('Invalid OTP. Please try again.');
    }
  };
</script>

<style scoped lang="css">
  .otp-input {
    width: 50px;
    height: 50px;
    text-align: center;
    font-size: 24px;
    margin: 0 5px;
  }

  .otp-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
  }

  .otp-input::placeholder {
    color: #ccc;
  }

  .otp-input:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
  }
</style>
