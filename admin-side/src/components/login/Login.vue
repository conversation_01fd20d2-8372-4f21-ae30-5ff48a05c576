<template>
  <CCard class="p-4">
    <CCardBody>
      <CForm @submit.prevent="handleLogin" v-if="!showNewPasswordForm">
        <h1 class="text-center">ログイン</h1>
        <div cols="10">
          <div v-if="loginError" class="text-danger mb-3">
            {{ loginError }}
          </div>
        </div>

        <CInputGroup class="mb-3">
          <CInputGroupText>
            <CIcon icon="cil-user" />
          </CInputGroupText>
          <CFormInput
            id="loginId"
            placeholder="ログインID"
            autocomplete="username"
            v-model="loginId"
            required
          />
        </CInputGroup>
        <CInputGroup class="mb-4">
          <CInputGroupText>
            <CIcon icon="cil-lock-locked" />
          </CInputGroupText>
          <CFormInput
            type="password"
            placeholder="パスワード"
            autocomplete="current-password"
            v-model="password"
            required
          />
        </CInputGroup>
        <CRow>
          <CCol md="4"></CCol>
          <CCol md="4" class="text-center d-grid">
            <CButton
              id="loginBtn"
              type="submit"
              color="primary"
              class="px-4"
              :disabled="loading"
            >
              <span v-if="loading">処理中...</span>
              <span v-else>ログイン</span>
            </CButton>
          </CCol>
          <CCol md="4"></CCol>
        </CRow>
        <CRow class="mt-4">
          <CCol class="text-center">
            <CLink
              @click="navigateToRegister"
              href="javascript:void(0)"
              class="text-primary"
            >
              新規登録はこちら
            </CLink>
          </CCol>
        </CRow>
      </CForm>

      <!-- New Password Form (shown when password change is required) -->
      <CForm @submit.prevent="changePassword" v-if="showNewPasswordForm">
        <h4>パスワード変更</h4>
        <p>初期パスワードを変更してください</p>
        <div cols="10">
          <div v-if="loginError" class="text-danger mb-3">
            {{ loginError }}
          </div>
        </div>
        <CInputGroup class="mb-3">
          <CInputGroupText>
            <CIcon icon="cil-lock-locked" />
          </CInputGroupText>
          <CFormInput
            type="password"
            placeholder="新しいパスワード"
            v-model="newPassword"
            required
          />
        </CInputGroup>

        <CInputGroup class="mb-4">
          <CInputGroupText>
            <CIcon icon="cil-lock-locked" />
          </CInputGroupText>
          <CFormInput
            type="password"
            placeholder="新しいパスワード（確認）"
            v-model="confirmNewPassword"
            required
          />
        </CInputGroup>

        <CButton
          type="submit"
          color="success"
          class="px-4 w-100 mb-3"
          :disabled="
            loading ||
            !newPassword ||
            !confirmNewPassword ||
            newPassword !== confirmNewPassword
          "
        >
          パスワードを変更する
        </CButton>
      </CForm>
    </CCardBody>
  </CCard>
</template>

<script setup>
  defineOptions({name: 'LoginComponent'});
  import {ref, onMounted} from 'vue';
  import {useRouter} from 'vue-router';
  import {useAuthStore} from '@/store/auth';

  const router = useRouter();
  const authStore = useAuthStore();

  const loginId = ref('');
  const password = ref('');
  const loading = ref(false);
  const loginError = ref('');

  const showNewPasswordForm = ref(false);
  const newPassword = ref('');
  const confirmNewPassword = ref('');

  const handleLogin = async () => {
    if (!loginId.value || !password.value) {
      loginError.value = 'ログインIDとパスワードを入力してください。';
      return;
    }
    loading.value = true;
    loginError.value = '';
    try {
      const result = await authStore.login(loginId.value, password.value);
      if (result.type === 'SUCCESS') {
        router.push('/dashboard');
      } else if (result.type === 'NEW_PASSWORD_REQUIRED') {
        loginError.value = result.message;
        showNewPasswordForm.value = true;
      } else {
        loginError.value = result.message || 'エラーが発生しました。';
      }
    } catch (error) {
      loginError.value =
        error.message || 'ログインに失敗しました。入力情報を確認してください。';
    } finally {
      loading.value = false;
    }
  };

  const changePassword = async () => {
    if (newPassword.value !== confirmNewPassword.value) {
      loginError.value = 'パスワードが一致しません。';
      return;
    }
    loading.value = true;
    loginError.value = '';
    try {
      await authStore.completeNewPassword(newPassword.value);
      router.push('/dashboard');
    } catch (error) {
      loginError.value = error.message || 'パスワードの変更に失敗しました。';
    } finally {
      loading.value = false;
    }
  };

  const focusLoginID = () => {
    document.getElementById('loginId')?.focus();
  };

  const navigateToRegister = () => {
    router.push('/pages/register');
  };

  onMounted(() => {
    if (!showNewPasswordForm.value) {
      focusLoginID();
    }
  });
</script>
