<template>
  <CInputGroup :class="wrapperClass">
    <CInputGroupText v-if="icon">
      <CIcon :icon="icon" />
    </CInputGroupText>
    <template v-if="type === 'select'">
      <CFormSelect
        :model-value="field.value"
        @update:model-value="field.handleChange"
        @blur="field.handleBlur"
        :invalid="state.meta.errors.length > 0"
        :placeholder="placeholder"
        class="flex-grow-1"
      >
        <option value="">{{ placeholder }}</option>
        <option v-for="opt in options" :key="opt.value" :value="opt.value">
          {{ opt.label }}
        </option>
      </CFormSelect>
    </template>
    <template v-else>
      <CFormInput
        :type="type"
        :model-value="field.value"
        @update:model-value="field.handleChange"
        @blur="field.handleBlur"
        :placeholder="placeholder"
        :invalid="state.meta.errors.length > 0"
        class="flex-grow-1"
      />
    </template>
    <CFormFeedback v-if="state.meta.errors.length > 0" invalid>
      {{ state.meta.errors[0] }}
    </CFormFeedback>
  </CInputGroup>
</template>

<script setup>
  defineOptions({name: 'FormField'});
  const props = defineProps({
    field: {type: Object, required: true},
    state: {type: Object, required: true},
    type: {type: String, default: 'text'},
    options: {type: Array, default: () => []},
    placeholder: {type: String, default: ''},
    icon: {type: String, default: ''},
    wrapperClass: {type: String, default: ''},
  });
</script>
