import './aws-config';

import {createApp} from 'vue';
import App from './App.vue';
import router from './router';

import {iconsSet as icons} from '@/assets/icons';
import CIcon from '@coreui/icons-vue';
import CoreuiVue from '@coreui/vue';
import ContextMenu from '@imengyu/vue3-context-menu';
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css';
import VCCountdown from '@vue-layout/countdown';
import {createPinia} from 'pinia';

const pinia = createPinia();
const app = createApp(App);
app.use(router);
app.use(CoreuiVue);
app.use(ContextMenu);
app.provide('icons', icons);
app.component('CIcon', CIcon);
app.use(VCCountdown);
app.use(pinia);

app.mount('#app');
