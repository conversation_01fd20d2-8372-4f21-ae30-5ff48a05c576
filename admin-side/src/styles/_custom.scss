// Here you can add other styles
.btn-info {
  color: #fff;

  &:hover {
    color: #fff;
  }
}

.btn-danger {
  color: #fff;

  &:hover {
    color: #fff;
  }
}

.btn-edit {
  color: #fff;
  background-color: #2eb85c;
  border-color: #2eb85c;

  &:hover {
    color: #fff;
    background-color: #4dc374;
    border-color: #4dc374;
    text-decoration: none;
  }
}

.btn-search {
  color: #fff;
  background-color: #39f;
  border-color: #39f;

  &:hover {
    color: #fff;
    background-color: #0d86ff !important;
    border-color: #0080ff;
    text-decoration: none;
  }
}

.btn-bounce-email {
  color: #fff !important;
  background-color: #01a7ac !important;
  border-color: #01a7ac !important;

  &:hover {
    color: #fff;
    background-color: #32b7bb;
    border-color: #32b7bb;
    text-decoration: none;
  }

  &:focus {
    color: #fff;
    background-color: #01a7ac !important;
    border-color: #01a7ac !important;
    text-decoration: none;
  }
}
.btn-success {
  color: #fff;
  background-color: #2eb85c;
  border-color: #2eb85c;

  &:hover {
    color: #fff;
    background-color: #4dc374;
    border-color: #4dc374;
    text-decoration: none;
  }

  &:focus {
    color: #fff;
    background-color: #2eb85c;
    border-color: #2eb85c;
    text-decoration: none;
  }
}

/* Download and Upload Buttons */
.btn-download-disabled {
  opacity: 0.65;
  cursor: not-allowed !important;
  color: #fff;
  background-color: #636f83 !important;
  border-color: #636f83 !important;

  &:hover {
    color: #fff;
    background-color: #636f83 !important;
    border-color: #636f83 !important;
    text-decoration: none;
  }

  &:focus {
    color: #fff;
    background-color: #636f83 !important;
    border-color: #636f83 !important;
    text-decoration: none;
  }
}

.btn-upload-disabled {
  opacity: 0.65;
  cursor: not-allowed !important;
  color: #fff;
  background-color: #636f83;
  border-color: #636f83;

  &:hover {
    color: #fff;
    background-color: #636f83;
    border-color: #636f83;
    text-decoration: none;
  }

  &:focus {
    color: #fff;
    background-color: #636f83 !important;
    border-color: #636f83 !important;
    text-decoration: none;
  }
}

.btn-upload-disabled:not(:disabled):not(.disabled):active,
.show > .btn-upload-disabled.dropdown-toggle {
  color: #fff;
  background-color: #636f83;
  border-color: #636f83;
}

.pt-not-allow {
  cursor: not-allowed !important;
}

.form-control[readonly] {
  background-color: #d8dbe0;
  opacity: 1;
  color: #768192;
}
