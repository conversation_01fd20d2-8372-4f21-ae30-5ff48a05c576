<template>
  <router-view />
</template>
<script setup>
  import {useColorModes} from '@coreui/vue';
  import {onBeforeMount} from 'vue';
  import {useCommonStore} from './store/common';

  const {isColorModeSet, setColorMode} = useColorModes(
    'coreui-free-vue-admin-template-theme'
  );
  const store = useCommonStore();

  onBeforeMount(() => {
    const urlParams = new URLSearchParams(window.location.href.split('?')[1]);
    const theme = urlParams.get('theme')?.match(/^[A-Za-z0-9\s]+/)[0];
    if (theme) {
      setColorMode(theme);
      return;
    }

    if (isColorModeSet()) {
      return;
    }

    setColorMode(store.theme);
  });
</script>

<style lang="scss">
  // Import Main styles for this application
  @use 'styles/style';
  // We use those styles to show code examples, you should remove them in your application.
  @use 'styles/examples';
</style>
