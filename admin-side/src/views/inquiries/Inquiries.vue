<template>
  <div class="mb-3">
    <CCard>
      <CCardHeader>
        <CRow>
          <CCol sm="5">
            <strong>検索条件</strong>
          </CCol>
          <CCol sm="1">
            <div class="spinner-grow text-primary" v-if="loading">
              <span class="visually-hidden"></span>
            </div>
          </CCol>
        </CRow>
      </CCardHeader>
      <CCardBody>
        <CForm onsubmit="return false;">
          <CRow>
            <CCol sm="2">
              <label>お問い合わせ日</label>
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                :ref="'stDate'"
                required
                v-model="search_condition.startDate"
                :invalid="!dateValidate.startDate"
                @change="
                  e => {
                    dateValidate.startDate = e.target.validity.valid;
                  }
                "
              />
            </CCol>
            <CCol
              sm="auto"
              class="px-0 d-flex justify-content-center align-items-center"
            >
              <label>〜</label>
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                :ref="'enDate'"
                required
                v-model="search_condition.endDate"
                :invalid="!dateValidate.endDate"
                @change="
                  e => {
                    dateValidate.endDate = e.target.validity.valid;
                  }
                "
              />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CRow>
          <CCol sm="5" />
          <CCol sm="2">
            <div class="d-grid gap-2">
              <CButton
                :class="search_condition.status ? 'btn-disable-cursor' : null"
                class="btn-bounce-email"
                @click="download"
                block
              >
                ダウンロード
              </CButton>
            </div>
          </CCol>
          <CCol sm="5" />
        </CRow>
      </CCardFooter>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="csvModal"
      @close="closeCsvModal"
    >
      <CModalHeader>
        <CModalTitle>{{ csvModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton v-if="!loading" @click="closeCsvModal" color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import {ScaleLoader} from '@/components/Table';
  import {
    CButton,
    CCard,
    CCardBody,
    CCardFooter,
    CCardHeader,
    CCol,
    CFormInput,
    CModal,
    CRow,
  } from '@coreui/vue';

  export default {
    name: 'inquiries',
    components: {
      ScaleLoader,
      CButton,
      CCard,
      CCardBody,
      CCardFooter,
      CCardHeader,
      CFormInput,
      CModal,
      CRow,
      CCol,
    },
    data() {
      return {
        loading: false,
        // Screen params
        search_condition: {
          startDate: '',
          endDate: '',
        },
        // Validation
        csvModal: false,
        csvModalTitle: '確認',
        validateResult: [],

        // Date validation
        dateValidate: {
          startDate: true,
          endDate: true,
        },
      };
    },
    methods: {
      getScreenCondition() {
        console.log('getScreenCondition');

        // Start date
        let start_date = '';
        if (this.dateValidate.startDate) {
          start_date = this.search_condition.startDate;
        } else {
          start_date = 'Invalid Date';
        }
        // End date
        let end_date = '';
        if (this.dateValidate.endDate) {
          end_date = this.search_condition.endDate;
        } else {
          end_date = 'Invalid Date';
        }

        const condition = {
          start_datetime: start_date,
          end_datetime: end_date,
        };

        return condition;
      },
      download() {
        console.log('download');
        this.loading = true;
        this.csvModal = true;
        this.csvModalTitle = '確認';
        this.validateResult = [];

        const condition = this.getScreenCondition();

        // Request to server
        Methods.apiExecute('export-inquiry-csv-file', condition)
          .then(response => {
            this.loading = false;
            this.csvModal = false;
            window.location.href = response.data.url;
          })
          .catch(error => {
            console.log('error', JSON.stringify(error));
            this.loading = false;
            this.csvModal = true;
            this.csvModalTitle = '入力エラー';
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            );
          });
      },
      closeCsvModal() {
        console.log('closeCsvModal');
        this.csvModal = false;
      },
    },
  };
</script>

<style lang="scss">
  .btn-disable {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
  }

  .btn-disable-cursor {
    cursor: default;
  }
</style>
