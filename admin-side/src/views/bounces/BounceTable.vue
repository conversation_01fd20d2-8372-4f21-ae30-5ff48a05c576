<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" />{{ caption }} </slot>
      <span style="float: right; margin-left: 30px"
        >総件数: {{ total_count }}件</span
      >
      <span style="float: right">検索結果: {{ current_count }}件</span>
    </CCardHeader>
    <CCardBody>
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :loading="loading"
        :sorter-value="itemsSorter"
        :items="items"
        :fields="fields"
        :items-per-page="10"
        :active-page="activePage"
        :pagination="{doubleArrows: false, align: 'center'}"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #no="{item}"
          ><td style="text-align: right">{{ item.no }}</td></template
        >
        <template #toAddress="{item}"
          ><td style="text-align: left; max-width: 400px">
            {{ item.toAddress }}
          </td></template
        >
        <template #subject="{item}"
          ><td style="text-align: left">{{ item.subject }}</td></template
        >
        <template #detail="{item}">
          <td>
            <CButton
              size="sm"
              color="success"
              style="width: 100%"
              @click="openMemberModal(item.toAddress)"
              >メールアドレス利用会員</CButton
            >
          </td>
        </template>
      </CDataTable>
    </CCardBody>
  </CCard>
</template>

<script>
  import {CDataTable} from '@/components/Table';
  import {CIcon} from '@coreui/icons-vue';
  import {CButton, CCard, CCardBody, CCardHeader} from '@coreui/vue';

  export default {
    name: 'BounceTable',
    components: {
      CIcon,
      CButton,
      CCardBody,
      CCard,
      CCardHeader,
      CDataTable,
    },
    props: {
      items: Array,
      current_count: {
        type: String,
        default: '0',
      },
      total_count: {
        type: String,
        default: '0',
      },
      fields: {
        type: Array,
        default() {
          return [
            {key: 'no', label: 'No', _classes: 'text-center'},
            {key: 'createDatetime', label: '発生日時', _classes: 'text-center'},
            {
              key: 'toAddress',
              label: 'メールアドレス',
              _classes: 'text-center',
            },
            {key: 'subject', label: '件名', _classes: 'text-center'},
            {key: 'detail', label: '明細', _classes: 'text-center'},
          ];
        },
      },
      caption: {
        type: String,
        default: 'adminTable',
      },
      loading: Boolean,
      itemsSorter: Object,
    },
    data() {
      return {
        activePage: 1,
      };
    },
    watch: {
      $route: {
        immediate: true,
        handler(route) {
          if (route.query?.page) {
            this.activePage = Number(route.query.page);
          }
        },
      },
    },
    methods: {
      pageChange(val) {
        this.$router.push({query: {page: val}});
      },
      openMemberModal(email) {
        this.$emit('handle-member-Modal', email);
      },
      sorterChange(val) {
        this.$emit('sorter-change', val);
      },
    },
  };
</script>
