<template>
  <CCard>
    <CCardBody>
      <CDataTable
        hover
        striped
        border
        sorter
        :items="items"
        :fields="fields"
        :items-per-page="10"
        :active-page="activePage"
        :pagination="{doubleArrows: false, align: 'center'}"
        @page-change="pageChange"
        :sorter-value="itemsSorter"
      >
        <template #No="{index}"
          ><td style="text-align: right">{{ index + 1 }}</td></template
        >
        <template #nickname="{item}"
          ><td style="text-align: left">{{ item.nickname }}</td></template
        >
        <template #username="{item}"
          ><td style="text-align: left">{{ item.username }}</td></template
        >
        <template #email="{item}"
          ><td style="text-align: left">{{ item.email }}</td></template
        >
        <template #tel="{item}"
          ><td style="text-align: left">{{ item.tel }}</td></template
        >
      </CDataTable>
    </CCardBody>
  </CCard>
</template>

<script>
  import {CDataTable} from '@/components/Table';
  import {CCard, CCardBody} from '@coreui/vue';

  export default {
    name: 'UseMemberTable',
    components: {
      CDataTable,
      CCardBody,
      CCard,
    },
    props: {
      items: Array,
      fields: {
        type: Array,
        default() {
          return [
            {key: 'No', label: 'No', _classes: 'text-center', width: '10%'},
            {
              key: 'nickname',
              label: '会社名',
              _classes: 'text-center',
              width: '30%',
            },
            {
              key: 'username',
              label: '担当者名',
              _classes: 'text-center',
              width: '30%',
            },
            {
              key: 'email',
              label: 'メールアドレス',
              _classes: 'text-center',
              width: '30%',
            },
            {
              key: 'tel',
              label: '電話番号',
              _classes: 'text-center',
              width: '30%',
            },
          ];
        },
      },
      caption: {
        type: String,
        default: 'adminTable',
      },
      loading: Boolean,
      itemsSorter: Object,
    },
    data() {
      return {
        activePage: 1,
      };
    },
    watch: {
      $route: {
        immediate: true,
        handler(route) {
          if (route.query?.page) {
            this.activePage = Number(route.query.page);
          }
        },
      },
    },
    methods: {
      pageChange(val) {
        this.$router.push({query: {page: val}});
      },
    },
  };
</script>
