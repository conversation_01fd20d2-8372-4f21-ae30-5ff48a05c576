<template>
  <div class="mb-3">
    <CCard>
      <CCardHeader>
        <strong>検索条件</strong>
      </CCardHeader>
      <CCardBody>
        <CForm onsubmit="return false;">
          <CRow class="mb-3">
            <CCol sm="2">
              <label>発生日</label>
            </CCol>
            <CCol sm="auto" class="pr-2">
              <CFormInput
                type="date"
                v-model="startDateTime"
                :invalid="!dateValidate.startDateTime"
                @change="
                  e => {
                    dateValidate.startDateTime = e.target.validity.valid;
                  }
                "
              />
            </CCol>
            <div
              style="
                flex: 0;
                padding-right: 0;
                padding-left: 0;
                margin-bottom: 0;
                transform: translateY(7px);
              "
            >
              <label>～</label>
            </div>
            <CCol sm="auto" class="pr-2">
              <CFormInput
                type="date"
                :ref="'edDate'"
                horizontal
                v-model="endDateTime"
                :invalid="!dateValidate.endDateTime"
                @change="
                  e => {
                    dateValidate.endDateTime = e.target.validity.valid;
                  }
                "
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> メールアドレス </CCol>
            <CCol sm="4">
              <CFormInput name="to_address" v-model="toAddress" />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="2"> 件名 </CCol>
            <CCol sm="4">
              <CFormInput name="subject" v-model="subject" />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CRow class="align-items-center">
          <CCol sm="5"></CCol>
          <CCol sm="2">
            <div class="d-grid gap-2">
              <CButton
                size="sm"
                color="info"
                @click="getBouncesFromServer"
                block
                :disabled="loading"
                >検索</CButton
              >
            </div>
          </CCol>
          <CCol sm="5"></CCol>
        </CRow>
      </CCardFooter>
    </CCard>
    <CRow class="mt-3">
      <CCol sm="12">
        <BounceTableWrapper
          name="bounceList"
          :items="bounceList"
          :totalCount="totalCount"
          :currentCount="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          caption="不達メール一覧"
          @handle-member-Modal="openUserMemberListModal"
          :itemsSorter="itemsSorter"
          @sorter-change="sorterChange"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      color="info"
      size="lg"
      :keyboard="false"
      :visible="useMemberListModal"
      @close="
        () => {
          useMemberListModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>利用会員一覧</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <UseMemberTableWrapper
          name="useMemberList"
          :items="useMemberList"
          hover
          striped
          border
          :loading="loading"
          caption="利用会員一覧"
          :itemsSorter="userSorter"
        />
      </CModalBody>
      <CModalFooter>
        <CButton @click="useMemberListModal = false" color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>エラー確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-for="text in errorMsg" :key="text">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="(errorModal = false), (errorMsg = '')" color="dark"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '@/store/common';
  import {onBeforeMount, onMounted, ref} from 'vue';
  import {useRouter} from 'vue-router';
  import BounceTableWrapper from './BounceTable.vue';
  import UseMemberTableWrapper from './UseMemberTable.vue';

  const store = useCommonStore();
  const router = useRouter();

  const loading = ref(true);
  const errorModal = ref(false);
  const bounceList = ref([]);
  const errorMsg = ref('');
  const useMemberListModal = ref(false);
  const useMemberList = ref([]);
  const toAddress = ref('');
  const startDateTime = ref('');
  const endDateTime = ref('');
  const subject = ref('');
  const itemsSorter = ref({asc: false, column: 'createDatetime'});
  const userSorter = ref({asc: true, column: 'companyName'});
  // Counting
  const currentCount = ref(0);
  const totalCount = ref(0);
  // Date validation
  const dateValidate = ref({
    startDateTime: true,
    endDateTime: true,
  });
  // Route
  const prevRoute = ref(null);

  onBeforeMount(() => {
    prevRoute.value = router.options.history?.state?.back;
    store.set(['itemsSorter', {asc: false, column: 'createDatetime'}]);
  });

  const getBouncesFromServer = async () => {
    bounceList.value = [];
    currentCount.value = 0;
    totalCount.value = 0;

    loading.value = true;
    const searchCondition = {
      toAddress: toAddress.value.trim(),
      startDateTime: startDateTime.value,
      endDateTime: endDateTime.value,
      subject: subject.value.trim(),
      startDateTimeFlag: dateValidate.value.startDateTime,
      endDateTimeFlag: dateValidate.value.endDateTime,
    };
    await Methods.apiExecute('get-email-notification-list', searchCondition)
      .then(response => {
        if (response.status === 200) {
          totalCount.value = Base.number2string(
            response.data ? response.data.totalCount || 0 : 0
          );
          currentCount.value = Base.number2string(
            response.data ? response.data.currentCount || 0 : 0
          );

          bounceList.value = [];
          let count = 1;
          response.data.data.map(email => {
            bounceList.value.push({
              no: count++,
              createDatetime: Methods.getFormatDateTime(email.create_datetime),
              toAddress: email.to_address,
              subject: email.subject,
            });
          });
          itemsSorter.value = store.itemsSorter;
          loading.value = false;
          console.log('bounceList.value: ', bounceList.value);
        }
      })
      .catch(error => {
        loading.value = false;
        errorModal.value = true;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
      });
  };

  const openUserMemberListModal = email => {
    loading.value = true;
    const request = {
      toAddress: email,
    };
    Methods.apiExecute('get-use-bounces-list', request)
      .then(response => {
        useMemberList.value = [];
        useMemberListModal.value = true;
        if (response.status === 200) {
          loading.value = false;
          response.data.map(user => {
            useMemberList.value.push({
              nickname: user.nickname,
              username: user.username,
              email: user.email,
              tel: `${user.telcountrycode ?? ''}${user.tel}`,
            });
          });
        }
      })
      .catch(error => {
        loading.value = false;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
        errorModal.value = true;
      });
  };

  const sorterChange = val => {
    itemsSorter.value = val;
    store.set(['itemsSorter', val]);
  };

  onMounted(() => {
    getBouncesFromServer();
  });
</script>
