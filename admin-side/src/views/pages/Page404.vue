<template>
  <CContainer class="d-flex align-items-center min-vh-100">
    <CRow class="w-100 justify-content-center">
      <CCol md="6">
        <div class="w-100">
          <div class="clearfix">
            <h4 class="text-center">ページが見つかりません。</h4>
            <br />
            <div class="text-center">
              <CButton color="secondary" @click="gotoDashboard()"
                >出展画面へ</CButton
              >
            </div>
          </div>
        </div>
      </CCol>
    </CRow>
  </CContainer>
</template>

<script setup>
  import {CButton, CCol, CContainer, CRow} from '@coreui/vue';
  import {useRouter} from 'vue-router';
  const router = useRouter();
  const gotoDashboard = () => {
    if (router.currentRoute.name !== 'lots') {
      router.push('/exhibitions');
    }
  };
</script>
