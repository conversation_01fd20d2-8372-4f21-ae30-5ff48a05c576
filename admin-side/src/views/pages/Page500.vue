<template>
  <CContainer class="d-flex align-items-center min-vh-100">
    <CRow class="w-100 justify-content-center">
      <CCol md="6">
        <div class="clearfix">
          <h2 class="text-center">エラーが発生しました。</h2>
          <h6 class="text-center">管理者までご連絡ください。</h6>
          <br />
          <div class="text-center">
            <CButton color="secondary" @click="gotoLogin()"
              >ログインページに戻る</CButton
            >
          </div>
        </div>
      </CCol>
    </CRow>
  </CContainer>
</template>

<script>
  import {CButton, CCol, CContainer, CRow} from '@coreui/vue';
  export default {
    name: 'Page500',
    components: {CContainer, CCol, CRow, CButton},
    methods: {
      gotoLogin() {
        if (this.$router.currentRoute.name !== 'Login') {
          this.$router.push('/');
        }
      },
    },
  };
</script>
