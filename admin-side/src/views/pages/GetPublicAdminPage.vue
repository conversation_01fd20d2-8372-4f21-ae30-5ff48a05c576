<template>
  <div class="bg-body-tertiary min-vh-100 d-flex flex-row align-items-center">
    <CContainer>
      <CRow class="justify-content-center">
        <CCol md="12">
          <CCard class="mx-4">
            <CCardBody class="p-4">
              <h1>Public Admin Page</h1>
              <p class="text-medium-emphasis">
                Testing tenant identification by domain
              </p>

              <div v-if="loading" class="text-center">
                <CSpinner color="primary" />
                <p>Loading tenant data...</p>
              </div>

              <CAlert v-if="error" color="danger">
                {{ error }}
              </CAlert>

              <div v-if="tenantData" class="my-4">
                <h4>Detected Tenant Information</h4>

                <CListGroup>
                  <CListGroupItem>
                    <strong>Tenant ID:</strong> {{ tenantData?.tenantId }}
                  </CListGroupItem>

                  <CListGroupItem>
                    <strong>Hostname:</strong> {{ hostname }}
                  </CListGroupItem>

                  <CListGroupItem>
                    <strong>Is Custom Domain:</strong>
                    {{ isCustomDomain ? 'Yes' : 'No' }}
                  </CListGroupItem>
                </CListGroup>

                <div class="mt-4">
                  <h5>Raw Response Data:</h5>
                  <pre class="bg-light p-3">{{
                    JSON.stringify(tenantData, null, 2)
                  }}</pre>
                </div>
              </div>

              <div class="d-grid gap-2">
                <CButton color="primary" @click="fetchTenantData">
                  Refresh Data
                </CButton>
                <CButton color="secondary" @click="goToHomepage">
                  ホームページに戻る
                </CButton>
              </div>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
    </CContainer>
  </div>
</template>

<script setup>
  import {ref, onMounted} from 'vue';
  import Methods from '@/api/methods';
  import {useRouter} from 'vue-router';
  defineOptions({name: 'GetPublicAdminPage'});

  const router = useRouter();

  const loading = ref(false);
  const error = ref(null);
  const tenantData = ref(null);
  const hostname = ref(window.location.hostname);
  const isCustomDomain = ref(
    hostname.value !== 'localhost' && !hostname.value.includes('cloudfront.net')
  );

  const fetchTenantData = async () => {
    loading.value = true;
    error.value = null;

    try {
      const response = await Methods.apiExecute(
        'get-public-admin-page',
        {
          timestamp: new Date().getTime(),
        },
        {},
        true // isPublic flag
      );
      tenantData.value = response.data;
      console.log('Tenant data:', response.data);
    } catch (err) {
      console.error('Error fetching tenant data:', err);
      error.value = `Failed to fetch tenant data: ${err.message || 'Unknown error'}`;
    } finally {
      loading.value = false;
    }
  };

  const goToHomepage = () => {
    router.push('/');
  };

  // Lifecycle
  onMounted(() => {
    fetchTenantData();
  });
</script>
