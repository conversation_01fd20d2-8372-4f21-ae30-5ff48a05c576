<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" /> {{ caption }} </slot>
      <span name="total-count" style="float: right; margin-left: 30px"
        >総件数: {{ total_count }}件</span
      >
      <span name="current-count" style="float: right"
        >検索結果: {{ current_count }}件</span
      >
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :items="items"
        :fields="fields"
        :loading="loading"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        :itemsPerPageSelect="itemsPerPageSelect"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #no="{item, index}"
          ><td style="text-align: right; width: 5%">
            {{ index + 1 }}
          </td></template
        >
        <template #send_datetime="{item}"
          ><td style="width: 13%; text-align: center">
            {{ item.send_datetime }}
          </td></template
        >
        <template #sent_flag_name="{item}"
          ><td style="width: 10%">{{ item.sent_flag_name }}</td></template
        >

        <template #title="{item}"
          ><td style="width: 10%">{{ item.title }}</td></template
        >
        <template #body="{item}">
          <td>
            <div
              v-html="htmlRemoveAllImage(item.body)"
              style="
                overflow: hidden;
                text-overflow: ellipsis;
                display: block;
                max-height: 3.6em;
                line-height: 1.2em;
                word-break: break-all;
                white-space: pre-line;
              "
            ></div>
          </td>
        </template>
        <template #file="{item}">
          <td
            v-if="
              typeof item.file !== 'undefined' &&
              item.file !== null &&
              Object.keys(item.file).length > 0
            "
            style="width: 120px"
            class="text-center"
          >
            <CButton size="sm" block color="info" @click="viewFile(item)"
              >表示する</CButton
            >
          </td>
          <td v-else></td>
        </template>
        <template #btn_edit="{item}">
          <td style="width: 70px" class="text-center">
            <CButton
              v-if="item.sent_flag"
              size="sm"
              block
              color="secondary"
              @click="editNotice(item)"
              >参照</CButton
            >
            <CButton
              v-else
              size="sm"
              block
              color="edit"
              :disabled="isReadOnly"
              @click="editNotice(item)"
              >編集</CButton
            >
          </td>
        </template>
        <template #btn_delete="{item}">
          <td style="width: 70px" class="text-center">
            <CButton
              v-if="!item.sent_flag"
              size="sm"
              block
              color="danger"
              :disabled="isReadOnly"
              @click="openDeleteModal(item)"
              >削除</CButton
            >
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
    </CCardBody>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="localDeleteModal"
      @close="closeDeleteModal"
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loadingDelete">
          お知らせメールを削除してもよろしいですか？
        </div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loadingDelete"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="closeDeleteModal"
          color="dark"
          :disabled="loadingDelete"
          >キャンセル</CButton
        >
        <CButton @click="deleteNotice" color="primary" :disabled="loadingDelete"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </CCard>
</template>

<script>
  import {CDataTable, CPagination, ScaleLoader} from '@/components/Table';
  import {itemsPerPageSelect} from '@/views/common/customTableView.js';
  import {CIcon} from '@coreui/icons-vue';
  import {CButton, CCard, CCardBody, CCardHeader, CModal} from '@coreui/vue';
  import {useAuthStore} from '@/store/auth';

  export default {
    name: 'noticeTable',
    components: {
      ScaleLoader,
      CButton,
      CCard,
      CCardBody,
      CCardHeader,
      CDataTable,
      CModal,
      CPagination,
      CIcon,
    },
    setup() {
      const {isReadOnly} = useAuthStore();
      return {
        itemsPerPageSelect,
        isReadOnly,
      };
    },
    props: {
      items: Array,
      current_count: {
        type: String,
        default: '0',
      },
      total_count: {
        type: String,
        default: '0',
      },
      fields: {
        type: Array,
        default() {
          return [
            {key: 'no', label: 'No', _classes: 'text-center', width: '20%'},
            {
              key: 'send_datetime',
              label: '送信日時',
              _classes: 'text-center',
              width: '20%',
            },
            {
              key: 'sent_flag_name',
              label: 'ステータス',
              width: '20%',
              _classes: 'text-center',
            },
            {
              key: 'title',
              label: 'タイトル',
              _classes: 'text-center',
              width: '30%',
            },
            {key: 'body', label: '本文', _classes: 'text-center', width: '30%'},
            {
              key: 'file',
              label: '添付ファイル',
              _classes: 'text-center',
              width: '10%',
              sorter: false,
            },
            {
              key: 'btn_edit',
              label: '詳細',
              _classes: 'text-center',
              width: '10%',
            },
            {
              key: 'btn_delete',
              label: '削除',
              _classes: 'text-center',
              width: '10%',
            },
          ];
        },
      },
      caption: {
        type: String,
        default: 'noticeTable',
      },
      hover: Boolean,
      striped: Boolean,
      border: Boolean,
      small: Boolean,
      fixed: Boolean,
      dark: Boolean,
      loading: Boolean,
      loadingDelete: Boolean,
      deleteModal: Boolean,
      activePage: Number,
      itemsPerPage: Number,
      pages: Number,
      itemsSorter: Object,
    },
    data() {
      return {
        deleteItem: null,
        localDeleteModal: false,
        btn_clicked: false,
      };
    },
    watch: {
      deleteModal(newVal, oldVal) {
        console.log('items changed: ', newVal, ' | was: ', oldVal);
        this.localDeleteModal = newVal;
      },
    },
    methods: {
      editNotice(item) {
        console.log(`edite notice: ${JSON.stringify(item)}`);
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.$router.push({
          path: `noticeMails/notice-mail/${item.notice_email_no}`,
        });
      },
      deleteNotice() {
        console.log(`deleteNotice: ${JSON.stringify(this.deleteItem)}`);
        this.btn_clicked = false;
        // This.deleteModal = false
        if (
          typeof this.deleteItem !== 'undefined' &&
          this.deleteItem !== null
        ) {
          this.$emit('delete', this.deleteItem.notice_email_no);
          this.deleteItem = null;
        }
      },
      viewFile(item) {
        console.log(`viewFile: ${JSON.stringify(item)}`);
        this.$emit('viewFile', item.file);
      },
      openDeleteModal(item) {
        console.log(`openDeleteModal: ${JSON.stringify(item)}`);
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.deleteItem = item;
        this.$emit('onOpenDeleteModal');
      },
      closeDeleteModal() {
        this.btn_clicked = false;
        this.$emit('onCloseDeleteModal');
      },
      htmlRemoveAllImage(body) {
        return body ? body.replace(/<img[^>]*>/g, '') : '';
      },
      pageChange(val) {
        if (this.items.length > 0) {
          this.$emit('page-change', val);
        }
      },
      sorterChange(val) {
        this.$emit('sorter-change', val);
      },
      paginationChange(val) {
        this.$emit('pagination-change', val);
      },
    },
  };
</script>
