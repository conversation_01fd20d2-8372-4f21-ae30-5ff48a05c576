<template>
  <div class="mb-3">
    <CCard>
      <CCardHeader>
        <strong>検索条件</strong>
      </CCardHeader>
      <CCardBody>
        <CForm onsubmit="return false;">
          <CRow class="mb-3">
            <CCol sm="2">
              <label>ステータス</label>
            </CCol>
            <CCol sm="9">
              <CFormCheck
                v-for="option in options_sent_flag"
                :key="option.value"
                :id="option.value"
                :value="option.value"
                :label="option.label"
                v-model="search_condition.sent_flag"
                :inline="true"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2">
              <label>タイトル</label>
            </CCol>
            <CCol sm="9">
              <CFormInput
                name="title"
                class="mb-0"
                v-model="search_condition.title"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="2">
              <label>送信日</label>
            </CCol>
            <CCol sm="auto" class="pr-2">
              <CFormInput
                :ref="'stDate'"
                type="date"
                horizontal
                v-model="search_condition.startDate"
                :invalid="!dateValidate.startDate"
                @change="
                  e => {
                    dateValidate.startDate = e.target.validity.valid;
                  }
                "
              />
            </CCol>
            <CCol
              sm="auto"
              class="px-0 d-flex justify-content-center align-items-center"
            >
              <label>～</label>
            </CCol>
            <CCol sm="auto" class="pr-2">
              <CFormInput
                :ref="'enDate'"
                type="date"
                horizontal
                v-model="search_condition.endDate"
                :invalid="!dateValidate.endDate"
                @change="
                  e => {
                    dateValidate.endDate = e.target.validity.valid;
                  }
                "
              />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CRow class="align-items-center">
          <CCol xs="5" class="mb-3 mb-xl-0 text-right"></CCol>
          <CCol xs="2" class="mb-3 mb-xl-0 text-right">
            <div class="d-grid gap-2">
              <CButton size="sm" color="info" @click="search" block>
                検索
              </CButton>
            </div>
          </CCol>
          <CCol xs="1" class="mb-3 mb-xl-0 text-right"></CCol>
          <CCol xs="2" class="mb-3 mb-xl-0 text-right"></CCol>
          <CCol xs="2" class="mb-3 mb-xl-0 text-right">
            <div class="d-grid gap-2">
              <CButton
                size="sm"
                color="primary"
                @click="addNewNotice"
                block
                :disabled="isReadOnly"
                >新規登録</CButton
              >
            </div>
          </CCol>
        </CRow>
      </CCardFooter>
    </CCard>
    <CRow class="mt-3">
      <CCol sm="12">
        <CTableWrapper
          :items="noticeList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          :loadingDelete="loadingDelete"
          :deleteModal="deleteModal"
          caption="お知らせメール一覧"
          @delete="deleteNotice"
          @viewFile="openViewFileModal"
          @onOpenDeleteModal="deleteModal = true"
          @onCloseDeleteModal="deleteModal = false"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="fileViewModal"
      @close="closeViewFileModal"
    >
      <CModalHeader>
        <CModalTitle>添付ファイル一覧</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div class="mb-2">
          <CRow>
            <CCol v-for="(fLang, i) in s3urlList" :key="i">
              <div class="ml-1">{{ language_options[i] }}</div>
              <CDataTable
                :items="fLang"
                :fields="[{key: 'fname', label: language_options[i]}]"
                :header="false"
              >
                <template #fname="{item}">
                  <a
                    style="line-height: 2"
                    @contextmenu.prevent="onContextMenuOpen($event, item)"
                    @click="fileNameClickEvent(item)"
                    href="javascript:void(0);"
                  >
                    {{ item.substring(item.lastIndexOf('/') + 1, item.length) }}
                  </a>
                </template>
              </CDataTable>
            </CCol>
          </CRow>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="closeViewFileModal" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="validateModal"
      @close="
        () => {
          validateModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入力エラー</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="validateModal = false" color="dark" :disabled="loading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <ContextMenu v-model:show="contextMenu.show" :options="contextMenu.options">
      <context-menu-item label="ファイルを開く" @click="getFileViewUrl" />
      <context-menu-item label="ダウンロード" @click="getFileDownloadUrl" />
    </ContextMenu>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import DownloadFile from '@/api/uploadFileToS3';
  import Base from '@/common/base';
  import {CDataTable} from '@/components/Table';
  import {useCommonStore} from '@/store/common';
  import {ContextMenu, ContextMenuItem} from '@imengyu/vue3-context-menu';
  import useFileDownload from '../../common/useFileDownload';
  import CTableWrapper from './noticeTable.vue';
  import {useAuthStore} from '@/store/auth';

  export default {
    name: 'noticeMailList',
    components: {
      CTableWrapper,
      ContextMenu,
      ContextMenuItem,
      CDataTable,
    },
    setup() {
      const store = useCommonStore();
      const {download} = useFileDownload();
      const {isReadOnly} = useAuthStore();
      return {download, store, isReadOnly};
    },
    data() {
      return {
        // 検索条件
        language_options: {ja: '日本語', en: '英語'},
        options_sent_flag: [
          {value: '0', label: '未送信'},
          {value: '1', label: '送信済み'},
        ],
        loading: true,
        // 削除用変数
        loadingDelete: false,
        deleteModal: false,
        // Download modal
        fileViewModal: false,
        s3urlList: [],

        // Screen params
        noticeList: [],
        search_condition: {
          sent_flag: ['0', '1'],
          startDate: '',
          endDate: '',
        },
        activePage: 1,
        itemsPerPage: 10,
        pages: 1,
        itemsSorter: {asc: false, column: 'send_datetime'},

        // Validation
        validateModal: false,
        validateResult: [],

        // Counting
        current_count: 0,
        total_count: 0,

        // Date validation
        dateValidate: {
          startDate: true,
          endDate: true,
        },

        // Context menu
        contextMenu: {
          show: false,
          options: {
            zIndex: 9999,
            minWidth: 230,
            x: 500,
            y: 500,
          },
          data: null,
        },
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;
        console.log(vm.prevRoute.name);
        // 登録・編集画面からの遷移時のみ検索条件を保持する
        if (
          vm.prevRoute.name === 'お知らせメール編集' ||
          vm.prevRoute.name === 'お知らせメール登録'
        ) {
          vm.search_condition = vm.store.noticesSearchCondition;
        } else {
          // それ以外は初期化
          vm.store.set([
            'noticesSearchCondition',
            {sent_flag: ['0', '1'], startDate: '', endDate: ''},
          ]);
          vm.store.set(['activePage', 1]);
          vm.store.set(['itemsPerPage', 10]);
          vm.store.set(['itemsSorter', {asc: false, column: 'send_datetime'}]);
        }
      });
    },
    mounted() {
      console.log('mounted');
      this.getConstantsData()
        .then(() => {
          this.getNotifyData();
        })
        .catch(error => {
          console.log(JSON.stringify(error));
          this.loading = false;
          Methods.parseHtmlResponseError(this.$router, error);
        });
    },
    watch: {
      $route: {
        immediate: true,
        handler(route) {
          if (route.query?.page) {
            this.activePage = Number(route.query.page);
          }
        },
      },
      search_condition: {
        handler(newVal) {
          this.store.set(['noticesSearchCondition', newVal]);
        },
        deep: true,
        immediate: false,
      },
      itemsPerPage(newVal) {
        if (this.noticeList && this.noticeList.length > newVal) {
          this.pages =
            Number.parseInt(this.noticeList.length / newVal, 10) +
            (this.noticeList.length % newVal > 0 ? 1 : 0);
        } else {
          this.pages = 1;
        }
      },
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
    },
    methods: {
      getScreenCondition() {
        console.log('getScreenCondition');

        // Start date
        let start_date = '';
        if (this.dateValidate.startDate) {
          start_date = this.search_condition.startDate;
        } else {
          start_date = 'Invalid Date';
        }
        // End date
        let end_date = '';
        if (this.dateValidate.endDate) {
          end_date = this.search_condition.endDate;
        } else {
          end_date = 'Invalid Date';
        }

        const search_condition = {
          sent_flag: this.search_condition.sent_flag,
          title: this.search_condition.title,
          start_datetime: start_date,
          end_datetime: end_date,
        };

        return search_condition;
      },
      async getNotifyData() {
        console.log('getNotifyData');
        this.loading = true;
        this.validateResult = [];
        this.noticeList = [];
        const search_condition = this.getScreenCondition();
        await this.getNoticesFromServer(search_condition).then(notices => {
          this.noticeList = notices;

          // 登録・編集画面からの遷移時は閲覧してたページに戻す
          if (
            this.prevRoute &&
            (this.prevRoute.name === 'お知らせメール編集' ||
              this.prevRoute.name === 'お知らせメール登録')
          ) {
            this.itemsPerPage = this.store.itemsPerPage;
            this.itemsSorter = this.store.itemsSorter;
          }
          this.pages =
            Number.parseInt(this.noticeList.length / this.itemsPerPage, 10) +
            (this.noticeList.length % this.itemsPerPage > 0 ? 1 : 0);
          this.activePage =
            this.store.activePage > this.pages
              ? Number(this.pages)
              : this.store.activePage;
          this.$router.push({query: {page: this.activePage}}).catch(() => {});
          return Promise.resolve();
        });
      },
      async search() {
        console.log('search');
        this.loading = true;
        this.validateResult = [];
        this.noticeList = [];
        const search_condition = this.getScreenCondition();
        await this.getNoticesFromServer(search_condition).then(notices => {
          this.noticeList = notices;

          this.pages =
            Number.parseInt(this.noticeList.length / this.itemsPerPage, 10) +
            (this.noticeList.length % this.itemsPerPage > 0 ? 1 : 0);
          this.sorterChange({asc: false, column: 'send_datetime'});
        });
      },
      getConstantsData() {
        const request = {
          key_strings: ['LANGUAGE_CODE'],
        };
        // Request to server
        return Methods.apiExecute(
          'get-constants-by-keys-language',
          request
        ).then(response => {
          if (response.status === 200) {
            this.language_options = {};
            for (const constant of response.data) {
              this.language_options[constant.value1] = constant.value2;
            }
          }
          return Promise.resolve();
        });
      },
      getNoticesFromServer(search_condition) {
        console.log('getNoticesFromServer');

        this.current_count = 0;
        this.total_count = 0;

        // Request to server
        return Methods.apiExecute('get-notice-email-list', search_condition)
          .then(response => {
            if (response.status === 200) {
              this.loading = false;
              this.compModal = true;
              const noticeList = response.data;
              console.log('noticeList = ', noticeList);

              this.total_count = noticeList ? noticeList.total_count || 0 : 0;

              if (typeof noticeList === 'undefined' || noticeList === null) {
                return Promise.resolve([]);
              }
              // Processing data
              const tmpMap = new Map();
              for (const noti of noticeList.data) {
                const tmp1 = tmpMap.get(String(noti.notice_email_no));
                if (tmp1) {
                  if (tmp1.language_code !== noti.language_code) {
                    tmp1[`body_${noti.language_code}`] = noti.body;
                    // Tmp1["file_" + noti.language_code] = noti.file
                    if (
                      typeof noti.file !== 'undefined' &&
                      noti.file !== null &&
                      noti.file.length > 0
                    ) {
                      tmp1.file[noti.language_code] = noti.file;
                    }
                  }
                } else {
                  noti[`body_${noti.language_code}`] = noti.body;
                  // Noti["file_" + noti.language_code] = noti.file
                  const tmpFi = {};
                  if (
                    typeof noti.file !== 'undefined' &&
                    noti.file !== null &&
                    noti.file.length > 0
                  ) {
                    tmpFi[noti.language_code] = noti.file;
                  }
                  noti.file = tmpFi;
                  tmpMap.set(String(noti.notice_email_no), noti);
                }
              }
              console.log(`tmpMap = ${JSON.stringify(tmpMap)}`);

              const finalList = Array.from(tmpMap.values());
              // Add row number
              for (let i = 0; i < finalList.length; i++) {
                finalList[i].no = i + 1;
              }
              console.log(`finalList = ${JSON.stringify(finalList)}`);

              this.current_count = finalList ? finalList.length : 0;

              return Promise.resolve(finalList);
            }
            return Promise.resolve([]);
          })
          .catch(error => {
            console.log('error: ', error);
            this.loading = false;
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            );
            this.openValidateModal();
            return Promise.resolve([]);
          });
      },
      addNewNotice() {
        console.log('addNewNotice');
        this.$router.push({path: '/noticeMails/new'});
      },
      deleteNotice(notice_email_no) {
        console.log(`deleteNotice: ${JSON.stringify(notice_email_no)}`);
        this.loadingDelete = true;
        this.validateModal = false;
        // Request to server
        Methods.apiExecute('delete-notice-email', {
          notice_email_no,
        })
          .then(response => {
            console.log(`response = ${JSON.stringify(response)}`);
            this.loadingDelete = false;
            this.deleteModal = false;
            if (response.status === 200) {
              this.loading = false;
              this.search();
            }
          })
          .catch(error => {
            console.log(error);
            this.loading = false;
            this.loadingDelete = false;
            this.deleteModal = false;
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            );
            this.validateModal = true;
          });
      },
      openViewFileModal(files) {
        console.log('openViewFileModal');
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.s3urlList = files;
        this.fileViewModal = true;
      },
      getFileDownloadUrl() {
        console.log('getFileDownloadUrl');
        // Get download url from file server
        const fileUrl = this.contextMenu.data;
        DownloadFile.getDownloadUrl(fileUrl).then(res => {
          console.log(`res: ${JSON.stringify(res)}`);
          this.download(res, Base.getFileName(fileUrl));
        });
      },
      getFileViewUrl() {
        console.log('getFileViewUrl');
        // Get file viewing url from file server
        const fileUrl = this.contextMenu.data;
        DownloadFile.getFileViewUrl(fileUrl).then(res => {
          console.log(`res: ${JSON.stringify(res)}`);
          window.open(res, '_blank');
        });
      },
      fileNameClickEvent(fileUrl) {
        console.log('fileNameClickEvent');
        // Filename click event handler
        if (fileUrl !== null && fileUrl !== '') {
          DownloadFile.getFile(fileUrl).then(res => {
            console.log(`res: ${JSON.stringify(res)}`);
            window.open(res, '_blank');
          });
        }
      },
      closeViewFileModal() {
        this.btn_clicked = false;
        this.fileViewModal = false;
        this.s3urlList = [];
      },
      openValidateModal() {
        this.validateModal = true;
      },
      pageChange(val) {
        this.store.set(['activePage', val]);
        this.$router.push({query: {page: val}}).catch(() => {});
      },
      paginationChange(val) {
        this.itemsPerPage = val;
        this.store.set(['itemsPerPage', val]);
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
        this.pageChange(1);
      },
      onContextMenuOpen(e, item) {
        this.contextMenu.show = true;
        this.contextMenu.options.x = e.x;
        this.contextMenu.options.y = e.y;
        this.contextMenu.data = item;
      },
    },
  };
</script>
