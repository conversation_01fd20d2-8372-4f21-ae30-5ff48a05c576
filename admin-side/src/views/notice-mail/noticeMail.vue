<template>
  <CCard class="mb-3">
    <CCardHeader class="form-inline">
      <label
        ><strong
          >お知らせメール{{ this.$route.params.id ? '編集' : '登録' }}</strong
        ></label
      >
      <div style="margin-left: 10px">
        <scale-loader
          :loading="loading"
          :color="color"
          :height="height"
          :width="width"
        ></scale-loader>
      </div>
    </CCardHeader>
    <CCardBody>
      <CForm onsubmit="return false;">
        <CRow class="mb-3">
          <CCol sm="2" class="d-flex align-items-center">
            <label>送信日時</label>
            <CBadge color="danger" class="ms-auto">必須</CBadge>
          </CCol>
          <CCol sm="auto">
            <CFormInput
              ref="sendDate"
              type="date"
              horizontal
              v-model="sendDate"
              :disabled="isBtnDisabled"
              :invalid="!dateValidate.sendDate"
              :max="maxDate"
              @change="
                e => {
                  dateValidate.sendDate = e.target.validity.valid;
                }
              "
            />
          </CCol>
          <CCol sm="auto">
            <CFormInput
              :ref="'sendTime'"
              type="time"
              horizontal
              v-model="sendTime"
              :disabled="isBtnDisabled"
              :invalid="!dateValidate.sendTime"
              @change="
                e => {
                  dateValidate.sendTime = e.target.validity.valid;
                }
              "
            >
            </CFormInput>
          </CCol>
        </CRow>
        <div v-for="lang in languages" :key="lang.code">
          <CRow class="mb-3" v-if="languages.length > 1">
            <h6>【{{ lang.name }}欄】</h6>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="d-flex align-items-center">
              <label>タイトル</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="9">
              <CFormInput
                type="text"
                v-model="title[lang.code]"
                :disabled="isBtnDisabled"
                @change="editorTextChange(lang.code)"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="d-flex align-items-start">
              <label>本文</label>
              <label class="ms-auto">
                <CBadge color="danger">必須</CBadge>
              </label>
            </CCol>
            <CCol sm="9">
              <CFormTextarea
                v-model="body[lang.code]"
                class="col-sm-9"
                rows="10"
                :disabled="isBtnDisabled"
                @change="editorTextChange(lang.code)"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2">
              <label>添付ファイル</label>
            </CCol>
            <CCol sm="9">
              <FileSelection
                :ref="'fileUpload_' + lang.code"
                :key="lang.code"
                :items="file[lang.code]"
                :isBtnDisabled="isBtnDisabled"
                @onFilesChange="onFileCompChange($event, lang.code)"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="5" />
            <CCol sm="2">
              <CButton
                name="btnTestSendEmail"
                size="sm"
                color="info"
                :disabled="isBtnDisabled"
                :class="isBtnDisabled ? 'pt-not-allow' : ''"
                @click="btnTestSendEmailClicked(lang.code)"
                block
                >テストメール送信
              </CButton>
            </CCol>
            <CCol sm="5" />
          </CRow>
          <br />
        </div>
      </CForm>
      <CElementCover v-if="loading" :opacity="0.8" />
    </CCardBody>
    <CCardFooter>
      <CButton class="mx-1" color="secondary" @click="goBack"
        >登録を中止して一覧に戻る</CButton
      >
      <CButton
        class="mx-1"
        color="primary"
        @click="openRegistModal"
        :disabled="isBtnDisabled"
        :class="isBtnDisabled ? 'pt-not-allow' : ''"
        >{{ this.$route.params.id ? '更新' : '登録' }}する</CButton
      >
      <CButton
        color="danger"
        class="float-right"
        @click="openDeleteModal"
        v-if="$route.params.id"
        :disabled="isBtnDisabled"
        :class="isBtnDisabled ? 'pt-not-allow' : ''"
        >削除</CButton
      >
      <CElementCover v-if="loading" :opacity="0.8" />
    </CCardFooter>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="registModal"
      @close="
        () => {
          registModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{ registModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loading && validateResult.length === 0">
          この内容で{{
            this.$route.params.id ? '更新' : '登録'
          }}してもよろしいですか？
        </div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="registModal = false"
          color="dark"
          :disabled="loading"
          v-if="validateResult.length === 0"
          >キャンセル
        </CButton>
        <CButton @click="btnRegistClicked" color="primary" :disabled="loading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入力エラー</CModalTitle>
      </CModalHeader>
      <CModalBody> データの取得が失敗しました！ </CModalBody>
      <CModalFooter>
        <CButton @click="errorModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="compModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
          btn_clicked = false;
          next(false);
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{
          (this.$route.params.id ? '編集' : '登録') + '中止確認'
        }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            cancelModal = false;
            btn_clicked = false;
            next(false);
          "
          color="dark"
          >キャンセル</CButton
        >
        <CButton
          @click="
            () => {
              cancelModal = false;
              next();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="deleteModal"
      @close="
        () => {
          deleteModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loadingDelete && validateResult.length === 0">
          お知らせを削除してもよろしいですか？
        </div>

        <div style="margin-left: 10px">
          <scale-loader
            :loading="loadingDelete"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter v-if="validateResult.length === 0">
        <CButton
          @click="deleteModal = false"
          color="dark"
          :disabled="loadingDelete"
          >キャンセル</CButton
        >
        <CButton @click="deleteNotice" color="primary" :disabled="loadingDelete"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="sendModal"
      @close="
        () => {
          sendModal = false;
          errorMsg = '';
        }
      "
    >
      <CModalHeader>
        <CModalTitle>テストメール送信</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol sm="4">
            <label>メールアドレス</label>
          </CCol>
          <CCol sm="8">
            <CFormInput
              name="direct_contract_price"
              v-model="receivers"
              :disabled="loading"
            />
          </CCol>
        </CRow>
        <div v-for="text in errorMsg" :key="text" class="text-danger">
          {{ text }}
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="(sendModal = false), (errorMsg = '')"
          color="dark"
          :disabled="loading"
        >
          <div>キャンセル</div>
        </CButton>
        <CButton color="primary" @click="sentEmail" :disabled="loading"
          >送信する</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="sentCompModal"
      @close="
        () => {
          sentCompModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>送信完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>送信が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="sentCompModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>
  </CCard>
</template>

<script>
  import Methods from '@/api/methods';
  import {CElementCover, ScaleLoader} from '@/components/Table';
  import {useAuthStore} from '@/store/auth';
  import {
    CBadge,
    CButton,
    CCard,
    CCardBody,
    CCardFooter,
    CCardHeader,
    CCloseButton,
    CCol,
    CForm,
    CFormInput,
    CFormTextarea,
    CModal,
    CRow,
  } from '@coreui/vue';
  import FileSelection from './fileSelection.vue';

  export default {
    name: 'noticeMail',
    components: {
      FileSelection,
      ScaleLoader,
      CButton,
      CCloseButton,
      CCard,
      CCardBody,
      CCardFooter,
      CCardHeader,
      CCol,
      CElementCover,
      CFormInput,
      CModal,
      CRow,
      CFormTextarea,
      CBadge,
      CForm,
    },
    setup() {
      const {isReadOnly} = useAuthStore();
      return {isReadOnly};
    },
    data() {
      return {
        // Spinner
        color: '#5dc596',
        height: '10px',
        width: '4px',
        loading: false,

        // Screen options
        orig_notice: null,
        notice: null,
        languages: [],
        registModal: false,
        registConfirmDialog: false,
        compModal: false,
        errorModal: false,
        changeFlag: false,
        cancelModal: false,

        // File
        btnRegist: false,

        // Screen params
        email_priority: null,
        sendDate: '',
        sendTime: '',
        title: [],
        body_title_upper_row: [],
        body_title_lower_row: [],
        body: [],
        file: [],

        // Validation
        validateResult: [],
        registModalTitle: '確認',

        // Delete modal
        deleteModal: false,
        loadingDelete: false,

        // Email sending
        sentData: '',
        receivers: '',
        sendModal: false,
        errorMsg: '',
        sentCompModal: false,
        options_email_priority: [],

        btn_clicked: false,

        // Date validation
        dateValidate: {
          sendDate: true,
          sendTime: true,
        },

        // 最大日付
        maxDate: '',
      };
    },
    beforeRouteLeave(to, from, next) {
      if (this.changeFlag) {
        this.next = next;
        this.cancelModal = true;
      } else {
        next();
      }
    },
    watch: {
      sendDate(newVal, oldVal) {
        if (oldVal.length !== 0) {
          this.changeFlag = true;
        }
      },
      sendTime(newVal, oldVal) {
        if (oldVal.length !== 0) {
          this.changeFlag = true;
        }
      },
      'body.lang.code'(newVal, oldVal) {
        console.log(newVal);
        this.changeFlag = true;
      },
    },
    computed: {
      isBtnDisabled() {
        return this.loading || (this.notice && this.notice.sent_flag === 1);
      },
    },
    async mounted() {
      console.log('mounted');
      await this.onMounted();

      if (this.isReadOnly) {
        // 一般の管理者の場合は[/dashboard]ページに遷移する
        this.$router.push({path: '/dashboard'});
      }
    },
    methods: {
      async onMounted() {
        await this.getConstantsData()
          .then(() => {
            this.getMaxDate();
            return this.getNoticeData();
          })
          .catch(error => {
            console.log(JSON.stringify(error));
            this.loading = false;
            Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getConstantsData() {
        this.loading = true;
        const request = {
          key_strings: ['LANGUAGE_CODE', 'EMAIL_NOTICE_PRIORITY'],
        };
        // Request to server
        return Methods.apiExecute('get-constants-by-keys', request).then(
          response => {
            if (response.status === 200) {
              this.languages = [];
              this.options_email_priority = [];
              for (const constant of response.data) {
                switch (constant.key_string) {
                  case 'LANGUAGE_CODE':
                    this.languages.push({
                      code: constant.value1,
                      name: constant.value2,
                    });
                    break;
                  case 'EMAIL_NOTICE_PRIORITY':
                    this.options_email_priority.push({
                      value: constant.value1,
                      label: constant.value2,
                    });
                    break;
                  default:
                    break;
                }
              }
            }
            return Promise.resolve();
          }
        );
      },
      getNoticeData() {
        const id = this.$route.params.id;

        // Original初期化
        this.orig_notice = null;

        if (typeof id === 'undefined' || id === null) {
          this.loading = false;
          // Add new mode
          this.showScreen();
          return Promise.resolve();
        } else {
          // Edit mode
          const search_condition = {
            notice_email_no: id,
          };

          return this.getNoticesFromServer(search_condition)
            .then(notices => {
              this.loading = false;
              if (typeof notices === 'undefined' || notices === null) {
                this.notice = null;
                this.errorModal = true;
              } else {
                this.notice = notices[0];
                console.log(`notice = ${JSON.stringify(this.notice)}`);
                // Copy not by reference
                this.orig_notice = JSON.parse(JSON.stringify(this.notice));
                this.showScreen();
              }
              return Promise.resolve();
            })
            .catch(error => {
              console.log(error);
              Methods.parseHtmlResponseError(this.$router, error);
            });
        }
      },
      getMaxDate() {
        const today = new Date(
          Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000
        );
        const nextYearDate = new Date(today);
        nextYearDate.setDate(today.getDate() + 364);
        nextYearDate.setHours(0, 0, 0, 0); // 時間をリセットして日付のみ取得

        // YYYY-MM-DD形式で取得
        const year = nextYearDate.getFullYear();
        const month = String(nextYearDate.getMonth() + 1).padStart(2, '0');
        const day = String(nextYearDate.getDate()).padStart(2, '0');

        this.maxDate = `${year}-${month}-${day}`;
      },
      getNoticesFromServer(search_condition) {
        // Request to server
        return Methods.apiExecute(
          'get-notice-email-by-notice-email-no',
          search_condition
        ).then(response => {
          if (response.status === 200) {
            this.compModal = false;
            const noticeList = response.data;

            if (typeof noticeList === 'undefined' || noticeList === null) {
              return Promise.resolve(null);
            } else {
              // Processing data
              const tmpMap = new Map();
              noticeList.data.forEach(noti => {
                const tmp1 = tmpMap.get(String(noti.notice_email_no));
                if (tmp1) {
                  if (tmp1.language_code !== noti.language_code) {
                    tmp1.language_code.push(noti.language_code);
                    tmp1.title.push(noti.title);
                    tmp1.body_title_upper_row.push(noti.body_title_upper_row);
                    tmp1.body_title_lower_row.push(noti.body_title_lower_row);
                    tmp1.body.push(noti.body);
                    tmp1.file.push(noti.file);
                  }
                } else {
                  const noti_tmp = JSON.parse(JSON.stringify(noti));
                  noti_tmp.language_code = [noti.language_code];
                  noti_tmp.title = [noti.title];
                  noti_tmp.body_title_upper_row = [noti.body_title_upper_row];
                  noti_tmp.body_title_lower_row = [noti.body_title_lower_row];
                  noti_tmp.body = [noti.body];
                  noti_tmp.file = [noti.file];
                  tmpMap.set(String(noti_tmp.notice_email_no), noti_tmp);
                }
              });

              const finalList = Array.from(tmpMap.values());
              return Promise.resolve(finalList);
            }
          } else {
            return Promise.resolve([]);
          }
        });
      },
      showScreen() {
        if (this.notice) {
          const options = {year: 'numeric', month: '2-digit', day: '2-digit'};
          // End datetime
          this.sendDate = Methods.getFormatDate(this.notice.send_date);
          this.sendTime = this.notice.send_time;

          // Prepare data for each language
          this.email_priority = null;
          this.title = {};
          this.body_title_upper_row = {};
          this.body_title_lower_row = {};
          this.body = {};
          this.file = {};
          for (const i in this.languages) {
            const lang = this.languages[i];
            const j = this.notice.language_code.indexOf(lang.code);
            if (j > -1) {
              this.email_priority = String(this.notice.email_priority);
              this.title[lang.code] = this.notice.title[j];
              this.body_title_upper_row[lang.code] =
                this.notice.body_title_upper_row[j];
              this.body_title_lower_row[lang.code] =
                this.notice.body_title_lower_row[j];
              this.body[lang.code] = this.notice.body[j];
              this.file[lang.code] = this.notice.file[j];
            } else {
              this.email_priority = null;
              this.title[lang.code] = '';
              this.body_title_upper_row[lang.code] = '';
              this.body_title_lower_row[lang.code] = '';
              this.body[lang.code] = '';
              this.file[lang.code] = [];
            }
          }
          console.log('title: ', this.title);
        } else {
          const options = {year: 'numeric', month: '2-digit', day: '2-digit'};
          const tmpDate = new Date();
          // End datetime
          this.sendDate = Methods.getFormatDate(tmpDate);
          this.sendTime = Methods.getFormatTime(tmpDate);
          console.log('sendDate', this.sendDate, this.sendTime);

          this.email_priority = null;
          this.title = {};
          this.body_title_upper_row = {};
          this.body_title_lower_row = {};
          this.body = {};
          this.file = {};
          for (const i in this.languages) {
            const lang = this.languages[i];
            console.log(`lang = ${lang}`);
            this.title[lang.code] = '';
            this.body_title_upper_row[lang.code] = '';
            this.body_title_lower_row[lang.code] = '';
            this.body[lang.code] = '';
            this.file[lang.code] = [];
          }
        }
      },
      goBack() {
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        if (this.constantsOpened) {
          this.$router.go(-1);
        } else {
          this.$router.push({path: '/noticeMails'});
        }
      },
      openRegistModal() {
        this.registModalTitle = '確認';
        this.compModal = false;
        this.registModal = true;
        this.loading = false;
        this.validateResult = [];
      },
      registNotice() {
        // Get data from screen
        this.getScreenInputData();

        if (!this.notice) {
          console.log('notice is empty!');
        }

        console.log(`notice = ${JSON.stringify(this.notice)}`);

        // Prepare move or delete files
        return this.getMoveOrDeleteFiles().then(delete_files => {
          const send_data = {
            data: this.notice,
            delete_files,
          };
          console.log(`send_data = ${JSON.stringify(send_data)}`);

          const api_name = this.$route.params.id
            ? 'edit-notice-email'
            : 'create-notice-email';
          return Methods.apiExecute(api_name, send_data)
            .then(response => {
              this.registModal = false;
              console.log(`response = ${JSON.stringify(response)}`);
              if (response.status === 200) {
                this.loading = false;
                // This.compModal = true
                this.changeFlag = false;
                this.$router.push({path: '/noticeMails'});
              }
              return Promise.resolve();
            })
            .catch(error => {
              console.log(error);
              this.loading = false;
              this.registModal = false;
              Methods.parseHtmlResponseError(this.$router, error);
              return Promise.resolve();
            });
        });
      },
      getMoveOrDeleteFiles() {
        if (this.orig_notice && this.orig_notice.file !== null) {
          const orig_files = [];
          for (let i = 0; i < this.orig_notice.file.length; i++) {
            for (let j = 0; j < this.orig_notice.file[i].length; j++) {
              orig_files.push(String(this.orig_notice.file[i][j]));
            }
          }
          const new_files = [];
          for (const i in this.file) {
            for (let j = 0; j < this.file[i].length; j++) {
              new_files.push(this.file[i][j]);
            }
          }

          // Get delete files
          const delete_files = [];
          for (let i = 0; i < orig_files.length; i++) {
            if (new_files.indexOf(orig_files[i]) === -1) {
              delete_files.push(orig_files[i]);
            }
          }

          // Callback
          return Promise.resolve(delete_files);
        } else {
          return Promise.resolve(null);
        }
      },
      getScreenInputData() {
        if (typeof this.notice === 'undefined' || this.notice === null) {
          this.notice = {};
        }

        // Email priority
        this.notice.email_priority = this.email_priority
          ? Number(this.email_priority)
          : 0;

        const options = {year: 'numeric', month: '2-digit', day: '2-digit'};
        // Send date
        if (this.dateValidate.sendDate) {
          if (this.sendDate === '') {
            this.notice.send_date = this.sendDate;
          } else {
            this.notice.send_date = Methods.getFormatDate(this.sendDate);
          }
        } else {
          this.notice.send_date = 'Invalid Date';
        }

        // Send time
        if (this.dateValidate.sendTime) {
          this.notice.send_time = `${this.sendTime}:00`;
        } else {
          this.notice.send_time = 'Invalid Time';
        }

        this.notice.language_code = [];
        this.notice.language_name = [];
        this.notice.title = [];
        this.notice.body_title_upper_row = [];
        this.notice.body_title_lower_row = [];
        this.notice.body = [];
        this.notice.file = [];
        for (const i in this.languages) {
          const lang = this.languages[i];
          this.notice.language_code.push(lang.code);
          this.notice.language_name.push(lang.name);

          this.notice.title.push(this.title[lang.code]);
          this.notice.body_title_upper_row.push(
            this.body_title_upper_row[lang.code]
          );
          this.notice.body_title_lower_row.push(
            this.body_title_lower_row[lang.code]
          );
          this.notice.body.push(this.body[lang.code]);

          let tmpFiles = '';
          if (
            typeof this.file[lang.code] !== 'undefined' &&
            this.file[lang.code] !== null
          ) {
            for (const index in this.file[lang.code]) {
              if (index > 0) {
                tmpFiles += ',';
              }
              tmpFiles += this.file[lang.code][index];
            }
          }
          tmpFiles = `{${tmpFiles}}`;
          this.notice.file.push(tmpFiles);
        }
      },
      onFileCompChange(files, lang_code) {
        console.log(`lang_code = ${JSON.stringify(lang_code)}`);
        console.log(`onFileCompChange = ${JSON.stringify(files)}`);
        this.changeFlag = true;

        const tmpFiles = [];
        for (const index in files) {
          if (
            typeof files[index].s3url !== 'undefined' &&
            files[index].s3url !== null &&
            files[index].s3url !== ''
          ) {
            tmpFiles.push(files[index].s3url);
          }
        }
        this.file[lang_code] = tmpFiles;
      },
      async btnRegistClicked() {
        console.log('btnRegistClicked');
        this.changeFlag = false;

        if (this.validateResult.length > 0) {
          // Close modal
          this.loading = false;
          this.registModal = false;
        } else {
          // Open modal
          this.loading = true;

          // Validation
          await this.validation();

          // エラーがない場合はデータ更新へ進める
          if (this.validateResult.length === 0) {
            // Upload file to s3
            await this.uploadRecursion(this.languages).then(resList => {
              // Update the newest file infos
              for (const res in resList) {
                this.onFileCompChange(
                  resList[res].files,
                  resList[res].lang_code
                );
              }
              // Regist notice to DB
              return this.registNotice();
            });
          } else {
            this.loading = false;
            this.registModalTitle = '入力エラー';
          }
        }
      },
      uploadRecursion(languages) {
        return Promise.all(
          languages.map(lang => {
            return new Promise((resolve, reject) => {
              this.$refs[`fileUpload_${lang.code}`][0].uploadFiles(
                event,
                updatedFiles => {
                  return resolve({files: updatedFiles, lang_code: lang.code});
                }
              );
            });
          })
        ).then(resList => {
          console.log('resList', resList);
          return Promise.resolve(resList);
        });
      },
      validation() {
        this.validateResult = [];
        this.getScreenInputData();

        const validate_data = {
          validation_mode: true,
          data: this.notice,
        };
        console.log('validate_data = ', validate_data);

        const api_name = this.$route.params.id
          ? 'edit-notice-email'
          : 'create-notice-email';
        return Methods.apiExecute(api_name, validate_data)
          .then(response => {
            console.log(`validation response = ${JSON.stringify(response)}`);
            // Validation error results
            if (response.status === 400) {
              this.validateResult.push(response.message);
            }
            return Promise.resolve();
          })
          .catch(error => {
            console.log(JSON.stringify(error));
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            )[0];
            return Promise.resolve();
          });
      },
      editorTextChange(code) {
        const i =
          this.notice && this.notice.language_code
            ? this.notice.language_code.indexOf(code)
            : -1;
        if (
          !this.$route.params.id ||
          i === -1 ||
          this.title[code] !== this.notice.title[i] ||
          this.body_title_upper_row[code] !==
            this.notice.body_title_upper_row[i] ||
          this.body_title_lower_row[code] !==
            this.notice.body_title_lower_row[i] ||
          this.body[code] !== this.notice.body[i]
        ) {
          this.changeFlag = true;
        }
      },
      openDeleteModal() {
        this.loadingDelete = false;
        this.deleteModal = true;
      },
      async deleteNotice() {
        this.loadingDelete = true;
        this.validateResult = [];
        const notice_email_no = this.$route.params.id;
        console.log(`deleteNoticeEmail: ${JSON.stringify(notice_email_no)}`);

        // Request to server
        await Methods.apiExecute('delete-notice-email', {
          notice_email_no,
        })
          .then(response => {
            console.log(`response = ${JSON.stringify(response)}`);
            this.loadingDelete = false;
            this.deleteModal = false;
            if (response.status === 200) {
              this.goBack();
            }
          })
          .catch(error => {
            console.log(error);
            this.loadingDelete = false;
            // This.deleteModal = false
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            );
          });
      },
      btnTestSendEmailClicked(lang_code) {
        console.log('btnTestSendEmailClicked');
        this.sendModal = true;
        this.sentData = {
          language_code: lang_code,
          title: this.title[lang_code],
          body: this.body[lang_code],
          body_title_upper_row: this.body_title_upper_row[lang_code],
          body_title_lower_row: this.body_title_lower_row[lang_code],
        };
        this.receivers = '';
      },
      async sentEmail() {
        console.log('sentEmail');
        // Upload file to s3
        if (this.sentData === null || typeof this.sentData === 'undefined') {
          return;
        }
        this.loading = true;
        await this.uploadRecursion([{code: this.sentData.language_code}]).then(
          resList => {
            // Update the newest file infos
            for (const res in resList) {
              this.onFileCompChange(resList[res].files, resList[res].lang_code);
            }
            // Regist notice to DB
            const req = {
              receivers: this.receivers,
              languageCode: this.sentData.language_code,
              title: this.sentData.title ? this.sentData.title : '',
              body: this.sentData.body ? this.sentData.body : '',
              body_title_upper_row: this.sentData.body_title_upper_row
                ? this.sentData.body_title_upper_row
                : '',
              body_title_lower_row: this.sentData.body_title_lower_row
                ? this.sentData.body_title_lower_row
                : '',
              files: this.file[this.sentData.language_code],
            };
            console.log('send email req: ', req);

            return Methods.apiExecute('sent-notice-email', req)
              .then(response => {
                this.loading = false;
                this.sendModal = false;
                this.sentCompModal = true;
              })
              .catch(error => {
                this.loading = false;
                this.errorMsg = Methods.parseHtmlResponseError(
                  this.$router,
                  error
                );
              });
          }
        );
      },
    },
  };
</script>
