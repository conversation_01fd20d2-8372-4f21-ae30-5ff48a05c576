<template>
  <div v-bind:class="loading === true ? 'btn-disable' : null">
    <CCard>
      <CCardHeader>
        <CRow>
          <CCol sm="5">
            <strong>入札会お知らせメール編集画面</strong>
          </CCol>
          <CCol sm="1">
            <div class="spinner-grow text-primary" v-if="loading">
              <span class="visually-hidden"></span>
            </div>
          </CCol>
        </CRow>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="5">
              <CFormSelect
                name="exhibition_name"
                v-model="search_condition.exhibition_name"
                :options="options_exhibition_name"
                class="form-group"
              />
            </CCol>
            <CCol sm="5">
              <CFormSelect
                name="language_code"
                v-model="search_condition.language_code"
                :options="tenantLanguageList"
                class="form-group"
              />
            </CCol>
            <CCol md="2" class="mb-3 mb-xl-0 text-right d-grid">
              <CButton
                size="sm"
                color="info"
                @click="getExhibitionInformation"
                block
                >検索</CButton
              >
            </CCol>
          </CRow>
          <div v-if="clicked_search">
            <CCard class="mb-3">
              <CCardHeader class="form-inline">
                <slot name="header"> <CIcon name="info" />入札会情報 </slot>
              </CCardHeader>
              <CCardBody>
                <CRow class="g-3 mb-2">
                  <CCol md="3">
                    <label>下見開始日時</label>
                  </CCol>
                  <CCol md="9">
                    <div>
                      <a>{{ exhibitionInfo.previewStartDatetime }}</a>
                    </div>
                  </CCol>
                </CRow>
                <CRow class="g-3 mb-2">
                  <CCol md="3">
                    <label>入札会日時</label>
                  </CCol>
                  <CCol md="9">
                    <div>
                      <a>{{ exhibitionInfo.startEndDatetime }}</a>
                    </div>
                  </CCol>
                </CRow>
                <CRow class="g-3 mb-2">
                  <CCol md="3">
                    <label>閲覧終了日時</label>
                  </CCol>
                  <CCol md="9">
                    <div>
                      <a>{{ exhibitionInfo.previewEndDatetime }}</a>
                    </div>
                  </CCol>
                </CRow>
                <CRow class="g-3">
                  <CCol md="3">
                    <label>出展数</label>
                  </CCol>
                  <CCol md="9">
                    <div>
                      <a>{{ exhibitionInfo.exhibitionItemCount }}</a>
                    </div>
                  </CCol>
                </CRow>
              </CCardBody>
            </CCard>
            <template v-for="(email, index) in emailData" :key="index">
              <CForm :class="index > 0 ? 'mt-3' : ''">
                <CCard>
                  <CCardHeader>
                    <strong>
                      {{
                        sendTarget.filter(
                          target => target.value === email.classification
                        )[0].label
                      }}
                    </strong>
                    <strong
                      :hidden="email.sentFlag === '0'"
                      class="text-danger"
                    >
                      ※送信済みのため編集できません</strong
                    >
                  </CCardHeader>
                  <CCardBody
                    v-bind:class="email.sentFlag === '1' ? 'btn-disable' : null"
                  >
                    <CRow class="mb-2">
                      <CCol md="3">
                        <label>送信有無</label>
                      </CCol>
                      <CCol
                        :disabled="email.sentFlag === '1'"
                        md="9"
                        class="pl-0"
                      >
                        <CFormCheck
                          type="radio"
                          inline
                          v-for="send_options in send_options"
                          :key="send_options.value"
                          :label="send_options.label"
                          :value="send_options.value"
                          :custom="true"
                          name="role_id"
                          v-model="email.sendFlag"
                        />
                      </CCol>
                    </CRow>
                    <div
                      v-bind:class="
                        email.sentFlag === '1' ? 'btn-disable' : null
                      "
                    >
                      <CRow class="mb-2">
                        <CCol md="3">
                          <label>メール 送信日時</label>
                          <CBadge
                            :hidden="
                              email.sentFlag === '1' || email.sendFlag === '0'
                            "
                            color="danger"
                          >
                            必須</CBadge
                          >
                        </CCol>
                        <CCol md="auto">
                          <CFormInput
                            type="date"
                            v-model="email.date"
                            :invalid="!dateValidate[email.dateValKey]"
                            :disabled="
                              email.sentFlag === '1' || email.sendFlag === '0'
                            "
                            :max="maxDate"
                            @change="
                              e => {
                                dateValidate[email.dateValKey] =
                                  e.target.validity.valid;
                              }
                            "
                          ></CFormInput>
                        </CCol>
                        <CCol md="auto">
                          <CFormInput
                            type="time"
                            v-model="email.time"
                            :invalid="!dateValidate[email.timeValKey]"
                            :disabled="
                              email.sentFlag === '1' || email.sendFlag === '0'
                            "
                            @change="
                              e => {
                                dateValidate[email.timeValKey] =
                                  e.target.validity.valid;
                              }
                            "
                          ></CFormInput>
                        </CCol>
                      </CRow>
                      <template
                        v-for="child in email.childEmails"
                        :key="`${email.classification}_${child.language_code}`"
                      >
                        <div>
                          <CRow class="email-content mb-2">
                            <CCol md="3">
                              <label>メール タイトル</label>
                              <CBadge
                                :hidden="
                                  email.sentFlag === '1' ||
                                  email.sendFlag === '0'
                                "
                                color="danger"
                              >
                                必須</CBadge
                              >
                            </CCol>
                            <CCol md="9" class="pl-0">
                              <CFormInput
                                horizontal
                                v-model="child.title"
                                :disabled="
                                  email.sentFlag === '1' ||
                                  email.sendFlag === '0'
                                "
                              ></CFormInput>
                            </CCol>
                          </CRow>
                          <CRow class="email-content mb-2">
                            <CCol md="3">
                              <label>メール 本文</label>
                            </CCol>
                            <CCol md="9" class="pl-0">
                              <CFormTextarea
                                horizontal
                                rows="10"
                                v-model="child.body"
                                :disabled="
                                  email.sentFlag === '1' ||
                                  email.sendFlag === '0'
                                "
                              />
                            </CCol>
                          </CRow>
                          <CRow class="mb-2">
                            <CCol md="3">
                              <label>添付ファイル</label>
                            </CCol>
                            <CCol md="9" class="pl-0">
                              <FileSelection
                                :ref="
                                  'fileUpload_' +
                                  email.classification +
                                  '_' +
                                  child.language_code
                                "
                                :key="
                                  email.classification +
                                  '_' +
                                  child.language_code
                                "
                                :items="child.file"
                                @onFileChange="
                                  onFileCompChange(
                                    $event,
                                    child.language_code,
                                    email.classification
                                  )
                                "
                              />
                            </CCol>
                          </CRow>
                          <CRow>
                            <CCol sm="5" />
                            <CCol sm="2">
                              <CButton
                                size="sm"
                                color="info"
                                @click="
                                  (sendModal = true),
                                    (sentData = {email: email, child: child}),
                                    (receivers = '')
                                "
                                block
                                >テストメール送信</CButton
                              >
                            </CCol>
                            <CCol sm="5" />
                          </CRow>
                        </div>
                      </template>
                    </div>
                  </CCardBody>
                </CCard>
              </CForm>
            </template>
          </div>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CButton
          v-if="exhibition_name"
          color="light"
          class="mx-1"
          @click="goBack"
        >
          <div v-if="selectStatus">登録を中止して一覧に戻る</div>
          <div v-if="!selectStatus">更新を中止して一覧に戻る</div>
        </CButton>
        <CButton
          v-if="clicked_search"
          color="primary"
          class="mx-1"
          @click="registModal = true"
        >
          <div v-if="selectStatus">登録する</div>
          <div v-if="!selectStatus">更新する</div>
        </CButton>
      </CCardFooter>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="registModal"
      @close="
        () => {
          registModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>登録確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!errorStatus">この内容で登録してもよろしいですか？</div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-for="text in errorMsg" :key="text">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="(registModal = false), (errorMsg = []), (errorStatus = false)"
          :disabled="buttonLoading"
          color="dark"
        >
          <div v-if="errorStatus">OK</div>
          <div v-if="!errorStatus">キャンセル</div>
        </CButton>
        <CButton
          v-if="!errorStatus"
          @click="registEmail"
          :disabled="buttonLoading"
          :loading="buttonLoading"
          color="primary"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 完了 </CModalTitle>
      </CModalHeader>
      <CModalBody> 処理が完了しました。 </CModalBody>
      <CModalFooter>
        <CButton @click="goBack" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="sentCompModal"
      @close="
        () => {
          sentCompModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 送信完了 </CModalTitle>
      </CModalHeader>
      <CModalBody> 送信が完了しました。 </CModalBody>
      <CModalFooter>
        <CButton @click="sentCompModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>
          {{ this.selectStatus ? '登録' : '編集' }}中止確認
        </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="cancelModal = false" color="dark">キャンセル</CButton>
        <CButton @click="next()" color="danger">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="sendModal"
      @close="
        () => {
          sendModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> テストメール送信 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol sm="4">
            <label>メールアドレス</label>
          </CCol>
          <CCol sm="8">
            <CFormInput name="direct_contract_price" v-model="receivers" />
          </CCol>
        </CRow>
        <div v-for="text in errorMsg" :key="text" class="text-danger">
          {{ text }}
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          :disabled="send_loading"
          @click="(sendModal = false), (errorMsg = [])"
          color="dark"
        >
          <div>キャンセル</div>
        </CButton>
        <CButton :disabled="send_loading" color="primary" @click="sentEmail"
          >送信する</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import ScaleLoader from '@/components/Table/ScaleLoader.vue';
  import {useAuthStore} from '@/store/auth';
  import FileSelection from './fileSelection';

  export default {
    name: 'ExhibitionMailInfo',
    props: {
      exhibition_name: {
        type: String,
        require: false,
      },
    },
    components: {
      FileSelection,
      ScaleLoader,
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.exhibitionsOpened = from.fullPath.includes('exhibitions');
      });
    },
    beforeRouteLeave(to, from, next) {
      if (Base.objectsAreIdentical(this.origEmailData, this.emailData)) {
        // eslint-disable-next-line callback-return
        next();
      } else {
        this.next = next;
        this.cancelModal = true;
      }
    },
    data() {
      return {
        authStore: useAuthStore(),
        registModal: false,
        cancelModal: false,
        compModal: false,
        sendModal: false,
        sentCompModal: false,
        buttonLoading: false,
        loading: true,
        selectStatus: true,
        searchStatus: false,
        exhibitionsOpened: null,
        exhibitionEmailNo: '',
        constantList: '',
        emailData: [],
        origEmailData: [],
        errorMsg: [],
        errorStatus: false,
        sendTarget: [],
        sentData: '',
        receivers: '',
        languageList: [
          {value: 'ja', label: '日本語'},
          {value: 'en', label: '英語'},
          {value: 'fr', label: 'フランス語'},
          {value: 'de', label: 'ドイツ語'},
          {value: 'es', label: 'スペイン語'},
        ],
        tenantLanguageList: [],
        send_options: [
          {label: '送信する', value: '1'},
          {label: '送信しない', value: '0'},
        ],
        options_exhibition_name: [],
        exhibitionInfo: {
          previewStartDatetime: '',
          startEndDatetime: '',
          previewEndDatetime: '',
          exhibitionItemCount: '',
        },
        search_condition: {
          exhibition_name: '',
          language_code: '',
        },

        // 送信中
        send_loading: false,

        // 検索ボタン押下
        clicked_search: false,

        dateValidate: {},

        // 最大日付
        maxDate: '',
      };
    },
    async mounted() {
      this.loading = true;
      this.getConstants();
    },
    methods: {
      registEmail() {
        // Preventing multiple clicks
        if (this.buttonLoading) {
          console.log('Preventing multiple clicks');
          return;
        }
        this.loading = true;
        this.buttonLoading = true;

        const emailReq = [];
        const emailLanguageReq = [];
        const exhibitionName = this.search_condition.exhibition_name;
        const languageCode = this.search_condition.language_code;
        this.emailData.map(email => {
          if (email.sentFlag === '0') {
            emailReq.push({
              exhibitionEmailNo: email.exhibitionEmailNo,
              exhibitionName: email.exhibitionName,
              classification: Number.parseInt(email.classification, 10),
              status: email.status,
              dateTimeChangeFlag:
                email.sourceDateTime &&
                email.date &&
                email.time &&
                Date.parse(email.sourceDateTime) !==
                  Date.parse(`${email.date} ${email.time}`),
              sendFlag: Number.parseInt(email.sendFlag, 10),
              target: this.sendTarget.find(
                y => y.value === email.classification
              ).label,
              date: email.date,
              time: email.time,
              dateTimeValiFlag:
                this.dateValidate[email.dateValKey] &&
                this.dateValidate[email.timeValKey],
            });
            email.childEmails.map(language => {
              emailLanguageReq.push({
                exhibitionName: email.exhibitionName,
                classification: Number.parseInt(email.classification, 10),
                languageCode: languageCode,
                sendFlag: Number.parseInt(email.sendFlag, 10),
                target: this.sendTarget.find(
                  y => y.value === email.classification
                ).label,
                exhibitionEmailLocalizedNo:
                  language.exhibition_email_localized_no,
                title: language.title,
                body: language.body,
                footer: language.footer,
                file: language.file,
              });
            });
          }
        });

        Methods.apiExecute('regist-exhibition-mail-language-list', {
          exhibitionName,
          emailReq,
          emailLanguageReq,
        })
          .then(response => {
            if (response.status === 200) {
              this.buttonLoading = false;
              this.registModal = false;
              this.compModal = true;
              this.loading = false;
              // Update original data
              this.origEmailData = JSON.parse(JSON.stringify(this.emailData));
              // Back to top
              Base.scrollToTop();
            }
          })
          .catch(error => {
            this.loading = false;
            this.buttonLoading = false;
            this.errorStatus = true;
            this.registModal = true;
            this.compModal = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      goBack() {
        this.compModal = false;
        if (this.exhibition_name) {
          this.$router.push({path: '/auctions'});
        } else {
          this.getExhibitionInformation();
        }
      },
      getConstants() {
        const request = {
          key_strings: ['EXHIBITION_EMAIL', 'LANGUAGE_CODE'],
        };
        Methods.apiExecute('get-constants-by-keys-language', request)
          .then(response => {
            if (response.status === 200) {
              this.constantList = response.data;

              for (const constant of this.constantList) {
                switch (constant.key_string) {
                  case 'EXHIBITION_EMAIL':
                    if (
                      constant.language_code ===
                      this.authStore.user.language_code
                    ) {
                      this.sendTarget.push({
                        label: constant.value2,
                        value: constant.value1,
                      });
                    }
                    break;
                  case 'LANGUAGE_CODE':
                    // TODO ここの処理を修正する
                    // for (const i of this.$cookies
                    //   .get('language_code_list')
                    //   .split(',')) {
                    //   if (i === constant.value1) {
                    //     this.languageList.push({
                    //       label: constant.value2,
                    //       value: constant.value1,
                    //     });
                    //   }
                    // }
                    console.log('languageList: TODO ');
                    break;
                  default:
                    break;
                }
              }
              this.getExhibitionPulldown();
              this.getTenantLanguage();
              this.getMaxDate();
            }
          })
          .catch(error => {
            this.loading = false;
            this.errorStatus = true;
            this.registModal = true;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getTenantLanguage() {
        Methods.apiExecute('get-tenant-language-list', {})
          .then(response => {
            if (response.status === 200) {
              this.tenantLanguageList = response.data.language_code_list.map(
                cd => {
                  const lang_list = this.languageList.find(
                    item => item.value === cd
                  );
                  return {
                    value: cd,
                    label: lang_list ? lang_list.label : '',
                  };
                }
              );
              this.tenantLanguageList.unshift({
                value: '',
                label: '',
              });
              this.loading = false;
            }
          })
          .catch(error => {
            this.loading = false;
            this.errorStatus = true;
            this.registModal = true;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getMaxDate() {
        const today = new Date(
          Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000
        );
        const nextYearDate = new Date(today);
        nextYearDate.setDate(today.getDate() + 364);
        nextYearDate.setHours(0, 0, 0, 0); // 時間をリセットして日付のみ取得

        // YYYY-MM-DD形式で取得
        const year = nextYearDate.getFullYear();
        const month = String(nextYearDate.getMonth() + 1).padStart(2, '0');
        const day = String(nextYearDate.getDate()).padStart(2, '0');

        this.maxDate = `${year}-${month}-${day}`;
      },
      getExhibitionInformation() {
        if (
          this.search_condition.exhibition_name === null ||
          this.search_condition.exhibition_name === ''
        ) {
          this.errorStatus = true;
          this.errorMsg = ['開催回名、言語を選択してください。'];
          this.registModal = true;
          this.clicked_search = false;
          return;
        }
        this.searchStatus = true;
        this.loading = true;
        const request = {
          exhibitionName: this.search_condition.exhibition_name,
        };
        Methods.apiExecute('get-exhibition-information', request)
          .then(response => {
            if (response.status === 200) {
              const info = response.data[0];
              console.log('info: ', info);
              this.exhibitionInfo.previewStartDatetime =
                Methods.getFormatDateTime(info.preview_start_datetime);
              this.exhibitionInfo.startEndDatetime = `${Methods.getFormatDateTime(info.start_datetime)} ~ ${Methods.getFormatDateTime(info.end_datetime)}`;
              this.exhibitionInfo.previewEndDatetime =
                Methods.getFormatDateTime(info.preview_end_datetime);
              this.exhibitionInfo.exhibitionItemCount = `${info.exhibition_item_count ? info.exhibition_item_count : 0}点`;
            }
            this.getEmailLanguagues();
          })
          .catch(error => {
            this.loading = false;
            this.errorStatus = true;
            this.registModal = true;
            this.clicked_search = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      sentEmail() {
        this.send_loading = true;
        console.log('this.sentData :', this.sentData);
        const req = {
          receivers: this.receivers,
          exhibitionName: this.search_condition.exhibition_name,
          languageCode: this.sentData.child.language_code,
          language: this.languageList.find(
            y => y.value === this.sentData.child.language_code
          ).label,
          classification: this.sendTarget.find(
            y => y.value === this.sentData.email.classification
          ).label,
          title: this.sentData?.child ? this.sentData.child.title : '',
          body: this.sentData?.child ? this.sentData.child.body : '',
          files: this.sentData?.child ? this.sentData.child.file : '',
        };
        Methods.apiExecute('sent-email', req)
          .then(response => {
            if (response.status === 200) {
              this.sendModal = false;
              this.sentCompModal = true;
            }
            this.send_loading = false;
          })
          .catch(error => {
            this.send_loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getExhibitionPulldown() {
        Methods.apiExecute('get-exhibition-name-pulldown', {})
          .then(response => {
            if (response.status === 200) {
              console.log('getExhibitionPulldown: ', response.data);
              this.options_exhibition_name = [];
              this.options_exhibition_name.push({label: '', value: null});
              response.data.map(pulldown => {
                this.options_exhibition_name.push({
                  value: pulldown.exhibition_name,
                  label: pulldown.exhibition_name,
                });
              });
              if (this.exhibition_name) {
                const exhibitionName = this.options_exhibition_name.find(
                  x => x.value === this.exhibition_name
                );
                if (exhibitionName) {
                  this.search_condition.exhibition_name = this.exhibition_name;
                }
              }
            }
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getEmailLanguagues() {
        const request = {
          exhibition_name: this.search_condition.exhibition_name,
          language_code: this.search_condition.language_code,
        };
        Methods.apiExecute('get-exhibition-email-language-list', request)
          .then(response => {
            if (response.status === 200) {
              this.emailData = [];
              response.data.map(email => {
                this.selectStatus = false;
                const dateValKey = `${email.classification}DateVal`;
                const timeValKey = `${email.classification}TimeVal`;
                this.emailData.push({
                  exhibitionEmailNo: email.exhibition_email_no,
                  exhibitionName: email.exhibition_name,
                  classification: String(email.classification),
                  status: email.send_datetime ? 1 : 0, // 1 : 更新ステータス , 0 : 登録ステータス
                  sendFlag: String(email.send_flag),
                  sentFlag: String(email.sent_flag),
                  sourceDateTime: email.send_datetime,
                  date: Methods.getFormatDate(email.send_datetime),
                  time: Methods.getFormatTime(email.send_datetime),
                  dateValKey,
                  timeValKey,
                  childEmails: email.email_language_json,
                });
              });
              for (const target of this.sendTarget) {
                const result_target = this.emailData.find(
                  emailtarget => emailtarget.classification === target.value
                );
                // 新規登録時
                if (!result_target) {
                  const childEmails = [];
                  this.selectStatus = true;
                  childEmails.push({
                    language_code: this.search_condition.language_code,
                    title: '',
                    body: '',
                    footer: '',
                    file: [],
                  });
                  this.emailData.push({
                    exhibitionName: this.search_condition.exhibition_name,
                    classification: target.value,
                    status: 0, // 登録ステータス
                    sendFlag: '0',
                    sentFlag: '0',
                    date: '',
                    time: '',
                    dateValKey: `${target.value}DateVal`,
                    timeValKey: `${target.value}TimeVal`,
                    childEmails,
                  });
                }
                // Reset date validation
                this.dateValidate[`${target.value}DateVal`] = true;
                this.dateValidate[`${target.value}TimeVal`] = true;
              }
            }

            // Set the original data
            this.origEmailData = JSON.parse(JSON.stringify(this.emailData));
            this.clicked_search = true;
            this.loading = false;

            console.log('this.selectStatus: ', this.selectStatus);
            console.log('this.emailData: ', this.emailData);
          })
          .catch(error => {
            this.clicked_search = false;
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      clearEmail(e) {
        const email = this.emailData.find(
          x => x.classification === e.classification
        );
        email.childEmails.map(language => {
          language.title = '';
          language.body = '';
          language.footer = '';
          language.file = [];
        });
        email.date = '';
        email.time = '';
      },
      onFileCompChange(files, lang_code, target) {
        const tmpFiles = [];
        for (const index in files) {
          if (
            typeof files[index].s3url !== 'undefined' &&
            files[index].s3url !== null &&
            files[index].s3url !== ''
          ) {
            tmpFiles.push(files[index].s3url);
          }
        }
        const email = this.emailData.find(x => x.classification === target);
        email.childEmails.find(x => x.language_code === lang_code).file =
          tmpFiles;
      },
    },
  };
</script>

<style lang="scss">
  .email-content {
    .col-sm-9 {
      width: 100% !important;
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  .email-class {
    .col-sm-3 {
      width: 100% !important;
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  .btn-disable {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
  }
</style>
