<template>
  <div>
    <CCard>
      <CForm>
        <CCardHeader>
          <strong>入札会詳細</strong>
        </CCardHeader>
        <CCardBody>
          <CCol sm="12">
            <strong>入札会名</strong>
          </CCol>
          <CRow
            v-for="(lang, key, index) in languageList"
            :key="index"
            class="mt-3"
          >
            <CCol sm="2">
              <label :class="languageList.length > 1 ? 'pl-3' : ''">{{
                lang.label
              }}</label>
            </CCol>
            <CCol sm="10">
              <label>{{
                exhibitionData['exhibition_name_' + lang.value]
              }}</label>
            </CCol>
            <CCol sm="3"></CCol>
          </CRow>
        </CCardBody>

        <CCardHeader
          style="
            border-top: 1px solid;
            border-bottom: none;
            border-color: #d8dbe0;
          "
        >
          <strong>時間設定</strong>
        </CCardHeader>
        <CCardBody>
          <CRow class="mb-3">
            <CCol sm="3">
              <label>下見開始日時</label>
            </CCol>
            <CCol sm="9">
              <label>{{
                getFormatDateTime(exhibitionData.preview_start_datetime)
              }}</label>
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">
              <label>入札開始日時</label>
            </CCol>
            <CCol sm="9">
              <label>{{
                getFormatDateTime(exhibitionData.start_datetime)
              }}</label>
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">
              <label>入札終了日時</label>
            </CCol>
            <CCol sm="9">
              <label>{{
                getFormatDateTime(exhibitionData.end_datetime)
              }}</label>
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">
              <label>閲覧終了日時</label>
            </CCol>
            <CCol sm="9">
              <label>{{
                getFormatDateTime(exhibitionData.preview_end_datetime)
              }}</label>
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="3">
              <label>方式</label>
            </CCol>
            <CCol sm="9">
              <label>{{ exhibitionData.auctionClassificationName }}</label>
            </CCol>
          </CRow>
          <CRow
            v-if="
              String(exhibitionData.pitchOption) === '1' &&
              pitchWidthList?.length > 1
            "
            class="mt-3"
          >
            <CCol sm="3">
              <label>入札単位</label>
            </CCol>
            <CCol sm="9">
              <label>{{ getPitchWidth(exhibitionData.pitch_width) }}</label>
            </CCol>
          </CRow>
          <CRow
            v-if="exhibitionData.auctionClassification === '1'"
            class="mt-3"
          >
            <CCol sm="3"> 延長設定 </CCol>
            <CCol sm="9" class="pl-0">
              {{
                flag_options?.find(
                  x => x.value === String(exhibitionData.extendFlag)
                )?.label || ''
              }}
            </CCol>
            <CCol sm="3" />
            <CCol sm="9">
              <label v-if="exhibitionData.extendFlag === '1'">
                終了
                {{ exhibitionData.extend_judge_minutes }}
                分前までに入札があったら
                {{ exhibitionData.extend_minutes }} 分延長する。
              </label>
            </CCol>
            <CCol sm="3" />
            <CCol sm="9">
              <label v-if="exhibitionData.extendFlag === '1'">
                延長は最大
                {{ getFormatDateTime(exhibitionData.max_extend_datetime) }}
                までとする。
              </label>
            </CCol>
          </CRow>
          <CRow
            v-if="exhibitionData.auctionClassification === '1'"
            class="mt-3"
          >
            <CCol sm="3"> あと少し表示 </CCol>
            <CCol sm="9" class="pl-0">
              <label>{{
                flag_options?.find(
                  x => x.value === exhibitionData.littleMoreDisplayFlag
                )?.label
              }}</label>
            </CCol>
            <CCol sm="3" />
            <CCol sm="9">
              <label v-if="exhibitionData.littleMoreDisplayFlag === '1'">
                最低落札価格まで
                {{
                  exhibitionData.more_little_judge_pitch
                    ? exhibitionData.more_little_judge_pitch.toLocaleString()
                    : ''
                }}
                入札単位以内の場合はあと少し表示する。
              </label>
            </CCol>
          </CRow>
          <CElementCover v-if="loading" :opacity="0.8" />
        </CCardBody>
        <CCardFooter>
          <CButton
            color="light"
            class="mx-1"
            @click="
              () => {
                $router.push({path: '/auctions'});
              }
            "
            >一覧に戻る</CButton
          >
        </CCardFooter>
      </CForm>
    </CCard>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import {CElementCover} from '@/components/Table/index';

  export default {
    name: 'ExhibitionDetail',
    components: {
      CElementCover,
    },
    data() {
      return {
        // Spinner
        color: '#5dc596',
        height: '10px',
        width: '4px',

        exhibitionData: {},
        exhibition_name_json: {},
        constantList: [],
        languageList: [],
        auctionClassificationList: [],
        bidCommitClassificationList: [],
        pitchWidthList: [],
        flag_options: [
          {label: 'なし', value: '0'},
          {label: 'あり', value: '1'},
        ],
        loading: true,
        errMsgArray: [],
        next: Function,
      };
    },
    mounted() {
      this.getConstants()
        .then(constants => {
          this.constantList = constants;
          this.getExhibitions().then(exhibition => {
            this.loading = false;
            this.exhibitionData = exhibition;
          });
        })
        .catch(error => {
          console.log(error);
          Methods.parseHtmlResponseError(this.$router, error);
        });
    },
    methods: {
      getConstants() {
        return Methods.apiExecute('get-constants-by-keys', {
          key_strings: [
            'AUCTION_CLASSIFICATION',
            'LANGUAGE_CODE',
            'BID_COMMIT_CLASSIFICATION',
            'PITCH_WIDTH',
            'DEFAULT_PITCH_WIDTH',
          ],
        }).then(response => {
          if (response.status === 200) {
            const constantList = response.data;

            for (const row of constantList) {
              switch (row.key_string) {
                case 'AUCTION_CLASSIFICATION':
                  this.auctionClassificationList.push({
                    label: row.value2,
                    value: row.value1,
                  });
                  break;
                case 'LANGUAGE_CODE':
                // TODO: 多言語対応項目の整形
                // for (const i of this.$cookies
                //   .get('language_code_list')
                //   .split(',')) {
                //   if (i === row.value1) {
                //     this.languageList.push({
                //       label: row.value2,
                //       value: row.value1,
                //     });
                //   }
                // }
                // break;
                case 'BID_COMMIT_CLASSIFICATION':
                  this.bidCommitClassificationList.push({
                    label: row.value2,
                    value: row.value1,
                  });
                  break;
                case 'PITCH_WIDTH':
                  this.pitchWidthList.push({
                    label: `${Number(row.value1).toLocaleString()}(${row.value2})`,
                    value: row.value1,
                  });
                  break;
                default:
                  break;
              }
            }

            console.log('constantList: ', constantList);

            if (typeof constantList === 'undefined' || constantList === null) {
              return Promise.resolve(null);
            }
            return Promise.resolve(constantList);
          }
          return Promise.resolve(null);
        });
      },
      getExhibitions() {
        const request = {
          exhibition_no: this.$route.params.id,
          preview_start_datetime_from: null,
          preview_start_datetime_to: null,
          start_datetime_from: null,
          start_datetime_to: null,
          previewFromDateFlag: true,
          previewToDateFlag: true,
          bidFromDateFlag: true,
          bidToDateFlag: true,
        };
        return Methods.apiExecute('get-exhibitions', request).then(response => {
          if (response.status === 200) {
            this.loading = false;
            const exhibitionData = response.data[0];

            // 取得データ設定
            for (const row of exhibitionData.localized_json_array) {
              exhibitionData[`exhibition_name_${row.f1}`] = row.f2;
            }
            for (const key of Object.keys(
              exhibitionData.exhibition_classification_info
            )) {
              switch (key) {
                case 'auctionClassification':
                  exhibitionData.auctionClassification = String(
                    exhibitionData.exhibition_classification_info[key]
                  );
                  break;
                case 'bidCommitClassification':
                  exhibitionData.bidCommitClassification = String(
                    exhibitionData.exhibition_classification_info[key]
                  );
                  break;
                case 'extendFlag':
                  exhibitionData.extendFlag = String(
                    exhibitionData.exhibition_classification_info[key]
                  );
                  break;
                case 'littleMoreDisplayFlag':
                  exhibitionData.littleMoreDisplayFlag = String(
                    exhibitionData.exhibition_classification_info[key]
                  );
                  break;
                default:
                  break;
              }
            }
            exhibitionData.pitchOption = exhibitionData.pitch_option;
            exhibitionData.auctionClassificationName =
              this.auctionClassificationList.filter(
                x => x.value === exhibitionData.auctionClassification
              )[0].label;
            exhibitionData.pitchWidth = this.constantList.filter(
              x =>
                x.key_string === 'DEFAULT_PITCH_WIDTH' &&
                x.value1 === exhibitionData.auctionClassification
            )[0].value2;
            for (const i of document.getElementsByName('extend_flag')) {
              i.disabled = true;
            }
            for (const i of document.getElementsByName(
              'little_more_display_flag'
            )) {
              i.disabled = true;
            }

            console.log('exhibitionData: ', exhibitionData);
            if (
              typeof exhibitionData === 'undefined' ||
              exhibitionData === null
            ) {
              return Promise.resolve(null);
            }
            return Promise.resolve(exhibitionData);
          }
          return Promise.resolve(null);
        });
      },
      getPitchWidth(pitchWidth) {
        return (
          this.pitchWidthList.find(x => x.value === pitchWidth)?.label || ''
        );
      },
      getFormatDateTime(datetime) {
        return Methods.getFormatDateTime(datetime);
      },
      goBack() {
        this.$router.push({path: '/auctions'});
      },
    },
  };
</script>
