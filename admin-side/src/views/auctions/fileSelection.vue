<template>
  <div>
    <div v-if="fileInfos">
      <div v-for="(fileInfo, index) in fileInfos" :key="index">
        <div class="row mb-2" v-if="true">
          <div class="col-sm-6">
            <a
              @contextmenu.prevent="$refs.menu.open($event, fileInfo.s3url)"
              @click="fileNameClickEvent(fileInfo.s3url)"
              href="javascript:void(0);"
              :style="
                fileInfo.s3url
                  ? ''
                  : 'color: inherit; text-decoration: inherit;'
              "
            >
              {{ fileInfo.fileName }}
            </a>
          </div>
          <div class="col-sm-5">
            <CButton
              @click="openFileDeleteModal(fileInfo)"
              :disabled="false"
              color="danger"
              block
              >添付ファイルを削除する</CButton
            >
          </div>
        </div>
      </div>
      <VueContext ref="menu">
        <li>
          <a href="#" @click.prevent="getFileViewUrl($refs.menu.data)"
            >ファイルを開く</a
          >
        </li>
        <li>
          <a href="#" @click.prevent="getFileDownloadUrl($refs.menu.data)"
            >ダウンロード</a
          >
        </li>
      </VueContext>
    </div>

    <div class="row mb-2">
      <div class="col-sm-2">
        <label class="btn btn-secondary btn-block">
          参照...
          <input
            type="file"
            ref="fileupload"
            multiple
            data-max="3"
            @change="selectFile"
            hidden
          />
        </label>
      </div>
      <div class="col-sm-4"></div>
    </div>

    <div class="row mb-2">
      <div class="col-sm-11">
        <div v-if="message" class="alert alert-light" role="alert">
          <ul>
            <li v-for="(ms, i) in message.split('\n')" :key="i">
              {{ ms }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="fileDeleteModal"
      @close="
        () => {
          fileDeleteModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 削除確認 </CModalTitle>
      </CModalHeader>
      <CModalBody> 添付ファイルを削除してもよろしいですか？ </CModalBody>
      <CModalFooter>
        <CButton @click="fileDeleteModal = false" color="light"
          >キャンセル</CButton
        >
        <CButton @click="deleteFile" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="fileUploadModal"
      @close="
        () => {
          fileUploadModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> アップロード確認 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        添付ファイルをアップロードしてもよろしいですか？
      </CModalBody>
      <CModalFooter>
        <CButton @click="fileUploadModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 入力エラー </CModalTitle>
      </CModalHeader>
      <CModalBody>
        {{ errorMsg }}
      </CModalBody>
      <CModalFooter>
        <CButton @click="errorModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import UploadFile from '@/api/uploadFileToS3';
  import VueContext from '@imengyu/vue3-context-menu';

  export default {
    name: 'fileSelection',
    components: {
      VueContext,
    },
    props: {
      items: Array,
      disabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        // Select file
        selectedFiles: null,
        message: '',
        // Delete file
        fileDeleteModal: false,
        deleteFileTemp: null,
        // Upload file
        fileUploadModal: false,

        // Data
        fileInfos: [],
        deleteFiles: [],

        // エラーmodal
        errorModal: false,
        errorMsg: '',
      };
    },
    watch: {
      items(newVal, oldVal) {
        if (newVal && oldVal && this.objectsAreIdentical(newVal, oldVal)) {
          return;
        }
        this.itemsChange(newVal, oldVal);
      },
      fileInfo(newVal, oldVal) {
        console.log('fileInfo changed: ', newVal, ' | was: ', oldVal);
        this.onChange();
      },
    },
    created() {
      this.itemsChange(this.items, null);
    },
    mounted() {
      this.getFiles(response => {
        // Console.log(`response. ${JSON.stringify(response)}`)
      });
    },
    methods: {
      getFiles(callback) {
        callback(this.items);
      },
      selectFile() {
        if (typeof this.fileInfos === 'undefined' || this.fileInfos === null) {
          this.fileInfos = [];
        }

        // 5個まで登録できる
        if (this.fileInfos.length >= 5) {
          this.errorModal = true;
          this.errorMsg = '添付ファイルは5個までしか登録できません。';
          this.$refs.fileupload.value = null;
          return;
        }

        this.selectedFiles = event.target.files;
        for (let i = 0; i < this.selectedFiles.length; i++) {
          if (this.checkFileSize(this.selectedFiles[i])) {
            this.errorModal = true;
            this.errorMsg = '5MB以内のファイルをアップロードしてください。';
            this.$refs.fileupload.value = null;
          } else {
            this.fileInfos.push({
              id: `local${this.fileInfos.length}`,
              fileName: this.selectedFiles[i].name,
              file: this.selectedFiles[i],
              s3url: null,
            });
          }
        }
        this.uploadFiles(event, updatedFiles => {
          console.log('updatedFiles', updatedFiles);
          this.onChange();
          this.$refs.fileupload.value = null;
        });
        //  This.$refs.fileupload.value=null
      },
      checkFileSize(file) {
        if (file?.size) {
          if (file.size > 5 * 1024 * 1024) {
            return true;
          }
        }
        return null;
      },
      uploadFiles(event, callback) {
        if (event) {
          event.preventDefault();
        }

        this.message = '';
        this.upload(0, this.fileInfos, null, s3response => {
          // Console.log(`s3response = ${JSON.stringify(s3response)}`)
          if (callback) {
            return callback(this.fileInfos);
          }
        });
      },
      upload(idx, fileList, s3response, callback) {
        // Console.log("idx = " + idx)
        if (
          typeof fileList === 'undefined' ||
          fileList === null ||
          idx >= fileList.length
        ) {
          return callback(s3response);
        }

        if (s3response === null) {
          s3response = [];
        }

        const fileInfo = fileList[idx];
        // Upload s3 unlinked items only
        if (fileInfo.s3url !== null && fileInfo.s3url !== '') {
          return this.upload(idx + 1, fileList, s3response, callback);
        }

        const api_type = 'exhibition-email';
        /*
         * If (this.display_code === '1') {
         *   api_type = 'exhibition-email'
         * }
         */
        UploadFile.upload(api_type, fileInfo.file, data => {
          // Console.log(`data. ${JSON.stringify(data)}`)

          if (data.status === 200) {
            if (
              typeof this.fileInfos !== 'undefined' &&
              this.fileInfos !== null
            ) {
              const fItem = this.fileInfos.find(item => item === fileInfo);
              if (typeof fItem !== 'undefined' && fItem !== null) {
                fItem.s3url = data.message;
              }
            }
          } else {
            const prevMessage = this.message ? `${this.message}\n` : '';
            this.message = prevMessage + data.message;
          }

          s3response.push(data.message);
          this.upload(idx + 1, fileList, s3response, callback);
        });
      },
      deleteFile() {
        const file = this.deleteFileTemp;
        // Console.log(`file. ${JSON.stringify(file)}`)
        if (file.s3url === null) {
          this.fileInfos = this.fileInfos.filter(item => {
            return item !== file;
          });
        } else {
          const fItem = this.fileInfos.find(item => item === file);
          if (typeof fItem === 'undefined' || fItem === null) {
            if (
              typeof this.deleteFiles === 'undefined' ||
              this.deleteFiles === null
            ) {
              this.deleteFiles = [];
            }
            this.deleteFiles.push(file);
          } else {
            this.fileInfos = this.fileInfos.filter(item => item != file);
          }
        }
        this.fileDeleteModal = false;
        this.deleteFileTemp = null;
        this.onChange();
      },
      onUploaded() {
        // Console.log("onUploaded: emitted")
        this.$emit('onUploaded', this.fileInfos);
      },
      openFileDeleteModal(file) {
        // Console.log(`openFileDeleteModal`)
        this.fileDeleteModal = true;
        this.deleteFileTemp = file;
      },
      objectsAreIdentical(obj1, obj2) {
        return (
          obj1.length === obj2.length &&
          JSON.stringify(obj1) === JSON.stringify(obj2)
        );
      },
      getFileDownloadUrl(fileUrl) {
        /*
         * Console.log("getFileDownloadUrl: " + fileUrl)
         * get download url from file server
         */
        if (fileUrl !== null && fileUrl !== '') {
          UploadFile.getDownloadUrl(fileUrl).then(res => {
            // Console.log("res: " + JSON.stringify(res))
            window.location.href = res;
          });
        }
      },
      getFileViewUrl(fileUrl) {
        /*
         * Console.log("getFileViewUrl")
         * get file viewing url from file server
         */
        if (fileUrl !== null && fileUrl !== '') {
          UploadFile.getFileViewUrl(fileUrl).then(res => {
            // Console.log("res: " + JSON.stringify(res))
            window.open(res, '_blank');
          });
        }
      },
      onChange() {
        console.log(`fileInfo: ${JSON.stringify(this.fileInfos)}`);
        this.$emit('onFileChange', this.fileInfos);
      },
      fileNameClickEvent(fileUrl) {
        /*
         * Console.log("fileNameClickEvent")
         * Filename click event handler
         */
        if (fileUrl !== null && fileUrl !== '') {
          UploadFile.getFile(fileUrl).then(res => {
            window.open(res, '_blank');
          });
        }
      },
      itemsChange(newVal, oldVal) {
        console.log('items changed: ', newVal, ' | was: ', oldVal);
        if (typeof this.items !== 'undefined' && this.items !== null) {
          const tmpItems = this.items;
          if (typeof tmpItems !== 'undefined' && tmpItems !== null) {
            this.fileInfos = [];
            for (let i = 0; i < tmpItems.length; i++) {
              const fileUrl = tmpItems[i];
              let actualFileName = '';
              if (typeof fileUrl !== 'undefined' && fileUrl !== null) {
                actualFileName = fileUrl.substring(
                  fileUrl.lastIndexOf('/') + 1,
                  fileUrl.length
                );
              }
              this.fileInfos.push({
                id: `local${this.fileInfos.length}`,
                fileName: actualFileName,
                file: null,
                s3url: tmpItems[i],
              });
            }
          }
        }
      },
    },
  };
</script>
