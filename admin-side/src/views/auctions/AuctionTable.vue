<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header" class="fl-r">
        <div style="float: left"><CIcon name="cil-grid" /> {{ caption }}</div>
      </slot>
      <span style="float: right; margin-left: 30px"
        >総件数: {{ totalCount }}件</span
      >
      <span style="float: right">検索結果: {{ currentCount }}件</span>
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        style="font-size: 12px"
        :loading="loading"
        :items="items"
        :fields="fields"
        :position="position"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        :itemsPerPageSelect="itemsPerPageSelect"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #row_number="{item}"
          ><td class="text-right" style="width: 50px">
            <div :id="'item_' + item.row_number">{{ item.row_number }}</div>
          </td></template
        >
        <template #exhibition_name="{item}">
          <td class="text-left">
            <div>{{ item.exhibition_name }}</div>
          </td>
        </template>
        <template #preview_start_datetime="{item}">
          <td class="text-center" style="width: 100px">
            <div>{{ getFormatDateTime(item.preview_start_datetime) }}</div>
          </td>
        </template>
        <template #start_datetime="{item}">
          <td class="text-center" style="width: 130px">
            <div>{{ getFormatDateTime(item.start_datetime) }}</div>
            <div>~</div>
            <div>{{ getFormatDateTime(item.end_datetime) }}</div>
          </td>
        </template>
        <template #preview_end_datetime="{item}">
          <td class="text-center" style="width: 100px">
            <div>{{ getFormatDateTime(item.preview_end_datetime) }}</div>
          </td>
        </template>
        <template #status="{item}"
          ><td class="text-left" style="min-width: 100px">
            <div>{{ item.status }}</div>
          </td></template
        >
        <template #auctionClassification="{item}">
          <td class="text-center" style="width: 90px">
            <div>{{ item.auctionClassification }}</div>
          </td>
        </template>
        <template #exhibition_item_count="{item}">
          <td class="text-right" style="min-width: 100px">
            <div>{{ item.exhibition_item_count.toLocaleString() }}点</div>
            <CButton
              v-if="roleId < 30 && !item.endFlag"
              size="sm"
              color="success"
              class="btn-detail"
              @click="editExhibitionLotByName(item)"
              >出展編集</CButton
            >
          </td>
        </template>
        <template #contract_count="{item}">
          <td class="text-right" style="min-width: 100px">
            <div>{{ item.contract_count.toLocaleString() }}点</div>
            <div>
              ({{
                item.exhibition_item_count === 0
                  ? '-'
                  : round1(
                      (item.contract_count / item.exhibition_item_count) * 100
                    )
              }}%)
            </div>
          </td>
        </template>
        <template #bid_count="{item}">
          <td class="text-right" style="min-width: 100px">
            <div>{{ item.bid_count.toLocaleString() }}件</div>
          </td>
        </template>
        <template #detail="{item}">
          <td v-if="roleId < 30" class="text-center" style="width: 85px">
            <CButton
              v-if="item.endFlag"
              size="sm"
              color="secondary"
              class="btn-detail mb-1"
              @click="handleDetailClick(item.exhibition_no)"
              >詳細</CButton
            >
            <CButton
              v-else
              size="sm"
              color="success"
              class="btn-detail mb-1"
              @click="editExhibition(item)"
              >編集</CButton
            >
            <CButton
              size="sm"
              color="secondary"
              class="btn-detail mb-1"
              @click="editExhibitionInfoMail(item)"
              >メール</CButton
            >
            <CButton
              size="sm"
              color="info"
              class="btn-detail"
              @click="exhibitionStatus(item)"
              >状況</CButton
            >
          </td>
          <td v-else class="text-center" style="width: 85px">
            <CButton
              size="sm"
              color="primary"
              class="btn-detail mb-1"
              @click="
                $router.push({
                  path: `/auctions/${item.exhibition_no}/detail`,
                })
              "
              >詳細</CButton
            >
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
    </CCardBody>
  </CCard>
</template>

<script setup>
  import Methods from '@/api/methods';
  import {CDataTable, CPagination} from '@/components/Table';
  import {itemsPerPageSelect} from '@/views/common/customTableView.js';
  import {defineEmits, defineProps, ref} from 'vue';
  import {useRouter} from 'vue-router';
  import {useLotStore} from '../../store/lot';

  const props = defineProps({
    items: Array,
    currentCount: {
      type: String,
      default: '0',
    },
    totalCount: {
      type: String,
      default: '0',
    },
    caption: {
      type: String,
      default: 'exhibitionTable',
    },
    position: Number,
    loading: Boolean,
    activePage: Number,
    itemsPerPage: Number,
    pages: Number,
    itemsSorter: Object,
    roleId: Number,
  });
  const emit = defineEmits([
    'get-position',
    'page-change',
    'sorter-change',
    'pagination-change',
  ]);

  const router = useRouter();
  const {setSelectedExhibition} = useLotStore();

  const btnClicked = ref(false);
  const fields = [
    {key: 'row_number', label: 'No', _classes: 'text-center'},
    {key: 'exhibition_name', label: '入札会名', _classes: 'text-center'},
    {
      key: 'preview_start_datetime',
      label: '下見開始日時',
      _classes: 'text-center',
    },
    {key: 'start_datetime', label: '入札日時', _classes: 'text-center'},
    {
      key: 'preview_end_datetime',
      label: '閲覧終了日時',
      _classes: 'text-center',
    },
    {key: 'status', label: 'ステータス', _classes: 'text-center'},
    {key: 'auctionClassification', label: '方式', _classes: 'text-center'},
    {key: 'exhibition_item_count', label: '出展数', _classes: 'text-center'},
    {key: 'contract_count', label: '成約数', _classes: 'text-center'},
    {key: 'bid_count', label: '総入札数', _classes: 'text-center'},
    {key: 'detail', label: '詳細', _classes: 'text-center'},
  ];

  const editExhibition = item => {
    if (btnClicked.value) {
      return;
    }
    btnClicked.value = true;
    emit('get-position', item.row_number);
    router.push({path: `/auctions/${item.exhibition_no}/edit`});
  };
  const editExhibitionInfoMail = item => {
    if (btnClicked.value) {
      return;
    }
    btnClicked.value = true;
    router.push({
      name: '入札会お知らせメール',
      params: {exhibition_name: item.exhibition_name},
    });
  };

  const exhibitionStatus = item => {
    if (btnClicked.value) {
      return;
    }
    btnClicked.value = true;
    emit('get-position', item.row_number);
    router.push({path: `/auctions/${item.exhibition_no}/status`});
  };

  const editExhibitionLot = item => {
    if (btnClicked.value) {
      return;
    }
    btnClicked.value = true;
    router.push({
      path: '/auctions',
      params: {prop_exhibition_no: item.exhibition_no},
    });
  };

  const pageChange = val => {
    if (props.items.length > 0) {
      emit('page-change', val);
    }
  };

  const sorterChange = val => {
    emit('sorter-change', val);
  };
  const paginationChange = val => {
    emit('pagination-change', val);
  };
  const getFormatDateTime = datetime => {
    return Methods.getFormatDateTime(datetime);
  };
  const editExhibitionLotByName = item => {
    if (btnClicked.value) {
      return;
    }
    btnClicked.value = true;
    setSelectedExhibition(item.exhibition_no);
    router.push({path: '/exhibitions'}); // 出展一覧へ遷移
  };
  const round1 = val => {
    const number = Number(val) || 0;
    return (Math.round(number * 100) / 100).toFixed(1);
  };
  const handleDetailClick = exhibitionNo => {
    router.push({path: `/auctions/${exhibitionNo}/detail`});
  };
</script>
<style type="text/css">
  .btn-detail {
    width: 100%;
  }
</style>
