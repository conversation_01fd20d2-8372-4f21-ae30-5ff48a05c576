<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardHeader>
        <strong>権限設定管理</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> 対象 </CCol>
            <CCol sm="5">
              <CFormSelect
                name="resource_type"
                :options="permissionObjects"
                v-model="search_condition.object"
              />
            </CCol>
          </CRow>
        </CForm>
        <CRow class="align-items-center mt-4">
          <CCol sm="5"></CCol>
          <CCol sm="2" class="mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="search" block>検索</CButton>
          </CCol>
          <CCol sm="1" />
          <CCol sm="2"></CCol>
          <CCol sm="2"></CCol>
        </CRow>
      </CCardBody>
      <CElementCover v-if="loading" :opacity="0.8" />
    </CCard>
    <CRow>
      <CCol sm="12">
        <ProductInfoTable
          name="resourceList"
          :items="dispResourceList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          caption="項目一覧"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
          @update-permission="confirmUpdatePermission"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isConfirmDialog"
      @close="
        () => {
          isConfirmDialog = false;
          errorMsg = '';
          errorStatus = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>登録確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!errorStatus">この内容で更新してもよろしいですか？</div>
        <div v-for="text in errorMsg" :key="text">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              isConfirmDialog = false;
              errorMsg = '';
              errorStatus = false;
            }
          "
          :disabled="loading"
          color="dark"
        >
          <div v-if="errorStatus">OK</div>
          <div v-if="!errorStatus">キャンセル</div>
        </CButton>
        <CButton
          v-if="!errorStatus"
          @click="updatePermission"
          :disabled="loading"
          :loading="loading"
          color="primary"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      title="確認"
      color="primary"
      v-model:show="isErrorDialog"
      :closeOnBackdrop="false"
    >
      <div v-if="validateResult">
        <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
      </div>
      <template #header>
        <h5 class="modal-title">確認</h5>
        <CCloseButton @click="isErrorDialog = false" />
      </template>
      <template #footer>
        <CButton @click="isErrorDialog = false" color="dark">閉じる</CButton>
      </template>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="successModal"
      @close="
        () => {
          successModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="reloadPage" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
          btnClicked = false;
          next(false);
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{
          (selectStatus ? '登録' : '編集') + '中止確認'
        }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              cancelModal = false;
              btnClicked = false;
              next(false);
            }
          "
          color="dark"
          >キャンセル</CButton
        >
        <CButton
          @click="
            () => {
              cancelModal = false;
              next();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="failModal"
      @close="
        () => {
          failModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>失敗</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>更新エラーが発生しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="failModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {ScaleLoader} from '@/components/Table';
  import {useCommonStore} from '@/store/common';
  import {CElementCover} from '@/components/Table';
  import {useRoute, useRouter} from 'vue-router';
  import ProductInfoTable from '../../components/tenant/permission/PermissionTable.vue';

  export default {
    name: 'ProductInfo',
    components: {
      ProductInfoTable,
      ScaleLoader,
    },
    setup() {
      const store = useCommonStore();
      return {store};
    },
    data() {
      return {
        loading: true,
        router: useRouter(),
        // 検索条件
        permissionObjects: [
          {label: '', value: null},
          {label: '会員', value: '1'},
          {label: '商品', value: '2'},
        ],

        // Screen params
        resourceList: [],
        dispResourceList: [],
        search_condition: {
          object: null,
        },
        activePage: 1,
        itemsPerPage: 10,
        pages: 1,
        itemsSorter: {},

        // Counting
        current_count: 0,
        total_count: 0,

        // CSV用
        validateResult: [],

        // Error dialog
        isErrorDialog: false,
        isConfirmDialog: false,
        cancelModal: false,
        successModal: false,
        failModal: false,
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;

        // 初期化
        vm.store.set(['resourceSearchCondition', {object: '1'}]);
        vm.store.set(['activePage', 1]);
        vm.store.set(['position', 0]);
        vm.store.set(['itemsPerPage', 10]);
        vm.store.set(['itemsSorter', {asc: true, column: 'resource_type'}]);
      });
    },
    async mounted() {
      await this.getPermission();
    },
    watch: {
      $route: {
        immediate: true,
        handler(route) {
          if (route.query && route.query.page) {
            this.activePage = Number(route.query.page);
          }
        },
      },
      search_condition: {
        handler(newVal) {
          this.store.set(['resourceSearchCondition', newVal]);
        },
        deep: true,
        immediate: false,
      },
      itemsPerPage(newVal) {
        if (this.resourceList.length > newVal) {
          this.pages =
            parseInt(this.resourceList.length / newVal, 10) +
            (this.resourceList.length % newVal > 0 ? 1 : 0);
        } else {
          this.pages = 1;
        }
      },
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
    },
    methods: {
      getPermission() {
        Methods.apiExecute('get-admin-role')
          .then(response => {
            if (response.status === 200) {
              this.resourceList = response.data;
              console.log('response:', response.data);
              console.log('権限情報:', this.resourceList);

              for (const resource of this.resourceList) {
                if (
                  resource.function_id === 1 &&
                  resource.target_group_id === 1
                ) {
                  resource.rowspan = 5;
                } else if (
                  resource.function_id === 1 &&
                  resource.target_group_id === 2
                ) {
                  resource.rowspan = 4;
                }
              }

              this.dispResourceList = this.resourceList;
              console.log('権限情報（後）:', this.resourceList);
            }
            this.loading = false;
          })
          .catch(error => {
            console.log(error);
            this.loading = false;
            this.validateResult = Methods.parseHtmlResponseError(
              this.router,
              error
            );
          });
      },
      confirmUpdatePermission() {
        this.isConfirmDialog = true;
      },
      updatePermission() {
        // 処理を実行
        // モーダル系のフラグ
        this.loading = true;
        this.isConfirmDialog = false;
        console.log('resourceList:', this.resourceList);

        Methods.apiExecute('update-admin-role', this.resourceList)
          .then(response => {
            console.log(`response: ${JSON.stringify(response)}`);
            this.isConfirmDialog = false;
            this.loading = false;
            if (response.status === 200) {
              this.successModal = true;
            } else {
              this.failModal = true;
            }
          })
          .catch(error => {
            console.log(`error: ${JSON.stringify(error)}`);
            this.loading = false;
            this.isConfirmDialog = false;
            if (
              error.response &&
              (error.response.status === 400 || error.response.status === 409)
            ) {
              this.failModal = true;
            }
            Methods.parseHtmlResponseError(this.$router, error);
          });

        this.router.push({name: 'UpdatePermission'}).catch(() => {});
      },
      search() {
        // 検索条件に応じてresourceListを絞り込む
        if (this.search_condition.object === '1') {
          this.dispResourceList = this.resourceList.filter(
            item => item.target_group_id === 1
          );
        } else if (this.search_condition.object === '2') {
          this.dispResourceList = this.resourceList.filter(
            item => item.target_group_id === 2
          );
        } else {
          this.dispResourceList = this.resourceList;
        }
      },
      pageChange(val) {
        this.store.set(['activePage', val]);
        this.$router.push({query: {page: val}}).catch(() => {});
      },
      paginationChange(val) {
        this.itemsPerPage = val;
        this.store.set(['itemsPerPage', val]);
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
        this.pageChange(1);
      },
      reloadPage(event) {
        console.log('reloadPage');
        this.successModal = false;
        this.router.go(0);
      },
    },
  };
</script>
