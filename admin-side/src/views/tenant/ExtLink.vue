<script setup>
  import {ref} from 'vue';
  import GmoCloudEC from '../../components/tenant/extlink/GmoCloudEC.vue';
  import GmoPaymentGateway from '../../components/tenant/extlink/GmoPaymentGateway.vue';
  import SFTP from '../../components/tenant/extlink/SFTP.vue';
  import ShareThis from '../../components/tenant/extlink/ShareThis.vue';
  const tabPanePillsActiveKey = ref(1);
</script>
<template>
  <CCard class="text-center mb-3">
    <CCardHeader>
      <!-- <span>外部連携管理</span> -->
      <CNav variant="tabs" class="card-header-tabs">
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 1"
            @click="
              () => {
                tabPanePillsActiveKey = 1;
              }
            "
          >
            GMO PaymentGateway
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 2"
            @click="
              () => {
                tabPanePillsActiveKey = 2;
              }
            "
          >
            GMO クラウドEC
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 3"
            @click="
              () => {
                tabPanePillsActiveKey = 3;
              }
            "
          >
            SFTP
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 4"
            @click="
              () => {
                tabPanePillsActiveKey = 4;
              }
            "
          >
            ShareThis
          </CNavLink>
        </CNavItem>
      </CNav>
    </CCardHeader>
    <CCardBody>
      <CTabContent>
        <CTabPane
          role="tabpanel"
          aria-labelledby="home-tab"
          :visible="tabPanePillsActiveKey === 1"
        >
          <GmoPaymentGateway />
        </CTabPane>
        <CTabPane
          role="tabpanel"
          aria-labelledby="profile-tab"
          :visible="tabPanePillsActiveKey === 2"
        >
          <GmoCloudEC />
        </CTabPane>
        <CTabPane
          role="tabpanel"
          aria-labelledby="profile-tab"
          :visible="tabPanePillsActiveKey === 3"
        >
          <SFTP />
        </CTabPane>
        <CTabPane
          role="tabpanel"
          aria-labelledby="profile-tab"
          :visible="tabPanePillsActiveKey === 4"
        >
          <ShareThis />
        </CTabPane>
      </CTabContent>
    </CCardBody>
  </CCard>
</template>
