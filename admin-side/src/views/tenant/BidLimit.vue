<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardHeader>
        <strong>入札上限額設定</strong>
      </CCardHeader>
      <CCardBody>
        <AddNewItem
          :initialItem="latestBidLimit"
          @add="validOrUpdateBidLimit"
        />
      </CCardBody>
    </CCard>
    <CRow>
      <CCol sm="12">
        <BidLimitTable
          name="resourceList"
          :items="resourceList"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          caption="設定履歴"
        />
        <CElementCover v-if="loading" :opacity="0.8" />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isErrorDialog"
      @close="
        () => {
          isErrorDialog = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody closeButton>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="isErrorDialog = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isConfirmDialog"
      @close="
        () => {
          isConfirmDialog = false;
          errorMsg = '';
          errorStatus = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>登録確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!errorStatus">この内容で保存してもよろしいですか？</div>
        <div v-for="text in errorMsg" :key="text">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              isConfirmDialog = false;
              errorMsg = '';
              errorStatus = false;
            }
          "
          :disabled="loading"
          color="dark"
        >
          <div v-if="errorStatus">OK</div>
          <div v-if="!errorStatus">キャンセル</div>
        </CButton>
        <CButton
          v-if="!errorStatus"
          @click="updateCreditRemaining"
          :disabled="loading"
          :loading="loading"
          color="primary"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="successModal"
      @close="
        () => {
          successModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="reloadPage" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
          btnClicked = false;
          next(false);
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{
          (selectStatus ? '登録' : '編集') + '中止確認'
        }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              cancelModal = false;
              btnClicked = false;
              next(false);
            }
          "
          color="dark"
          >キャンセル</CButton
        >
        <CButton
          @click="
            () => {
              cancelModal = false;
              next();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="failModal"
      @close="
        () => {
          failModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>失敗</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>更新エラーが発生しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="failModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '@/store/common';
  import {computed, onMounted, ref, watch} from 'vue';
  import {CElementCover} from '@/components/Table';
  import {useRoute, useRouter} from 'vue-router';
  import AddNewItem from '../../components/tenant/bidLimit/AddNewItem.vue';
  import BidLimitTable from '../../components/tenant/bidLimit/BidLimitTable.vue';

  const route = useRoute();
  const router = useRouter();
  const store = useCommonStore();

  const loading = ref(true);

  // Screen params
  const resourceList = ref([]);

  // Counting
  const current_count = ref(0);
  const total_count = ref(0);

  // CSV用
  const validateResult = ref([]);

  // dialog
  const isErrorDialog = ref(false);
  const isConfirmDialog = ref(false);
  const cancelModal = ref(false);
  const successModal = ref(false);
  const failModal = ref(false);

  // 入札上限額設定のデータ
  const bidLimitData = ref({
    is_bid_limit_flag: null,
    limit_amount: null,
    reset_type: null,
    reset_date: null,
    start_date: null,
    end_date: null,
  });

  // 入札上限額設定のデータ
  const latestBidLimit = ref({
    is_bid_limit_flag: null,
    limit_amount: null,
    reset_type: null,
    reset_date: null,
    start_date: null,
    end_date: null,
  });

  onMounted(() => {
    loading.value = true;
    Methods.apiExecute('get-tenant-credit-remaining')
      .then(response => {
        if (response.status === 200) {
          resourceList.value = response.data.filter(
            item => item.bid_limit_flag === 1
          );

          if (response.data) {
            const raw =
              response.data
                .slice()
                .sort(
                  (a, b) =>
                    (b.tenant_credit_remaining_history_no || 0) -
                    (a.tenant_credit_remaining_history_no || 0)
                )[0] || null;

            latestBidLimit.value = JSON.parse(JSON.stringify(raw));

            // latestBidLimit.value.bid_limit_flagが0ならFalse、1ならTrue
            latestBidLimit.value.bid_limit_flag =
              latestBidLimit.value.bid_limit_flag === 1;
            latestBidLimit.value.reset_type =
              latestBidLimit.value.reset_type === null
                ? null
                : latestBidLimit.value.reset_type === 0
                  ? 'auction'
                  : 'period';
            latestBidLimit.value.reset_date =
              latestBidLimit.value.reset_date === null
                ? null
                : latestBidLimit.value.reset_date;
            latestBidLimit.value.start_datetime = formatDate(
              latestBidLimit.value.start_datetime
            );
            latestBidLimit.value.end_datetime = formatDate(
              latestBidLimit.value.end_datetime
            );
          }
        }
        loading.value = false;
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        validateResult.value = Methods.parseHtmlResponseError(router, error);
      });
  });

  const formatDate = date => {
    if (!date) return null;
    const d = date instanceof Date ? date : new Date(date);
    if (isNaN(d)) return null; // 無効な日付はnull

    const yyyy = d.getFullYear();
    const mm = String(d.getMonth() + 1).padStart(2, '0'); // 月は0始まり
    const dd = String(d.getDate()).padStart(2, '0');

    return `${yyyy}-${mm}-${dd}`; // yyyy-MM-dd
  };

  const validOrUpdateBidLimit = item => {
    console.log('validOrUpdateBidLimit');

    bidLimitData.value = item;

    if (bidLimitData.value.is_bid_limit_flag) {
      // 入力値があるので入力確認を行う
      validateBidLimit(bidLimitData.value);
    } else {
      // 入力値がないので、そのまま入札上限額設定を更新
      isConfirmDialog.value = true;
    }
  };

  const validateBidLimit = item => {
    console.log('validateBidLimit', item);
    const errors = [];

    // 入札上限額が数値かどうかチェック
    if (!item.limit_amount) {
      errors.push('入札上限額(円)を入力してください。');
    } else if (isNaN(item.limit_amount)) {
      errors.push('入札上限額(円)は数値で入力してください。');
    } else if (item.limit_amount < 1) {
      errors.push('入札上限額(円)は1円以上を入力してください。');
    }

    // リセット形式が選択されているかどうかチェック
    // 基本的にデフォルト値が入っている
    if (!item.reset_type) {
      errors.push('リセット型式を選択してください。');
    }

    // リセット型式が「期間」で、リセット日付が選択されているかどうかチェック
    // 基本的にデフォルト値が入っている
    if (item.reset_type === 'period' && !item.reset_date) {
      errors.push('リセット日付を選択してください。');
    }

    // 適用開始日が入力されているかどうかチェック
    if (!item.start_date) {
      errors.push('適用開始日を入力してください。');
    }

    // 適用終了日が入力されているかどうかチェック
    if (!item.end_date) {
      errors.push('適用終了日を入力してください。');
    }

    // 適用開始日が適用終了日より前かどうかチェック
    if (item.start_date && item.end_date) {
      const startDate = new Date(item.start_date);
      const endDate = new Date(item.end_date);
      if (startDate > endDate) {
        errors.push('適用開始日は適用終了日より前に設定してください。');
      }
    }

    if (errors.length > 0) {
      isErrorDialog.value = true;
      validateResult.value = errors;
    } else {
      isConfirmDialog.value = true;
    }
  };

  const updateCreditRemaining = () => {
    console.log('updateCreditRemaining', bidLimitData.value);

    // モーダル系のフラグ
    loading.value = true;
    isConfirmDialog.value = false;

    // 入力値を数値・日付に変換
    const bidLimit = JSON.parse(JSON.stringify(bidLimitData.value));
    if (bidLimitData.value.is_bid_limit_flag) {
      bidLimit.is_bid_limit_flag = 1;
      bidLimit.limit_amount = Number(bidLimitData.value.limit_amount);
      bidLimit.reset_type = bidLimitData.value.reset_type === 'auction' ? 0 : 1;
      bidLimit.reset_date =
        bidLimitData.value.reset_type === 'auction'
          ? null
          : Number(bidLimitData.value.reset_date);
    } else {
      bidLimit.is_bid_limit_flag = 0;
      bidLimit.limit_amount = null;
      bidLimit.reset_type = null;
      bidLimit.reset_date = null;
      bidLimit.start_date = null;
      bidLimit.end_date = null;
    }

    Methods.apiExecute('upsert_tenant_credit_remaining', bidLimit)
      .then(response => {
        console.log(`response: ${JSON.stringify(response)}`);
        isConfirmDialog.value = false;
        loading.value = false;
        if (response.status === 200) {
          successModal.value = true;
        } else {
          failModal.value = true;
        }
      })
      .catch(error => {
        loading.value = false;
        isConfirmDialog.value = false;
        if (
          error.response &&
          (error.response.status === 400 || error.response.status === 409)
        ) {
          failModal.value = true;
        }
        Methods.parseHtmlResponseError(router, error);
      });
  };

  const reloadPage = event => {
    console.log('reloadPage');
    successModal.value = false;
    router.go(0);
  };
</script>
