<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> 対象 </CCol>
            <CCol sm="5">
              <CFormSelect
                name="resource_type"
                :options="objectOptions"
                v-model="search_condition.object"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="2"> 外部システム </CCol>
            <CCol sm="5" class="form-inline">
              <CFormSelect
                name="language_code"
                :options="extSystemOptions"
                v-model="search_condition.extSystem"
                class="form-group col-sm-3 pl-0"
                addInputClasses="w-100"
              />
            </CCol>
          </CRow>
        </CForm>

        <CRow class="align-items-center mt-4">
          <CCol sm="5"></CCol>
          <CCol sm="2" class="mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="search" block>検索</CButton>
          </CCol>
          <CCol sm="1" />
          <CCol sm="2"></CCol>
          <CCol sm="2"></CCol>
        </CRow>
      </CCardBody>
    </CCard>
    <CRow>
      <CCol sm="12">
        <ItemTable
          name="resourceList"
          :items="resourceList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          caption="項目一覧"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
          @add-new-item="addNewItem"
          @delete-item="deleteItem"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isErrorDialog"
      @close="
        () => {
          isErrorDialog = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody closeButton>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="isErrorDialog = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '@/store/common';
  import {computed, onMounted, ref, watch} from 'vue';
  import {useRoute, useRouter} from 'vue-router';
  import ItemTable from '../../components/tenant/itemMapping/ItemMappingTable.vue';

  const route = useRoute();
  const router = useRouter();
  const store = useCommonStore();

  const loading = ref(true);
  const constantList = ref([]);
  // 検索条件
  const objectOptions = ref([]);
  const extSystemOptions = ref([]);

  // Screen params
  const resourceList = ref([]);
  const search_condition = ref({
    object: '1',
    extSystem: '1',
  });
  const activePage = ref(1);
  const itemsPerPage = ref(10);
  const pages = ref(1);
  const itemsSorter = ref({asc: true, column: 'system_column_name'});

  // Counting
  const current_count = ref(0);
  const total_count = ref(0);

  // CSV用
  const validateResult = ref([]);

  // Error dialog
  const isErrorDialog = ref(false);

  /*
   * BeforeRouteEnter(to, from, next) {
   *   next(vm => {
   *     vm.prevRoute = from
   */

  /*
   *     // 初期化
   *     vm.store.set(['resourceSearchCondition', {object : '1', extSystem : '1'}])
   *     vm.store.set(['activePage', 1])
   *     vm.store.set(['position', 0])
   *     vm.store.set(['itemsPerPage', 10])
   *     vm.store.set(['itemsSorter', {asc : true, column : 'resource_type'}])
   *   })
   * }
   */

  onMounted(() => {
    getConstants()
      .then(() => {
        getResourceList()
          .then(postage => {
            resourceList.value = postage;

            itemsPerPage.value = store.itemsPerPage;

            pages.value =
              parseInt(resourceList.value.length / itemsPerPage.value, 10) +
              (resourceList.value.length % itemsPerPage.value > 0 ? 1 : 0);
            activePage.value =
              store.activePage > pages.value
                ? Number(pages.value)
                : store.activePage;
            router.push({query: {page: activePage.value}}).catch(() => {});
          })
          .catch(error => {
            console.log(error);
            loading.value = false;
            isErrorDialog.value = true;
            validateResult.value = Methods.parseHtmlResponseError(
              router,
              error
            );
          });
      })
      .catch(error => {
        console.log(error);
        Methods.parseHtmlResponseError(router, error);
      });
  });

  watch(
    () => route.query,
    query => {
      if (query?.page) {
        activePage.value = Number(query.page);
      }
    }
  );

  watch(
    () => search_condition,
    newVal => {
      store.set(['resourceSearchCondition', newVal]);
    }
  );

  watch(
    () => itemsPerPage,
    newVal => {
      if (resourceList.value.length > newVal) {
        pages.value =
          parseInt(resourceList.value.length / newVal, 10) +
          (resourceList.value.length % newVal > 0 ? 1 : 0);
      } else {
        pages.value = 1;
      }
    }
  );

  const totalCount = computed(() => {
    return Base.number2string(total_count.value);
  });
  const currentCount = computed(() => {
    return Base.number2string(current_count.value);
  });

  const getConstants = () => {
    loading.value = true;
    return Methods.apiExecute('get-constants-by-keys', {
      key_strings: ['TENANT_OBJECT', 'EXT_SYSTEM'],
    }).then(response => {
      if (response.status === 200) {
        constantList.value = response.data;
        objectOptions.value.push({value: 1, label: '会員'});
        objectOptions.value.push({value: 2, label: '商品'});
        objectOptions.value.push({value: 3, label: '落札結果'});

        extSystemOptions.value.push({value: 1, label: 'クラウドEC'});
        extSystemOptions.value.push({value: 2, label: '外部システム2'});
        return Promise.resolve();
      }
      return Promise.resolve();
    });
  };

  const search = () => {
    isErrorDialog.value = false;
    if (
      search_condition.value.object === null &&
      search_condition.value.extSystem === null
    ) {
      validateResult.value = ['1つ以上の条件を選択してください。'];
      isErrorDialog.value = true;
      return;
    }
    getResourceList()
      .then(data => {
        resourceList.value = data;

        pages.value =
          parseInt(resourceList.value.length / itemsPerPage.value, 10) +
          (resourceList.value.length % itemsPerPage.value > 0 ? 1 : 0);
        sorterChange({asc: true, column: 'resource_type'});
      })
      .catch(error => {
        loading.value = false;
        Methods.parseHtmlResponseError(router, error);
      });
  };

  const getResourceList = () => {
    total_count.value = 0;
    current_count.value = 0;

    console.log('検索条件:', search_condition.value);

    // Request to server
    return Methods.apiExecute(
      'get-item-mapping-list',
      search_condition.value
    ).then(response => {
      if (response.status === 200) {
        loading.value = false;
        const resourceList = response.data.data;
        total_count.value = response.data ? response.data.total_count || 0 : 0;
        current_count.value = response.data
          ? response.data.current_count || 0
          : 0;
        return Promise.resolve(resourceList);
      }
      return Promise.resolve(null);
    });
  };

  const pageChange = val => {
    store.set(['activePage', val]);
    router.push({query: {page: val}}).catch(() => {});
  };
  const paginationChange = val => {
    itemsPerPage.value = val;
    store.set(['itemsPerPage', val]);
  };
  const sorterChange = val => {
    itemsSorter.value = val;
    store.set(['itemsSorter', val]);
    pageChange(1);
  };
  const addNewItem = () => {
    console.log('addNewItem');
    resourceList.value.push({
      id: resourceList.value.length + 1,
      auction_item_name: '',
      export_column_name: '',
    });
  };
  const deleteItem = id => {
    resourceList.value = resourceList.value.filter(item => item.id !== id);
  };
</script>
