<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardHeader>
        <strong>CSV項目設定管理</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> テンプレート選択 </CCol>
            <CCol sm="5">
              <CFormSelect
                name="template"
                :options="templateOptions"
                v-model="search_condition.template_id"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="2"> 言語 </CCol>
            <CCol sm="3" class="form-inline">
              <CFormSelect
                name="language_code"
                :options="languageOptions"
                v-model="search_condition.language_code"
                class="form-group col-sm-3 pl-0"
                addInputClasses="w-100"
              />
            </CCol>
          </CRow>
        </CForm>

        <CRow class="align-items-center mt-4">
          <CCol sm="5"></CCol>
          <CCol sm="2" class="mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="search" block>検索</CButton>
          </CCol>
          <CCol sm="1" />
          <CCol sm="2"></CCol>
          <CCol sm="2"></CCol>
        </CRow>
      </CCardBody>
    </CCard>
    <CRow>
      <span class="warn"
        >必ず保存ボタンをクリックし、変更内容を保存してください。</span
      >
    </CRow>
    <CRow>
      <CCol sm="12">
        <CsvItemTable
          name="resourceList"
          :items="resourceList"
          :total_count="totalCount"
          :current_count="currentCount"
          :fieldList="fieldList"
          :notEditableList="notEditableList"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          caption="項目一覧"
          @add-new-item="addNewItem"
          @delete-item="deleteItem"
          @move-up="moveUp"
          @move-down="moveDown"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isErrorDialog"
      @close="
        () => {
          isErrorDialog = false;
          errorMsg = [];
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody closeButton>
        <div v-if="errorMsg">
          <div v-for="(val, i) in errorMsg" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            isErrorDialog = false;
            errorMsg = [];
          "
          color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="confirmModal"
      @close="
        () => {
          confirmModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>この内容で保存してもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="confirmModal = false" color="dark">キャンセル</CButton>
        <CButton @click="saveDisplayItems" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader :closeButton="false">
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              compModal = false;
            }
          "
          color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>

    <!-- 固定保存ボタン -->
    <div class="fixed-save-button">
      <CButton
        color="primary"
        size="sm"
        @click="confirmModal = true"
        :disabled="loading"
      >
        <i class="fas fa-save"></i> 保存
      </CButton>
    </div>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '@/store/common';
  import {computed, onMounted, ref, watch} from 'vue';
  import {useRouter} from 'vue-router';
  import CsvItemTable from '../../components/tenant/csvItem/CsvItemTable.vue';

  const router = useRouter();
  const store = useCommonStore();

  const loading = ref(true);
  // 検索条件
  const templateOptions = ref([]);
  const languageOptions = ref([]);

  // Screen params
  const resourceList = ref([]);
  const search_condition = ref({
    template_id: '1',
    language_code: 'ja',
  });

  // Counting
  const current_count = ref(0);
  const total_count = ref(0);

  // notEditableList
  const notEditableList = ref([]);

  // Error dialog
  const isErrorDialog = ref(false);
  const errorMsg = ref([]);

  // Confirm dialog
  const confirmModal = ref(false);

  // Completion dialog
  const compModal = ref(false);

  // 選択言語
  const preLanguageCode = ref('ja');

  // テンプレートリスト
  const constTemplateOptions = ref([
    {value: '1', label: '入札履歴'},
    {value: '2', label: '入札順位'},
    {value: '3', label: '入札結果'},
  ]);

  // 言語リスト
  const constLanguageOptions = ref([
    {value: 'ja', label: '日本語'},
    {value: 'en', label: '英語'},
    {value: 'fr', label: 'フランス語'},
    {value: 'de', label: 'ドイツ語'},
    {value: 'es', label: 'スペイン語'},
  ]);

  // 項目リスト
  const fieldList = ref([]);

  onMounted(() => {
    loading.value = true;
    getConstants()
      .then(() => {
        getResourceList()
          .then(postage => {
            resourceList.value = postage;
            loading.value = false;
          })
          .then(() => {
            // 全項目名の取得
            getFieldList()
              .then(() => {
                loading.value = false;
                preLanguageCode.value = search_condition.value.language_code;
              })
              .catch(error => {
                console.log(error);
                loading.value = false;
                errorMsg.value = Methods.parseHtmlResponseError(router, error);
                isErrorDialog.value = true;
              });
          })
          .catch(error => {
            console.log(error);
            loading.value = false;
            errorMsg.value = Methods.parseHtmlResponseError(router, error);
            isErrorDialog.value = true;
          });
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
        isErrorDialog.value = true;
      });
  });

  watch(
    () => search_condition,
    newVal => {
      store.set(['resourceSearchCondition', newVal]);
    }
  );

  const totalCount = computed(() => {
    return Base.number2string(total_count.value);
  });

  const currentCount = computed(() => {
    return Base.number2string(current_count.value);
  });

  const getConstants = () => {
    return Methods.apiExecute('get-tenant-language-list', {}).then(response => {
      if (response.status === 200) {
        // テンプレート
        constTemplateOptions.value.forEach(element => {
          templateOptions.value.push({
            value: element.value,
            label: element.label,
          });
        });

        // 言語リスト
        languageOptions.value = response.data.language_code_list.map(cd => {
          const lang_list = constLanguageOptions.value.find(
            item => item.value === cd
          );
          return {
            value: cd,
            label: lang_list ? lang_list.label : '',
          };
        });
        return Promise.resolve();
      }
    });
  };

  const search = () => {
    isErrorDialog.value = false;
    if (
      search_condition.value.template_id === '' &&
      search_condition.value.language_code === ''
    ) {
      errorMsg.value = ['1つ以上の条件を選択してください。'];
      isErrorDialog.value = true;
      return;
    }
    loading.value = true;
    getResourceList()
      .then(postage => {
        resourceList.value = postage;
        if (preLanguageCode.value !== search_condition.value.language_code) {
          getFieldList()
            .then(() => {
              preLanguageCode.value = search_condition.value.language_code;
              loading.value = false;
            })
            .catch(error => {
              console.log(error);
              loading.value = false;
              errorMsg.value = Methods.parseHtmlResponseError(router, error);
              isErrorDialog.value = true;
            });
        } else {
          loading.value = false;
        }
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
        isErrorDialog.value = true;
      });
  };

  const getResourceList = () => {
    // Request to server
    return Methods.apiExecute(
      'get-csv-field-list',
      search_condition.value
    ).then(response => {
      if (response.status === 200) {
        const resourceList = response.data.data;
        total_count.value = response.data ? response.data.total_count || 0 : 0;
        current_count.value = response.data
          ? response.data.current_count || 0
          : 0;
        notEditableList.value = response.data.not_editable_list || [];
        return Promise.resolve(resourceList);
      }
      return Promise.resolve(null);
    });
  };

  const getFieldList = () => {
    // 全項目名の取得
    const param = {
      language_code: search_condition.value.language_code,
    };
    return Methods.apiExecute('get-field-all-list', param).then(response => {
      if (response.status === 200) {
        fieldList.value = response.data.data;
      }
      return Promise.resolve();
    });
  };

  const addNewItem = item => {
    if (resourceList.value.some(field => field.field_no === item.field_no)) {
      errorMsg.value = ['同じ項目は追加できません。'];
      isErrorDialog.value = true;
      return;
    }

    // 新しい配列を作成
    resourceList.value = [
      ...resourceList.value,
      {
        ...item,
        id: resourceList.value.length + 1,
      },
    ];
  };

  const deleteItem = item => {
    const index = resourceList.value.findIndex(
      field => field.field_no === item.field_no
    );
    if (index !== -1) {
      // 新しい配列を作成
      resourceList.value = resourceList.value.filter(
        field => field.field_no !== item.field_no
      );
    }
  };

  const moveUp = item => {
    const index = resourceList.value.findIndex(
      field => field.field_no === item.field_no
    );
    if (index > 0) {
      // 新しい配列を作成
      const newList = [...resourceList.value];
      [newList[index - 1], newList[index]] = [
        newList[index],
        newList[index - 1],
      ];
      resourceList.value = newList;
    }
  };

  const moveDown = item => {
    const index = resourceList.value.findIndex(
      field => field.field_no === item.field_no
    );
    if (index !== -1 && index < resourceList.value.length - 1) {
      // 新しい配列を作成
      const newList = [...resourceList.value];
      [newList[index], newList[index + 1]] = [
        newList[index + 1],
        newList[index],
      ];
      resourceList.value = newList;
    }
  };

  // DB変更処理
  const executeApiAndRefresh = async (apiName, params) => {
    loading.value = true;
    try {
      const response = await Methods.apiExecute(apiName, params);
      if (response.status === 200) {
        // 一覧の再取得
        const postage = await getResourceList();
        resourceList.value = postage;
        confirmModal.value = false;
        loading.value = false;
        compModal.value = true;
      }
    } catch (error) {
      console.log(error);
      confirmModal.value = false;
      loading.value = false;
      compModal.value = false;
      errorMsg.value = Methods.parseHtmlResponseError(router, error);
      isErrorDialog.value = true;
    }
  };

  const saveDisplayItems = async () => {
    if (resourceList.value.length === 0) {
      confirmModal.value = false;
      errorMsg.value = ['項目一覧が空のため、更新できません。'];
      isErrorDialog.value = true;
      return;
    } else {
      if (total_count.value === 0) {
        confirmModal.value = false;
        errorMsg.value = ['新規登録はできません。'];
        isErrorDialog.value = true;
        return;
      }
      // 更新処理
      const input_data_list = resourceList.value.map(item => item.field_no);
      const params = {
        template_id: Number(search_condition.value.template_id),
        language_code: search_condition.value.language_code,
        input_data_list: input_data_list,
      };
      await executeApiAndRefresh('update-csv-field-list', params);
    }
  };
</script>

<style scoped>
  .fixed-save-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    border-radius: 50px;
  }

  .fixed-save-button button {
    border-radius: 50px;
    padding: 12px 24px;
    font-weight: bold;
    transition: all 0.3s ease;
  }

  .warn {
    color: #f00;
  }
</style>
