<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>管理者登録</strong>
      </CCardHeader>
      <CCardBody>
        <CForm onsubmit="return false;">
          <CRow class="mb-3">
            <CCol sm="3" class="d-flex align-items-center">
              <label>氏名</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="4">
              <CFormInput name="admin_name" v-model="adminData.admin_name" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3" class="d-flex align-items-center">
              <label>メールアドレス</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="4">
              <CFormInput name="login_id" v-model="adminData.login_id" />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="3" class="d-flex align-items-center">
              <label>パスワード</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="4">
              <CFormInput
                name="password"
                autocomplete="new-password"
                v-model="adminData.password"
                type="password"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3"></CCol>
            <CCol sm="7" style="max-width: 100%">
              <label style="line-height: 25px"
                >※パスワードは8文字以上で、大文字・小文字・数字をすべて含めて指定してください。</label
              >
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3" class="d-flex align-items-center">
              <label>パスワード(確認)</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="4">
              <CFormInput
                name="password_confirm"
                v-model="adminData.password_confirm"
                type="password"
                autocomplete="new-password"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3"> 権限 </CCol>
            <CCol sm="4">
              <CFormCheck
                type="radio"
                inline
                v-for="role_options in role_options"
                :key="role_options.value"
                :label="role_options.label"
                :value="role_options.value"
                :custom="true"
                name="role_id"
                v-model="adminData.role_id"
              />
            </CCol>
          </CRow>
        </CForm>
        <CElementCover v-if="loading" :opacity="0.8" />
      </CCardBody>
      <CCardFooter>
        <CButton color="light" class="mx-1" @click="goBack"
          >登録を中止して一覧に戻る</CButton
        >
        <CButton color="primary" class="mx-1" @click="register"
          >登録する</CButton
        >
      </CCardFooter>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="registModal"
      @close="
        () => {
          registModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 確認 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loading && validateResult.length === 0">
          この内容で登録してもよろしいですか？
        </div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          v-if="validateResult.length === 0"
          @click="registModal = false"
          color="dark"
          :disabled="loading"
          >キャンセル
        </CButton>
        <CButton @click="btnRegistClicked" color="primary" :disabled="loading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="backModal"
      @close="
        () => {
          backModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 更新中止確認 </CModalTitle>
      </CModalHeader>
      <CModalBody> 入力内容は破棄されますがよろしいですか？ </CModalBody>
      <CModalFooter>
        <CButton @click="backModal = false" color="dark">キャンセル</CButton>
        <CButton
          @click="
            () => {
              backModal = false;
              next();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {ScaleLoader} from '@/components/Table';
  import {useAuthStore} from '@/store/auth';

  export default {
    name: 'AdminRegist',
    components: {ScaleLoader},
    data() {
      return {
        authStore: useAuthStore(),
        // Regist modal
        loading: false,
        validateResult: [],
        registModal: false,
        // Back modal
        backModal: false,
        // Check change
        origData: null,

        adminData: {
          admin_name: '',
          login_id: '',
          password: '',
          password_confirm: '',
          role_id: '10',
          delete_flag: '0',
        },
        role_options: [],
        status_options: [
          {label: '有効', value: '0'},
          {label: '無効', value: '1'},
        ],

        next: Function,
      };
    },
    mounted() {
      // Roleを取得する
      this.getConstantsData()
        .then(() => {
          // Set original data
          this.origData = JSON.parse(JSON.stringify(this.adminData));
        })
        .catch(error => {
          console.log(JSON.stringify(error));
          this.loading = false;
          Methods.parseHtmlResponseError(this.$router, error);
        });

      if (this.authStore.user.role_id === '30') {
        // 一般の管理者の場合は[/dashboard]ページに遷移する
        this.$router.push({path: '/dashboard'});
      }
    },
    beforeRouteLeave(to, from, next) {
      if (Base.objectsAreIdentical(this.origData, this.adminData)) {
        // eslint-disable-next-line callback-return
        next();
      } else {
        this.next = next;
        this.backModal = true;
      }
    },
    methods: {
      getConstantsData() {
        const params = {
          key_strings: ['ADMIN_ROLE'],
        };
        // Request to server
        return Methods.apiExecute('get-constants-by-keys', params).then(
          response => {
            if (response.status === 200) {
              const roles = response.data;
              this.role_options = [];
              for (let i = 0; i < roles.length; i++) {
                this.role_options.push({
                  value: roles[i].value1,
                  label: roles[i].value2,
                });
              }
            }
            return Promise.resolve();
          }
        );
      },
      register() {
        this.registModal = true;
        this.loading = false;
        this.validateResult = [];
      },
      btnRegistClicked() {
        if (this.validateResult.length > 0) {
          this.registModal = false;
          return;
        }
        this.loading = true;

        const params = {
          data: {
            admin_name: this.adminData.admin_name,
            login_id: this.adminData.login_id,
            password: this.adminData.password,
            password_confirm: this.adminData.password_confirm,
            role_id: this.adminData.role_id,
            delete_flag: this.adminData.delete_flag,
          },
        };
        console.log(`params = ${JSON.stringify(params)}`);
        // Request to server
        Methods.apiExecute('create-admin', params)
          .then(response => {
            this.loading = false;
            this.registModal = false;
            const admin_no = response.data;
            console.log(`status = ${JSON.stringify(response.status)}`);
            console.log(`admin_no = ${JSON.stringify(admin_no)}`);
            // Set original data
            this.origData = JSON.parse(JSON.stringify(this.adminData));
            this.goBack();
          })
          .catch(error => {
            this.loading = false;
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            );
          });
      },
      goBack() {
        console.log('goBack');
        this.$router.push({path: '/admins'});
      },
    },
  };
</script>

<style scoped>
  label {
    line-height: 35px;
  }
</style>
