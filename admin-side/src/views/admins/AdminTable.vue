<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" />{{ caption }} </slot>
      <div style="float: right">
        <CButton
          size="sm"
          color="primary"
          :disabled="authStore.user.role_id === '30'"
          @click="registAdmin"
          style="width: 160px; margin-left: auto"
          >新規登録</CButton
        >
      </div>
      <span style="float: right; margin-right: 30px"
        >総件数: {{ total_count }}件</span
      >
    </CCardHeader>
    <CCardBody>
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :loading="loading"
        :items="items"
        :fields="fields"
        @update:sorter-value="sorterChange"
      >
        <template #no="{index}">
          <td class="text-right">{{ index + 1 }}</td>
        </template>
        <template #delete_flag="{item}">
          <td>
            <div>{{ item.delete_flag === 0 ? '有効' : '無効' }}</div>
          </td>
        </template>
        <template #edit="{item}">
          <td>
            <CButton
              v-if="item.delete_flag"
              size="sm"
              color="dark"
              style="width: 100%"
              @click="editAdmin(item)"
              disabled
              class="button disabled"
              >編集</CButton
            >
            <CButton
              v-if="!item.delete_flag"
              size="sm"
              color="primary"
              :disabled="authStore.user.role_id === '30'"
              style="width: 100%"
              @click="editAdmin(item)"
              >編集</CButton
            >
          </td>
        </template>
      </CDataTable>
    </CCardBody>
  </CCard>
</template>

<script>
  import CDataTable from '@/components/Table/CDataTable.vue';
  import {CIcon} from '@coreui/icons-vue';
  import {CButton, CCard, CCardBody, CCardHeader} from '@coreui/vue';
  import {useAuthStore} from '@/store/auth';
  export default {
    name: 'AdminTable',
    components: {
      CButton,
      CCard,
      CCardHeader,
      CCardBody,
      CDataTable,
      CIcon,
    },
    props: {
      items: Array,
      total_count: {
        type: String,
        default: '0',
      },
      fields: {
        type: Array,
        default() {
          return [
            {
              key: 'no',
              label: 'No',
              _classes: 'text-right',
              _style: 'text-align: center !important;',
            },
            {key: 'admin_name', label: '氏名', _style: 'text-align: center'},
            {
              key: 'login_id',
              label: 'ログインID',
              _style: 'text-align: center',
            },
            {key: 'role_name', label: '権限', _style: 'text-align: center'},
            {
              key: 'delete_flag',
              label: '有効／無効',
              _style: 'text-align: center',
            },
            {key: 'edit', label: '編集', _classes: 'text-center'},
          ];
        },
      },
      caption: {
        type: String,
        default: 'adminTable',
      },
      loading: Boolean,
      itemsSorter: Object,
    },
    data() {
      return {
        btn_clicked: false,
        authStore: useAuthStore(),
      };
    },
    methods: {
      editAdmin(item) {
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.$router.push({path: `admins/${item.admin_no}/edit`});
      },
      registAdmin() {
        console.log('registAdmin');
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.$router.push({path: 'admins/regist'});
      },
      pageChange(val) {
        this.$router.push({query: {page: val}});
      },
      sorterChange(val) {
        this.$emit('sorter-change', val);
      },
    },
  };
</script>
