<template>
  <div>
    <CRow>
      <CCol sm="12">
        <CTableWrapper
          name="adminList"
          :items="adminList"
          :total_count="totalCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          caption="管理者一覧"
          :itemsSorter="itemsSorter"
          @sorter-change="sorterChange"
        />
      </CCol>
    </CRow>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '@/store/common';
  import {CCol, CRow} from '@coreui/vue';
  import CTableWrapper from './AdminTable.vue';
  import {useAuthStore} from '@/store/auth';

  export default {
    name: 'AdminList',
    components: {
      CTableWrapper,
      CRow,
      CCol,
    },
    setup() {
      const store = useCommonStore();
      return {store};
    },
    data() {
      return {
        authStore: useAuthStore(),
        loading: true,

        // Screen params
        adminList: [],
        admin_roles: {},
        itemsSorter: {asc: true, column: 'admin_name'},

        // Counting
        total_count: 0,
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;
        // 登録・編集画面からの遷移時のみ検索条件を保持する
        if (
          vm.prevRoute.name !== '管理者編集' &&
          vm.prevRoute.name !== '管理者登録'
        ) {
          // 初期化
          vm.store.set(['itemsSorter', {asc: true, column: 'admin_name'}]);
        }
      });
    },
    mounted() {
      this.checkRole()
        .then(() => {
          this.getConstantsData()
            .then(() => {
              this.getAdminsFromServer().then(admins => {
                this.adminList = admins;
                this.loading = false;
              });
            })
            .catch(error => {
              console.log(JSON.stringify(error));
              this.loading = false;
              const errMsg = Methods.parseHtmlResponseError(
                this.$router,
                error
              );
            });
        })
        .catch(error => {
          console.log(error);
        });
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
    },
    methods: {
      checkRole() {
        // 管理者のみ見える
        const role_id = this.authStore.user.role_id;
        console.log(`role_id: ${role_id}`);
        /*
         * If (String(role_id) !== String(10)) {
         *   this.$router.push({path : '/dashboard'}).catch(()=>{})
         *   return Promise.reject()
         * }
         */
        return Promise.resolve();
      },
      getConstantsData() {
        const params = {
          key_strings: ['ADMIN_ROLE'],
        };
        // Request to server
        return Methods.apiExecute('get-constants-by-keys', params).then(
          response => {
            if (response.status === 200) {
              const roles = response.data;
              this.admin_roles = {};
              for (let i = 0; i < roles.length; i++) {
                this.admin_roles[roles[i].value1] = roles[i].value2;
              }
            }
            console.log(`admin_roles = ${JSON.stringify(this.admin_roles)}`);
            return Promise.resolve();
          }
        );
      },
      getAdminsFromServer() {
        this.total_count = 0;

        // Request to server
        return Methods.apiExecute('get-admin-list', {}).then(response => {
          this.loading = false;
          if (response.status === 200) {
            const adminList = response.data;
            console.log(`adminList = ${JSON.stringify(adminList)}`);

            this.total_count = adminList ? adminList.length : 0;

            if (typeof adminList === 'undefined' || adminList === null) {
              return Promise.resolve(null);
            }
            // Get role name
            for (let i = 0; i < adminList.length; i++) {
              adminList[i].role_name = this.admin_roles[adminList[i].role_id];
            }

            // 登録・編集画面からの遷移時は閲覧してたページに戻す
            if (
              this.prevRoute &&
              (this.prevRoute.name === '管理者編集' ||
                this.prevRoute.name === '管理者登録')
            ) {
              this.itemsSorter = this.store.itemsSorter;
            }

            return Promise.resolve(adminList);
          }
          return Promise.resolve(null);
        });
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
      },
    },
  };
</script>
