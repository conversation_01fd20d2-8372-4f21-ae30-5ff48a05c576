<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>管理者編集</strong>
      </CCardHeader>
      <CCardBody>
        <CForm onsubmit="return false;">
          <CRow class="mb-3">
            <CCol sm="3" class="d-flex align-items-center">
              <label>氏名</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="4">
              <CFormInput
                name="admin_name"
                :disabled="authStore.user.role_id === '30'"
                v-model="adminData.admin_name"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3" class="d-flex align-items-center">
              <label>ログインID</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="4">
              <CFormInput
                name="login_id"
                readonly
                :disabled="authStore.user.role_id === '30'"
                v-model="adminData.login_id"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="3">
              <label>パスワード</label>
            </CCol>
            <CCol sm="4">
              <CFormInput
                name="password"
                autocomplete="new-password"
                type="password"
                :disabled="authStore.user.role_id === '30'"
                v-model="adminData.password"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3"></CCol>
            <CCol sm="7" style="max-width: 100%">
              <label style="line-height: 25px"
                >※パスワードは8文字以上で、大文字・小文字・数字をすべて含めて指定してください。</label
              >
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3">
              <label>パスワード(確認)</label>
            </CCol>
            <CCol sm="4">
              <CFormInput
                name="password_confirm"
                autocomplete="new-password"
                type="password"
                :disabled="authStore.user.role_id === '30'"
                v-model="adminData.password_confirm"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3"> 権限 </CCol>
            <CCol sm="9">
              <CFormCheck
                type="radio"
                inline
                v-for="role_options in role_options"
                :key="role_options.value"
                :label="role_options.label"
                :value="role_options.value"
                :custom="true"
                :disabled="authStore.user.role_id === '30'"
                name="role_id"
                v-model="adminData.role_id"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="3"> 状態 </CCol>
            <CCol sm="9">
              <CFormCheck
                type="radio"
                ref="delete_flag"
                inline
                v-for="status_options in status_options"
                :key="status_options.value"
                :label="status_options.label"
                :value="status_options.value"
                :custom="true"
                :disabled="authStore.user.role_id === '30'"
                name="delete_flag"
                v-model="adminData.delete_flag"
              />
            </CCol>
          </CRow>
        </CForm>
        <CElementCover v-if="loading" :opacity="0.8" />
      </CCardBody>
      <CCardFooter>
        <CButton color="light" class="mx-1" @click="goBack"
          >編集を中止して一覧に戻る</CButton
        >
        <CButton
          color="primary"
          class="mx-1"
          :disabled="authStore.user.role_id === '30'"
          @click="editAdmin"
          >更新する</CButton
        >
      </CCardFooter>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="editModal"
      @close="
        () => {
          editModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loading && validateResult.length === 0">
          この内容で更新してもよろしいですか？
        </div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          v-if="validateResult.length === 0"
          name="btn-cancel"
          @click="editModal = false"
          color="dark"
          :disabled="loading"
          >キャンセル</CButton
        >
        <CButton
          name="btn-ok"
          @click="btnEditClicked"
          color="primary"
          :disabled="loading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="backModal"
      @close="
        () => {
          backModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>更新中止確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="backModal = false" color="dark">キャンセル</CButton>
        <CButton
          @click="
            () => {
              backModal = false;
              next();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {CElementCover, ScaleLoader} from '@/components/Table';
  import {
    CBadge,
    CButton,
    CCard,
    CCardBody,
    CCardFooter,
    CCardHeader,
    CCol,
    CForm,
    CFormCheck,
    CFormInput,
    CModal,
    CRow,
  } from '@coreui/vue';
  import {useAuthStore} from '@/store/auth';
  export default {
    name: 'AdminEdit',
    components: {
      ScaleLoader,
      CModal,
      CButton,
      CCard,
      CCardHeader,
      CCardBody,
      CCardFooter,
      CFormInput,
      CRow,
      CCol,
      CForm,
      CElementCover,
      CFormCheck,
      CBadge,
    },
    data() {
      return {
        authStore: useAuthStore(), // 一般権限の場合は編集不可
        // Edit modal
        loading: true,
        editModal: false,
        validateResult: [],
        // Back modal
        backModal: false,
        // Check change
        origData: null,
        adminData: {
          admin_name: '',
          login_id: '',
          password: '',
          password_confirm: '',
          role_id: '10',
          delete_flag: '0',
        },
        role_options: [],
        status_options: [
          {label: '有効', value: '0'},
          {label: '無効', value: '1'},
        ],

        next: Function,
      };
    },
    async mounted() {
      // Roleを取得する
      await this.getConstantsData()
        .then(async () => {
          // 管理者情報取得
          await this.getAdminData();
        })
        .catch(error => {
          console.log(JSON.stringify(error));
          this.loading = false;

          Methods.parseHtmlResponseError(this.$router, error);
        });
    },
    beforeRouteLeave(to, from, next) {
      if (Base.objectsAreIdentical(this.origData, this.adminData)) {
        next();
      } else {
        this.next = next;
        this.backModal = true;
      }
    },
    methods: {
      async getAdminData() {
        // 管理者情報取得
        const id = this.$route.params.id;
        console.log(`id = ${id}`);
        const search_cond = {
          admin_no: id,
        };
        await this.getAdminFromServer(search_cond)
          .then(admin => {
            this.adminData = admin;
            this.adminData.delete_flag = String(admin.delete_flag);
            // Set original data
            this.origData = JSON.parse(JSON.stringify(this.adminData));
            // Loading
            this.loading = false;

            // Disable radio groups
            if (this.$route.params.id === this.authStore.user.admin_no) {
              // 権限
              this.$refs.role_id.$children.map(el => {
                document
                  .getElementById(el.safeId)
                  .setAttribute('disabled', true);
              });
              // 状態
              this.$refs.delete_flag.$children.map(el => {
                document
                  .getElementById(el.safeId)
                  .setAttribute('disabled', true);
              });
            }
          })
          .catch(error => {
            this.loading = false;
            const errMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getConstantsData() {
        const params = {
          key_strings: ['ADMIN_ROLE'],
        };
        // Request to server
        return Methods.apiExecute('get-constants-by-keys', params).then(
          response => {
            if (response.status === 200) {
              const roles = response.data;
              this.role_options = [];
              for (let i = 0; i < roles.length; i++) {
                this.role_options.push({
                  value: roles[i].value1,
                  label: roles[i].value2,
                });
              }
            }
            return Promise.resolve();
          }
        );
      },
      getAdminFromServer(search_cond) {
        // Request to server
        return Methods.apiExecute('get-admin-by-admin-no', search_cond).then(
          response => {
            this.loading = false;
            if (response.status === 200) {
              const admin = response.data[0];
              console.log(`admin = ${JSON.stringify(admin)}`);
              if (typeof admin === 'undefined' || admin === null) {
                return Promise.resolve();
              }
              return Promise.resolve(admin);
            }
            return Promise.resolve();
          }
        );
      },
      editAdmin() {
        console.log(this.adminData);
        this.editModal = true;
        this.loading = false;
        this.validateResult = [];
      },
      btnEditClicked() {
        console.log('btnEditClicked');

        if (this.validateResult.length > 0) {
          this.editModal = false;
          // This.validateResult = []
          return;
        }
        this.loading = true;

        const params = {
          data: {
            admin_no: this.$route.params.id,
            admin_name: this.adminData.admin_name,
            login_id: this.adminData.login_id,
            password: this.adminData.password,
            password_confirm: this.adminData.password_confirm,
            role_id: this.adminData.role_id,
            delete_flag: this.adminData.delete_flag,
          },
        };
        console.log(`params = ${JSON.stringify(params)}`);
        // Request to server
        Methods.apiExecute('edit-admin', params)
          .then(async response => {
            this.loading = false;
            this.editModal = false;
            const admin_no = response.data;
            console.log(`status = ${JSON.stringify(response.status)}`);
            console.log(`admin_no = ${JSON.stringify(admin_no)}`);

            // Force refresh auth session if the current user edited their own admin_name
            if (this.$route.params.id === this.authStore.user.admin_no) {
              await this.authStore.fetchAuthSession(true); // forceRefresh = true
            }

            // Set original data
            this.origData = JSON.parse(JSON.stringify(this.adminData));
            this.goBack();
          })
          .catch(error => {
            this.loading = false;
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            );
          });
      },
      goBack() {
        console.log('goBack');
        this.$router.push({path: '/admins'});
      },
    },
  };
</script>

<style scoped>
  label {
    line-height: 35px;
  }
</style>
