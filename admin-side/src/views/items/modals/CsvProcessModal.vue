<template>
  <CModal
    title="CSV"
    color="primary"
    :show="show"
    :closeOnBackdrop="false"
    @update:show="$emit('update:show', $event)"
  >
    <div style="margin-left: 10px">
      <scale-loader
        :loading="loading"
        color="#5dc596"
        height="10px"
        width="4px"
      ></scale-loader>
    </div>
    <div v-if="errorMessages">
      <div v-for="val in errorMessages" :key="val">{{ val }}</div>
    </div>
    <template #header>
      <h5 class="modal-title">確認</h5>
      <CCloseButton v-if="!loading" @click="handleClose" />
    </template>
    <template #footer>
      <CButton v-if="!loading" @click="handleClose" color="dark">
        閉じる
      </CButton>
      <a></a>
    </template>
  </CModal>
</template>

<script setup>
  import ScaleLoader from '@/components/Table/ScaleLoader.vue';

  // Props
  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    errorMessages: {
      type: Array,
      default: () => [],
    },
  });

  // Emits
  const emit = defineEmits(['update:show', 'close']);

  // Handle close action
  const handleClose = () => {
    emit('close');
  };
</script>
