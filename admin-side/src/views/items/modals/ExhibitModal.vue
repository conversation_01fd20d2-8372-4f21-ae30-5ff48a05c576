<template>
  <CModal
    :title="title"
    color="warning"
    :show="show"
    :closeOnBackdrop="false"
    @update:show="$emit('update:show', $event)"
  >
    <div v-if="!loading">商品を出品します。よろしいですか？</div>
    <div style="margin-left: 10px">
      <scale-loader
        :loading="loading"
        color="#5dc596"
        height="10px"
        width="4px"
      ></scale-loader>
    </div>
    <template #footer>
      <CButton
        v-if="validateResult.length === 0"
        @click="handleCancel"
        color="dark"
        :disabled="loading"
      >
        キャンセル
      </CButton>
      <CButton @click="handleConfirm" color="primary" :disabled="loading">
        OK
      </CButton>
    </template>
  </CModal>
</template>

<script setup>
  import ScaleLoader from '@/components/Table/ScaleLoader.vue';

  // Props
  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '出品確認',
    },
    validateResult: {
      type: Array,
      default: () => [],
    },
  });

  // Emits
  const emit = defineEmits(['update:show', 'confirm', 'cancel']);

  // Handle confirm action
  const handleConfirm = () => {
    emit('confirm');
  };

  // Handle cancel action
  const handleCancel = () => {
    emit('cancel');
  };
</script>
