<template>
  <CModal
    :title="title"
    color="warning"
    :show="show"
    :closeOnBackdrop="false"
    @update:show="$emit('update:show', $event)"
  >
    <div v-if="!loading">商品を買取済みにします。よろしいですか？</div>
    <div style="margin-left: 10px">
      <scale-loader
        :loading="loading"
        color="#5dc596"
        height="10px"
        width="4px"
      ></scale-loader>
    </div>
    <template #footer>
      <CButton
        v-if="validateResult.length === 0"
        @click="handleCancel"
        color="dark"
        :disabled="loading"
      >
        キャンセル
      </CButton>
      <CButton @click="handleConfirm" color="primary" :disabled="loading">
        OK
      </CButton>
    </template>
  </CModal>
</template>

<script setup>
  import ScaleLoader from '@/components/Table/ScaleLoader.vue';

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '買取確認',
    },
    validateResult: {
      type: Array,
      default: () => [],
    },
  });

  // Emits
  const emit = defineEmits(['update:show', 'confirm', 'cancel']);

  const handleConfirm = () => {
    emit('confirm');
  };

  const handleCancel = () => {
    emit('cancel');
  };
</script>
