<template>
  <CModal
    title="依頼履歴"
    size="lg"
    color="info"
    :show="show"
    @update:show="$emit('update:show', $event)"
  >
    <div class="mb-9">
      <CHistoryTableWrapper
        :items="requestHistories"
        :loading="loading"
        hover
        striped
        border
        small
        fixed
      />
    </div>
    <template #footer>
      <CButton @click="handleClose" color="dark">OK</CButton>
    </template>
  </CModal>
</template>

<script setup>
  import CHistoryTableWrapper from '@/views/items/requestHistoryTable';

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    requestHistories: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['update:show', 'close']);

  const handleClose = () => {
    emit('close');
  };
</script>
