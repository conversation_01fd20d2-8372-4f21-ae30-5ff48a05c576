<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>商品情報編集</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <div class="data-group">
            <!-- 商品詳細 -->
            <div v-if="loading" class="my-3 text-center">
              <CSpinner />
            </div>
            <ItemForm
              :is-disabled="isCompleted || isReadOnly"
              :loading="loading"
            />
          </div>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CButton class="mx-1" color="light" @click="goBack">
          {{ '編集を中止して一覧に戻る' }}
        </CButton>
        <CButton
          class="mx-1"
          color="primary"
          @click="updateItem"
          :disabled="!isDataChanged"
          v-if="!isReadOnly"
        >
          {{ '更新する' }}
        </CButton>
      </CCardFooter>
    </CCard>

    <CModal
      :keyboard="false"
      :visible="showDiscardModal"
      @close="
        () => {
          showDiscardModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle id="StaticBackdropExampleLabel">更新中止確認</CModalTitle>
      </CModalHeader>
      <CModalBody>変更内容は破棄されますがよろしいですか？</CModalBody>
      <CModalFooter>
        <CButton
          color="secondary"
          @click="
            () => {
              showDiscardModal = false;
            }
          "
        >
          キャンセル
        </CButton>
        <CButton color="primary" @click="discardChangesAndMoveBack">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      :keyboard="false"
      :visible="confirmUpdateModal"
      @close="
        () => {
          confirmUpdateModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>更新の確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!modalLoading && errMsgArray.length === 0">
          この内容で更新を行いますか？
        </div>
        <div v-if="modalLoading" class="loading-message">
          更新処理中です。お待ちください...
          <div style="margin-left: 10px">
            <scale-loader
              :loading="true"
              color="#5dc692"
              height="10px"
              width="4px"
            ></scale-loader>
          </div>
        </div>
        <div v-if="errMsgArray.length > 0" class="error-messages">
          <div v-for="(val, i) in errMsgArray" :key="i" class="error-message">
            {{ val }}
          </div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          v-if="errMsgArray.length === 0"
          color="secondary"
          @click="confirmUpdateModal = false"
          :disabled="modalLoading"
        >
          キャンセルする
        </CButton>
        <CButton color="primary" @click="updateModalOk" :disabled="true">
          更新する
        </CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import ScaleLoader from '@/components/Table/ScaleLoader.vue';
  import {useItem} from './composables/useItem.js';
  import {onMounted, ref, provide} from 'vue';
  import {useRoute, useRouter} from 'vue-router';
  import ItemForm from './parts/ItemForm.vue';
  import {useAuthStore} from '@/store/auth';

  const router = useRouter();
  const route = useRoute();
  const {isReadOnly} = useAuthStore();

  const {
    modalLoading,
    loading,
    errMsgArray,
    itemDetailData,
    isCompleted,
    isDataChanged,
    updateItem: updateItemComposable,
    initialize,
  } = useItem();

  const showDiscardModal = ref(false);
  const confirmUpdateModal = ref(false);

  // Provide itemDetailData to child components
  provide('itemDetailData', itemDetailData);

  onMounted(() => {
    if (route.params.id) {
      initialize(route.params.id);
    } else {
      console.error('❌ No item ID found in route params');
      goBack();
    }
  });

  const goBack = () => {
    if (isDataChanged.value) {
      showDiscardModal.value = true;
    } else {
      discardChangesAndMoveBack();
    }
  };

  const discardChangesAndMoveBack = () => {
    showDiscardModal.value = false;
    router.push({path: '/items'});
  };

  const updateItem = () => {
    errMsgArray.value = [];
    confirmUpdateModal.value = true;
  };

  const updateModalOk = () => {
    if (errMsgArray.value && errMsgArray.value.length > 0) {
      confirmUpdateModal.value = false;
    } else {
      updateItemComposable();
    }
  };
</script>
<style type="text/css">
  .tableSpace {
    padding: 10px;
  }

  .topSpace {
    padding-top: 10px;
  }

  .imageTable > tr > th {
    text-align: center;
    font-weight: normal;
  }

  .imageTable > tr > td {
    text-align: center;
    border: 1px solid #d8dbe0;
    padding: 30px 5px;
  }

  .inline {
    display: inline;
  }

  .radioTittle {
    padding-right: 15px;
  }
</style>
