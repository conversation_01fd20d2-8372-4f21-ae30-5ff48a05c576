<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header">
        <CIcon name="cil-grid" /> {{ caption }}
        <span style="float: right; margin-left: 30px"
          >検索結果: {{ current_count }}件</span
        >
        <span style="float: right">総件数: {{ total_count }}件</span>
      </slot>
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        style="font-size: 12px"
        :loading="loading"
        :items="items"
        :fields="fields"
        :position="position"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        :itemsPerPageSelect="itemsPerPageSelect"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #manage_no="{item}">
          <td class="text-center" style="width: 8%">
            {{ item.manage_no || '' }}
          </td>
        </template>
        <template #request_no="{item}">
          <td class="text-center" style="width: 8%">
            {{ item.request_no || '' }}
          </td>
        </template>
        <template #item_field="{item}" style="width: 20%">
          <td v-if="item.manage_no" class="text-left">
            <div class="product-name">
              <a class="link-text cursor-pointer" @click="editItem(item)">
                {{ getProductName(item) }}
              </a>
            </div>
          </td>
          <td v-else class="text-left">
            <div><span style="color: red">商品情報未登録</span></div>
          </td>
        </template>
        <template #status="{item}">
          <td class="text-center" style="width: 11%">
            <div>{{ item.status_name || getStatusName(item.status) }}</div>
            <div v-if="item.status_name === '在庫'" style="text-align: center">
              <CButton
                size="sm"
                color="link btn-file"
                @click="openPurchasedModal(item)"
                >買取済みにする</CButton
              >
            </div>
            <div
              v-if="item.classification_name === '成約'"
              style="text-align: center"
            >
              {{ item.status_date }}
            </div>
            <div
              v-if="item.status_name === '成約'"
              v-bind:class="{
                red: item.delivery_selected_name == '未選択',
                blue:
                  item.delivery_selected_name == '自分で配送' ||
                  item.delivery_selected_name == '配送代行',
              }"
            >
              {{ `配送方法：${item.delivery_selected_name}` }}
            </div>
          </td>
        </template>
        <template #exhibition_name="{item}">
          <td class="text-center" style="width: 20%">
            {{ item.exhibition_name }}
          </td>
        </template>
        <template #operation="{item}">
          <td style="width: 20%">
            <div class="btn-operation-row">
              <!-- <CButton v-if="item.status === '在庫'" size="sm" color="info btn-history" @click="viewChat(item)"></CButton> -->
              <div
                v-if="item.status_name === '出品中'"
                class="btn-operation-col"
              >
                <CButton
                  size="sm"
                  color="info btn-history"
                  @click="
                    $router.push({
                      name: '問合せチャット',
                      params: {
                        id: item.exhibition_item_no,
                        manage_no: item.manage_no,
                        classification_name: item.classification_name,
                      },
                    })
                  "
                >
                  問合せチャット</CButton
                >
                <div
                  v-if="
                    item.inquiry_chat_un_read_count &&
                    item.inquiry_chat_un_read_count > 0
                  "
                  class="btn-operation-row text-center"
                  style="color: red"
                >
                  未読 {{ item.inquiry_chat_un_read_count }}件
                </div>
              </div>
              <div v-if="item.status_name === '成約'" class="btn-operation-col">
                <CButton
                  size="sm"
                  color="info btn-history"
                  :disabled="item.delivery_selected_flag"
                  @click="
                    $router.push({
                      name: '取引チャット',
                      params: {
                        id: item.exhibition_item_no,
                        classification_name: item.classification_name,
                        delivery_selected_name: item.delivery_selected_name,
                      },
                    })
                  "
                >
                  取引チャット</CButton
                >
                <div
                  v-if="
                    item.bid_chat_un_read_count &&
                    item.bid_chat_un_read_count > 0
                  "
                  class="btn-operation-row text-center"
                  style="color: red"
                >
                  未読 {{ item.bid_chat_un_read_count }}件
                </div>
              </div>
              <div
                v-if="item.classification_name === '出品代行'"
                class="btn-operation-col"
              >
                <CButton
                  size="sm"
                  color="info btn-history"
                  @click="
                    $router.push({
                      name: '依頼チャット',
                      params: {id: item.item_request_no},
                    })
                  "
                  >依頼チャット</CButton
                >
                <div
                  v-if="
                    item.item_chat_un_read_count &&
                    item.item_chat_un_read_count > 0
                  "
                  class="btn-operation-row text-center"
                  style="color: red"
                >
                  未読 {{ item.item_chat_un_read_count }}件
                </div>
              </div>
            </div>
            <div class="btn-operation-row">
              <div class="btn-operation-col">
                <CButton
                  v-if="item.classification_name === '出品代行'"
                  size="sm"
                  color="info btn-history"
                  @click="viewRequestHistory(item)"
                  >履歴</CButton
                >
              </div>
            </div>
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
    </CCardBody>
  </CCard>
</template>

<script setup>
  import Methods from '@/api/methods';
  import {CDataTable} from '@/components/Table';
  import {dateFormat} from '@/views/common/commonFilters';
  import {itemsPerPageSelect} from '@/views/common/customTableView';
  import {CCard} from '@coreui/vue';
  import {useRouter} from 'vue-router';
  import {useItemSearch} from './composables/useItemSearch';

  const searchComposable = useItemSearch();

  const props = defineProps({
    current_count: {
      type: String,
      default: '0',
    },
    total_count: {
      type: String,
      default: '0',
    },
    fields: {
      type: Array,
      default() {
        return [
          {key: 'manage_no', label: '管理番号', _classes: 'text-center'},
          {key: 'request_no', label: '申請番号', _classes: 'text-center'},
          {key: 'classification', label: '区分', _classes: 'text-center'},
          {key: 'item_field', label: '商品名', _classes: 'text-center'},
          {key: 'status', label: 'ステータス', _classes: 'text-center'},
          {key: 'exhibition_name', label: '入札会', _classes: 'text-center'},
          {key: 'operation', label: '操作', _classes: 'text-center'},
        ];
      },
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    caption: {
      type: String,
      default: 'itemTable',
    },
    position: Number,
    loading: Boolean,
    loadingDelete: Boolean,
    activePage: Number,
    itemsPerPage: Number,
    itemsSorter: Object,
    roleId: Number,
    pages: {
      type: Number,
      default: 1,
    },

    // 現在日付の取得
    nowDate: {
      default() {
        const options = {year: 'numeric', month: '2-digit', day: '2-digit'};
        const tmpDate = new Date();
        const ret = Methods.getFormatDate(
          tmpDate.toLocaleDateString('ja-JP', options),
          'yyyy/MM/dd'
        );
        return ret;
      },
    },
  });

  const router = useRouter();
  const emit = defineEmits([
    'page-change',
    'pagination-change',
    'sorter-change',
    'openExhibitModal',
    'openPurchasedModal',
    'onViewRequestHistory',
  ]);

  const editItem = item => {
    console.log('edit item: ', item);
    router.push({path: `/items/${item.item_no}/edit`});
  };
  const viewChat = item => {
    console.log(`viewChat: ${JSON.stringify(item)}`);
    router.push({path: 'items/chat'});
  };
  const openExhibitModal = item => {
    console.log('openExhibitModal');
    emit('openExhibitModal', {
      item,
    });
  };
  const openPurchasedModal = item => {
    console.log('openPurchasedModal');
    emit('openPurchasedModal', item);
  };
  const viewRequestHistory = item => {
    emit('onViewRequestHistory', item);
  };
  const pageChange = val => {
    if (props.items.length > 0) {
      emit('page-change', val);
    }
  };
  const paginationChange = val => {
    emit('pagination-change', val);
  };
  const sorterChange = val => {
    emit('sorter-change', val);
  };
  const getChatCount = item => {
    if (item.status_name === '出品中') {
      return item.inquiry_chat_un_read_count;
    }
    if (item.status_name === '買取') {
      return item.item_chat_un_read_count;
    }
    if (item.status_name === '成約') {
      return item.bid_chat_un_read_count;
    }
    return 0;
  };

  const getProductName = item => {
    try {
      if (item.localized_json_array && item.localized_json_array.length > 0) {
        const localizedData = item.localized_json_array[0];
        if (
          localizedData &&
          localizedData.f2 &&
          localizedData.f2.product_name
        ) {
          return localizedData.f2.product_name;
        }
      }
      return item.product_name || '商品名未設定';
    } catch (error) {
      console.error('Error extracting product name:', error);
      return '商品名取得エラー';
    }
  };

  const getStatusName = statusCode => {
    const statusOption = searchComposable.optionsStatus.value.find(
      option => String(option.value) === String(statusCode)
    );
    return statusOption ? statusOption.label : `不明(${statusCode})`;
  };
</script>

<style type="text/css">
  .btn-operation-row {
    width: 100%;
    margin: 0.2rem 0;
  }

  .btn-operation-col {
    width: 50%;
    display: inline-block;
    padding: 0.25rem;
    vertical-align: top;
  }

  .btn-status {
    width: 100%;
    padding: 0.2rem;
  }

  .btn-history-col {
    width: 100%;
    display: inline-block;
    padding: 0.25rem;
  }

  .btn-history {
    width: 100%;
    padding: 0.25rem;
  }

  .btn-file {
    width: 100%;
    margin: 0.25rem !important;
  }

  .red {
    color: red;
  }

  .blue {
    color: blue;
  }

  .cursor-pointer {
    cursor: pointer;
  }
  .product-name {
    display: flex;
    justify-content: center;
  }
</style>
