<template>
  <div>
    <CDataTable
      :loading="loading"
      :hover="hover"
      :striped="striped"
      :border="border"
      :small="small"
      :fixed="fixed"
      :items="items"
      :fields="fields"
      :items-per-page="small ? 10 : 5"
      :dark="dark"
      :sorter="true"
      :sorterValue="itemsSorter"
      :pagination="{doubleArrows: false, align: 'center'}"
      @update:sorter-value="sorterChange"
    >
      <template #category="{item}">
        <td v-if="item.approve_status === '未対応'" style="color: #231698">
          {{ item.category }}
        </td>
        <td v-else-if="item.approve_status.includes('却下')" style="color: red">
          {{ item.approve_status }}({{ item.admin_name }})
        </td>
        <td v-else>{{ item.approve_status }}({{ item.admin_name }})</td>
      </template>
    </CDataTable>
  </div>
</template>

<script>
  export default {
    name: 'requestHistoryTable',
    props: {
      items: {
        type: Array,
        default: () => [],
      },
      hover: <PERSON><PERSON><PERSON>,
      striped: Boolean,
      border: Boolean,
      small: <PERSON><PERSON>an,
      fixed: <PERSON><PERSON>an,
      dark: <PERSON><PERSON>an,
      loading: <PERSON>olean,
    },
    data() {
      return {
        itemsSorter: {asc: false, column: 'report_datetime'},
        fields: [
          {key: 'create_datetime', label: '日時', _classes: 'text-center'},
          {
            key: 'category',
            label: '内容',
            _style: 'text-align: center; width: 60%',
          },
        ],
      };
    },
    methods: {
      sorterChange(val) {
        this.itemsSorter = val;
      },
    },
  };
</script>
<style type="text/css">
  .color_red {
    color: red;
  }
</style>
