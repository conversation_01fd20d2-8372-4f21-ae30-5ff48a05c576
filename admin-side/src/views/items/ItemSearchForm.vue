<template>
  <CCard>
    <CCardHeader>
      <strong>検索条件</strong>
    </CCardHeader>
    <CCardBody>
      <CForm>
        <!-- Status Section -->
        <CRow>
          <CCol sm="2">
            <label>ステータス</label>
          </CCol>
          <CCol sm="9">
            <CFormCheck
              v-for="(option, index) in optionsStatus"
              :key="`status-${option.value}`"
              :id="`status-${option.value}`"
              :label="option.label"
              v-model="localSearchCondition.status[index]"
              :inline="true"
            />
          </CCol>
        </CRow>

        <!-- Management Number Section -->
        <CRow class="mb-2">
          <CCol sm="2">
            <label>管理番号</label>
          </CCol>
          <CCol sm="auto">
            <CFormInput
              name="stock_no_from"
              v-model="localSearchCondition.manage_no_from"
            />
          </CCol>
          <CCol sm="auto">
            <label class="m-1">〜</label>
          </CCol>
          <CCol sm="auto">
            <CFormInput
              name="stock_no_to"
              v-model="localSearchCondition.manage_no_to"
            />
          </CCol>
        </CRow>

        <!-- Request Number Section -->
        <CRow class="mb-2">
          <CCol sm="2">
            <label>申請番号</label>
          </CCol>
          <CCol sm="auto">
            <CFormInput
              name="request_no_from"
              v-model="localSearchCondition.request_no_from"
            />
          </CCol>
          <CCol sm="auto">
            <label class="m-1">〜</label>
          </CCol>
          <CCol sm="auto">
            <CFormInput
              name="request_no_to"
              v-model="localSearchCondition.request_no_to"
            />
          </CCol>
        </CRow>

        <!-- Exhibition Name Section -->
        <CRow>
          <CCol sm="2"> 入札会 </CCol>
          <CCol sm="3">
            <CFormSelect
              :options="optionsExhibitionName"
              v-model="localSearchCondition.exhibition_name"
            />
          </CCol>
        </CRow>

        <!-- Unread Message Section -->
        <CRow class="mt-3">
          <CCol sm="2">
            <label>未読メッセージ</label>
          </CCol>
          <CCol sm="9">
            <CFormCheck
              v-for="(option, index) in optionsMessage"
              :key="`message-${option.value}`"
              :id="`message-${option.value}`"
              :label="option.label"
              v-model="localSearchCondition.unread_message[index]"
              :inline="true"
            />
          </CCol>
        </CRow>
      </CForm>
    </CCardBody>
    <CCardFooter>
      <CRow class="align-items-center justify-content-end">
        <CCol sm="5"></CCol>
        <CCol sm="2" class="d-grid gap-2">
          <CButton
            size="sm"
            color="info"
            @click="handleSearch"
            :disabled="loading"
            block
          >
            検索
          </CButton>
        </CCol>
        <!-- Action Buttons Component -->
        <ItemActionButtons
          :loading="loading"
          :searchCondition="localSearchCondition"
          :optionsStatus="optionsStatus"
          :optionsMessage="optionsMessage"
          :optionsExhibitionName="optionsExhibitionName"
          :itemsSorter="itemsSorter"
          @csv-download-complete="$emit('csv-download-complete')"
          @csv-upload-complete="$emit('csv-upload-complete')"
          @zip-upload-complete="$emit('zip-upload-complete')"
          @file-operation-error="$emit('file-operation-error', $event)"
        />
      </CRow>
    </CCardFooter>
  </CCard>
</template>

<script setup>
  import {reactive, watch} from 'vue';
  import ItemActionButtons from './ItemActionButtons.vue';

  const props = defineProps({
    searchCondition: {
      type: Object,
      required: true,
    },
    optionsStatus: {
      type: Array,
      required: true,
    },
    optionsMessage: {
      type: Array,
      required: true,
    },
    optionsExhibitionName: {
      type: Array,
      required: true,
    },
    itemsSorter: {
      type: Object,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  // Emits
  const emit = defineEmits([
    'update:searchCondition',
    'search',
    'csv-download-complete',
    'csv-upload-complete',
    'zip-upload-complete',
    'file-operation-error',
  ]);

  const localSearchCondition = reactive({
    status: [...(props.searchCondition.status || [false, false, false, false])],
    manage_no_from: props.searchCondition.manage_no_from || '',
    manage_no_to: props.searchCondition.manage_no_to || '',
    request_no_from: props.searchCondition.request_no_from || '',
    request_no_to: props.searchCondition.request_no_to || '',
    exhibition_name: props.searchCondition.exhibition_name || '',
    unread_message: [
      ...(props.searchCondition.unread_message || [false, false]),
    ],
  });

  let isUpdatingFromProps = false;

  watch(
    () => localSearchCondition,
    newVal => {
      if (isUpdatingFromProps) {
        return;
      }
      emit('update:searchCondition', {...newVal});
    },
    {deep: true}
  );

  watch(
    () => props.searchCondition,
    newVal => {
      isUpdatingFromProps = true;

      // Update local state when parent changes (silently)
      localSearchCondition.status = [
        ...(newVal.status || [false, false, false, false]),
      ];
      localSearchCondition.manage_no_from = newVal.manage_no_from || '';
      localSearchCondition.manage_no_to = newVal.manage_no_to || '';
      localSearchCondition.request_no_from = newVal.request_no_from || '';
      localSearchCondition.request_no_to = newVal.request_no_to || '';
      localSearchCondition.exhibition_name = newVal.exhibition_name || '';
      localSearchCondition.unread_message = [
        ...(newVal.unread_message || [false, false]),
      ];
      setTimeout(() => {
        isUpdatingFromProps = false;
      }, 0);
    },
    {deep: true, immediate: true}
  );

  // Handle search button click
  const handleSearch = () => {
    emit('search');
  };
</script>
