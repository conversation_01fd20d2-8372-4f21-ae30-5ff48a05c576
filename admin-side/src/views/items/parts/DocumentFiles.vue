<template>
  <div>
    <CRow>
      <CCol sm="10">書類</CCol>
    </CRow>
    <CRow class="form-group" v-for="doc in documentFiles" :key="doc.id">
      <CCol sm="1"></CCol>
      <CCol sm="3" class="text-right">{{ doc.label }}</CCol>
      <CCol class="col-sm-6">
        <a>{{ doc.key ? 'あり' : 'なし' }}</a>
      </CCol>
    </CRow>
  </div>
</template>

<script>
  import FileComp from './FileComp';
  export default {
    components: {
      FileComp,
    },
    props: {
      documentFiles: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        // ImagePreviewUrl : null
      };
    },
    created() {
      console.log('fileInfo: ', this.fileInfo);
    },
    computed: {},
    methods: {},
  };
</script>
