<template>
  <div>
    <a
      v-if="fileInfo.key"
      @contextmenu.prevent="onContextMenuOpen($event, fileInfo.key)"
      @click="fileNameClickEvent(fileInfo.key)"
      href="javascript:void(0);"
      :style="fileInfo.key ? '' : 'color: inherit; text-decoration: inherit;'"
      v-bind:class="{link: fileInfo.key}"
    >
      {{ title }}
    </a>
    <a v-else>{{ nullText }}</a>

    <ContextMenu v-model:show="contextMenu.show" :options="contextMenu.options">
      <context-menu-item label="ファイルを開く" @click="getFileViewUrl" />
      <context-menu-item label="ダウンロード" @click="getFileDownloadUrl" />
    </ContextMenu>
  </div>
</template>

<script setup>
  import FileService from '@/api/uploadFileToS3';
  import Base from '@/common/base';
  import {ContextMenu, ContextMenuItem} from '@imengyu/vue3-context-menu';
  import {computed, defineProps, ref} from 'vue';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    fileInfo: {
      type: Object,
      default: () => {},
    },
    nullText: {
      type: String,
      default: 'あとで提出する',
    },
  });

  const contextMenu = ref({
    show: false,
    options: {
      zIndex: 9999,
      minWidth: 230,
      x: 500,
      y: 500,
    },
    data: null,
  });

  const fileName = computed(() => {
    return props.fileInfo.key
      ? Base.getFileName(props.fileInfo.key)
      : props.nullText;
  });
  const getFileDownloadUrl = key => {
    console.log(`getFileDownloadUrl: ${key}`);
    // Get download url from file server
    if (key) {
      FileService.getDownloadUrl(key, res => {
        console.log(`res: ${JSON.stringify(res)}`);
        window.location.href = res;
      });
    }
  };
  const getFileViewUrl = key => {
    console.log('getFileViewUrl');
    // Get file viewing url from file server
    if (key) {
      FileService.getFileViewUrl(key, res => {
        console.log(`res: ${JSON.stringify(res)}`);
        window.open(res, '_blank');
      });
    }
  };

  const fileNameClickEvent = key => {
    console.log('fileNameClickEvent');
    // Filename click event handler
    if (key) {
      FileService.getFile(key, res => {
        console.log(`res: ${JSON.stringify(res)}`);
        window.open(res, '_blank');
      });
    }
  };

  const onContextMenuOpen = (e, item) => {
    // Show context menu
    contextMenu.value.show = true;
    contextMenu.value.options.x = e.x;
    contextMenu.value.options.y = e.y;
    contextMenu.value.data = item;
  };
</script>
<style scoped lang="scss">
  .close-button {
    position: absolute;
    z-index: 1;
    margin: auto;
  }

  .files-flex-list-box .image {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .btn-file {
    width: 100%;
    margin: 0.25rem !important;
  }
</style>
