<template>
  <div class="single-image">
    <!-- <p>{{ image.label || '' }}</p> -->
    <div v-if="imgPreview" class="image">
      <a @click="openDialog(imageIndex)">
        <CImg fluid :src="imgPreview" align="center" />
      </a>
    </div>
  </div>
</template>
<script setup>
  import noPhoto from '@/assets/noImage.jpg';
  import {getFullImagePath} from '@/views/common/commonFilters';
  import {computed} from 'vue';

  const props = defineProps({
    type: {
      type: Number,
    },
    imageIndex: {
      type: Number,
    },
    image: {
      type: Object,
      default: () => ({}),
    },
    openDialog: {
      type: Function,
      default: Object,
    },
    getPreviewData: {
      type: Function,
      default: Object,
    },
  });
  const title = ref('右');
  const imagePreviewUrl = ref(null);

  const imgPreview = computed(() => {
    const src =
      props.image && props.image.key
        ? getFullImagePath(props.image.key)
        : noPhoto;
    return src;
  });

  watch(
    () => props.image.key,
    () => {
      getPreviewData(props.type, props.imageIndex);
    }
  );
</script>
<style scoped lang="scss">
  .close-button {
    position: absolute;
    z-index: 1;
    margin: auto;
  }

  .action {
    display: block;
  }

  .files-flex-list-box .image {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  // .image {
  //   text-align: center;
  //   border: 1px solid #d8dbe0;
  //   width: 150px;
  //   height: 80px;
  // }
  .single-image {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: end;
    height: 100%;
  }
</style>
