<template>
  <div>
    <TCInputRadioGroup
      :label="label"
      class="custom-form-radio"
      :options="options"
      :inline="true"
      :checked="model"
      @update:checked="onChangeModelValue"
      :error="!!isErrorRadio"
      messages="1"
      :disabled="isAllDisabled"
      :name="refTxt"
    />
    <CInput
      :type="inputType"
      v-bind:class="{disabled: isTxtDisabled, err: !!isErrorOther}"
      @keydown="() => $emit('onTextAreaKeydown')"
      v-model="inputTextVal"
      :disabled="isTxtDisabled"
      :placeholder="placeholder"
      :ref="refTxt"
    >
      <template #append>
        <div class="append-text">
          <span>{{ appendText }}</span>
        </div>
      </template>
    </CInput>
  </div>
</template>
<script>
  import TCInputRadioGroup from './TCInputRadioGroup';
  export default {
    components: {
      TCInputRadioGroup,
    },
    props: {
      model: {
        type: String,
      },
      modelText: {
        type: String,
        default: null,
      },
      label: {
        type: String,
        default: null,
      },
      options: {
        type: Array,
        default: () => [],
      },
      isErrorRadio: {
        type: String,
        default: null,
      },
      isErrorOther: {
        type: String,
        default: null,
      },
      refTxt: {
        type: String,
        default: null,
      },
      placeholder: {
        type: String,
        default: null,
      },
      errTxtNextToRadio: {
        type: Boolean,
        default: false,
      },
      isAllDisabled: {
        type: Boolean,
        default: false,
      },
      inputType: {
        type: String,
        default: 'text',
      },
      appendText: {
        type: String,
        default: null,
      },
    },
    data() {
      return {
        errors: {
          content: null,
        },
      };
    },
    computed: {
      isTxtDisabled() {
        return (
          this.isAllDisabled ||
          !(
            (this.options.find(x => x.value === this.model) || {})
              .isTextInput === true
          )
        );
      },
      inputVal: {
        get() {
          return this.model;
        },
        set(val) {
          this.$emit('onChangeModelValue', val);
        },
      },
      inputTextVal: {
        get() {
          return this.modelText;
        },
        set(val) {
          this.$emit('onChangeModelTextValue', val);
        },
      },
    },
    methods: {
      onChangeModelValue(val) {
        console.log('onChangeModelValue', val);
        const isTextInput = (this.options.find(x => x.value === val) || {})
          .isTextInput;
        this.$emit('onChangeModelValue', {val, isTextInput});
      },
      onChangeModelTextValue(val) {
        this.$emit('onChangeModelTextValue', val);
      },
    },
  };
</script>
<style scoped lang="css">
  .append-text {
    display: flex;
    align-items: flex-end;
  }

  .append-text span {
    margin-left: 5px;
    line-height: normal;
    font-size: medium;
  }
</style>
