<template>
  <div>
    <CRow>
      <CCol sm="10">任意画像</CCol>
    </CRow>
    <CRow>
      <CCol sm="1"></CCol>
      <CCol class="form-group">
        <table class="imageTable">
          <template v-for="cnt in [1, 2]" :key="`label${cnt}`">
            <tr>
              <template
                v-for="(item, index) in itemRequestFiles.filter(
                  (x, i) => i >= (cnt - 1) * 5 && i < cnt * 5
                )"
                :key="`img${cnt}${index}`"
              >
                <th
                  v-if="index > 0"
                  :key="`space_${cnt}${index}`"
                  class="tableSpace"
                ></th>
                <th>{{ item.title || '' }}</th>
              </template>
            </tr>
            <tr>
              <template
                v-for="(item, index) in itemRequestFiles.filter(
                  (x, i) => i >= (cnt - 1) * 5 && i < cnt * 5
                )"
                :key="`img${cnt}${index}`"
              >
                <th
                  v-if="index > 0"
                  :key="`space_${cnt}${index}`"
                  class="tableSpace"
                ></th>
                <td>
                  <single-image
                    :image="item"
                    :imageIndex="(cnt - 1) * 5 + index"
                    :openDialog="openImageInputDialog"
                    :getPreviewData="getPreviewData"
                  />
                </td>
              </template>
            </tr>
          </template>
        </table>
      </CCol>
    </CRow>

    <!-- 画像アップロードモーダル start -->
    <ImageInputDialog
      :imageIndex="imageIndex"
      :type="2"
      :isOpenDialog="imageUploadDialog"
      :closeDialog="closeImageInputDialog"
      :getPreviewData="getPreviewData"
    />
    <!-- 画像アップロードモーダル end -->
  </div>
</template>

<script>
  import ImageInputDialog from './ImageInputDialog.vue';
  import SingleImage from './SingleImage';
  export default {
    components: {
      SingleImage,
      ImageInputDialog,
    },
    props: {
      itemRequestFiles: {
        type: Array,
        default: () => [],
      },
      isViewOnly: {
        type: Boolean,
        default: false,
      },
      getPreviewData: {
        type: Function,
        default: Object,
      },
    },
    data() {
      return {
        imageUploadDialog: false,
        imageIndex: 0,
      };
    },
    computed: {},
    methods: {
      openImageInputDialog(index) {
        console.log('openImageInputDialog:', index);
        if (this.isViewOnly) {
          return;
        }
        this.imageIndex = index;
        this.imageUploadDialog = true;
      },
      closeImageInputDialog() {
        this.imageUploadDialog = false;
      },
      updateImage(index) {
        console.log('updateImage: ', index);
      },
    },
  };
</script>
<style scoped lang="scss">
  .topSpace {
    padding-top: 10px;
  }

  .tableSpace {
    padding: 10px;
  }

  .imageTable > tr > th {
    text-align: center;
    font-weight: normal;
  }

  .imageTable > tr > td {
    text-align: center;
    border: 1px solid #d8dbe0;
    // padding: 30px 5px;
    width: 150px;
    height: 80px;
  }
</style>
