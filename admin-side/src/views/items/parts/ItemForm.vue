<template>
  <div v-show="!loading">
    <!-- 管理番号 -->
    <CRow class="mb-2">
      <CCol sm="2">
        <label>管理番号</label>
      </CCol>
      <CCol sm="8">
        <CFormInput :value="itemDetailData.manage_no" disabled />
      </CCol>
    </CRow>

    <!-- 商品名 -->
    <CRow class="mb-2">
      <CCol sm="2">
        <label>商品名</label>
      </CCol>
      <CCol sm="8">
        <CFormInput :value="productName" disabled />
      </CCol>
    </CRow>

    <!-- モデル -->
    <CRow class="mb-2">
      <CCol sm="2">
        <label>モデル</label>
      </CCol>
      <CCol sm="8">
        <CFormInput :value="model" disabled />
      </CCol>
    </CRow>

    <!-- メーカー -->
    <CRow class="mb-2">
      <CCol sm="2">
        <label>メーカー</label>
      </CCol>
      <CCol sm="8">
        <CFormInput :value="maker" disabled />
      </CCol>
    </CRow>

    <!-- ステータス -->
    <CRow class="mb-2">
      <CCol sm="2">
        <label>ステータス</label>
      </CCol>
      <CCol sm="8">
        <CFormSelect
          :model-value="String(itemDetailData.status)"
          :disabled="isDisabled"
          @update:model-value="onStatusChange"
        >
          <option
            v-for="option in statusOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </CFormSelect>
      </CCol>
    </CRow>

    <!-- ロット番号 -->
    <CRow class="mb-2">
      <CCol sm="2">
        <label>ロット番号</label>
      </CCol>
      <CCol sm="8">
        <CFormInput :value="itemDetailData.lot_no || 'なし'" disabled />
      </CCol>
    </CRow>

    <!-- 成約価格 -->
    <CRow class="mb-2">
      <CCol sm="2">
        <label>成約価格</label>
      </CCol>
      <CCol sm="8">
        <CFormInput :value="itemDetailData.success_price || 'なし'" disabled />
      </CCol>
    </CRow>

    <!-- Status Change Success Modal -->
    <CModal
      title="成功"
      v-model:show="statusChangeModal"
      :closeOnBackdrop="false"
    >
      <div>ステータスが正常に変更されました。</div>
      <template #footer>
        <CButton @click="statusChangeModal = false" color="primary">OK</CButton>
      </template>
    </CModal>
  </div>
</template>

<script setup>
  import {computed, ref, inject} from 'vue';
  import {useItemSearch} from '../composables/useItemSearch.js';

  const props = defineProps({
    isDisabled: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  // Get itemDetailData from parent component
  const itemDetailData = inject('itemDetailData');

  const {optionsStatus} = useItemSearch();
  const statusChangeModal = ref(false);
  const statusOptions = computed(() => {
    return optionsStatus.value;
  });
  const getLocalizedValue = (key, languageCode = 'ja') => {
    if (!itemDetailData.value?.localized_json_array) return '';
    const localizedData = itemDetailData.value.localized_json_array.find(
      item => item.f1 === languageCode
    );
    return localizedData?.f2?.[key] || '';
  };

  const productName = computed(() => {
    return getLocalizedValue('product_name');
  });
  const model = computed(() => {
    return getLocalizedValue('model');
  });
  const maker = computed(() => {
    return getLocalizedValue('maker');
  });

  const onStatusChange = newValue => {
    itemDetailData.value.status = parseInt(newValue);
    statusChangeModal.value = true;
  };
</script>
<style scoped lang="scss">
  .action {
    display: block;
  }

  .data-group {
    border: solid 1px black;
    padding: 10px;
    margin-bottom: 10px;
  }

  .inline {
    display: inline;
  }

  .radioTittle {
    padding-right: 15px;
  }
</style>
