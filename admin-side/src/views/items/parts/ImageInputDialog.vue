<script>
  import ScaleLoader from '@/components/Table/ScaleLoader.vue';
  import useItem from '@/store/useItem';

  export default {
    setup() {
      const itemStore = useItem();
      return {
        itemStore,
      };
    },
    components: {ScaleLoader},
    props: {
      imageIndex: {
        type: Number,
      },
      type: {
        type: Number,
      },
      isOpenDialog: {
        type: Boolean,
        default: false,
      },
      closeDialog: {
        type: Function,
        default: Object,
      },
      getPreviewData: {
        type: Function,
        default: Object,
      },
    },
    data() {
      return {
        tempImage: {
          title: null,
          preview: null,
          fileName: null,
          file: null,
          key: null,
        },
        errors: {},
        loading: false,
        imageClearModal: false,
      };
    },
    computed: {
      imgPreview() {
        return this.tempImage.preview || null;
      },
    },
    watch: {
      imageIndex() {
        console.log('imageIndex', this.imageIndex);
        this.reloadData();
      },
      'tempImage.key'() {
        this.getPreviewData(this.type, this.imageIndex);
      },
      isOpenDialog(val) {
        console.log('isOpenDialog', val);
        if (val) {
          this.reloadData();
        }
      },
    },
    created() {
      console.log('created');
      this.reloadData();
    },
    unmounted() {
      console.log('destroyed');
    },
    methods: {
      chooseFiles() {
        document.getElementById('file-input1').click();
      },
      reloadData() {
        this.errors = {};
        const img =
          this.type === 1
            ? Object.assign({}, this.itemStore.itemImages[this.imageIndex])
            : Object.assign({}, this.itemStore.optionalImages[this.imageIndex]);
        this.tempImage = Object.assign(this.tempImage, img, {
          preview: this.getPreviewData(this.type, this.imageIndex),
        });
      },
      clearImage() {
        console.log('clearImage: ', this.imageIndex);
        console.log('clearImage: ', this.tempImage);
        this.loading = true;
        if (this.tempImage.file || this.tempImage.key) {
          this.itemStore
            .clearImage(this.imageIndex, this.type, this.tempImage)
            .then(() => {
              this.tempImage.key = null;
              this.tempImage.file = null;
              this.tempImage.fileName = null;
              this.tempImage.preview = null;
              if (this.type === 2) {
                this.tempImage.title = null;
              }
              this.loading = false;
              this.imageClearModal = false;
              this.closeDialog();
            })
            .catch(err => {
              console.log('err: ', err);
              this.imageClearModal = false;
              this.loading = false;
            });
        }
      },
      updateImage() {
        console.log('imageIndex: ', this.imageIndex);
        console.log('editImage: ', this.tempImage);

        if (this.tempImage.file || this.tempImage.key) {
          /*
           * Validate
           * 任意画像の場合はタイトルが必須
           */
          if (this.type === 2 && !this.tempImage.title) {
            this.errors = {title: '入力してください。'};
            return;
          }
          // Upload to s3
          this.loading = true;
          this.itemStore
            .uploadItemImage(this.imageIndex, this.type, this.tempImage)
            .then(key => {
              console.log('updateImage: ', key);
              this.tempImage.key = key;
              this.loading = false;
              this.closeDialog();
            })
            .catch(err => {
              console.log('err: ', err);
              this.loading = false;
            });
        } else {
          this.closeDialog();
        }
      },
      selectFileChange(file) {
        console.log('selectFileChange');
        if (file.target.files.length > 0) {
          const err = this.checkFileSize(
            file.target.files[0],
            '5MB以内のファイルをアップロードしてください。'
          );
          if (err) {
            this.errors = Object.assign({}, this.errors, {itemImage: err});
          } else {
            this.tempImage.file = file.target.files[0];
            this.tempImage.fileName = file.target.files[0].name;
            this.tempImage.key = null;

            // Get preview image
            this.createImagePreview(file.target.files[0])
              .then(dataURI => {
                this.tempImage.preview = dataURI;
              })
              .catch(err2 => {
                console.log('err2: ', err2);
              });
          }
        }
      },
      createImagePreview(file) {
        console.log('createImage');
        if (this.disabled) {
          return Promise.resolve(null);
        }
        const formData = new FormData();
        formData.append('file', file);

        // 動画表示用
        return Promise.resolve()
          .then(() => {
            if (file.name.indexOf('.mp4') !== -1) {
              return this.getVideoCover(file);
            }
            return Promise.resolve(file);
          })
          .then(fileCover => {
            console.log('fileCover: OK');
            return new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = e => {
                const dataURI = e.target.result;
                return resolve(dataURI);
              };
              reader.readAsDataURL(fileCover);
            });
          });
      },
      getVideoCover(file, seekTo = 0.0) {
        console.log('getting video cover for file: ', file);
        return new Promise((resolve, reject) => {
          // Load the file to a video player
          const videoPlayer = document.createElement('video');
          videoPlayer.setAttribute('src', URL.createObjectURL(file));
          videoPlayer.load();
          videoPlayer.addEventListener('error', ex => {
            reject(new Error('error when loading video file', ex));
          });
          // Load metadata of the video to get video duration and dimensions
          videoPlayer.addEventListener('loadedmetadata', () => {
            // Seek to user defined timestamp (in seconds) if possible
            if (videoPlayer.duration < seekTo) {
              reject(new Error('video is too short.'));
              return;
            }
            // Delay seeking or else 'seeked' event won't fire on Safari
            setTimeout(() => {
              videoPlayer.currentTime = seekTo;
            }, 200);
            // Extract video thumbnail once seeking is complete
            videoPlayer.addEventListener('seeked', () => {
              console.log('video is now paused at %ss.', seekTo);
              // Define a canvas to have the same dimension as the video
              const canvas = document.createElement('canvas');
              canvas.width = videoPlayer.videoWidth;
              canvas.height = videoPlayer.videoHeight;
              // Draw the video frame to canvas
              const ctx = canvas.getContext('2d');
              ctx.drawImage(videoPlayer, 0, 0, canvas.width, canvas.height);
              // Return the canvas image as a blob
              ctx.canvas.toBlob(
                blob => {
                  resolve(blob);
                },
                'image/jpeg',
                0.75 /* Quality */
              );
            });
          });
        });
      },
      checkFileSize(file, errMsg) {
        if (file && file.size) {
          if (file.size > 5 * 1024 * 1024) {
            return errMsg;
          }
        }
        return null;
      },
      openImageClearModal() {
        this.imageClearModal = true;
      },
    },
  };
</script>
<template>
  <CModal
    backdrop="static"
    :keyboard="false"
    :visible="isOpenDialog"
    @close="closeDialog"
  >
    <CModalHeader>
      <CModalTitle>画像</CModalTitle>
    </CModalHeader>
    <CModalBody>
      <div v-if="imgPreview" class="popup-content-form-filebox js-fileDrop">
        <a @click.stop="chooseFiles">
          <input
            type="file"
            :name="`file${imageIndex}`"
            id="file-input1"
            accept=".jpg,.png"
            @change="selectFileChange"
            hidden
          />
          <CImg fluid :src="imgPreview" align="center" class="grey lighten-2" />
          <div class="spinner">
            <CSpinner v-if="loading" />
          </div>
        </a>
      </div>
      <div v-else class="popup-content-form-filebox js-fileDrop">
        <p class="text-center">
          アップロードしたいファイルを<br />
          ここにドラッグ＆ドロップ
        </p>
        <label>
          <input
            type="file"
            name="file"
            id="file-input"
            accept=".jpg,.png"
            @change="selectFileChange"
            hidden
          />またはファイルをアップロード
        </label>
      </div>
      <div v-if="type === 2" class="text-center popup-content-form-wrap">
        <p>タイトルを入れる</p>
        <input
          type="text"
          name="text"
          v-model="tempImage.title"
          v-bind:class="{err: errors.title}"
          @keydown="() => (errors.title = null)"
          ref="title"
        />
        <p class="err-txt" v-if="errors.title">{{ errors.title }}</p>
      </div>
    </CModalBody>
    <CModalFooter>
      <CButton @click="closeDialog" color="dark" :disabled="loading"
        >キャンセル</CButton
      >
      <CButton
        @click="openImageClearModal"
        color="danger"
        :disabled="loading || !imgPreview"
        >削除</CButton
      >
      <CButton @click="updateImage" color="primary" :disabled="loading"
        >OK</CButton
      >
    </CModalFooter>
  </CModal>

  <CModal
    backdrop="static"
    color="warning"
    :keyboard="false"
    :visible="imageClearModal"
    @close="
      () => {
        imageClearModal = false;
      }
    "
  >
    <CModalHeader>
      <CModalTitle>確認</CModalTitle>
    </CModalHeader>
    <CModalBody>
      <div v-if="!loading">画像を削除してもよろしいですか？</div>
      <div style="margin-left: 10px">
        <scale-loader
          :loading="loading"
          color="#5dc596"
          height="10px"
          width="4px"
        ></scale-loader>
      </div>
    </CModalBody>
    <CModalFooter>
      <CButton @click="imageClearModal = false" color="dark" :disabled="loading"
        >キャンセル</CButton
      >
      <CButton @click="clearImage" color="primary" :disabled="loading"
        >OK</CButton
      >
    </CModalFooter>
  </CModal>
</template>

<style>
  .modal-header .close {
    color: #fff;
  }

  .popup-content-form-filebox {
    position: relative;
    width: 100%;
    max-width: 380px;
    margin: 0 auto;
    background: #e5e5e5;
    border: 1px solid #dddddd;
    padding: 30px 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  .popup-content-form-filebox p {
    display: block;
    text-align: center;
    font-size: 16px;
    margin-top: 45px;
    line-height: 2;
  }

  .popup-content-form-filebox label {
    width: 100%;
    max-width: 240px;
    margin: 70px auto 0;
    padding: 14px 15px;
    border-color: #666;
    background: #666;
    display: block;
    border-radius: 5px;
    font-size: 14px;
    color: #fff;
    line-height: 1;
    text-align: center;
    -webkit-transition: all ease 0.7s;
    transition: all ease 0.7s;
  }

  .popup-content-form-filebox .spinner {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .popup-content-form-wrap {
    margin-top: 30px;
  }

  .popup-content-form-wrap p {
    font-size: 18px;
    font-weight: bold;
    display: block;
    text-align: left;
    margin-bottom: 0;
  }

  .popup-content-form-wrap input {
    width: 100%;
    background: #fff;
    border: 1px solid #e4e4e4;
    margin-top: 10px;
    color: inherit;
    font-size: 16px;
    padding: 11px 20px;
    -webkit-appearance: none;
  }

  .popup-content-form-wrap p.err-txt {
    font-size: 14px;
  }

  .popup-content-form-wrap p {
    font-size: 18px;
    font-weight: bold;
    display: block;
    text-align: left;
  }

  p.err-txt {
    font-size: 12px;
    color: #ff0000;
  }
</style>
