<template>
  <div>
    <TCInputRadioGroup
      :label="label"
      class="custom-form-radio"
      :options="options"
      :inline="true"
      :checked="model"
      @update:checked="onChangeModelValue"
      :error="!!isErrorRadio"
      messages="1"
      :disabled="isAllDisabled"
      :name="refTxt"
    />
    <slot :isTxtDisabled="isTxtDisabled"></slot>
  </div>
</template>
<script>
  import TCInputRadioGroup from './TCInputRadioGroup';
  export default {
    components: {
      TCInputRadioGroup,
    },
    props: {
      model: {
        type: String,
      },
      label: {
        type: String,
        default: null,
      },
      options: {
        type: Array,
        default: () => [],
      },
      isErrorRadio: {
        type: String,
        default: null,
      },
      refTxt: {
        type: String,
        default: null,
      },
      isAllDisabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        errors: {
          content: null,
        },
      };
    },
    computed: {
      isTxtDisabled() {
        return (
          this.isAllDisabled ||
          !(
            (this.options.find(x => x.value === this.model) || {})
              .isTextInput === true
          )
        );
      },
      inputVal: {
        get() {
          return this.model;
        },
        set(val) {
          this.$emit('onChangeModelValue', val);
        },
      },
      inputTextVal: {
        get() {
          return this.modelText;
        },
        set(val) {
          this.$emit('onChangeModelTextValue', val);
        },
      },
    },
    methods: {
      onChangeModelValue(val) {
        console.log('onChangeModelValue', val);
        const isTextInput = (this.options.find(x => x.value === val) || {})
          .isTextInput;
        this.$emit('onChangeModelValue', {val, isTextInput});
      },
      onChangeModelTextValue(val) {
        this.$emit('onChangeModelTextValue', val);
      },
    },
  };
</script>
