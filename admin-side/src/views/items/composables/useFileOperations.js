import {ref} from 'vue';
import {useRouter} from 'vue-router';
import Methods from '@/api/methods';
import UploadFile from '@/api/uploadFileToS3';

/**
 * Composable for file operations (CSV/ZIP upload/download)
 * Handles file processing, S3 uploads, and modal states
 */
export function useFileOperations() {
  const router = useRouter();

  // CSV/ZIP modal state
  const csvModal = ref(false);
  const loadingCsv = ref(false);
  const errMsgArray = ref([]);

  /**
   * Close CSV modal
   */
  const closeCsvModal = onClose => {
    console.log('closeCsvModal');
    csvModal.value = false;

    // Call callback function (typically to refresh search results)
    if (onClose && typeof onClose === 'function') {
      onClose();
    }
  };

  /**
   * Handle CSV file upload
   */
  const csvUpload = async (event, fileInputRef) => {
    console.log('CsvUpload');
    let fileInfo = null;
    for (let i = 0; i < event.target.files.length; i++) {
      fileInfo = event.target.files[i];
      break;
    }

    // Clear file input
    if (fileInputRef && fileInputRef.value) {
      fileInputRef.value.value = null;
    }

    if (typeof fileInfo === 'undefined' || fileInfo === null) {
      return;
    }

    // Show modal
    csvModal.value = true;
    loadingCsv.value = true;
    errMsgArray.value = [];

    // Upload to S3
    const apiType = 'csv-upload';

    return new Promise((resolve, reject) => {
      UploadFile.upload(apiType, fileInfo, async data => {
        console.log(`data. ${JSON.stringify(data)}`);
        if (data.status === 200) {
          const s3url = data.message;
          const reqParam = {
            url: s3url,
          };

          try {
            // Request to import CSV API
            const response = await Methods.apiExecute(
              'import-item-csv-file',
              reqParam
            );
            console.log(`import-item-csv-file = ${JSON.stringify(response)}`);
            loadingCsv.value = false;
            errMsgArray.value = ['処理が完了しました。'];
            resolve(response);
          } catch (error) {
            loadingCsv.value = false;
            console.log(JSON.stringify(error));
            errMsgArray.value = Methods.parseHtmlResponseError(router, error);
            reject(error);
          }
        } else {
          loadingCsv.value = false;
          errMsgArray.value = ['アップロードに失敗しました。'];
          reject(new Error('Upload failed'));
        }
      });
    });
  };

  /**
   * Handle ZIP file upload
   */
  const zipUpload = async (event, zipFileInputRef) => {
    console.log('zipUpload');
    let zipFileInfo = null;
    for (let i = 0; i < event.target.files.length; i++) {
      zipFileInfo = event.target.files[i];
      break;
    }

    // Clear file input
    if (zipFileInputRef && zipFileInputRef.value) {
      zipFileInputRef.value.value = null;
    }

    if (typeof zipFileInfo === 'undefined' || zipFileInfo === null) {
      return;
    }

    // Show modal
    csvModal.value = true;
    loadingCsv.value = true;
    errMsgArray.value = [];

    // Upload to S3
    const apiType = 'csv-upload';

    return new Promise((resolve, reject) => {
      UploadFile.upload(apiType, zipFileInfo, async data => {
        console.log(`data. ${JSON.stringify(data)}`);
        if (data.status === 200) {
          const s3url = data.message;
          const reqParam = {
            url: s3url,
          };

          try {
            // Request to import ZIP API
            const response = await Methods.apiExecute(
              'import-item-zip-file',
              reqParam
            );
            console.log(`import-item-zip-file = ${JSON.stringify(response)}`);
            loadingCsv.value = false;
            errMsgArray.value = ['処理が完了しました。'];
            resolve(response);
          } catch (error) {
            loadingCsv.value = false;
            console.log(JSON.stringify(error));
            errMsgArray.value = Methods.parseHtmlResponseError(router, error);
            reject(error);
          }
        } else {
          loadingCsv.value = false;
          errMsgArray.value = ['アップロードに失敗しました。'];
          reject(new Error('Upload failed'));
        }
      });
    });
  };

  /**
   * Handle CSV download
   */
  const csvDownload = async (
    searchCondition,
    optionsStatus,
    optionsMessage,
    optionsExhibitionName,
    itemsSorter
  ) => {
    console.log('csvDownload');

    csvModal.value = true;
    loadingCsv.value = true;
    errMsgArray.value = [];

    // Transform search condition to API format
    const status = optionsStatus
      .map((x, i) => (searchCondition.status[i] ? x.value : null))
      .filter(y => y);

    const unreadMessage = optionsMessage
      .map((x, i) => (searchCondition.unread_message[i] ? x.value : null))
      .filter(y => y);

    const exhibitionName =
      searchCondition.exhibition_name === null
        ? null
        : (
            optionsExhibitionName.find(
              x => x.value === searchCondition.exhibition_name
            ) || {}
          ).label;

    // Request parameters
    const cond = {
      status,
      manage_no_from: searchCondition.manage_no_from,
      manage_no_to: searchCondition.manage_no_to,
      request_no_from: searchCondition.request_no_from,
      request_no_to: searchCondition.request_no_to,
      exhibition_name: exhibitionName,
      unread_message: unreadMessage,
    };

    // Add sorting
    cond.sorter = itemsSorter;
    console.log('search_condition', cond);

    try {
      // Request to server
      const response = await Methods.apiExecute('export-items-csv-file', cond);
      if (response.status === 200) {
        const csv = response.data;
        window.location.href = csv.url;
      }
      loadingCsv.value = false;
      csvModal.value = false;
      return response;
    } catch (error) {
      loadingCsv.value = false;
      errMsgArray.value = Methods.parseHtmlResponseError(router, error);
      throw error;
    }
  };

  // Return all reactive data and methods
  return {
    // Modal state
    csvModal,
    loadingCsv,
    errMsgArray,

    // Methods
    csvUpload,
    zipUpload,
    csvDownload,
    closeCsvModal,
  };
}
