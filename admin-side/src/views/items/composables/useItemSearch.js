import {ref, reactive, computed, watch} from 'vue';
import {useRouter, useRoute} from 'vue-router';
import Methods from '@/api/methods';
import Base from '@/common/base';

export function useItemSearch() {
  const router = useRouter();
  const route = useRoute();

  // Loading states
  const loading = ref(false);
  const searchConditionErrorModal = ref(false);
  const errMsgArray = ref([]);

  // Search results
  const itemList = ref([]);
  const itemListOrig = ref([]);
  const currentCount = ref(0);
  const totalCount = ref(0);
  const constantList = ref([]);

  // Search options
  const optionsStatus = ref([
    {value: '1', label: '在庫'},
    {value: '2', label: '出品中'},
    {value: '3', label: '出品中(キャンセル)'},
    {value: '4', label: '成約'},
  ]);

  const optionsMessage = ref([
    {value: '1', label: '有'},
    {value: '0', label: '無'},
  ]);

  const optionsExhibitionName = ref([]);

  // Default search condition - Use boolean arrays for checkboxes (will be converted to numeric arrays)
  const defaultSearchCondition = {
    status: [false, false, false, false], // 4 status options
    manage_no_from: '',
    manage_no_to: '',
    request_no_from: '',
    request_no_to: '',
    exhibition_name: '',
    unread_message: [false, false], // 2 unread message options
  };

  // Search condition (reactive)
  const searchCondition = reactive({...defaultSearchCondition});

  // Computed properties for formatted counts
  const formattedTotalCount = computed(() => {
    return Base.number2string(totalCount.value);
  });

  const formattedCurrentCount = computed(() => {
    return Base.number2string(currentCount.value);
  });

  /**
   * Transform search condition arrays to API format with numeric arrays
   * Architecture Decision: Send numeric arrays instead of Japanese labels for better performance
   *
   * Data structure sent to backend:
   * {
   *   "status": string[],             // [1, 2, 3, 4] (numeric arrays)
   *   "manage_no": { "from": string, "to": string },
   *   "request_no": { "from": string, "to": string },
   *   "exhibition_name": string,
   *   "unread_message": string[]      // [0, 1] (numeric arrays)
   * }
   */
  const transformSearchCondition = () => {
    // Transform status checkboxes to numeric array
    const status = optionsStatus.value
      .map((x, i) => (searchCondition.status[i] ? Number(x.value) : null))
      .filter(y => y !== null);

    // Transform unread message checkboxes to numeric array
    const unreadMessage = optionsMessage.value
      .map((x, i) =>
        searchCondition.unread_message[i] ? Number(x.value) : null
      )
      .filter(y => y !== null);

    // Get exhibition name (now stored directly as the name, not index)
    const exhibitionName = searchCondition.exhibition_name || null;

    // Return data structure with numeric arrays
    const result = {
      status,
      manage_no: {
        from: searchCondition.manage_no_from || '',
        to: searchCondition.manage_no_to || '',
      },
      request_no: {
        from: searchCondition.request_no_from || '',
        to: searchCondition.request_no_to || '',
      },
      exhibition_name: exhibitionName,
      unread_message: unreadMessage,
    };
    return result;
  };

  /**
   * Get constants data from API
   */
  const getConstantsData = async () => {
    loading.value = true;
    const params = {
      key_strings: ['SELECT_MAKER', 'SELECT_FUMEI_NASI'],
    };

    try {
      const response = await Methods.apiExecute(
        'get-constants-by-keys',
        params
      );
      constantList.value = response.data;
      return Promise.resolve();
    } catch (error) {
      console.log(JSON.stringify(error));
      loading.value = false;
      Methods.parseHtmlResponseError(router, error);
      return Promise.reject(error);
    }
  };

  /**
   * Get exhibition pulldown options
   */
  const getExhibitionPulldown = async () => {
    try {
      const response = await Methods.apiExecute('get-exhibition-pulldown', {});
      if (response.status === 200) {
        const pulldown = response.data;
        optionsExhibitionName.value = [];
        optionsExhibitionName.value.push({
          value: null,
          label: 'すべて',
        });
        if (pulldown) {
          for (let i = 0; i < pulldown.length; i++) {
            optionsExhibitionName.value.push({
              value: pulldown[i].exhibition_name,
              label: pulldown[i].exhibition_name,
            });
          }
        }
      }
      return Promise.resolve();
    } catch (error) {
      console.log(JSON.stringify(error));
      loading.value = false;
      Methods.parseHtmlResponseError(router, error);
      return Promise.reject(error);
    }
  };

  /**
   * Execute search and get items
   */
  const getItems = async () => {
    searchConditionErrorModal.value = false;
    errMsgArray.value = [];

    // Reset counts
    currentCount.value = 0;
    totalCount.value = 0;

    const request = transformSearchCondition();
    try {
      const response = await Methods.apiExecute('get-items', request);
      const data = response.data;
      currentCount.value =
        data && data.current_count ? data.current_count || 0 : 0;
      totalCount.value = data && data.total_count ? data.total_count || 0 : 0;

      if (response.status === 200) {
        const items = data.data.map(x => {
          const fumeiNasi =
            constantList.value.find(
              y =>
                y.key_string === 'SELECT_FUMEI_NASI' &&
                y.value1 === String(x.free_field?.inspec_expire)
            ) || {};
          return {
            ...x,
            checked: false,
          };
        });

        itemList.value = items;
        itemListOrig.value = JSON.parse(JSON.stringify(itemList.value));
        loading.value = false;

        return Promise.resolve(items);
      }
      return Promise.resolve([]);
    } catch (error) {
      console.log(error);
      loading.value = false;
      errMsgArray.value = Methods.parseHtmlResponseError(router, error);
      if (errMsgArray.value && errMsgArray.value.length > 0) {
        searchConditionErrorModal.value = true;
      }
      throw error;
    }
  };

  /**
   * Main search function
   */
  const search = async () => {
    loading.value = true;
    await getItems();
  };

  /**
   * Initialize the search functionality
   */
  const initialize = async () => {
    Object.assign(searchCondition, defaultSearchCondition);

    try {
      await getConstantsData();
      await getExhibitionPulldown();
      await search();
    } catch (error) {
      console.error('Failed to initialize search:', error);
    }
  };

  return {
    loading,
    searchCondition,
    itemList,
    itemListOrig,
    currentCount: formattedCurrentCount,
    totalCount: formattedTotalCount,
    constantList,
    errMsgArray,
    searchConditionErrorModal,

    // Options
    optionsStatus,
    optionsMessage,
    optionsExhibitionName,

    // Methods
    search,
    getItems,
    getConstantsData,
    getExhibitionPulldown,
    initialize,
    transformSearchCondition,
  };
}
