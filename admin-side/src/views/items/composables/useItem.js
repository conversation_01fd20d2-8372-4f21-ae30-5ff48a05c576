import {ref, computed} from 'vue';
import {useRouter} from 'vue-router';
import Methods from '@/api/methods';

/**
 * Composable for item detail functionality
 * Handles item data fetching using get-item-detail API
 */
export function useItem() {
  const router = useRouter();

  const loading = ref(false);
  const modalLoading = ref(false);
  const errMsgArray = ref([]);

  const itemDetailData = ref({
    lot_no: null,
    item_no: null,
    manage_no: null,
    exhibition_no: null,
    area_id: null,
    status: null,
    quantity: null,
    lowest_bid_price: null,
    lowest_bid_accept_price: null,
    lowest_bid_quantity: null,
    lowest_bid_accept_quantity: null,
    default_end_datetime: null,
    preview_start_datetime: null,
    preview_end_datetime: null,
    price_display_flag: null,
    localized_json_array: [],
    ancillary_json_array: null,
    success_company_name: null,
    success_price: null,
    currency_id: null,
  });

  const origItemData = ref(null);

  const isCompleted = computed(() => {
    return itemDetailData.value.status === 4; // SOLD OUT
  });

  /**
   * Reset all data to initial state
   */
  const reset = () => {
    loading.value = false;
    modalLoading.value = false;
    errMsgArray.value = [];
    itemDetailData.value = {
      lot_no: null,
      item_no: null,
      manage_no: null,
      exhibition_no: null,
      area_id: null,
      status: null,
      quantity: null,
      lowest_bid_price: null,
      lowest_bid_accept_price: null,
      lowest_bid_quantity: null,
      lowest_bid_accept_quantity: null,
      default_end_datetime: null,
      preview_start_datetime: null,
      preview_end_datetime: null,
      price_display_flag: null,
      localized_json_array: [],
      ancillary_json_array: null,
      success_company_name: null,
      success_price: null,
      currency_id: null,
    };
    origItemData.value = null;
  };

  /**
   * Get item detail data from API using get-item-detail endpoint
   */
  const getItemDetail = async itemNo => {
    try {
      loading.value = true;
      const params = {
        item_no: itemNo,
      };

      const response = await Methods.apiExecute('get-item-detail', params);
      console.log(
        '%c 🈁: useItem -> response ',
        'font-size:16px;background-color:#0ebc78;color:white;',
        response
      );

      if (
        response.status === 200 &&
        response.data &&
        response.data.length > 0
      ) {
        return response.data[0];
      }
      return null;
    } catch (error) {
      console.error('Error getting item detail:', error);
      loading.value = false;
      Methods.parseHtmlResponseError(router, error);
      throw error;
    }
  };

  /**
   * Process and set item data
   */
  const processItemData = itemData => {
    // Set item detail data directly from API response
    itemDetailData.value = {
      ...itemData,
    };

    // Store original data for change detection
    origItemData.value = JSON.parse(JSON.stringify(itemData));
    loading.value = false;
  };

  /**
   * Check if data has been changed
   */
  const isDataChanged = computed(() => {
    if (!origItemData.value) return false;
    const isChanged =
      JSON.stringify(itemDetailData.value) !==
      JSON.stringify(origItemData.value);
    return isChanged;
  });

  /**
   * Update item data
   */
  const updateItem = async () => {
    try {
      modalLoading.value = true;
      errMsgArray.value = [];

      const params = {
        validation_mode: false,
        item: itemDetailData.value,
      };

      const response = await Methods.apiExecute('update-item', params);

      if (response.status === 400) {
        errMsgArray.value.push(response.message);
      } else {
        // Update original data for change-checking
        origItemData.value = JSON.parse(JSON.stringify(itemDetailData.value));
        router.push({path: '/items'});
      }
    } catch (error) {
      console.error('Error updating item:', error);
      errMsgArray.value = Methods.parseHtmlResponseError(router, error);
    } finally {
      modalLoading.value = false;
    }
  };

  /**
   * Initialize item data
   */
  const initialize = async itemNo => {
    try {
      reset();
      const itemData = await getItemDetail(itemNo);

      if (itemData) {
        processItemData(itemData);
      }
    } catch (error) {
      console.error('Error initializing item:', error);
      throw error;
    }
  };

  return {
    // Reactive data
    loading,
    modalLoading,
    errMsgArray,
    itemDetailData,

    // Computed
    isCompleted,

    // Methods
    reset,
    getItemDetail,
    processItemData,
    isDataChanged,
    updateItem,
    initialize,
  };
}
