import {ref} from 'vue';
import Methods from '@/api/methods';

/**
 * Composable for item actions (exhibit, purchase, request history)
 * Handles modal states and API calls for item actions
 */
export function useItemActions() {
  // Exhibit modal state
  const exhibitModal = ref(false);
  const loadingExhibitModal = ref(false);
  const exhibitModalTitle = ref('出品確認');
  const validateResult = ref([]);

  // Purchase modal state
  const purchasedModal = ref(false);
  const loadingPurchasedModal = ref(false);
  const purchasedModalTitle = ref('買取確認');
  const purchaseRequestNo = ref(null);
  const purchaseClassification = ref(null);

  // Request history modal state
  const requestHistoryModal = ref(false);
  const loadingRequestHistory = ref(false);
  const requestHistories = ref([]);

  /**
   * Open exhibit modal
   */
  const openExhibitModal = data => {
    console.log('openExhibitModal', data);
    exhibitModal.value = true;
    validateResult.value = [];
    exhibitModalTitle.value = '確認';
  };

  /**
   * Close exhibit modal
   */
  const closeExhibitModal = () => {
    console.log('closeExhibitModal');
    exhibitModal.value = false;
    loadingExhibitModal.value = false;
  };

  /**
   * Execute exhibit action
   */
  const exhibit = async () => {
    console.log('exhibit');
    if (validateResult.value.length > 0) {
      loadingExhibitModal.value = false;
      exhibitModal.value = false;
      return;
    }
    loadingExhibitModal.value = true;
    // TODO: Implement actual exhibit API call
  };

  /**
   * Open purchase modal
   */
  const openPurchasedModal = item => {
    console.log('onOpenPurchasedModal', item);
    purchasedModal.value = true;
    validateResult.value = [];
    purchasedModalTitle.value = '確認';

    purchaseClassification.value = item.classification;
  };

  /**
   * Close purchase modal
   */
  const closePurchasedModal = () => {
    purchasedModal.value = false;
    loadingPurchasedModal.value = false;
  };

  /**
   * Execute purchase action
   */
  const purchase = async onSuccess => {
    console.log('purchase');
    if (validateResult.value.length > 0) {
      loadingPurchasedModal.value = false;
      purchasedModal.value = false;
      return;
    }
    loadingPurchasedModal.value = true;

    try {
      const response = await Methods.apiExecute('purchase-item', {
        manage_no: purchaseRequestNo.value,
        classification: purchaseClassification.value,
      });

      loadingPurchasedModal.value = false;
      purchasedModal.value = false;

      // Call success callback (typically to refresh search results)
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess();
      }

      return response;
    } catch (error) {
      console.log(error);
      loadingPurchasedModal.value = false;
      throw error;
    }
  };

  /**
   * Get request history for an item
   */
  const getRequestHistory = async item => {
    loadingRequestHistory.value = true;
    requestHistories.value = [];

    try {
      const response = await Methods.apiExecute('get-request-history', {
        request_no: item.request_no,
      });
      requestHistories.value = response.data;
      loadingRequestHistory.value = false;
      return response.data;
    } catch (error) {
      console.log(error);
      loadingRequestHistory.value = false;
      throw error;
    }
  };

  /**
   * Open request history modal and load data
   */
  const onViewRequestHistory = async item => {
    console.log('onViewRequestHistory');
    console.log('item: ', item);

    try {
      await getRequestHistory(item);
      requestHistoryModal.value = true;
    } catch (error) {
      console.error('Failed to load request history:', error);
    }
  };

  /**
   * Close request history modal
   */
  const closeRequestHistoryModal = () => {
    console.log('closeRequestHistoryModal');
    requestHistoryModal.value = false;
  };

  // Return all reactive data and methods
  return {
    // Exhibit modal
    exhibitModal,
    loadingExhibitModal,
    exhibitModalTitle,
    validateResult,
    openExhibitModal,
    closeExhibitModal,
    exhibit,

    // Purchase modal
    purchasedModal,
    loadingPurchasedModal,
    purchasedModalTitle,
    purchaseRequestNo,
    purchaseClassification,
    openPurchasedModal,
    closePurchasedModal,
    purchase,

    // Request history modal
    requestHistoryModal,
    loadingRequestHistory,
    requestHistories,
    getRequestHistory,
    onViewRequestHistory,
    closeRequestHistoryModal,
  };
}
