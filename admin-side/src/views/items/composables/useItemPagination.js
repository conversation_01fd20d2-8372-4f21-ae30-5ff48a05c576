import {ref, computed, watch} from 'vue';
import {useRouter, useRoute} from 'vue-router';
import {useCommonStore} from '@/store/common';

/**
 * Composable for pagination and sorting functionality
 * Handles page navigation, items per page, and sorting with router integration
 */
export function useItemPagination() {
  const router = useRouter();
  const route = useRoute();
  const store = useCommonStore();

  // Pagination state
  const activePage = ref(1);
  const itemsPerPage = ref(10);
  const pages = ref(1);
  const itemsSorter = ref({asc: true, column: 'manage_no'});

  /**
   * Calculate pages based on item list length
   */
  const calculatePages = itemListLength => {
    if (itemListLength > itemsPerPage.value) {
      pages.value =
        parseInt(itemListLength / itemsPerPage.value, 10) +
        (itemListLength % itemsPerPage.value > 0 ? 1 : 0);
    } else {
      pages.value = 1;
    }
  };

  /**
   * Handle page change
   */
  const pageChange = val => {
    activePage.value = val;
    store.set(['activePage', val]);
    router.push({query: {page: val}}).catch(() => {});
  };

  /**
   * Handle pagination change (items per page)
   */
  const paginationChange = val => {
    itemsPerPage.value = val;
    store.set(['itemsPerPage', val]);
  };

  /**
   * Handle sorter change
   */
  const sorterChange = val => {
    itemsSorter.value = val;
    store.set(['itemsSorter', val]);
    pageChange(1); // Reset to first page when sorting changes
  };

  /**
   * Initialize pagination from store or route
   */
  const initializePagination = (prevRoute = null) => {
    // Check if coming from specific routes to preserve pagination state
    if (prevRoute?.name === '在庫詳細') {
      itemsPerPage.value = store.itemsPerPage || 10;
      itemsSorter.value = store.itemsSorter || {asc: true, column: 'manage_no'};
    } else {
      // Reset to default
      store.set(['activePage', 1]);
      store.set(['itemsPerPage', 10]);
      store.set(['itemsSorter', {asc: false, column: 'lotNo'}]);
    }
  };

  /**
   * Update pagination after search results
   */
  const updatePaginationAfterSearch = itemListLength => {
    calculatePages(itemListLength);

    // Ensure active page is within valid range
    const maxPage = pages.value;
    if (store.activePage > maxPage) {
      activePage.value = maxPage;
    } else {
      activePage.value = store.activePage || 1;
    }

    // Update URL with current page
    router.push({query: {page: activePage.value}}).catch(() => {});
  };

  // Watch for route changes to handle page parameter
  watch(
    () => route.query?.page,
    page => {
      if (page) {
        activePage.value = Number(page);
      }
    },
    {immediate: true}
  );

  // Watch for items per page changes to recalculate pages
  watch(
    () => itemsPerPage.value,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        // This will be handled by the parent component when item list changes
        // We just store the new value here
        store.set(['itemsPerPage', newVal]);
      }
    }
  );

  // Return all reactive data and methods
  return {
    // Reactive data
    activePage,
    itemsPerPage,
    pages,
    itemsSorter,

    // Methods
    pageChange,
    paginationChange,
    sorterChange,
    calculatePages,
    initializePagination,
    updatePaginationAfterSearch,
  };
}
