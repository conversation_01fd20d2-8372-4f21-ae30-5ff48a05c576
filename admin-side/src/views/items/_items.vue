<template>
  <div>
    <!-- Search Form Component -->
    <ItemSearchForm
      :searchCondition="searchCondition"
      :optionsStatus="optionsStatus"
      :optionsMessage="optionsMessage"
      :optionsExhibitionName="optionsExhibitionName"
      :itemsSorter="itemsSorter"
      :loading="loading"
      @update:searchCondition="handleSearchConditionUpdate"
      @search="handleSearch"
      @csv-download-complete="handleFileOperationComplete"
      @csv-upload-complete="handleFileOperationComplete"
      @zip-upload-complete="handleFileOperationComplete"
      @file-operation-error="
        error => console.error('File operation error:', error)
      "
    />
    <CRow class="mt-3">
      <CCol sm="12">
        <CTableWrapper
          name="itemList"
          :items="itemList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          caption="自社商品管理一覧"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
          @openExhibitModal="openExhibitModal"
          @openPurchasedModal="openPurchasedModal"
          @onViewRequestHistory="onViewRequestHistory"
        />
      </CCol>
    </CRow>

    <!-- Modal Components -->
    <ExhibitModal
      v-model:show="exhibitModal"
      :loading="loadingExhibitModal"
      :title="exhibitModalTitle"
      :validateResult="validateResult"
      @confirm="handleExhibitConfirm"
      @cancel="closeExhibitModal"
    />

    <PurchaseModal
      v-model:show="purchasedModal"
      :loading="loadingPurchasedModal"
      :title="purchasedModalTitle"
      :validateResult="validateResult"
      @confirm="handlePurchaseConfirm"
      @cancel="closePurchasedModal"
    />

    <RequestHistoryModal
      v-model:show="requestHistoryModal"
      :loading="loadingRequestHistory"
      :requestHistories="requestHistories"
      @close="closeRequestHistoryModal"
    />

    <CsvProcessModal
      v-model:show="csvModal"
      :loading="loadingCsv"
      :errorMessages="errMsgArray"
      @close="handleCsvModalClose"
    />
  </div>
</template>

<script setup>
  import {watch, onMounted} from 'vue';
  import {useRoute} from 'vue-router';
  import CTableWrapper from './itemTable.vue';

  // Import composables
  import {useItemSearch} from './composables/useItemSearch';
  import {useItemActions} from './composables/useItemActions';
  import {useFileOperations} from './composables/useFileOperations';
  import {useItemPagination} from './composables/useItemPagination';

  // Import components
  import ItemSearchForm from './ItemSearchForm.vue';
  import ExhibitModal from './modals/ExhibitModal.vue';
  import PurchaseModal from './modals/PurchaseModal.vue';
  import RequestHistoryModal from './modals/RequestHistoryModal.vue';
  import CsvProcessModal from './modals/CsvProcessModal.vue';

  // Route
  const route = useRoute();

  // Initialize composables
  const searchComposable = useItemSearch();
  const actionsComposable = useItemActions();
  const fileOpsComposable = useFileOperations();
  const paginationComposable = useItemPagination();

  // Destructure composable returns
  const {
    loading,
    searchCondition,
    itemList,
    currentCount,
    totalCount,
    optionsStatus,
    optionsMessage,
    optionsExhibitionName,
    search,
    initialize: initializeSearch,
  } = searchComposable;

  const {
    exhibitModal,
    loadingExhibitModal,
    exhibitModalTitle,
    validateResult,
    purchasedModal,
    loadingPurchasedModal,
    purchasedModalTitle,
    requestHistoryModal,
    loadingRequestHistory,
    requestHistories,
    openExhibitModal,
    closeExhibitModal,
    exhibit,
    openPurchasedModal,
    closePurchasedModal,
    purchase,
    onViewRequestHistory,
    closeRequestHistoryModal,
  } = actionsComposable;

  const {csvModal, loadingCsv, errMsgArray, closeCsvModal} = fileOpsComposable;

  const {
    activePage,
    itemsPerPage,
    pages,
    itemsSorter,
    pageChange,
    paginationChange,
    sorterChange,
    updatePaginationAfterSearch,
  } = paginationComposable;

  onMounted(async () => {
    await initializeSearch();
  });

  watch(
    () => route.query?.page,
    page => {
      if (page) {
        pageChange(Number(page));
      }
    },
    {immediate: true}
  );

  watch(
    () => itemList.value,
    newList => {
      updatePaginationAfterSearch(newList.length);
    }
  );

  // Update from emit update:searchCondition
  const handleSearchConditionUpdate = newCondition => {
    Object.assign(searchCondition, newCondition);
  };

  const handleSearch = () => {
    search();
  };

  const handleExhibitConfirm = async () => {
    await exhibit(() => search());
  };

  const handlePurchaseConfirm = async () => {
    await purchase(() => search());
  };

  const handleCsvModalClose = () => {
    closeCsvModal(() => search());
  };

  const handleFileOperationComplete = () => {
    search();
  };
</script>
