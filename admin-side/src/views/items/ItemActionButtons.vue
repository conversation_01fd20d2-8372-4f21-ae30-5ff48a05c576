<template>
  <!-- CSV Download Button -->
  <CCol sm="2" class="d-grid" style="padding-left: 0px">
    <CButton
      size="sm"
      class="btn-bounce-email"
      @click="handleCsvDownload"
      :disabled="loading"
      block
    >
      出品CSV
      <CIcon name="download" style="margin: 0" />
    </CButton>
  </CCol>

  <!-- CSV Upload Button -->
  <CCol sm="2" class="d-grid" style="padding-left: 0px" v-if="!isReadOnly">
    <label
      class="btn btn-success btn-sm btn-block"
      style="margin-bottom: 0"
      :class="{disabled: loading}"
    >
      出品CSV
      <CIcon name="upload" style="margin: 0" />
      <input
        type="file"
        accept=".csv"
        ref="fileImportRef"
        @change="handleCsvUpload"
        :disabled="loading"
        hidden
      />
    </label>
  </CCol>

  <!-- ZIP Upload Button -->
  <CCol sm="1" class="d-grid" style="padding-left: 0px" v-if="!isReadOnly">
    <label
      class="btn btn-success btn-sm btn-block"
      style="margin-bottom: 0"
      :class="{disabled: loading}"
    >
      ZIP
      <CIcon name="upload" style="margin: 0" />
      <input
        type="file"
        accept=".zip"
        ref="zipFileImportRef"
        @change="handleZipUpload"
        :disabled="loading"
        hidden
      />
    </label>
  </CCol>
</template>

<script setup>
  import {ref} from 'vue';
  import {useFileOperations} from './composables/useFileOperations';
  import {useAuthStore} from '@/store/auth';

  const {isReadOnly} = useAuthStore();

  // Props
  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    searchCondition: {
      type: Object,
      required: true,
    },
    optionsStatus: {
      type: Array,
      required: true,
    },
    optionsMessage: {
      type: Array,
      required: true,
    },
    optionsExhibitionName: {
      type: Array,
      required: true,
    },
    itemsSorter: {
      type: Object,
      required: true,
    },
  });

  // Emits
  const emit = defineEmits([
    'csv-download-complete',
    'csv-upload-complete',
    'zip-upload-complete',
    'file-operation-error',
  ]);

  const fileImportRef = ref(null);
  const zipFileImportRef = ref(null);

  const {csvUpload, zipUpload, csvDownload} = useFileOperations();

  const handleCsvDownload = async () => {
    try {
      await csvDownload(
        props.searchCondition,
        props.optionsStatus,
        props.optionsMessage,
        props.optionsExhibitionName,
        props.itemsSorter
      );
      emit('csv-download-complete');
    } catch (error) {
      emit('file-operation-error', error);
    }
  };

  // Handle CSV upload
  const handleCsvUpload = async event => {
    try {
      await csvUpload(event, fileImportRef);
      emit('csv-upload-complete');
    } catch (error) {
      emit('file-operation-error', error);
    }
  };

  // Handle ZIP upload
  const handleZipUpload = async event => {
    try {
      await zipUpload(event, zipFileImportRef);
      emit('zip-upload-complete');
    } catch (error) {
      emit('file-operation-error', error);
    }
  };
</script>
