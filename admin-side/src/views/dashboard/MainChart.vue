<template>
  <CChart type="line" :data="data" :options="options" ref="mainChartRef" />
</template>

<script setup>
  import {getStyle} from '@coreui/utils';
  import {CChart} from '@coreui/vue-chartjs';
  import {computed, onMounted, ref} from 'vue';

  const props = defineProps({
    labels: Object,
    datasets: Object,
    yAxesMax: {
      type: Number,
    },
  });

  const mainChartRef = ref();

  const data = computed(() => ({
    labels: props.labels || [],
    datasets: props.datasets || [],
  }));

  const options = computed(() => ({
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      x: {
        grid: {
          color: getStyle('--cui-border-color-translucent'),
          drawOnChartArea: false,
        },
        ticks: {
          color: getStyle('--cui-body-color'),
        },
      },
      y: {
        beginAtZero: true,
        border: {
          color: getStyle('--cui-border-color-translucent'),
        },
        grid: {
          color: getStyle('--cui-border-color-translucent'),
        },
        max: props.yAxesMax,
        ticks: {
          color: getStyle('--cui-body-color'),
          maxTicksLimit: 5,
          stepSize: Math.ceil(props.yAxesMax / 5),
        },
      },
    },
    elements: {
      line: {
        tension: 0.4,
      },
      point: {
        radius: 0,
        hitRadius: 10,
        hoverRadius: 4,
        hoverBorderWidth: 3,
      },
    },
  }));

  const defaultDatasetsChartLineFunct = () => {
    const labels = [];
    const brandSuccess = getStyle('success2') || '#4dbd74';
    const brandInfo = getStyle('info') || '#20a8d8';
    const brandDanger = getStyle('danger') || '#f86c6b';

    const elements = 7;
    const datas = [];

    for (let i = 0; i <= elements; i++) {
      datas.push(0);
      labels.push(i);
    }
    const datasets = [
      {
        label: '入札数',
        backgroundColor: hexToRgba(brandInfo, 10),
        borderColor: brandInfo,
        pointHoverBackgroundColor: brandInfo,
        borderWidth: 2,
        data: datas,
      },
    ];
  };

  onMounted(() => {
    document.documentElement.addEventListener('ColorSchemeChange', () => {
      if (mainChartRef.value) {
        // MainChartRef.value.chart
        options.value.scales.x.grid.borderColor = getStyle(
          '--cui-border-color-translucent'
        );
        // MainChartRef.value.chart
        options.value.scales.x.grid.color = getStyle(
          '--cui-border-color-translucent'
        );
        // MainChartRef.value.chart
        options.value.scales.x.ticks.color = getStyle('--cui-body-color');
        // MainChartRef.value.chart
        options.value.scales.y.grid.borderColor = getStyle(
          '--cui-border-color-translucent'
        );
        // MainChartRef.value.chart
        options.value.scales.y.grid.color = getStyle(
          '--cui-border-color-translucent'
        );
        // MainChartRef.value.chart
        options.value.scales.y.ticks.color = getStyle('--cui-body-color');
        mainChartRef.value.chart.update();
      }
    });
  });
</script>
