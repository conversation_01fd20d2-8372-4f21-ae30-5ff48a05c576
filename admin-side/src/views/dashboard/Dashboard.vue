<template>
  <div class="mb-3">
    <div class="mb-3">
      <CCard>
        <CCardHeader>
          <strong>検索条件</strong>
        </CCardHeader>
        <CCardBody>
          <CForm>
            <CRow>
              <CCol sm="2"> 入札会 </CCol>
              <CCol sm="4">
                <CFormSelect
                  name="exhibition_id"
                  :options="options_exhibition_name"
                  v-model="search_condition.exhibition_id"
                  class="form-group"
                />
              </CCol>
            </CRow>
          </CForm>
        </CCardBody>
        <CCardFooter>
          <CRow class="align-items-center">
            <CCol xs="5" class="mb-3 mb-xl-0 text-end"></CCol>
            <CCol xs="2" class="mb-3 mb-xl-0 text-end">
              <div class="d-grid gap-2">
                <CButton size="sm" color="info" @click="searchDashboard" block
                  >検索</CButton
                >
              </div>
            </CCol>
            <CCol xs="1" />
            <CCol xs="2" class="mb-3 mb-xl-0 text-end"></CCol>
            <CCol xs="2" class="mb-3 mb-xl-0 text-end"></CCol>
          </CRow>
        </CCardFooter>
      </CCard>
    </div>

    <div v-if="loading" class="my-3 text-center">
      <CSpinner variant="grow" />
    </div>

    <div v-show="!loading">
      <CCard>
        <CCardHeader>{{ '' }}</CCardHeader>
        <CCardBody>
          <CRow>
            <CCol sm="12" lg="12">
              <CRow>
                <template v-for="value in summary_values" :key="value.text">
                  <CCol sm="2" :class="value.style_box || 'default_box'">
                    <CCallout
                      class="btn-block"
                      :class="value.style_title || 'default_title'"
                    >
                      <small class="text-muted text-title">
                        <CIcon :name="value.icon" />{{ value.text }}
                      </small>
                      <br />
                      <div class="text-end" :class="value.style_number || ''">
                        <strong class="h2 text-price">{{
                          value.number
                        }}</strong>
                      </div>
                      <div
                        class="text-end"
                        :class="value.style_number_sub || ''"
                      >
                        <small class="h7">{{
                          value.number_sub || '&nbsp;'
                        }}</small>
                      </div>
                      <small :class="'text-' + value.color">
                        <CIcon
                          name="cil-caret-bottom"
                          v-if="value.color === 'danger'"
                        />
                        <CIcon
                          name="cil-caret-top"
                          v-if="value.color === 'info'"
                        />
                        {{ value.percent }} %
                      </small>
                      <small class="text-muted text-zenkaihi"> 前回比</small>
                    </CCallout>
                  </CCol>
                </template>
              </CRow>
              <hr class="mt-0" />
              <CRow>
                <CCol sm="8">
                  <CCard class="mb-4">
                    <CCardHeader>
                      <CRow>
                        <CCol sm="6">
                          <h4 id="traffic" class="card-title mb-0">入札数</h4>
                        </CCol>
                        <CCol
                          sm="6"
                          class="pl-0 form-inline justify-content-md-end"
                        ></CCol>
                      </CRow>
                    </CCardHeader>
                    <CCardBody>
                      <MainChart
                        style="height: 300px; margin-top: 40px"
                        :datasets="defaultDatasetsChartLine"
                        :labels="defaultDatasetsChartLineLabel"
                        :yAxesMax="chartYAxesMax"
                      />
                    </CCardBody>
                  </CCard>
                </CCol>
                <CCol sm="4" class="text-center">
                  <CCard>
                    <CCardHeader>
                      <div>入札数上位商品</div>
                    </CCardHeader>
                    <CCardBody>
                      <template v-for="(value, index) in topBid" :key="index">
                        <div sm="12" class="progress-group">
                          <div class="progress-group-header">
                            <span class="title">{{ value.text }}</span>
                            <span class="ml-auto font-weight-bold">
                              {{ value.userNumber }}
                            </span>
                          </div>
                          <div class="progress-group-bars">
                            <CProgress
                              class="progress-xs"
                              :value="value.value"
                              color="success"
                            />
                          </div>
                        </div>
                      </template>
                    </CCardBody>
                  </CCard>
                </CCol>
              </CRow>
              <CRow class="mt-3">
                <CCol sm="6" lg="6">
                  <CCard>
                    <CCardHeader>
                      <div>
                        会員別入札金額
                        <span style="float: right; font-size: 12px"
                          >総入札額:
                          {{
                            number2string(
                              dashboard_current_data
                                ? dashboard_current_data.sum_bid
                                : null
                            )
                          }}$</span
                        >
                        <span
                          style="
                            float: right;
                            font-size: 12px;
                            margin-right: 30px;
                          "
                          >入札会員数:
                          {{
                            number2string(
                              dashboard_current_data
                                ? dashboard_current_data.sum_bid_member_count
                                : null
                            )
                          }}会員</span
                        >
                      </div>
                    </CCardHeader>
                    <CCardBody>
                      <template
                        v-for="(value, index) in bidAmount"
                        :key="index"
                      >
                        <div class="progress-group mb-4">
                          <CCol sm="2">
                            <div class="progress-group-prepend">
                              <span class="progress-group-text">
                                {{ value.text1 }}
                              </span>
                            </div>
                          </CCol>
                          <!-- <CCol sm="8"> -->
                          <div class="progress-group-bars">
                            <CProgress
                              class="progress-xs"
                              color="info"
                              :value="value.value"
                            />
                          </div>
                          <CCol v-if="!isReadOnly" sm="2">
                            <div class="text-end">
                              <span
                                class="progress-group-text"
                                style="word-wrap: normal"
                              >
                                {{ value.text2 }}
                              </span>
                            </div>
                          </CCol>
                        </div>
                      </template>
                    </CCardBody>
                  </CCard>
                </CCol>
                <CCol sm="6" lg="6">
                  <CCard>
                    <CCardHeader>
                      <div>カテゴリ別入札金額割合</div>
                    </CCardHeader>
                    <CCardBody>
                      <CRow>
                        <CCol sm="6" lg="6">
                          <CChartDoughnut
                            :data="{
                              labels: defaultDatasetsDoughnut[0].label,
                              datasets: [
                                {
                                  data: defaultDatasetsDoughnut[0].data,
                                  backgroundColor:
                                    defaultDatasetsDoughnut[0].backgroundColor,
                                },
                              ],
                            }"
                            :labels="[]"
                            :options="chartOptions"
                          />
                        </CCol>
                        <CCol sm="6" lg="6">
                          <template
                            v-for="value in defaultDatasetsDoughnutData"
                            :key="value.label"
                          >
                            <div class="progress-group mb-4">
                              <CCol sm="2">
                                <small class="text-muted">
                                  <CIcon
                                    name="cil-square"
                                    v-bind:style="{
                                      'background-color': value.backgroundColor,
                                    }"
                                  />
                                </small>
                              </CCol>
                              <CCol sm="7">
                                <span class="progress-group-text">
                                  {{ value.label }}
                                </span>
                              </CCol>
                              <CCol sm="3" class="text-end">
                                <span
                                  class="progress-group-text"
                                  style="word-wrap: normal"
                                >
                                  {{ value.data }}
                                </span>
                              </CCol>
                            </div>
                          </template>
                        </CCol>
                      </CRow>
                    </CCardBody>
                  </CCard>
                </CCol>
              </CRow>
            </CCol>
          </CRow>
          <br />
        </CCardBody>
      </CCard>
    </div>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import {getStyle, hexToRgba} from '@coreui/utils/src';
  import {CChartDoughnut} from '@coreui/vue-chartjs';
  import moment from 'moment';
  import MainChart from './MainChart.vue';
  import {useAuthStore} from '@/store/auth';

  export default {
    name: 'DashboardComponent',
    components: {
      MainChart,
      CChartDoughnut,
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;
      });
    },
    setup() {
      const {isReadOnly} = useAuthStore();
      return {isReadOnly};
    },

    data() {
      return {
        defaultDatasetsChartLineLabel: [],
        defaultDatasetsDoughnut: [
          {
            label: [],
            backgroundColor: [],
            data: [],
          },
        ],
        defaultDatasetsDoughnutData: [],
        chartOptions: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              enabled: true,
            },
          },
        },
        defaultDatasetsChartLine: [],
        summary_values: [
          {
            color: 'info',
            icon: 'handshake',
            text: '成約数',
            number: '―',
            percent: 0,
            style_title: 'exhibition_item_count_title',
            style_number: 'exhibition_item_count_number',
            style_box: 'exhibition_item_count_box',
          },
          {
            color: 'info',
            icon: 'cil-user',
            text: 'ログイン会員数',
            number: '―',
            percent: 0,
            style_title: 'login_title',
            style_number: 'login_number',
            style_box: 'login_box',
          },
          {
            color: 'info',
            icon: 'gavel-solid',
            text: '総入札数',
            number: '―',
            percent: 0,
            style_box: 'bid_count_box',
            style_number: 'bid_count_number',
          },
          {
            color: 'info',
            icon: 'cil-yen',
            text: '入札額合計（円）',
            number: '―',
            percent: 0,
            style_title: 'current_price_title',
            style_box: 'current_price_box',
            style_number: 'current_price_number',
          },
          {
            color: 'info',
            icon: 'cil-yen',
            text: 'うち最落越え（円）',
            number: '―',
            percent: 0,
            style_title: 'lowest_title',
            style_box: 'lowest_box',
            style_number: 'lowest_number',
          },
          {
            color: 'info',
            icon: 'cil-yen',
            text: '総落札金額（円）',
            number: 'ー',
            style_number: 'hummer_bid_number',
            style_box: 'hummer_bid_box',
          },
        ],
        topBid: [],
        bidAmount: [],
        loading: true,

        // Search box
        options_exhibition_name: [],
        options_category: [],
        search_condition: {
          exhibition_id: '0',
          category: 0,
        },

        // Dashboard data
        dashboard_current_data: null,
        dashboard_previous_data: null,
        chartYAxesMax: 250, // 入札数の最大値
      };
    },
    mounted() {
      if (this.prevRoute && this.prevRoute.name === 'Login') {
        this.$router.go({path: this.$router.currentRoute.path, force: true});
      } else {
        this.loading = false;
      }

      // Get constants
      this.getConstantsData().then(() => {
        this.getExhibitionPulldown().then(() => {
          console.log('Get constants successful');
          this.searchDashboard();
        });
      });
    },
    methods: {
      // Get constants
      getConstantsData() {
        return Promise.resolve();
      },
      // Get exhibition pull down
      getExhibitionPulldown() {
        console.log('getExhibitionPulldown');
        // Request to server
        return Methods.apiExecute('get-exhibition-pulldown', {})
          .then(response => {
            if (response.status === 200) {
              const pulldown = response.data;
              this.options_exhibition_name = [];
              for (let i = 0; i < pulldown.length; i++) {
                this.options_exhibition_name.push({
                  value: i,
                  label: pulldown[i].exhibition_name,
                });
              }
            }
            return Promise.resolve();
          })
          .catch(error => {
            console.log(JSON.stringify(error));
            this.loading = false;
            Methods.parseHtmlResponseError(this.$router, error);
            return Promise.reject(error);
          });
      },
      // Search dashboard data by search condition
      searchDashboard() {
        this.loading = true;
        console.log('searchDashboard');
        const selected_exh_index = -1;
        const selected_exh = this.options_exhibition_name.find((x, i) => {
          return x.value === Number(this.search_condition.exhibition_id);
        });
        const params = {
          exhibition_name: selected_exh ? selected_exh.label : null,
          area_id: null,
        };

        // Request to server
        return Methods.apiExecute('get-dashboard', params)
          .then(response => {
            if (response.status === 200) {
              if (response.data.length > 0) {
                this.dashboard_current_data = response.data[0];
                // Get previous exhibition
                let next_exh = null;
                const next_area_id = params.area_id;
                if (
                  selected_exh_index !== -1 &&
                  this.options_exhibition_name &&
                  selected_exh_index < this.options_exhibition_name.length - 1
                ) {
                  next_exh =
                    this.options_exhibition_name[selected_exh_index + 1].label;
                }
                return this.searchPreviousExhibition(
                  next_exh,
                  next_area_id
                ).then(() => {
                  // Display data
                  this.prepareDisplayData();
                });
              }
            }
            return Promise.resolve();
          })
          .catch(error => {
            console.log(JSON.stringify(error));
            this.loading = false;
            Methods.parseHtmlResponseError(this.$router, error);
            return Promise.reject(error);
          })
          .finally(() => {
            this.loading = false;
          });
      },
      // Search previous exhibition data
      searchPreviousExhibition(exhibition_name, area_id) {
        console.log('searchPreviousExhibition');
        this.dashboard_previous_data = null;
        if (
          typeof exhibition_name === 'undefined' ||
          exhibition_name === null
        ) {
          return Promise.resolve();
        }
        const params = {
          exhibition_name,
          area_id,
        };
        // Request to server
        return Methods.apiExecute('get-dashboard', params)
          .then(response => {
            if (response.status === 200) {
              if (response.data.length > 0) {
                this.dashboard_previous_data = response.data[0];
              }
            }
            return Promise.resolve();
          })
          .catch(error => {
            console.log(JSON.stringify(error));
            this.loading = false;
            Methods.parseHtmlResponseError(this.$router, error);
            return Promise.reject(error);
          });
      },
      // Prepare data for displaying
      prepareDisplayData() {
        /*
         * Header summary data
         * 出品商品数
         */
        this.summary_values[0].color = this.zenkaihi_color_contract();
        this.summary_values[0].number = `${this.number2string(this.dashboard_current_data.sum_contract_count)}/${this.number2string(this.dashboard_current_data.sum_exhibition_item_count)}`;
        this.summary_values[0].number_sub = `成約率: ${this.seiyaku_percent()}%`;
        this.summary_values[0].percent = this.zenkaihi_percent_contract();

        // ログインユーザー数
        this.summary_values[1].color = this.zenkaihi_color('sum_login');
        this.summary_values[1].number = this.number2string(
          this.dashboard_current_data.sum_login
        );
        this.summary_values[1].percent = this.zenkaihi_percent('sum_login');

        // 総入札数
        this.summary_values[2].color = this.zenkaihi_color('sum_bid_count');
        this.summary_values[2].number = this.number2string(
          this.dashboard_current_data.sum_bid_count
        );
        this.summary_values[2].percent = this.zenkaihi_percent('sum_bid_count');

        // 入札額合計
        this.summary_values[3].color = this.zenkaihi_color('sum_current_price');
        this.summary_values[3].number = this.number2string(
          Math.floor(this.dashboard_current_data.sum_current_price)
        );
        this.summary_values[3].percent = this.zenkaihi_percent(
          'sum_current_price',
          1
        );

        // うち最落越え
        this.summary_values[4].color = this.zenkaihi_color('sum_lowest_bid');
        this.summary_values[4].number = this.number2string(
          Math.floor(this.dashboard_current_data.sum_lowest_bid)
        );
        this.summary_values[4].percent = this.zenkaihi_percent(
          'sum_lowest_bid',
          1
        );

        // 総落札金額（円）
        this.summary_values[5].color = this.zenkaihi_color('sum_hummer_bid');
        this.summary_values[5].number = this.number2string(
          Math.floor(this.dashboard_current_data.sum_hummer_bid)
        );
        this.summary_values[5].percent = this.zenkaihi_percent(
          'sum_hummer_bid',
          1
        );

        // 入札数
        this.prepareDatasetsChart();

        // 入札数上位商品
        this.topBid = [];
        if (this.dashboard_current_data.bid_ranking !== null) {
          let sum_bid_count = 0;
          this.dashboard_current_data.bid_ranking.map(x => {
            sum_bid_count += x.bid_count;
            return x;
          });
          this.dashboard_current_data.bid_ranking.map(x => {
            this.topBid.push({
              text: this.getBidRankingText('', x.productName),
              userNumber: `${x.bid_count} BID (${this.round1((x.bid_count * 100) / sum_bid_count)}%)`, // "28 BID (40%)"
              precision: 1,
              value: Number(this.round1((x.bid_count * 100) / sum_bid_count)),
            });
            return x;
          });
        }

        // 会員別入札金額
        this.bidAmount = [];
        if (this.dashboard_current_data.bid_member_ranking !== null) {
          let sum_bid_price_count = 0;
          this.dashboard_current_data.bid_member_ranking.map(x => {
            sum_bid_price_count += x.sum_bid_price;
            return x;
          });
          this.dashboard_current_data.bid_member_ranking.map(x => {
            if (x.company_name || x.sum_bid_price) {
              this.bidAmount.push({
                text1: x.company_name,
                text2: `${this.number2string(x.sum_bid_price)}円`,
                value: Number(
                  this.round1((x.sum_bid_price * 100) / sum_bid_price_count)
                ),
              });
            }
            return x;
          });
        }

        // カテゴリ別入札金額割合
        this.defaultDatasetsDoughnutData = [];
        this.defaultDatasetsDoughnut = [
          {
            label: [],
            backgroundColor: [],
            data: [],
          },
        ];
        if (this.dashboard_current_data.category_bid_summary !== null) {
          let sum_bid_member_ranking = 0;
          this.dashboard_current_data.category_bid_summary.map(x => {
            sum_bid_member_ranking += x.bid_summary;
            return x;
          });

          this.dashboard_current_data.category_bid_summary.map((x, index) => {
            const tmp_color =
              x.category_color ||
              this.randomColor(
                this.dashboard_current_data.category_bid_summary.length,
                index
              );
            this.defaultDatasetsDoughnutData.push({
              label: x.category_name,
              backgroundColor: tmp_color,
              data: `${this.number2string(sum_bid_member_ranking === 0 ? 0 : Number(this.round1((x.bid_summary * 100) / sum_bid_member_ranking)))}%`,
            });

            this.defaultDatasetsDoughnut[0].label.push(x.category_name);
            this.defaultDatasetsDoughnut[0].backgroundColor.push(tmp_color);
            this.defaultDatasetsDoughnut[0].data.push(
              sum_bid_member_ranking === 0
                ? 0
                : Number(
                    this.round1((x.bid_summary * 100) / sum_bid_member_ranking)
                  )
            );

            return x;
          });
        }

        // Set y-axis max(入札数の最大値)
        const yAxesMax = this.dashboard_current_data.sum_bid_count;
        if (yAxesMax <= 10) {
          this.chartYAxesMax = 10;
        } else {
          // 10の倍数に切り上げ. ex) 12 -> 20
          this.chartYAxesMax = Math.ceil(yAxesMax / 10) * 10;
        }
      },
      prepareDatasetsChart() {
        this.defaultDatasetsChartLineLabel = [];
        const brandSuccess = getStyle('success2') || '#4dbd74';
        const brandInfo = getStyle('info') || '#20a8d8';
        const brandDanger = getStyle('danger') || '#f86c6b';

        const elements = this.dashboard_current_data.datetime_bid || [];
        const data1 = [];

        for (let i = 0; i < elements.length; i++) {
          data1.push(elements[i].bid_count);
          this.defaultDatasetsChartLineLabel.push(
            this.yearMonthDayFormat(elements[i].datetime)
          );
        }

        this.defaultDatasetsChartLine = [
          {
            label: '入札数',
            backgroundColor: hexToRgba(brandInfo, 10),
            borderColor: brandInfo,
            pointHoverBackgroundColor: brandInfo,
            borderWidth: 2,
            data: data1,
          },
        ];
      },
      // Value to string or -
      number2string(val) {
        if (typeof val === 'undefined' || val === null) {
          return '―';
        }
        return isNaN(val) ? String(val) : val.toLocaleString('ja-JP');
      },
      // Get random color
      randomColor(max, index) {
        const template = [
          '#F00000',
          '#ff9900',
          '#ffff00',
          '#008a00',
          '#0066cc',
          '#9933ff',
          '#66a3e0',
          '#888888',
          '#444444',
          '#b2b200',
          '#cce8cc',
          '#cce0f5',
        ];

        if (template.length > max) {
          return template[index];
        }

        // Separate color
        const letters = '0123456789ABCDEF';
        let color = '';
        const interval = (16 * 6) / max;

        while (color === '' || template.includes(color)) {
          color = '#';
          for (let i = 0; i < 6; i++) {
            const tmp = interval * index - (i + 1) * 16;
            const pos = tmp < 0 ? Math.floor(interval * index) - i * 16 : 15;
            console.log(`pos = ${pos}`);
            color += letters[pos > 0 ? pos : Math.floor(Math.random() * 16)];
          }
        }

        console.log(`interval = ${interval} | color(${index}) = ${color}`);
        return color;
      },
      round1(val) {
        const number = Number(val) || 0;
        return (Math.round(number * 100) / 100).toFixed(1);
      },
      zenkaihi_percent(column, wari) {
        if (this.dashboard_previous_data === null) {
          return '－';
        }
        if (this.dashboard_current_data === null) {
          return '－';
        }

        const wari_val = wari || 1;
        const val1 = this.dashboard_current_data[column] * wari_val;
        const val2 = this.dashboard_previous_data[column] * wari_val;

        if (val1 >= val2) {
          if (val2 === 0) {
            return '－';
          }
          return this.number2string(this.round1((val1 * 100) / val2 - 100));
        }
        if (val1 === 0) {
          return '－';
        }
        return this.number2string(this.round1((val2 * 100) / val1 - 100));
      },
      zenkaihi_color(column) {
        if (this.dashboard_previous_data === null) {
          return 'info';
        }
        if (this.dashboard_current_data === null) {
          return 'info';
        }

        const val1 = this.dashboard_current_data[column] || 0;
        const val2 = this.dashboard_previous_data[column] || 0;
        if (val1 >= val2) {
          return 'info';
        }
        return 'danger';
      },
      zenkaihi_percent_contract() {
        if (
          this.dashboard_previous_data === null ||
          this.dashboard_current_data === null
        ) {
          return '－';
        }
        if (
          this.dashboard_current_data.sum_exhibition_item_count === 0 ||
          this.dashboard_previous_data.sum_exhibition_item_count === 0
        ) {
          return '－';
        }

        const val1 =
          (this.dashboard_current_data.sum_contract_count * 100) /
          this.dashboard_current_data.sum_exhibition_item_count;
        const val2 =
          (this.dashboard_previous_data.sum_contract_count * 100) /
          this.dashboard_previous_data.sum_exhibition_item_count;

        if (val1 >= val2) {
          return this.number2string(this.round1(val1 - val2));
        }
        return this.number2string(this.round1(val2 - val1));
      },
      seiyaku_percent() {
        if (this.dashboard_current_data === null) {
          return '－';
        }
        if (this.dashboard_current_data.sum_exhibition_item_count === 0) {
          return '－';
        }
        return this.number2string(
          this.round1(
            (this.dashboard_current_data.sum_contract_count * 100) /
              this.dashboard_current_data.sum_exhibition_item_count
          )
        );
      },
      zenkaihi_color_contract() {
        if (
          this.dashboard_previous_data === null ||
          this.dashboard_current_data === null
        ) {
          return 'info';
        }
        if (
          this.dashboard_current_data.sum_exhibition_item_count === 0 ||
          this.dashboard_previous_data.sum_exhibition_item_count === 0
        ) {
          if (this.dashboard_previous_data.sum_exhibition_item_count === 0) {
            return 'info';
          }
          if (this.dashboard_previous_data.sum_contract_count === 0) {
            return 'info';
          }
          return 'danger';
        }

        const val1 =
          (this.dashboard_current_data.sum_contract_count * 100) /
          this.dashboard_current_data.sum_exhibition_item_count;
        const val2 =
          (this.dashboard_previous_data.sum_contract_count * 100) /
          this.dashboard_previous_data.sum_exhibition_item_count;

        if (val1 >= val2) {
          return 'info';
        }
        return 'danger';
      },
      monthDayFormat(val) {
        const tmpDate = new Date(val);
        return tmpDate ? moment(tmpDate).format('MM/DD HH:mm') : '';
      },
      yearMonthDayFormat(val) {
        const tmpDate = new Date(val);
        return tmpDate ? moment(tmpDate).format('YYYY/MM/DD HH:mm') : '';
      },
      getBidRankingText(lotNo, nickname) {
        let tmp = nickname;
        tmp = tmp.substring(0, 50);
        return tmp;
      },
    },
  };
</script>
<style type="text/css">
  .title {
    text-align: left;
    line-break: anywhere;
  }
</style>
<style type="text/css">
  @media (min-width: 576px) {
    .default_box {
      padding-right: 5px;
    }

    .default_title {
      padding: 0 0.25rem;
    }

    .exhibition_item_count_title {
      padding: 0px 0.25rem;
    }

    .exhibition_item_count_number {
      padding: 0px 0.4rem 0px 0.15rem;
    }

    .exhibition_item_count_box {
      padding-right: 5px;
    }

    .login_title {
      padding: 0px 0.25rem;
    }

    .login_number {
      padding: 0px 0.4rem 0px 0.15rem;
    }

    .login_box {
      padding-right: 5px;
      padding-left: 0px;
    }

    .bid_count_box {
      padding-right: 5px;
      padding-left: 0px;
    }

    .bid_count_number {
      padding: 0px 0.4rem 0px 0.15rem;
    }

    .current_price_title {
      padding: 0 0 0 0.15rem;
    }

    .current_price_box {
      padding-right: 5px;
      padding-left: 0px;
    }

    .current_price_number {
      padding: 0px 0.4rem 0px 0.15rem;
    }

    .lowest_title {
      padding: 0 0 0 0.15rem;
    }

    .lowest_box {
      padding-right: 5px;
      padding-left: 0px;
    }

    .lowest_number {
      padding: 0px 0.4rem 0px 0.15rem;
    }

    .hummer_bid_number {
      padding: 0px 0.35rem 0px 0.15rem;
    }

    .hummer_bid_box {
      padding-right: 5px;
      padding-left: 0px;
    }

    .text-title {
      font-size: 73% !important;
    }

    .text-price {
      font-size: 1.5rem !important;
    }

    .text-zenkaihi {
      font-size: 73% !important;
    }

    .exhibition_item_count_box {
      flex-basis: 16.66%;
      max-width: 16.66%;
    }

    .login_box {
      flex-basis: 16.66%;
      max-width: 16.66%;
    }

    .bid_count_box {
      flex-basis: 16.66%;
      max-width: 16.66%;
    }

    .current_price_box {
      flex-basis: 16.66%;
      max-width: 16.66%;
    }

    .lowest_box {
      flex-basis: 16.66%;
      max-width: 16.66%;
    }

    .hummer_bid_box {
      flex-basis: 16.66%;
      max-width: 16.66%;
    }
  }
</style>
