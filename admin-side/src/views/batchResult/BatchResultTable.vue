<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header" class="fl-r">
        <div style="float: left"><CIcon name="cil-grid" /> {{ caption }}</div>
        <div style="float: left; margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
      </slot>
      <span name="total-count" style="float: right; margin-left: 30px"
        >総件数: {{ total_count }}件</span
      >
      <span name="current-count" style="float: right"
        >検索結果: {{ current_count }}件</span
      >
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        style="font-size: 12px"
        :loading="loading"
        :items="items"
        :fields="fields"
        :position="position"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        :itemsPerPageSelect="itemsPerPageSelect"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #row_number="{item}"
          ><td class="text-right">
            <div :id="'item_' + item.row_number">{{ item.row_number }}</div>
          </td>
        </template>
        <template #execution_datetime="{item}">
          <td class="text-center" style="width: 15%">
            <div>{{ getFormatDateTime(item.execution_datetime) }}</div>
          </td>
        </template>
        <template #api_type="{item}">
          <td class="text-left" style="width: 20%">
            <div>{{ item.api_type === 'search' ? '商品情報取得' : '' }}</div>
          </td>
        </template>
        <template #all_data_count="{item}">
          <td class="text-right" style="width: 15%">
            <div>{{ item.all_data_count.toLocaleString() }}</div>
          </td>
        </template>
        <template #success_data_count="{item}">
          <td class="text-right" style="width: 15%">
            <div>{{ item.success_data_count.toLocaleString() }}</div>
          </td>
        </template>
        <template #error_data_count="{item}">
          <td class="text-right" style="width: 15%">
            <div>{{ item.error_data_count.toLocaleString() }}</div>
          </td>
        </template>
        <template #detail="{item}">
          <td class="text-center" style="width: 10%">
            <CButton
              v-if="item.error_data_count > 0"
              size="sm"
              block
              class="btn-edit"
              @click="detailDisplay(item)"
              >表示</CButton
            >
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
    </CCardBody>
  </CCard>
</template>

<script setup>
  import Methods from '@/api/methods';
  import {CDataTable, ScaleLoader} from '@/components/Table';
  import {itemsPerPageSelect} from '@/views/common/customTableView';
  import {defineEmits, defineProps, ref} from 'vue';
  import {useRouter} from 'vue-router';

  const emit = defineEmits([
    'get-position',
    'page-change',
    'sorter-change',
    'pagination-change',
  ]);

  const props = defineProps({
    items: Array,
    current_count: {
      type: String,
      default: '0',
    },
    total_count: {
      type: String,
      default: '0',
    },
    fields: {
      type: Array,
      default() {
        return [
          {key: 'row_number', label: 'No', _classes: 'text-center'},
          {
            key: 'execution_datetime',
            label: '実行日時',
            _classes: 'text-center',
          },
          {key: 'api_type', label: '処理種類', _classes: 'text-center'},
          {key: 'all_data_count', label: '全件数', _classes: 'text-center'},
          {
            key: 'success_data_count',
            label: '成功件数',
            _classes: 'text-center',
          },
          {
            key: 'error_data_count',
            label: 'エラー件数',
            _classes: 'text-center',
          },
          {key: 'detail', label: '詳細', _classes: 'text-center'},
        ];
      },
    },
    caption: {
      type: String,
      default: 'batchResultTable',
    },
    position: Number,
    loading: Boolean,
    activePage: Number,
    itemsPerPage: Number,
    pages: Number,
    itemsSorter: Object,
    roleId: Number,
  });

  const router = useRouter();

  const btn_clicked = ref(false);

  const detailDisplay = item => {
    if (btn_clicked.value) {
      return;
    }
    btn_clicked.value = true;
    emit('get-position', item.row_number);
    router.push({
      path: `/batchResult/${item.row_number}/detail`,
      query: {
        executionDateTime: item.execution_datetime,
        apiType: item.api_type,
      },
    });
  };

  const pageChange = val => {
    if (props.items.length > 0) {
      emit('page-change', val);
    }
  };
  const sorterChange = val => {
    emit('sorter-change', val);
  };
  const paginationChange = val => {
    emit('pagination-change', val);
  };
  const getFormatDateTime = datetime => {
    return Methods.getFormatDateTime(datetime);
  };
</script>
<style type="text/css">
  .btn-detail {
    width: 100%;
  }
</style>
