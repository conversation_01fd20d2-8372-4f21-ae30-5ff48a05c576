<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardBody>
        <CForm onsubmit="return false;">
          <CCard class="mb-3">
            <CCardHeader>
              <strong>エラー詳細情報</strong>
            </CCardHeader>
            <CCardBody>
              <CRow class="g-3 mb-3">
                <CCol md="2">
                  <label>実行日時</label>
                </CCol>
                <CCol md="9">
                  <div>
                    <a>{{ getFormatDateTime(this.executionDateTime) }}</a>
                  </div>
                </CCol>
              </CRow>
              <CRow class="g-3">
                <CCol md="2">
                  <label>処理種類</label>
                </CCol>
                <CCol md="9">
                  <div>
                    <a>{{ this.apiType === 'search' ? '商品情報取得' : '' }}</a>
                  </div>
                </CCol>
              </CRow>
            </CCardBody>
          </CCard>
          <CCard>
            <CCardHeader>
              <strong>検索条件</strong>
            </CCardHeader>
            <CCardBody>
              <CRow class="mb-2">
                <CCol sm="2">
                  <label>管理番号</label>
                </CCol>
                <CCol sm="2">
                  <CFormInput name="ubrandCode" v-model="ubrandCode" />
                </CCol>
              </CRow>
              <CRow class="mb-2">
                <CCol sm="2">
                  <label>商品ID</label>
                </CCol>
                <CCol sm="2">
                  <CFormInput
                    name="searchProductId"
                    v-model="searchProductId"
                  />
                </CCol>
              </CRow>
              <CRow class="mb-2">
                <CCol sm="2">
                  <label>エラーメッセージ</label>
                </CCol>
                <CCol sm="6">
                  <CFormInput
                    name="searchErrorMessage"
                    v-model="searchErrorMessage"
                  />
                </CCol>
              </CRow>
              <CRow>
                <CCol sm="5"></CCol>
                <CCol sm="2">
                  <CButton
                    size="sm"
                    color="info"
                    @click="searchErrorDetails"
                    block
                    :disabled="buttonLoading"
                    >検索</CButton
                  >
                </CCol>
                <CCol sm="5"></CCol>
              </CRow>
            </CCardBody>
          </CCard>
        </CForm>
      </CCardBody>
    </CCard>
    <CCard>
      <CCardHeader class="form-inline" style="display: block">
        <CIcon name="cil-grid" />ログ詳細一覧
        <span name="total-count" style="float: right; margin-left: 30px"
          >総件数: {{ totalCount }}件</span
        >
        <span name="current-count" style="float: right"
          >検索結果: {{ currentCount }}件</span
        >
      </CCardHeader>
      <CCardBody>
        <CDataTable
          hover
          striped
          border
          sorter
          :sorter-value="itemsSorter"
          :items="batchResultDetailList"
          :fields="fields"
          :items-per-page="30"
          :loading="loading"
          @update:sorter-value="sorterChange"
        >
          <template #ubrand_code="{item}">
            <td
              class="break-line"
              style="text-align: right; width: 8%; margin-right: 10px"
            >
              <div>{{ item.ubrand_code }}</div>
            </td>
          </template>
          <template #product_id="{item}">
            <td
              class="break-line"
              style="text-align: right; width: 8%; margin-right: 10px"
            >
              <div>{{ item.product_id }}</div>
            </td>
          </template>
          <template #hasRecommendation="{item}">
            <td class="break-line" style="text-align: center; width: 8%">
              <div>{{ item.hasRecommendation }}</div>
            </td>
          </template>
          <template #preview_start_dateTime="{item}">
            <td class="break-line" style="text-align: center; width: 10%">
              <div>{{ item.preview_start_dateTime || '' }}</div>
            </td>
          </template>
          <template #start_dateTime="{item}">
            <td class="break-line" style="text-align: center; width: 10%">
              <div>{{ item.start_datetime }}</div>
            </td>
          </template>
          <template #end_dateTime="{item}">
            <td class="break-line" style="text-align: center; width: 10%">
              <div>{{ item.end_datetime }}</div>
            </td>
          </template>
          <template #preview_end_dateTime="{item}">
            <td class="break-line" style="text-align: center; width: 10%">
              <div>{{ item.preview_end_datetime }}</div>
            </td>
          </template>
          <template #start_price="{item}">
            <td class="break-line" style="text-align: right; width: 10%">
              <div>{{ item.start_price }}</div>
            </td>
          </template>
          <template #lowest_bid_accept_price="{item}">
            <td class="break-line" style="text-align: right; width: 10%">
              <div>{{ item.lowest_bid_accept_price }}</div>
            </td>
          </template>
          <template #error_massage="{item}">
            <td class="break-line" style="text-align: left; width: 27%">
              <div v-for="message in item.error_message">
                {{ message }}
              </div>
            </td>
          </template>
        </CDataTable>
        <CRow class="bottom-menu sticky">
          <CCol sm="3">
            <CButton
              color="light"
              style="position: absolute; left: 0"
              @click="goBack"
            >
              結果一覧に戻る
            </CButton>
          </CCol>
          <CCol sm="9">
            <CButton style="disabled; cursor: default;"></CButton>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
    <CModal title="エラー確認" size="lg" v-model:show="errorModal">
      <div v-for="(text, i) in errorMsg" :key="i">{{ text }}</div>
      <template #footer>
        <CButton @click="(errorModal = false), (errorMsg = '')" color="dark"
          >OK</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {CDataTable} from '@/components/Table';
  import {useCommonStore} from '../../store/common';

  export default {
    name: 'BatchResultDetail',
    components: {CDataTable},
    setup() {
      const store = useCommonStore();
      return {
        store,
      };
    },
    data() {
      return {
        loading: true,
        buttonLoading: false,

        fields: [
          {key: 'ubrand_code', label: '管理番号', _classes: 'text-center'},
          {key: 'product_id', label: '商品ID', _classes: 'text-center'},
          // { key: "exhibition_name", label: "入札会名", _classes: "text-center" },
          {
            key: 'hasRecommendation',
            label: 'おすすめ有無',
            _classes: 'text-center',
          },
          {
            key: 'preview_start_datetime',
            label: '閲覧開始日時',
            _classes: 'text-center',
          },
          {key: 'start_datetime', label: '開始日時', _classes: 'text-center'},
          {key: 'end_datetime', label: '終了日時', _classes: 'text-center'},
          {
            key: 'preview_end_datetime',
            label: '閲覧終了日時',
            _classes: 'text-center',
          },
          {key: 'start_price', label: '開始価格', _classes: 'text-center'},
          {
            key: 'lowest_bid_accept_price',
            label: '最低落札価格',
            _classes: 'text-center',
          },
          {
            key: 'error_massage',
            label: 'エラーメッセージ',
            _classes: 'text-center',
          },
        ],

        // Title
        executionDateTime: '',
        apiType: '',

        // List
        batchResultDetailList: [],
        saveBatchResultDetailList: [],

        // Search
        ubrandCode: '',
        searchProductId: '',
        searchErrorMessage: '',

        itemsSorter: {asc: true, column: 'product_id'},
        errorModal: false,

        errorMsg: [],

        // Counting
        current_count: 0,
        total_count: 0,
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;
        vm.store.set(['itemsSorter', {asc: true, column: 'product_id'}]);
      });
    },
    mounted() {
      this.getErrorDetails();
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
    },
    methods: {
      async getErrorDetails() {
        this.loading = true;
        this.buttonLoading = true;
        await this.getBatchResultDetail()
          .then(response => {
            const errorInfo = this.$route.query;

            this.current_count = 0;
            this.total_count = 0;

            this.executionDateTime = errorInfo.executionDateTime;
            this.apiType = errorInfo.apiType;

            this.batchResultDetailList = [];

            this.batchResultDetailList = [
              ...this.batchResultDetailList,
              ...response,
            ];

            this.batchResultDetailList.forEach(
              detail => (detail.product_id = parseInt(detail.product_id))
            );
            this.saveBatchResultDetailList = this.batchResultDetailList;

            this.total_count = this.batchResultDetailList.length;
            this.current_count = this.batchResultDetailList.length;

            this.loading = false;
            this.buttonLoading = false;
          })
          .catch(error => {
            console.log('error', error);
          });
      },
      getBatchResultDetail() {
        const errorInfo = this.$route.query;
        const searchParams = {
          execution_datetime: this.getFormatDateTime(
            errorInfo.executionDateTime
          ),
        };
        // Console.log(`searchParams: ${JSON.stringify(searchParams)}`)
        return Methods.apiExecute('get-batch-result-detail', searchParams).then(
          response => {
            if (response.status === 200) {
              const batchResultDetail = response.data[0].error_details_field;

              // Console.log('batchResultDetail: ', batchResultDetail)

              if (
                typeof batchResultDetail === 'undefined' ||
                batchResultDetail === null
              ) {
                return Promise.resolve(null);
              } else {
                return Promise.resolve(batchResultDetail);
              }
            }
            return Promise.resolve(null);
          }
        );
      },
      searchErrorDetails() {
        this.loading = true;
        this.buttonLoading = true;

        const searchKeys = {
          ubrandCode: this.ubrandCode,
          productId: this.searchProductId,
          errorMessage: this.searchErrorMessage,
        };

        const details = this.saveBatchResultDetailList;

        const searchData = details.filter(detail => {
          // If no search key is entered, all data is displayed
          if (
            searchKeys.productId === '' &&
            searchKeys.errorMessage === '' &&
            searchKeys.ubrandCode === ''
          ) {
            return true;
          }

          // Ubrand code search
          let ubrandCodeSearchFlag = false;
          if (searchKeys.ubrandCode === '') {
            ubrandCodeSearchFlag = true;
          } else if (
            detail.ubrand_code &&
            detail.ubrand_code.toString().includes(searchKeys.ubrandCode)
          ) {
            // 名前を含む曖昧検索
            ubrandCodeSearchFlag = true;
          }

          let productIdSearchFlag = false;
          // 商品コード検索
          if (searchKeys.productId === '') {
            productIdSearchFlag = true;
          } else if (
            detail.product_id.toString().includes(searchKeys.productId)
          ) {
            // 名前を含む曖昧検索
            productIdSearchFlag = true;
          }

          // エラーメッセージ検索
          let errorMessageSearchFlag = false;
          if (searchKeys.errorMessage === '') {
            errorMessageSearchFlag = true;
          } else if (
            detail.error_message.some(message => {
              return message
                .toLowerCase()
                .includes(searchKeys.errorMessage.toLowerCase());
            })
          ) {
            errorMessageSearchFlag = true;
          }
          return (
            ubrandCodeSearchFlag &&
            productIdSearchFlag &&
            errorMessageSearchFlag
          );
        });

        this.batchResultDetailList = searchData;
        this.current_count = this.batchResultDetailList.length;

        this.loading = false;
        this.buttonLoading = false;
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
      },
      getFormatDateTime(datetime) {
        return Methods.getFormatDateTime(datetime);
      },
      goBack() {
        this.$router.push({name: '結果一覧', params: {back_flag: true}});
      },
    },
  };
</script>
<style lang="scss">
  .break-line {
    text-align: left;
    white-space: break-spaces;
  }

  .bottom-menu {
    background: #fff;
    padding-top: 5px;
    padding-bottom: 5px;
    margin-right: 0;
    margin-left: 0;
  }

  .sticky {
    position: sticky;
    bottom: 0;
  }
</style>
