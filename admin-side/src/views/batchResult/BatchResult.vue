<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardHeader>
        <strong>検索条件</strong>
      </CCardHeader>
      <CCardBody>
        <CForm onsubmit="return false;">
          <CRow class="form-group mb-3">
            <CCol sm="2">
              <label>管理番号</label>
            </CCol>
            <CCol sm="2" class="pl-0 form-inline">
              <CFormInput
                name="ubrandCode"
                v-model="search_condition.ubrandCode"
              />
            </CCol>
          </CRow>
          <CRow class="form-group mb-3">
            <CCol sm="2">
              <label>実行日時</label>
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                :ref="'execution_datetime_date_from'"
                horizontal
                v-model="search_condition.execution_datetime_date_from"
              />
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="time"
                :ref="'execution_datetime_time_from'"
                horizontal
                v-model="search_condition.execution_datetime_time_from"
                class="ml-2"
              />
            </CCol>
            <CCol sm="auto text-center align-self-center">
              <label class="mx-2">〜</label>
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="date"
                :ref="'execution_datetime_date_to'"
                horizontal
                v-model="search_condition.execution_datetime_date_to"
              />
            </CCol>
            <CCol sm="auto">
              <CFormInput
                type="time"
                :ref="'execution_datetime_time_to'"
                horizontal
                v-model="search_condition.execution_datetime_time_to"
                class="ml-2"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2">
              <label>エラー有無</label>
            </CCol>
            <CCol sm="9" class="pl-0 pt-2">
              <CFormCheck
                name="error_umu"
                type="radio"
                inline
                v-for="option in [
                  {value: '1', label: '指定なし'},
                  {value: '2', label: 'エラー有'},
                  {value: '3', label: 'エラー無'},
                ]"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :custom="true"
                v-model="search_condition.error_exists"
              />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CRow class="align-items-center">
          <CCol sm="5" class="mb-3 mb-xl-0 text-right" />
          <CCol sm="2" class="mb-3 mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="search" block>検索</CButton>
          </CCol>
          <CCol sm="5" class="mb-3 mb-xl-0 text-right" />
        </CRow>
      </CCardFooter>
    </CCard>
    <CRow>
      <CCol sm="12">
        <CTableWrapper
          name="batchResultList"
          :items="this.batchResultList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :position="position"
          :itemsSorter="itemsSorter"
          :roleId="roleId"
          caption="ログ一覧"
          @page-change="pageChange"
          @get-position="getPosition"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
        />
      </CCol>
    </CRow>
    <CModal title="エラー確認" v-model:show="errorModal">
      <div v-for="text in errorMsg" :key="text">{{ text }}</div>
      <template #footer>
        <CButton
          @click="(errorModal = false), (errorMsg = ''), (errorStatus = false)"
          color="dark"
          >OK</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '../../store/common';
  import CTableWrapper from './BatchResultTable.vue';
  import {useAuthStore} from '@/store/auth';

  export default {
    name: 'BatchResultList',
    props: {
      back_flag: {
        type: Boolean,
        require: false,
      },
    },
    setup() {
      const store = useCommonStore();

      return {
        store,
      };
    },
    components: {
      CTableWrapper,
    },
    data() {
      return {
        authStore: useAuthStore(),
        loading: true,
        errorModal: false,
        // Screen params
        batchResultList: [],
        // Search params
        search_condition: {
          ubrandCode: null,
          execution_datetime_date_from: null,
          execution_datetime_time_from: null,
          execution_datetime_date_to: null,
          execution_datetime_time_to: null,
          error_exists: '1',
        },
        activePage: 1,
        itemsPerPage: 10,
        pages: 1,
        itemsSorter: {asc: false, column: 'execution_datetime'},
        errorMsg: [],
        position: 0,
        backFlag: false,
        roleId: Number(this.authStore.user.role_id),

        // Counting
        current_count: 0,
        total_count: 0,
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;
        // 詳細画面からの遷移時のみ検索条件を取得
        if (vm.prevRoute.name === 'バッチ結果詳細') {
          vm.search_condition = vm.store.batchResultSearchCondition;
          if (vm.prevRoute.name === 'バッチ結果詳細') {
            vm.position = vm.store.position;
          }
        } else {
          // それ以外は初期化
          vm.store.set([
            'batchResultSearchCondition',
            {
              ubrandCode: null,
              execution_datetime_date_from: null,
              execution_datetime_time_from: null,
              execution_datetime_data_to: null,
              execution_datetime_time_to: null,
              error_exists: '1',
            },
          ]);
          vm.store.set(['activePage', 1]);
          vm.store.set(['position', 0]);
          vm.store.set(['itemsPerPage', 10]);
          vm.store.set([
            'itemsSorter',
            {asc: false, column: 'execution_datetime'},
          ]);
        }
      });
    },
    mounted() {
      this.backFlag = this.back_flag;
      this.getBatchResult()
        .then(batchResults => {
          this.batchResultList = batchResults;
          // 詳細画面からの遷移時は閲覧してたページに戻す
          if (this.prevRoute && this.prevRoute.name === 'バッチ結果詳細') {
            if (
              this.backFlag &&
              this.prevRoute &&
              this.prevRoute.name === 'バッチ結果詳細'
            ) {
              this.position = this.store.position;
            }
            this.itemsPerPage = this.store.itemsPerPage;
            this.itemsSorter = this.store.itemsSorter;
          }
          this.pages =
            parseInt(this.batchResultList.length / this.itemsPerPage, 10) +
            (this.batchResultList.length % this.itemsPerPage > 0 ? 1 : 0);
          this.activePage =
            this.store.activePage > this.pages
              ? Number(this.pages)
              : this.store.activePage;
          this.$router.push({query: {page: this.activePage}}).catch(() => {});
        })
        .catch(error => {
          console.log(error);
          this.loading = false;
          this.errorModal = true;
          this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
        });

      const self = this;
      setTimeout(() => {
        if (
          self.position &&
          document.getElementById(`item_${self.position}`) &&
          self.backFlag
        ) {
          document.getElementById(`item_${self.position}`).scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center',
          });
        }
      }, 1000);
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
    },
    watch: {
      $route: {
        immediate: true,
        handler(route) {
          if (route.query && route.query.page) {
            this.activePage = Number(route.query.page);
          }
        },
      },
      search_condition: {
        handler(newVal) {
          this.store.set(['batchResultSearchCondition', newVal]);
        },
        deep: true,
        immediate: false,
      },
      position: {
        handler(newVal) {
          this.store.set(['position', newVal]);
        },
        deep: true,
        immediate: false,
      },
      itemsPerPage(newVal) {
        if (this.batchResultList.length > newVal) {
          this.pages =
            parseInt(this.batchResultList.length / newVal, 10) +
            (this.batchResultList.length % newVal > 0 ? 1 : 0);
        } else {
          this.pages = 1;
        }
      },
    },
    methods: {
      search() {
        this.getBatchResult()
          .then(batchResults => {
            if (this.errorMsg.length === 0) {
              this.batchResultList = batchResults;

              this.pages =
                parseInt(this.batchResultList.length / this.itemsPerPage, 10) +
                (this.batchResultList.length % this.itemsPerPage > 0 ? 1 : 0);
              this.sorterChange({asc: false, column: 'execution_datetime'});
            } else {
              this.loading = false;
            }
          })
          .catch(error => {
            console.log(error);
            this.loading = false;
            this.errorModal = true;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      pageChange(val) {
        this.store.set(['activePage', val]);
        this.$router.push({query: {page: val}}).catch(() => {});
      },
      paginationChange(val) {
        this.itemsPerPage = val;
        this.store.set(['itemsPerPage', val]);
      },
      getPosition(val) {
        this.position = parseInt(val, 10);
        this.store.set(['position', this.position]);
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
        this.pageChange(1);
      },
      getBatchResult() {
        this.loading = true;

        this.current_count = 0;
        this.total_count = 0;

        const formDateTimeIsNotNull =
          this.search_condition.execution_datetime_date_from !== null ||
          this.search_condition.execution_datetime_time_from !== null;

        const toDateTimeIsNotNull =
          this.search_condition.execution_datetime_date_to !== null ||
          this.search_condition.execution_datetime_time_to !== null;

        const fromDateTime = formDateTimeIsNotNull
          ? `${this.search_condition.execution_datetime_date_from} ${this.search_condition.execution_datetime_time_from}`
              .trim()
              .replace('null', '')
              .replace('undefined', '')
          : null;

        const toDateTime = toDateTimeIsNotNull
          ? `${this.search_condition.execution_datetime_date_to} ${this.search_condition.execution_datetime_time_to}`
              .trim()
              .replace('null', '')
              .replace('undefined', '')
          : null;

        const paramFromDateTime =
          fromDateTime === '' ? null : this.checkSearchDateTime(fromDateTime);

        const paramToDateTime =
          toDateTime === '' ? null : this.checkSearchDateTime(toDateTime);

        const paramErrorExists = this.search_condition.error_exists;

        const searchParams = {
          ubrand_code: this.search_condition.ubrandCode,
          execution_datetime_from: paramFromDateTime,
          execution_datetime_to: paramToDateTime,
          error_exists: paramErrorExists,
        };
        console.log(`searchParams: ${JSON.stringify(searchParams)}`);

        return Methods.apiExecute('get-batch-result-list', searchParams).then(
          response => {
            if (response.status === 200) {
              this.loading = false;

              this.total_count = response.data
                ? response.data.total_count || 0
                : 0;
              this.current_count = response.data
                ? response.data.current_count || 0
                : 0;

              const batchResult = response.data.data;

              console.log('batchResult: ', batchResult);

              if (typeof batchResult === 'undefined' || batchResult === null) {
                return Promise.resolve(null);
              } else {
                return Promise.resolve(batchResult);
              }
            }
            return Promise.resolve(null);
          }
        );
      },
      // 検索の日時フォーマット
      checkSearchDateTime(datetime) {
        if (datetime === null) {
          return null;
        }
        const parts = datetime.split(' ');

        if (parts.every(element => element === '')) {
          return null;
        }
        if (parts.every(element => element !== '')) {
          // 日付と時刻の両方が指定された場合
          return datetime;
        } else if (parts[0] === '') {
          // 時刻のみが指定された場合
          const now = new Date();
          const timePart = parts[1];
          return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now
            .getDate()
            .toString()
            .padStart(2, '0')} ${timePart}`;
        } else {
          // 日付のみが指定された場合
          return `${parts[0]} 00:00`;
        }
      },
    },
  };
</script>
