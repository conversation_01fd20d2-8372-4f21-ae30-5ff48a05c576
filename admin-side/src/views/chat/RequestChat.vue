<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>
          申請番号：<span class="pr-4">{{ requestData.manage_no }}</span>
          会員名：<span class="pr-4">{{ requestData.member_name }}</span>
          状態：{{ requestData.request_category_name }}依頼
        </strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <div style="margin: 0 30px">
            <div class="data-group" id="chat-area">
              <CRow v-for="(item, i) in chatData" :key="i">
                <template v-if="item.update_category_id === '1'">
                  <CCol sm="5">
                    <div class="chatText receiveChat">
                      <div class="mb-2">
                        {{ item.create_datetime }}　【{{
                          item.request_category_name
                        }}】
                      </div>
                      <div style="white-space: pre-line">
                        {{ item.message }}
                      </div>
                      <div v-if="item.checked_admin_no === null">
                        <CButton
                          class="checkedBtn"
                          size="sm"
                          color="primary btn-status"
                          block
                          @click="checked(item.request_message_no)"
                        >
                          確 認
                        </CButton>
                      </div>
                      <div v-else class="mt-2">
                        確認済：{{ item.checked_admin_name }}
                      </div>
                    </div>
                  </CCol>
                </template>
                <template v-else>
                  <CCol sm="7"></CCol>
                  <CCol sm="5">
                    <div class="chatText sendChat">
                      <div class="mb-2">
                        {{ item.create_datetime }}　【{{
                          item.request_category_name
                        }}】
                      </div>
                      <div style="white-space: pre-line">
                        {{ item.message }}
                      </div>
                      <div class="mt-2">担当：{{ item.create_admin_name }}</div>
                    </div>
                  </CCol>
                </template>
              </CRow>
            </div>
            <div class="bottom-menu sticky">
              <CRow class="mb-3">
                <CCol>
                  <div v-for="(text, i) in errorMsg" :key="i" class="text-red">
                    {{ text }}
                  </div>
                  <CFormTextarea
                    ref="chatInput"
                    class="form-group col-sm-12 mb-0 px-0-imp"
                    addInputClasses="fixed-size col-sm-12"
                    rows="6"
                    v-model="chatMessage"
                    @change="changeFlag = true"
                    placeholder="コメントを入力してください"
                  />
                </CCol>
              </CRow>
              <CRow>
                <CCol sm="2" class="d-grid">
                  <CButton color="light" @click="goBack"> 一覧に戻る </CButton>
                </CCol>
                <CCol sm="5" />
                <CCol sm="1" class="">
                  <span style="float: right" @click="getChatData">
                    <CIcon :height="30" name="cil-reload" class="reload-icon" />
                  </span>
                </CCol>
                <CCol sm="2" class="d-grid">
                  <CButton
                    size="lg"
                    color="dark"
                    class="btn-status"
                    block
                    @click="reset"
                  >
                    クリア
                  </CButton>
                </CCol>
                <CCol sm="2" class="d-grid">
                  <CButton
                    size="lg"
                    color="primary"
                    class="btn-status"
                    block
                    @click="regist"
                  >
                    投稿する
                  </CButton>
                </CCol>
              </CRow>
            </div>
          </div>
        </CForm>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
  import Methods from '@/api/methods';

  export default {
    name: 'chat',
    data() {
      return {
        detail_request_no: this.$route.params.id,
        manage_no: null,
        member_no: null,
        requestData: {},
        chatData: {},
        chatMessage: null,
        loading: false,
        errorMsg: [],
      };
    },
    mounted() {
      if (this.$route.params.id) {
        this.getRequestData()
          .then(() => {
            this.$nextTick(() => {
              // Document.getElementById(this.$refs.chatInput.safeId).focus()
            });
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      } else {
        this.goBack();
      }
    },
    methods: {
      getRequestData() {
        this.loading = true;
        const params = {
          item_request_no: this.detail_request_no,
        };
        return Methods.apiExecute('get-chat-request-detail', params).then(
          response => {
            if (
              response.status === 200 &&
              response.data &&
              response.data.length > 0
            ) {
              this.loading = false;
              this.requestData = response.data[0];
              this.manage_no = this.requestData.manage_no;
              this.member_no = this.requestData.member_no;
              return this.getChatData();
            }
            return Promise.resolve();
          }
        );
      },
      getChatData() {
        this.loading = true;
        const search_condition = {
          manage_no: this.manage_no,
          member_no: this.member_no,
        };
        return Methods.apiExecute('get-chat', search_condition).then(
          response => {
            this.loading = false;
            this.chatData =
              response.data && response.data.length > 0 ? response.data : {};

            this.$nextTick(() => {
              const childNodes =
                document.getElementById('chat-area').childNodes;
              if (childNodes && childNodes.length > 0) {
                // ChildNodes[childNodes.length - 1]?.scrollIntoView()
              }
            });
            return Promise.resolve();
          }
        );
      },
      regist() {
        if (this.chatMessage) {
          this.loading = true;
          const params = {
            manage_no: this.manage_no,
            member_no: this.member_no,
            chat_message: this.chatMessage,
          };
          Methods.apiExecute('regist-chat', params)
            .then(response => {
              if (response.status === 200) {
                this.chatMessage = null;
                this.errorMsg = [];
                this.loading = false;
                this.getChatData();
              }
            })
            .catch(error => {
              this.loading = false;
              this.errorMsg = Methods.parseHtmlResponseError(
                this.$router,
                error
              );
            });
        }
      },
      checked(request_message_no) {
        this.loading = true;
        const params = {
          request_message_no,
        };
        Methods.apiExecute('update-chat', params)
          .then(response => {
            if (response.status === 200) {
              this.loading = false;
              this.getChatData();
            }
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      reset() {
        this.chatMessage = null;
      },
      goBack() {
        this.$router.go(-1);
      },
    },
  };
</script>

<style type="text/css">
  .chatText {
    border: 1px solid;
    padding: 10px;
    margin: 5px 0;
  }

  .receiveChat {
    background: #e2f0d9;
  }

  .sendChat {
    background: #deebf7;
  }

  .btn-confirm {
    margin: 5px 10px;
  }

  .bottom-menu {
    background: #fff;
    padding-top: 5px;
    padding-bottom: 5px;
    margin-right: 0;
    margin-left: 0;
  }

  .sticky {
    position: sticky;
    bottom: 0;
  }

  .fixed-size {
    resize: none;
  }

  .checkedBtn {
    margin-top: 20px;
    width: 15%;
  }

  .data-group {
    border: solid 1px black;
    padding: 5px 20px;
    margin-bottom: 10px;
    overflow-y: auto;
    /* overflow-x: hidden; */
    height: 500px;
  }

  .text-red {
    color: red;
  }

  .reload-icon {
    color: #ffff;
    background-color: #02b092d4;
    border-radius: 50%;
    padding: 5px;
    height: 100%;
    cursor: pointer;
  }
</style>
