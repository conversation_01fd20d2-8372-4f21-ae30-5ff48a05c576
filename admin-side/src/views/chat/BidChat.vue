<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>
          管理番号：<span class="pr-4">{{ resultData.manage_no }}</span>
          会員名：<span class="pr-4">{{
            resultData.bid_success_member_name
          }}</span>
          出品区分：<span class="pr-4">{{ classification_name }}</span>
          配送方法：<span class="pr-4">{{ delivery_selected_name }}</span>
        </strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <div style="margin: 0 30px">
            <div class="data-group" id="chat-area">
              <CRow v-for="(item, i) in chatData" :key="i">
                <template v-if="item.update_category_id === '2'">
                  <CCol sm="5">
                    <div class="chatText receiveChat">
                      <div class="mb-2">{{ item.create_datetime }}</div>
                      <div style="white-space: pre-line">
                        {{ item.message }}
                      </div>
                      <div v-if="item.checked_admin_no === null">
                        <CButton
                          class="checkedBtn"
                          size="sm"
                          color="primary btn-status"
                          block
                          @click="checked(item.successfull_message_no)"
                        >
                          確 認
                        </CButton>
                      </div>
                      <div v-else class="mt-2">
                        確認済：{{ item.checked_admin_name }}
                      </div>
                    </div>
                  </CCol>
                </template>
                <template v-else>
                  <CCol sm="7"></CCol>
                  <CCol sm="5">
                    <div class="chatText sendChat">
                      <div class="mb-2">{{ item.create_datetime }}</div>
                      <div style="white-space: pre-line">
                        {{ item.message }}
                      </div>
                      <div class="mt-2">担当：{{ item.admin_name }}</div>
                    </div>
                  </CCol>
                </template>
              </CRow>
            </div>
            <div class="bottom-menu sticky">
              <CRow class="form-group">
                <CCol>
                  <div v-for="(text, i) in errorMsg" :key="i" class="text-red">
                    {{ text }}
                  </div>
                  <CFormTextarea
                    ref="chatInput"
                    class="form-group col-sm-12 mb-0 px-0-imp"
                    addInputClasses="fixed-size col-sm-12"
                    rows="6"
                    v-model="chatMessage"
                    @change="changeFlag = true"
                    placeholder="コメントを入力してください"
                  />
                </CCol>
              </CRow>
              <CRow>
                <CCol>
                  <CButton
                    class="mx-1 font-weight-bold"
                    color="light"
                    block
                    @click="goBack"
                  >
                    一覧に戻る
                  </CButton>
                </CCol>
                <CCol sm="7" />
                <CCol class="form-inline" style="display: block">
                  <span style="float: right" @click="getChatData">
                    <CIcon :height="30" name="cil-reload" class="reload-icon" />
                  </span>
                </CCol>
                <CCol>
                  <CButton
                    size="lg"
                    color="dark btn-status"
                    block
                    @click="reset"
                  >
                    クリア
                  </CButton>
                </CCol>
                <CCol>
                  <CButton
                    size="lg"
                    color="primary btn-status"
                    block
                    @click="regist"
                  >
                    投稿する
                  </CButton>
                </CCol>
              </CRow>
            </div>
          </div>
        </CForm>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
  import Methods from '@/api/methods';

  export default {
    name: 'chat',
    data() {
      return {
        exhibition_item_no: null,
        classification_name: null,
        delivery_selected_name: null,
        resultData: {},
        chatData: {},
        chatMessage: null,
        selectExhibitionMessageNo: null,
        loading: false,
        errorMsg: [],
      };
    },
    mounted() {
      if (this.$route.params.id) {
        this.getResultData()
          .then(() => {
            this.$nextTick(() => {
              document.getElementById(this.$refs.chatInput.safeId).focus();
            });
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      } else {
        this.goBack();
      }
    },
    methods: {
      getResultData() {
        this.loading = true;
        this.exhibition_item_no = this.$route.params.id;
        this.classification_name = this.$route.params.classification_name;
        this.delivery_selected_name = this.$route.params.delivery_selected_name;
        const params = {
          exhibition_item_no: this.exhibition_item_no,
        };
        return Methods.apiExecute('get-chat-bid-detail', params).then(
          response => {
            if (response.status === 200) {
              this.loading = false;
              this.resultData = response.data[0];
              return this.getChatData();
            }
            return Promise.resolve();
          }
        );
      },
      getChatData() {
        this.loading = true;
        const search_condition = {
          exhibition_item_no: this.exhibition_item_no,
        };
        return Methods.apiExecute('get-bid-chat', search_condition).then(
          response => {
            this.loading = false;
            this.chatData =
              response.data && response.data.length > 0 ? response.data : {};

            this.$nextTick(() => {
              const childNodes =
                document.getElementById('chat-area').childNodes;
              if (childNodes && childNodes.length > 0) {
                childNodes[childNodes.length - 1].scrollIntoView();
              }
            });
            return Promise.resolve();
          }
        );
      },
      regist() {
        if (this.chatMessage) {
          this.loading = true;
          const params = {
            exhibition_item_no: this.exhibition_item_no,
            chat_message: this.chatMessage,
          };
          Methods.apiExecute('regist-bid-chat', params)
            .then(response => {
              if (response.status === 200) {
                this.chatMessage = null;
                this.errorMsg = [];
                this.loading = false;
                this.getChatData();
              }
            })
            .catch(error => {
              this.loading = false;
              this.errorMsg = Methods.parseHtmlResponseError(
                this.$router,
                error
              );
            });
        }
      },
      checked(successfull_message_no) {
        this.loading = true;
        const params = {
          successfull_message_no,
        };
        Methods.apiExecute('update-bid-chat', params)
          .then(response => {
            if (response.status === 200) {
              this.loading = false;
              this.getChatData();
            }
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      reset() {
        this.chatMessage = null;
      },
      goBack() {
        this.$router.go(-1);
      },
    },
  };
</script>

<style type="text/css">
  .chatText {
    border: 1px solid;
    padding: 10px;
    margin: 5px 0;
  }

  .receiveChat {
    background: #e2f0d9;
  }

  .sendChat {
    background: #deebf7;
  }

  .btn-confirm {
    margin: 5px 10px;
  }

  .bottom-menu {
    background: #fff;
    padding-top: 5px;
    padding-bottom: 5px;
    margin-right: 0;
    margin-left: 0;
  }

  .sticky {
    position: sticky;
    bottom: 0;
  }

  .fixed-size {
    resize: none;
  }

  .checkedBtn {
    margin-top: 20px;
    width: 15%;
  }

  .data-group {
    border: solid 1px black;
    padding: 5px 20px;
    margin-bottom: 10px;
    overflow-y: auto;
    /* overflow-x: hidden; */
    height: 500px;
  }

  .text-red {
    color: red;
  }

  .reload-icon {
    color: #ffff;
    background-color: #02b092d4;
    border-radius: 50%;
    padding: 5px;
    height: 100%;
    cursor: pointer;
  }
</style>
