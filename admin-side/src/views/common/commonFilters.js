import moment from 'moment';
moment.locale('ja');

export const dateFormat = date => {
  if (!date) {
    return '';
  }
  let dateStr = date;
  if (!dateStr.includes('-')) {
    dateStr = `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`;
  }
  const isoYear = new Date(dateStr).toISOString();
  const year = moment(isoYear).format('YYYY/MM/DD');
  return year;
};

export const addComma = val1 => {
  if (typeof val1 === 'undefined' || val1 === null || val1 === '') {
    return '';
  }
  if (isNaN(val1)) {
    return val1;
  }
  return Number(val1).toLocaleString();
};

export const subComma = val1 => {
  if (typeof val1 === 'undefined' || val1 === null || val1 === '') {
    return '';
  }
  // This.requestResultData.assessment_amount = String(val1).replace(/,/g, '')
  return String(val1).replace(/,/g, '');
};
export const getFullImagePath = file_path => {
  if (!file_path) {
    return null;
  }
  return `${import.meta.env.VITE_API_ENDPOINT.replace('/api/', '')}/${encodeURIComponent(file_path)}`;
};
