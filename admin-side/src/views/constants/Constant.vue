<template>
  <div class="mb-3">
    <CRow>
      <CCol sm="9">
        <CCard>
          <CCardHeader>
            <div>
              <strong v-if="!selectStatus">定数編集</strong>
              <strong v-if="selectStatus">定数登録</strong>
            </div>
          </CCardHeader>
          <CCardBody>
            <CForm onsubmit="return false;">
              <CCard class="mb-3">
                <CCardBody>
                  <CRow class="g-3 constant-class mb-3">
                    <CCol md="3" class="d-flex align-items-center">
                      <label>定数キー</label>
                      <CBadge color="danger" class="ms-auto" v-if="!isReadOnly"
                        >必須</CBadge
                      >
                    </CCol>
                    <CCol md="9">
                      <CFormInput
                        horizontal
                        v-model="constantData.keyString"
                        :disabled="isReadOnly"
                      />
                    </CCol>
                  </CRow>
                  <CRow class="g-3 constant-class">
                    <CCol md="3" class="d-flex align-items-center">
                      <label>名前</label>
                      <CBadge color="danger" class="ms-auto" v-if="!isReadOnly"
                        >必須</CBadge
                      >
                    </CCol>
                    <CCol md="9">
                      <CFormInput
                        class="sm-12"
                        horizontal
                        v-model="constantData.valueName"
                        :disabled="isReadOnly"
                      />
                    </CCol>
                  </CRow>
                </CCardBody>
              </CCard>
              <template v-for="(value, index) in values" :key="index">
                <CCard class="mb-3">
                  <CCardHeader>
                    <strong v-if="values && values.length > 1"
                      >{{ value.language }}
                    </strong>
                  </CCardHeader>
                  <CCardBody>
                    <CRow class="g-3 mb-3">
                      <CCol sm="3" class="d-flex align-items-center">
                        <label>値1</label>
                        <CBadge
                          color="danger"
                          class="ms-auto"
                          v-if="!isReadOnly"
                          >必須</CBadge
                        >
                      </CCol>
                      <CCol sm="9" class="constant-class">
                        <CFormInput
                          horizontal
                          v-model="value.value1"
                          @change="changeFlag = true"
                          :disabled="isReadOnly"
                        />
                      </CCol>
                    </CRow>
                    <CRow class="g-3 mb-3">
                      <CCol sm="3">
                        <label>値2</label>
                      </CCol>
                      <CCol sm="9" class="constant-class">
                        <CFormInput
                          horizontal
                          v-model="value.value2"
                          @change="changeFlag = true"
                          :disabled="isReadOnly"
                        />
                      </CCol>
                    </CRow>
                    <CRow class="g-3 mb-3">
                      <CCol sm="3">
                        <label>値3</label>
                      </CCol>
                      <CCol sm="9" class="constant-class">
                        <CFormInput
                          horizontal
                          v-model="value.value3"
                          @change="changeFlag = true"
                          :disabled="isReadOnly"
                        />
                      </CCol>
                    </CRow>
                    <CRow class="g-3 mb-3">
                      <CCol sm="3">
                        <label>値4</label>
                      </CCol>
                      <CCol sm="9" class="constant-class">
                        <CFormTextarea
                          horizontal
                          rows="6"
                          v-model="value.value4"
                          @change="changeFlag = true"
                          :disabled="isReadOnly"
                        />
                      </CCol>
                    </CRow>
                    <CRow class="g-3 mb-3">
                      <CCol sm="3">
                        <label>値5</label>
                      </CCol>
                      <CCol sm="9" class="constant-class">
                        <CFormTextarea
                          horizontal
                          rows="6"
                          v-model="value.value5"
                          @change="changeFlag = true"
                          :disabled="isReadOnly"
                        />
                      </CCol>
                    </CRow>
                    <CRow class="constant-file-class mb-3">
                      <CCol sm="3"><span>ファイル</span></CCol>
                      <CCol sm="3">
                        <div>
                          <a
                            @click="openFile(value.fileUrl)"
                            href="javascript:void(0);"
                            :style="
                              value.fileUrl
                                ? ''
                                : 'color: inherit; text-decoration: inherit;'
                            "
                            >{{ value.webFileUrl }}</a
                          >
                        </div>
                        <!-- <label sm="12" horizontal></label> -->
                      </CCol>
                      <CCol sm="2">
                        <div class="d-grid gap-2">
                          <label
                            class="btn btn-secondary btn-block"
                            :class="{disabled: isReadOnly}"
                          >
                            参照...
                            <input
                              type="file"
                              @change="uploadFile(index)"
                              hidden
                            />
                          </label>
                        </div>
                      </CCol>
                      <CCol sm="2" class="d-grid">
                        <CButton
                          class="btn-bounce-email px-0"
                          :disabled="!value.fileUrl || buttonLoading"
                          @click="downloadFile(value.fileUrl)"
                          >ダウンロード</CButton
                        >
                      </CCol>
                      <CCol sm="2">
                        <div class="d-grid gap-2">
                          <CButton
                            :disabled="!value.fileUrl || buttonLoading"
                            @click="deleteFile(index)"
                            class="btn btn-danger btn-block"
                            >削除する</CButton
                          >
                        </div>
                      </CCol>
                    </CRow>
                  </CCardBody>
                </CCard>
              </template>
              <CCard class="mb-3">
                <CCardBody>
                  <CRow class="g-3 mb-3">
                    <CCol sm="3">
                      <label>定数キー内表示順</label>
                    </CCol>
                    <CCol sm="2" class="constant-class">
                      <CFormInput
                        horizontal
                        v-model="constantData.order"
                        :disabled="isReadOnly"
                      />
                    </CCol>
                  </CRow>
                  <CRow class="mb-3">
                    <CCol md="3"><label>適用　開始日時</label></CCol>
                    <CCol md="2">
                      <CFormInput
                        type="date"
                        :ref="'stDate'"
                        horizontal
                        v-model="constantData.startDate"
                        :invalid="!dateTimeValid.startDate"
                        @change="checkDateTimeValid('startDate', $event)"
                        :disabled="isReadOnly"
                      >
                      </CFormInput>
                    </CCol>
                    <CCol md="2">
                      <CFormInput
                        :ref="'stTime'"
                        type="time"
                        horizontal
                        v-model="constantData.startTime"
                        :invalid="!dateTimeValid.startTime"
                        @change="checkDateTimeValid('startTime', $event)"
                        :disabled="isReadOnly"
                      >
                      </CFormInput>
                    </CCol>
                  </CRow>
                  <CRow class="mb-3">
                    <CCol md="3"><label>適用　終了日時</label></CCol>
                    <CCol md="2">
                      <CFormInput
                        type="date"
                        :ref="'edDate'"
                        horizontal
                        v-model="constantData.endDate"
                        :invalid="!dateTimeValid.endDate"
                        @change="checkDateTimeValid('endDate', $event)"
                        :disabled="isReadOnly"
                      ></CFormInput>
                    </CCol>
                    <CCol md="2">
                      <CFormInput
                        type="time"
                        :ref="'edTime'"
                        horizontal
                        v-model="constantData.endTime"
                        :invalid="!dateTimeValid.endTime"
                        @change="checkDateTimeValid('endTime', $event)"
                        :disabled="isReadOnly"
                      ></CFormInput>
                    </CCol>
                  </CRow>
                  <CRow class="g-3">
                    <CCol sm="3">
                      <label>説明</label>
                    </CCol>
                    <CCol sm="9" class="constant-class">
                      <CFormTextarea
                        horizontal
                        rows="6"
                        v-model="constantData.description"
                        :disabled="isReadOnly"
                      />
                    </CCol>
                  </CRow>
                </CCardBody>
              </CCard>
              <CButton
                color="secondary"
                class="mx-1"
                @click="goBack"
                :disabled="buttonLoading"
                >登録を中止して一覧に戻る</CButton
              >
              <CButton
                color="primary"
                class="mx-1"
                :disabled="buttonLoading"
                @click="openEditModal"
                v-if="!isReadOnly"
              >
                <div v-if="selectStatus">登録する</div>
                <div v-if="!selectStatus">更新する</div>
              </CButton>
              <CButton
                color="danger"
                class="float-right"
                :disabled="buttonLoading"
                @click="deleteModal = true"
                v-if="!selectStatus && !isReadOnly"
              >
                削除</CButton
              >
            </CForm>
            <CElementCover v-if="buttonLoading" :opacity="0.8" />
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="registModal"
      @close="
        () => {
          registModal = false;
          errorMsg = '';
          errorStatus = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>登録確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!errorStatus">この内容で登録してもよろしいですか？</div>
        <div v-for="text in errorMsg" :key="text">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              registModal = false;
              errorMsg = '';
              errorStatus = false;
            }
          "
          :disabled="buttonLoading"
          color="dark"
        >
          <div v-if="errorStatus">OK</div>
          <div v-if="!errorStatus">キャンセル</div>
        </CButton>
        <CButton
          v-if="!errorStatus"
          @click="registConstant"
          :disabled="buttonLoading"
          :loading="buttonLoading"
          color="primary"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="deleteModal"
      @close="
        () => {
          deleteModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>この定数を削除してもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="deleteModal = false" color="dark">キャンセル</CButton>
        <CButton @click="deleteConstant" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader :closeButton="false">
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              compModal = false;
              goBack();
            }
          "
          color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
          btnClicked = false;
          next(false);
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{
          (selectStatus ? '登録' : '編集') + '中止確認'
        }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              cancelModal = false;
              btnClicked = false;
              next(false);
            }
          "
          color="dark"
          >キャンセル</CButton
        >
        <CButton
          @click="
            () => {
              cancelModal = false;
              next();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import UploadFile from '@/api/uploadFileToS3';
  import Base from '@/common/base';
  import useFileDownload from '@/common/useFileDownload';
  import {CElementCover} from '@/components/Table';
  import {
    CBadge,
    CButton,
    CCard,
    CCardBody,
    CCardHeader,
    CCol,
    CForm,
    CFormInput,
    CFormTextarea,
    CModal,
    CModalBody,
    CModalFooter,
    CModalHeader,
    CModalTitle,
    CRow,
  } from '@coreui/vue';
  import {useAuthStore} from '@/store/auth';

  export default {
    name: 'Constant',
    components: {
      CButton,
      CCard,
      CCardBody,
      CCardHeader,
      CCol,
      CElementCover,
      CForm,
      CFormInput,
      CModal,
      CRow,
      CFormTextarea,
      CBadge,
      CModalHeader,
      CModalTitle,
      CModalBody,
      CModalFooter,
    },
    setup() {
      const {download} = useFileDownload();
      const {isReadOnly} = useAuthStore();
      return {download, isReadOnly, authStore: useAuthStore()};
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.constantsOpened = from.fullPath.includes('constants');
      });
    },
    beforeRouteLeave(to, from, next) {
      if (this.changeFlag) {
        this.next = next;
        this.cancelModal = true;
      } else {
        // eslint-disable-next-line callback-return
        next();
      }
    },
    data() {
      return {
        registModal: false,
        deleteModal: false,
        selectStatus: false,
        buttonLoading: false,
        compModal: false,
        constantsOpened: null,
        constantData: {},
        values: [],
        startDateTime: '',
        endDateTime: '',
        adminNo: '',
        selectedFiles: '',
        checkStatus: false,
        errorMsg: '',
        errors: [],
        errorStatus: false,
        changeFlag: false,
        cancelModal: false,
        btnClicked: false,

        // Check valid date time
        dateTimeValid: {
          startDate: true,
          startTime: true,
          endDate: true,
          endTime: true,
        },
      };
    },
    mounted() {
      this.onMounted();
    },
    watch: {
      constantData: {
        handler(newVal, oldVal) {
          if (Object.keys(oldVal).length !== 0) {
            this.changeFlag = true;
          }
        },
        deep: true,
        immediate: false,
      },
    },
    methods: {
      async onMounted() {
        this.selectStatus = !this.$route.params.id;
        this.adminNo = this.authStore.user.admin_no;
        await this.getLanguages();
      },
      goBack() {
        if (this.btnClicked) {
          return;
        }
        this.btnClicked = true;
        if (this.constantsOpened) {
          this.$router.go(-1);
        } else {
          this.$router.push({path: '/constants'});
        }
      },
      uploadFile(index) {
        this.changeFlag = true;
        this.buttonLoading = true;
        this.values[index].selectedFiles = event.target.files;
        this.values[index].webFileUrl = event.target.files[0].name;
        UploadFile.upload(
          'constant',
          this.values[index].selectedFiles[0],
          data => {
            this.values[index].fileUrl = data.message;
            this.buttonLoading = false;
          }
        );
      },
      deleteFile(index) {
        this.changeFlag = true;
        this.values[index].selectedFiles = '';
        this.values[index].webFileUrl = '';
        this.values[index].fileUrl = '';
      },
      getUrl(url) {
        return url ? url.split('/').pop() : '';
      },
      openFile(keyUrl) {
        const request = {
          key: keyUrl,
        };
        Methods.apiExecute('download-file', request)
          .then(response => {
            if (response.status === 200) {
              window.open(response.data, '_blank');
            }
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      downloadFile(keyUrl) {
        const request = {
          key: keyUrl,
        };
        Methods.apiExecute('download-file', request)
          .then(response => {
            if (response.status === 200) {
              this.download(response.data, Base.getFileName(keyUrl));
            }
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      async registConstant() {
        this.buttonLoading = true;
        const chilConstant = [];
        this.values.map(constant => {
          chilConstant.push({
            constantLocalizedNo: constant.constant_localized_no,
            languageCode: constant.languageCode,
            language: this.values.length > 1 ? constant.language : null,
            value1: constant.value1,
            value2: constant.value2,
            value3: constant.value3,
            value4: constant.value4,
            value5: constant.value5,
            fileUrl: constant.fileUrl,
            adminNo: this.adminNo,
          });
        });
        const constantReq = {
          constantNo: this.$route.params.id,
          keyString: this.constantData.keyString,
          valueName: this.constantData.valueName,
          sortOrder: this.constantData.order,
          description: this.constantData.description,
          startDate: this.constantData.startDate,
          startTime: this.constantData.startTime,
          endDate: this.constantData.endDate,
          endTime: this.constantData.endTime,
          startDateTimeFlag:
            this.dateTimeValid.startDate && this.dateTimeValid.startTime,
          endDateTimeFlag:
            this.dateTimeValid.endDate && this.dateTimeValid.endTime,
          adminNo: this.adminNo,
          chilConstants: chilConstant,
        };
        await Methods.apiExecute('regist-constants-language-list', constantReq)
          .then(response => {
            if (response.status === 200) {
              this.buttonLoading = false;
              this.registModal = false;
              this.compModal = true;
              this.changeFlag = false;
            }
          })
          .catch(error => {
            this.buttonLoading = false;
            this.errorStatus = true;
            this.registModal = true;
            this.compModal = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      deleteConstant() {
        const request = {
          constantNo: this.$route.params.id,
        };
        Methods.apiExecute('delete-constant', request)
          .then(response => {
            if (response.status === 200) {
              this.deleteModal = false;
            }
          })
          .catch(error => {
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
        this.compModal = true;
        this.deleteModal = false;
        this.changeFlag = false;
      },
      getLanguages() {
        this.buttonLoading = true;
        this.values = [];
        const request = {
          constantNo: this.$route.params.id,
        };
        return Methods.apiExecute('get-constants-language-list', request)
          .then(response => {
            this.values = [];
            if (response.status === 200) {
              response.data.map(constant => {
                this.startDateTime = constant.out_start_datetime
                  ? constant.out_start_datetime
                  : this.startDateTime;
                this.endDateTime = constant.out_end_datetime
                  ? constant.out_end_datetime
                  : this.endDateTime;
                this.constantData = {
                  keyString: constant.out_key_string
                    ? constant.out_key_string
                    : '',
                  valueName: constant.out_value_name
                    ? constant.out_value_name
                    : '',
                  order: constant.out_sort_order ? constant.out_sort_order : 0,
                  description: constant.out_description
                    ? constant.out_description
                    : '',
                  startDate: Methods.getFormatDate(this.startDateTime),
                  startTime: Methods.getFormatTime(this.startDateTime),
                  endDate: Methods.getFormatDate(this.endDateTime),
                  endTime: Methods.getFormatTime(this.endDateTime),
                };
                this.values.push({
                  language: constant.out_language_value2,
                  constant_localized_no: constant.out_constant_localized_no,
                  constant_no: constant.out_constant_no,
                  languageCode: constant.out_language_value1,
                  value1: constant.out_value1,
                  value2: constant.out_value2,
                  value3: constant.out_value3,
                  value4: constant.out_value4,
                  value5: constant.out_value5,
                  webFileUrl: this.getUrl(constant.out_file_url),
                  fileUrl: constant.out_file_url,
                });
              });
            }
            this.buttonLoading = false;
            return Promise.resolve();
          })
          .catch(error => {
            this.buttonLoading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
            return Promise.resolve();
          });
      },
      openEditModal() {
        this.registModal = true;
      },
      checkDateTimeValid(type, e) {
        this.dateTimeValid[type] = !!e.target?.validity?.valid;
      },
    },
  };
</script>

<style lang="scss">
  .constant-class {
    .col-sm-9 {
      width: 100% !important;
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  .constant-file-class {
    .col-sm-9 {
      width: 100% !important;
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  .view-only-form {
    background-color: #f8f9fa !important;
    pointer-events: none;
  }

  .view-only-form input,
  .view-only-form textarea,
  .view-only-form select {
    background-color: #e9ecef !important;
    color: #6c757d !important;
  }
</style>
