<template>
  <CRow class="mb-3">
    <CCol col="12" xl="12">
      <CCard>
        <CCardHeader>
          <strong>検索条件</strong>
        </CCardHeader>
        <CCardBody>
          <CForm onsubmit="return false;">
            <CRow class="mb-3">
              <CCol sm="2"> 定数キー </CCol>
              <CCol sm="6">
                <CFormSelect
                  name="country"
                  :options="options_constantKey"
                  v-model="search_condition.selectedConstantKey"
                />
              </CCol>
            </CRow>
          </CForm>
        </CCardBody>
        <CCardFooter>
          <CRow class="align-items-center">
            <CCol sm="5"></CCol>
            <CCol sm="2">
              <div class="d-grid gap-2">
                <CButton size="sm" color="info" @click="getConstantsData" block
                  >検索
                </CButton>
              </div>
            </CCol>
          </CRow>
        </CCardFooter>
      </CCard>
      <CCard class="mt-3">
        <CCardHeader class="form-inline" style="display: block">
          <CIcon name="cil-grid" />定数一覧
          <div style="float: right; margin-left: 30px">
            <CButton
              size="sm"
              color="primary"
              @click="addNewConstant"
              style="width: 160px; margin-left: auto"
              :disabled="isReadOnly"
              >新規登録</CButton
            >
          </div>
          <span style="float: right; margin-left: 30px"
            >総件数: {{ totalCount }}件</span
          >
          <span style="float: right">検索結果: {{ currentCount }}件</span>
        </CCardHeader>
        <CCardBody>
          <CDataTable
            hover
            striped
            border
            sorter
            :sorter-value="itemsSorter"
            :loading="loading"
            :items="constantsData"
            :fields="fields"
            :items-per-page="30"
            @update:sorter-value="sorterChange"
          >
            <template #constantKey="{item}">
              <td style="text-align: left">
                {{ item.constantKey }}
              </td>
            </template>
            <template #name="{item}">
              <td style="text-align: left">{{ item.name }}</td>
            </template>
            <template #order="{item}">
              <td style="text-align: right">{{ item.order }}</td>
            </template>
            <template #change="{item}">
              <td>
                <CButton
                  size="sm"
                  block
                  color="info"
                  @click="editConstant(item.constantNo)"
                  >{{ isReadOnly ? '詳細' : '編集' }}</CButton
                >
              </td>
            </template>
            <template #delete="{item}">
              <td>
                <CButton
                  size="sm"
                  block
                  color="danger"
                  @click="
                    deleteModal = true;
                    targetDeleteConstantId = item.constantNo;
                  "
                  :disabled="isReadOnly"
                  >削除</CButton
                >
              </td>
            </template>
          </CDataTable>
        </CCardBody>
      </CCard>
    </CCol>

    <CModal backdrop="static" :keyboard="false" :visible="deleteModal">
      <CModalHeader :closeButton="false">
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>この定数を削除してもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              deleteModal = false;
            }
          "
          color="dark"
          >キャンセル</CButton
        >
        <CButton @click="deleteConstant" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>
  </CRow>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {CDataTable} from '@/components/Table';
  import {useCommonStore} from '@/store/common';
  import {useAuthStore} from '@/store/auth';
  import {CIcon} from '@coreui/icons-vue';

  const fields = [
    {key: 'constantKey', label: '定数キー', _classes: 'text-center'},
    {key: 'name', label: '名前', _classes: 'text-center'},
    {key: 'order', label: '表示順', _classes: 'text-center'},
    {key: 'start', label: '適用開始日時', _classes: 'text-center'},
    {key: 'end', label: '適用終了日時', _classes: 'text-center'},
    {key: 'change', label: '編集/詳細', _classes: 'text-center'},
    {key: 'delete', label: '削除', _classes: 'text-center'},
  ];

  export default {
    name: 'Constants',
    components: {
      /*
       * CButton,
       * CCard,
       * CCardBody,
       * CCardFooter,
       * CCardHeader,
       * CCol,
       */
      CDataTable,
      /*
       * CForm,
       * CModal,
       * CRow,
       * CFormSelect,
       */
      CIcon,
    },
    setup() {
      const store = useCommonStore();
      const {isReadOnly} = useAuthStore();
      return {store, isReadOnly};
    },
    data() {
      return {
        deleteModal: false,
        constantsData: [],
        fields,
        targetDeleteConstantId: '',
        loading: true,
        itemsSorter: {asc: true, column: 'constantKey'},
        options_constantKey: [],
        search_condition: {
          selectedConstantKey: '',
        },

        // Counting
        current_count: 0,
        total_count: 0,
        btn_clicked: false,
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;
        // 登録・編集画面からの遷移時のみ検索条件を保持する
        if (
          vm.prevRoute.name === '定数編集' ||
          vm.prevRoute.name === '定数登録'
        ) {
          vm.search_condition = vm.store.constantSearchCondition;
        } else {
          // それ以外は初期化
          vm.store.set(['itemsSorter', {asc: true, column: 'constantKey'}]);
          vm.store.set(['constantSearchCondition', {selectedConstantKey: ''}]);
        }
      });
    },
    mounted() {
      this.getConstantKey()
        .then(() => {
          this.getConstantsData();
        })
        .catch(error => {
          this.loading = false;
          this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
        });
    },
    watch: {
      search_condition: {
        handler(newVal) {
          this.store.set(['constantSearchCondition', newVal]);
        },
        deep: true,
        immediate: false,
      },
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
    },
    methods: {
      getConstantKey() {
        this.loading = true;
        const request = {};
        return Methods.apiExecute('get-constant-key', request).then(
          response => {
            if (response.status === 200) {
              this.options_constantKey = [];
              this.options_constantKey.push({label: '', value: null});
              response.data.map(constant => {
                this.options_constantKey.push({
                  label: constant.key_string,
                  value: constant.key_string,
                });
              });

              // 登録・編集画面からの遷移時は閲覧してたページに戻す
              if (
                this.prevRoute &&
                (this.prevRoute.name === '定数編集' ||
                  this.prevRoute.name === '定数登録')
              ) {
                this.itemsSorter = this.store.itemsSorter;
              }
            }
            return Promise.resolve();
          }
        );
      },
      async getConstantsData() {
        this.loading = true;

        this.current_count = 0;
        this.total_count = 0;

        const request = {
          keyString: this.search_condition.selectedConstantKey,
          menteFlag: true,
        };
        await Methods.apiExecute('get-constant-list', request)
          .then(response => {
            if (response.status === 200) {
              this.total_count = response.data
                ? response.data.total_count || 0
                : 0;
              this.current_count = response.data
                ? response.data.current_count || 0
                : 0;

              this.constantsData = [];
              response.data.data.map(constant => {
                this.constantsData.push({
                  constantNo: constant.constant_no,
                  constantKey: constant.key_string,
                  name: constant.value_name,
                  order: constant.sort_order,
                  start: Methods.getFormatDateTime(
                    constant.start_datetime ? constant.start_datetime : ''
                  ),
                  end: Methods.getFormatDateTime(
                    constant.end_datetime ? constant.end_datetime : ''
                  ),
                });
              });

              // 登録・編集画面からの遷移時は閲覧してたページに戻す
              if (
                this.prevRoute &&
                (this.prevRoute.name === '定数編集' ||
                  this.prevRoute.name === '定数登録')
              ) {
                this.itemsSorter = this.store.itemsSorter;
              }

              this.loading = false;
            }
            console.log('this.constantsData:', this.constantsData);
            return Promise.resolve();
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
            return Promise.resolve();
          });
      },
      deleteConstant() {
        const request = {
          constantNo: this.targetDeleteConstantId,
        };
        Methods.apiExecute('delete-constant', request)
          .then(response => {
            if (response.status === 200) {
              this.loading = false;
              this.getConstantsData();
              this.deleteModal = false;
            }
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
      },
      addNewConstant() {
        console.log('addNewConstant');
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.$router.push('/constant/regist');
      },
      editConstant(constantNo) {
        console.log('editConstant');
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.$router.push(`/constant/${constantNo}`);
      },
    },
  };
</script>
