<template>
  <div
    style="outline: none; width: 100%"
    @drag="preventEvent"
    @dragstart="preventEvent"
    @dragend="preventEvent"
    @dragover="preventEvent"
    @dragenter="preventEvent"
    @dragleave="preventEvent"
    @drop="preventEvent"
  >
    <div
      class="image-container position-relative text-center"
      v-if="!images.length"
    >
      <div
        class="drag-upload-cover position-absolute"
        v-if="isDragover"
        @drop="onDrop"
      >
        <div class="centered full-width text-center text-primary">
          <svg
            class="icon-drag-drop"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path
              d="M444.5 15C407.7 15 378 44.8 378 81.5s29.8 66.5 66.5 66.5S511 118.2 511 81.5 481.2 15 444.5 15zm29.4 72.4h-23.5l.1 25.9c0 3.2-2.6 5.8-5.8 5.9-3.2 0-5.8-2.6-5.8-5.8l-.1-26h-23.6c-3.2 0-5.8-2.6-5.8-5.8s2.6-5.8 5.8-5.8h23.5l-.1-25.9c0-3.2 2.6-5.8 5.8-5.9 3.2 0 5.8 2.6 5.8 5.8l.1 26h23.6c3.3 0 5.8 2.6 5.8 5.8s-2.6 5.8-5.8 5.8zM199.3 191.3c21.5 0 38.9 17.6 38.9 39.3s-17.4 39.3-38.9 39.3-38.9-17.6-38.9-39.3c0-21.7 17.5-39.3 38.9-39.3zm185.4 201.3H86.3c-6.5 0-11.9-5.3-11.9-11.9v-32.4c0-2.5.7-4.8 2.1-6.9l41.3-58.4c3.7-5.2 10.8-6.5 16.1-3.1l56.4 36.8c4.5 3 10.3 2.5 14.4-1L313 220.1c5.1-4.5 13.1-3.8 17.2 1.7l61.5 79.7c1.6 2 2.5 4.6 2.5 7.2v74.4c0 5.2-4.3 9.5-9.5 9.5zm7.9 117.6h-58.8v-12h58.8v12zm-78.4 0h-58.8v-12h58.8v12zm-78.5 0h-58.8v-12h58.8v12zm-78.4 0H98.4v-12h58.8v12h.1zm-78.5 0H57.7c-14.3 0-27.9-5.4-38.3-15.3l8.3-8.7c8.2 7.8 18.8 12 30.1 12h21.1l-.1 12zm333.6-.1l-.3-12c17.8-.4 33.4-11.5 39.8-28.2l11.2 4.3c-8.1 21.3-28 35.4-50.7 35.9zM6.8 477c-3.2-7.1-4.7-14.7-4.7-22.5v-38.2h12v38.2c0 6.1 1.3 12.1 3.7 17.6l-11 4.9zm459.9-24.1h-12v-58.8h12v58.8zM14.1 396.7h-12v-58.8h12v58.8zm452.6-22.3h-12v-58.8h12v58.8zM14.1 318.3h-12v-58.8h12v58.8zM466.7 296h-12v-58.8h12V296zM14.1 239.8h-12V181h12v58.8zm452.6-22.2h-12v-58.8h12v58.8zM14.1 161.4h-12v-58.8h12v58.8zm2.4-76.1L5.3 81.2C13 59.9 33.4 45.5 56.1 45.5h.2v12h-.2c-17.7 0-33.6 11.2-39.6 27.8zm353.6-27.8h-58.8v-12h58.8v12zm-78.5 0h-58.8v-12h58.8v12zm-78.4 0h-58.8v-12h58.8v12zm-78.5 0H75.9v-12h58.8v12z"
            ></path>
          </svg>
          <h4 class="drop-text-here">
            <b>{{ dropText }}</b>
          </h4>
        </div>
      </div>
      <div
        v-else
        class="image-center position-absolute display-flex flex-column justify-content-center align-items-center"
        @dragover.prevent="onDragover"
      >
        <div>
          <svg
            class="image-icon-drag"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path
              d="M383.6 229l-.5 1.5.7 1.7c-.2-1.1-.2-2.2-.2-3.2zm-119.7-5.4l-.3 1.4.6 1.3c-.2-.8-.3-1.8-.3-2.7zm62.4 3.8l-.2 1 .5 1.1-.3-2.1z"
            ></path>
            <path
              d="M483 326.2l-43.5-100.5c-3.6-8.4-10.3-14.9-18.7-18.4-8.5-3.6-17.8-3.5-26.1.1L391 209c-3.3 1.4-6.1 3.6-8.4 6.3-3.6-8.2-10.2-14.6-18.6-18-8.5-3.4-17.7-3.3-26.1.3-6.1 2.7-10.9 6.8-13.9 12-3.7-8-10.2-14.3-18.4-17.6-8.5-3.4-17.8-3.3-26.1.3l-3.7 1.6c-6.3 2.7-11.2 7.1-14.3 12.4l-20.3-46.9c-4.2-9.8-10.7-16.8-18.7-20.2-8.1-3.5-17.2-3.2-26.5.8l-3.7 1.6c-8 3.5-13.3 9.3-15.5 16.9-2.1 7.3-1 16.2 3.1 25.6l83.4 188.2-64.7-39.8c-11.2-6.8-25.7-4.7-34.4 5.1-11.3 12.5-10.3 31.9 2 43.3l55.8 51.5 50.8 43.4c17.6 16.7 38.2 28.1 59.6 32.9 7.7 1.7 15.5 2.5 23.2 2.5 14.9 0 29.7-3.1 44.2-9.4l27.9-12.1c31.2-13.5 52.8-37.1 62.6-68.4 9.2-29.2 6.6-63-7.3-95.1zM383.6 229c0 1 .1 2.1.2 3.1l-.7-1.7.5-1.4zM281.7 466.6c-.2-.2-.5-.5-.7-.6l-50.4-43.1-55.6-51.5c-7.3-6.7-7.9-18.2-1.2-25.6 4.7-5.3 12.5-6.4 18.5-2.6l65.6 40.2c4.7 2.9 10.4 2.4 14.5-1.3 4.1-3.6 5.3-9.2 3.2-14.2l-83.7-189c-3.2-7.4-3.9-13.4-2.1-18.1 1.7-4.3 5.2-6.5 7.9-7.7l3.7-1.6c12.3-5.3 22.8-.6 28.6 12.9L310.2 350c1.4 3.2 5.1 4.6 8.3 3.3 3.2-1.4 4.7-5.1 3.3-8.3l-48.7-112.5c-2.2-5.2-3-10.8-2-15.4 1.1-5.4 4.5-9.3 9.9-11.7l3.7-1.6c5.3-2.3 11.1-2.3 16.4-.2 5.3 2.2 9.5 6.3 11.8 11.6l43.9 101.5c.7 1.6 1.9 2.7 3.5 3.4 1.6.6 3.3.6 4.8-.1 3.2-1.4 4.7-5.1 3.3-8.3l-32.8-75.9c-8.2-18.9 4.8-25.6 7.5-26.8 10.8-4.7 23.5.4 28.2 11.3l28.9 66.7c1.4 3.2 5.1 4.7 8.3 3.3 3.2-1.4 4.7-5.1 3.3-8.3l-19.4-44.8c-1.3-3-4.9-13.2 3.8-16.9l3.7-1.6c5.2-2.3 11.1-2.3 16.4 0 5.3 2.3 9.6 6.4 11.9 11.8L471.7 331c12.7 29.3 15.1 59.9 6.8 86.3-8.7 27.6-27.9 48.5-55.6 60.5L395 489.9c-38.9 16.9-80.1 8.4-113.3-23.3zm44.6-239.2l.3 2.1-.5-1.1.2-1zm-62.4-3.8l.3 2.7-.6-1.3.3-1.4zM31 217c3.2 0 6-2.6 6-5.7v-40c0-3.2-2.8-5.7-6-5.7s-6 2.6-6 5.7v40c0 3.2 2.8 5.7 6 5.7zm0-66.3c3.2 0 6-2.6 6-5.7v-40c0-3.2-2.8-5.7-6-5.7s-6 2.6-6 5.7v40c0 3.1 2.8 5.7 6 5.7zM148 296h-40c-3.2 0-5.7 2.3-5.7 5.5s2.6 5.5 5.7 5.5h40c3.2 0 5.7-2.3 5.7-5.5s-2.6-5.5-5.7-5.5zM37 237.6c0-3.2-2.8-5.7-6-5.7s-6 2.6-6 5.7v40c0 3.2 2.8 5.7 6 5.7s6-2.6 6-5.7v-40zM31 84.4c3.2 0 6-2.6 6-5.7v-40c0-3.2-2.8-5.7-6-5.7s-6 2.6-6 5.7v40c0 3.1 2.8 5.7 6 5.7zM81.6 296H49.1c-1.7 0-3.4-.6-5-1.3-2.9-1.3-6.3-.1-7.5 2.8-1.3 2.9 0 6.3 2.9 7.5 3 1.3 6.3 2 9.6 2h32.5c3.2 0 5.7-2.3 5.7-5.5s-2.5-5.5-5.7-5.5zm60.6-281c3.2 0 5.7-2.8 5.7-6s-2.6-6-5.7-6h-40c-3.2 0-5.7 2.8-5.7 6s2.6 6 5.7 6h40z"
            ></path>
            <path
              d="M323 122.4c-3.2 0-6 2.6-6 5.7v39.2c0 3.2 2.8 5.7 6 5.7s6-2.6 6-5.7v-39.2c0-3.1-2.8-5.7-6-5.7zm6-60.6c0-3.2-2.8-5.7-6-5.7s-6 2.6-6 5.7v40c0 3.2 2.8 5.7 6 5.7s6-2.6 6-5.7v-40zM301.2 15h3.6c6.8 0 12.2 5.6 12.2 12.4v8.1c0 3.2 2.8 5.7 6 5.7s6-2.6 6-5.7v-8.1C329 14.3 317.9 3 304.8 3h-3.6c-3.2 0-5.7 2.8-5.7 6s2.5 6 5.7 6zm-66.3 0h40c3.2 0 5.7-2.8 5.7-6s-2.6-6-5.7-6h-40c-3.2 0-5.7 2.8-5.7 6s2.5 6 5.7 6zm-60.6 292h40c3.2 0 5.7-2.3 5.7-5.5s-2.6-5.5-5.7-5.5h-40c-3.2 0-5.7 2.3-5.7 5.5s2.5 5.5 5.7 5.5zm-5.8-292h40c3.2 0 5.7-2.8 5.7-6s-2.6-6-5.7-6h-40c-3.2 0-5.7 2.8-5.7 6s2.6 6 5.7 6zM37.1 19.8c1.4 0 2.7-.6 3.8-1.5 2.3-2 5.2-3.2 8.2-3.2h26.8c3.2 0 5.7-2.8 5.7-6s-2.6-6-5.7-6H49.1c-5.9 0-11.5 2.5-15.9 6.5-2.3 2.1-2.5 5.9-.4 8.2 1.1 1.2 2.7 2 4.3 2z"
            ></path>
          </svg>
        </div>
        <div class="text-center">
          <label class="drag-text">{{ dragText }}</label>
          <br />
          <a class="browse-text">{{ browseText }}</a>
        </div>
        <div class="image-input position-absolute full-width full-height">
          <label :for="idUpload" class="full-width full-height cursor-pointer">
          </label>
        </div>
      </div>
    </div>

    <div
      class="image-container position-relative text-center image-list"
      v-else
    >
      <div
        class="drag-upload-cover position-absolute"
        v-if="isDragover"
        @drop="onDrop"
      >
        <div class="centered full-width text-center text-primary">
          <svg
            class="icon-drag-drop"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path
              d="M444.5 15C407.7 15 378 44.8 378 81.5s29.8 66.5 66.5 66.5S511 118.2 511 81.5 481.2 15 444.5 15zm29.4 72.4h-23.5l.1 25.9c0 3.2-2.6 5.8-5.8 5.9-3.2 0-5.8-2.6-5.8-5.8l-.1-26h-23.6c-3.2 0-5.8-2.6-5.8-5.8s2.6-5.8 5.8-5.8h23.5l-.1-25.9c0-3.2 2.6-5.8 5.8-5.9 3.2 0 5.8 2.6 5.8 5.8l.1 26h23.6c3.3 0 5.8 2.6 5.8 5.8s-2.6 5.8-5.8 5.8zM199.3 191.3c21.5 0 38.9 17.6 38.9 39.3s-17.4 39.3-38.9 39.3-38.9-17.6-38.9-39.3c0-21.7 17.5-39.3 38.9-39.3zm185.4 201.3H86.3c-6.5 0-11.9-5.3-11.9-11.9v-32.4c0-2.5.7-4.8 2.1-6.9l41.3-58.4c3.7-5.2 10.8-6.5 16.1-3.1l56.4 36.8c4.5 3 10.3 2.5 14.4-1L313 220.1c5.1-4.5 13.1-3.8 17.2 1.7l61.5 79.7c1.6 2 2.5 4.6 2.5 7.2v74.4c0 5.2-4.3 9.5-9.5 9.5zm7.9 117.6h-58.8v-12h58.8v12zm-78.4 0h-58.8v-12h58.8v12zm-78.5 0h-58.8v-12h58.8v12zm-78.4 0H98.4v-12h58.8v12h.1zm-78.5 0H57.7c-14.3 0-27.9-5.4-38.3-15.3l8.3-8.7c8.2 7.8 18.8 12 30.1 12h21.1l-.1 12zm333.6-.1l-.3-12c17.8-.4 33.4-11.5 39.8-28.2l11.2 4.3c-8.1 21.3-28 35.4-50.7 35.9zM6.8 477c-3.2-7.1-4.7-14.7-4.7-22.5v-38.2h12v38.2c0 6.1 1.3 12.1 3.7 17.6l-11 4.9zm459.9-24.1h-12v-58.8h12v58.8zM14.1 396.7h-12v-58.8h12v58.8zm452.6-22.3h-12v-58.8h12v58.8zM14.1 318.3h-12v-58.8h12v58.8zM466.7 296h-12v-58.8h12V296zM14.1 239.8h-12V181h12v58.8zm452.6-22.2h-12v-58.8h12v58.8zM14.1 161.4h-12v-58.8h12v58.8zm2.4-76.1L5.3 81.2C13 59.9 33.4 45.5 56.1 45.5h.2v12h-.2c-17.7 0-33.6 11.2-39.6 27.8zm353.6-27.8h-58.8v-12h58.8v12zm-78.5 0h-58.8v-12h58.8v12zm-78.4 0h-58.8v-12h58.8v12zm-78.5 0H75.9v-12h58.8v12z"
            ></path>
          </svg>
          <h4 class="drop-text-here">
            <b>{{ dropText }}</b>
          </h4>
        </div>
      </div>
      <div v-else @dragover.prevent="onDragover">
        <div
          class="preview-image full-width position-relative cursor-pointer"
          @click="openGallery(currentIndexImage)"
        >
          <div
            class="image-overlay position-relative full-width full-height"
          ></div>
          <div class="image-overlay-details full-width">
            <svg
              class="icon-overlay"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
            >
              <path
                d="M283.9 186.4h-64.6l-.4-71.1c-.1-8.8-7.2-15.9-16-15.9h-.1c-8.8.1-16 7.3-15.9 16.1l.4 70.9h-64.4c-8.8 0-16 7.2-16 16s7.2 16 16 16h64.6l.4 71.1c.1 8.8 7.2 15.9 16 15.9h.1c8.8-.1 16-7.3 15.9-16.1l-.4-70.9h64.4c8.8 0 16-7.2 16-16s-7.1-16-16-16z"
              ></path>
              <path
                d="M511.3 465.3L371.2 325.2c-1-1-2.6-1-3.6 0l-11.5 11.5c31.6-35.9 50.8-82.9 50.8-134.3C406.9 90.3 315.6-1 203.4-1 91.3-1 0 90.3 0 202.4s91.3 203.4 203.4 203.4c51.4 0 98.5-19.2 134.3-50.8l-11.5 11.5c-1 1-1 2.6 0 3.6l140.1 140.1c1 1 2.6 1 3.6 0l41.4-41.4c.9-.9.9-2.5 0-3.5zm-307.9-92.5C109.5 372.8 33 296.4 33 202.4S109.5 32.1 203.4 32.1s170.4 76.4 170.4 170.4-76.4 170.3-170.4 170.3z"
              ></path>
            </svg>
          </div>
          <div class="show-image centered">
            <img class="show-img img-responsive" :src="imagePreview" />
          </div>
        </div>
        <div
          class="image-bottom display-flex position-absolute full-width align-items-center justify-content-between"
          :class="!showPrimary && 'justify-content-end'"
        >
          <div
            class="image-bottom-left display-flex align-items-center"
            v-if="showPrimary"
          >
            <div
              v-if="showSetAsDefault"
              class="display-flex align-items-center"
              v-show="imageDefault"
            >
              <span class="image-primary display-flex align-items-center">
                <svg
                  class="image-icon-primary"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                >
                  <circle fill="#10BC83" cx="256" cy="256" r="256"></circle>
                  <path
                    fill="#FFF"
                    d="M216.7 350.9h-.1c-5.1 0-9.9-2.1-13.4-5.7l-74.2-76c-7.4-7.5-7.2-19.5.4-26.8 7.5-7.4 19.5-7.2 26.8.4L217 305l139.7-138.5c7.5-7.4 19.5-7.4 26.8.1s7.4 19.5-.1 26.8l-153.2 152c-3.7 3.5-8.5 5.5-13.5 5.5z"
                  ></path>
                </svg>
                {{ primaryText }}
              </span>
              <Popper trigger="click" :options="{placement: 'top'}">
                <div class="popper popper-custom">
                  {{ popupText }}
                </div>
                <template v-slot:reference>
                  <i class="cursor-pointer display-flex align-items-center">
                    <svg
                      class="image-icon-info"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 512 512"
                    >
                      <path
                        d="M256 32c30.3 0 59.6 5.9 87.2 17.6 26.7 11.3 50.6 27.4 71.2 48s36.7 44.5 48 71.2c11.7 27.6 17.6 56.9 17.6 87.2s-5.9 59.6-17.6 87.2c-11.3 26.7-27.4 50.6-48 71.2s-44.5 36.7-71.2 48C315.6 474.1 286.3 480 256 480s-59.6-5.9-87.2-17.6c-26.7-11.3-50.6-27.4-71.2-48s-36.7-44.5-48-71.2C37.9 315.6 32 286.3 32 256s5.9-59.6 17.6-87.2c11.3-26.7 27.4-50.6 48-71.2s44.5-36.7 71.2-48C196.4 37.9 225.7 32 256 32m0-32C114.6 0 0 114.6 0 256s114.6 256 256 256 256-114.6 256-256S397.4 0 256 0z"
                      ></path>
                      <path
                        d="M304.2 352H296c-4.4 0-8-3.6-8-8v-94.8c0-15.3-11.5-28.1-26.7-29.8-2.5-.3-4.8-.5-6.7-.5-23.7 0-44.6 11.9-57 30.1l-.1.1v-.1c-1 2-1.7 5.3.7 6.5.6.3 1.2.5 1.8.5h16c4.4 0 8 3.6 8 8v80c0 4.4-3.6 8-8 8h-8.2c-8.7 0-15.8 7.1-15.8 15.8v.3c0 8.7 7.1 15.8 15.8 15.8h96.4c8.7 0 15.8-7.1 15.8-15.8v-.3c0-8.7-7.1-15.8-15.8-15.8zM256 128c-17.7 0-32 14.3-32 32s14.3 32 32 32 32-14.3 32-32-14.3-32-32-32z"
                      ></path>
                    </svg>
                  </i>
                </template>
              </Popper>
            </div>
            <a
              v-if="showSetAsDefault"
              class="text-small mark-text-primary cursor-pointer"
              @click.prevent="markIsPrimary(currentIndexImage)"
              v-show="!imageDefault"
              >{{ markIsPrimaryText }}</a
            >
          </div>
          <div class="display-flex">
            <label
              v-if="showEdit"
              class="image-edit display-flex cursor-pointer"
              :for="idEdit"
            >
              <svg
                class="image-icon-edit"
                xmlns="http://www.w3.org/2000/svg"
                width="512"
                height="512"
                viewBox="0 0 512 512"
              >
                <path
                  d="M469.56 42.433C420.927-6.199 382.331-.168 378.087.68l-4.8.96L36.895 338.001 0 512l173.985-36.894 336.431-336.399.941-4.86c.826-4.257 6.65-42.984-41.797-91.414zM41.944 470.057L64.3 364.617c12.448 3.347 31.968 11.255 50.51 29.794 18.96 18.963 27.84 39.986 31.875 53.436l-104.741 22.21zm132.504-41.134c-6.167-16.597-17.199-37.794-36.775-57.371C119 352.88 99.435 342.57 83.739 336.879l155.156-155.15 97.066-97.051c11.069 2.074 34.864 8.95 57.253 31.338 22.708 22.708 30.95 48.358 33.734 60.428l-96.685 96.663-155.815 155.816zm278.41-278.383c-6.167-16.6-17.196-37.8-36.781-57.384-18.669-18.667-38.228-28.977-53.92-34.668l26.118-26.113c8.785.484 30.373 4.87 58.423 32.918l.001.002c28.085 28.074 32.467 49.675 32.946 58.463l-26.787 26.782z"
                ></path>
              </svg>
            </label>

            <a
              v-if="showDelete"
              class="image-delete display-flex cursor-pointer"
              @click.prevent="deleteImage(currentIndexImage)"
            >
              <svg
                class="image-icon-delete"
                xmlns="http://www.w3.org/2000/svg"
                width="512"
                height="512"
                viewBox="0 0 512 512"
              >
                <path
                  d="M448 64h-96V0H159.9l.066 64H32v32h32v416h384V96h32V64h-32zM192 32h128v32H192V32zm224 448H96V96h320v384zM192 160h32v256h-32V160zm96 0h32v256h-32V160z"
                ></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>

    <div
      class="image-list-container display-flex flex-wrap"
      v-if="images.length && multiple"
    >
      <div
        class="image-list-item position-relative cursor-pointer"
        :class="image.highlight && 'image-highlight'"
        v-for="(image, index) in images"
        :key="index"
        @click="changeHighlight(index)"
      >
        <div class="centered">
          <img
            class="show-img img-responsive"
            :src="image.postar ? image.postar : image.path"
          />
        </div>
      </div>
      <div
        class="image-list-item position-relative cursor-pointer display-flex justify-content-center align-items-center"
        v-if="images.length < maxImage && showAdd"
      >
        <svg
          class="icon add-image-svg"
          xmlns="http://www.w3.org/2000/svg"
          width="512"
          height="512"
          viewBox="0 0 512 512"
        >
          <path
            d="M511.5 227.5h-227V.5h-57v227H-.5v57h228v228h57v-228h227z"
          ></path>
        </svg>
        <div class="input-add-image position-absolute full-width full-height">
          <label
            :for="idUpload"
            class="display-block full-width full-height cursor-pointer"
          >
          </label>
        </div>
      </div>
    </div>
    <div>
      <input
        class="display-none"
        :id="idUpload"
        @change="uploadFieldChange"
        name="images"
        :multiple="multiple"
        :accept="accept"
        type="file"
        :disabled="disabled"
      />
      <input
        class="display-none"
        :id="idEdit"
        @change="editFieldChange"
        name="image"
        :accept="accept"
        type="file"
        :disabled="disabled"
      />
    </div>

    <vue-image-lightbox
      ref="lightbox"
      :show="showLightbox"
      @close="showLightbox = false"
      :images="images"
      @change="changeHighlight"
      :showCaption="false"
    />
  </div>
</template>

<script>
  import cloneDeep from 'lodash-es/cloneDeep';
  import findIndex from 'lodash-es/findIndex';
  import forEach from 'lodash-es/forEach';
  import orderBy from 'lodash-es/orderBy';
  import Popper from 'vue3-popper';
  import VueImageLightbox from './VueImageLightbox.vue';

  export default {
    name: 'VueUploadMultipleImage',

    props: {
      dragText: {
        type: String,
        default: 'Drag and Drop images (multiple)',
      },
      browseText: {
        type: String,
        default: '(or) Select',
      },
      primaryText: {
        type: String,
        default: 'Default',
      },
      markIsPrimaryText: {
        type: String,
        default: 'Set as default',
      },
      popupText: {
        type: String,
        default: 'This image is set as default',
      },
      dropText: {
        type: String,
        default: 'Drop file here ...',
      },
      accept: {
        type: String,
        default: 'image/gif,image/jpeg,image/png,image/bmp,image/jpg,video/mp4',
      },
      dataImages: {
        type: Array,
        default: () => {
          return [];
        },
      },
      multiple: {
        type: Boolean,
        default: true,
      },
      showPrimary: {
        type: Boolean,
        default: true,
      },
      maxImage: {
        type: Number,
        default: 5,
      },
      idUpload: {
        type: String,
        default: 'image-upload',
      },
      idEdit: {
        type: String,
        default: 'image-edit',
      },
      showSetAsDefault: {
        type: Boolean,
        default: false,
      },
      showEdit: {
        type: Boolean,
        default: true,
      },
      showDelete: {
        type: Boolean,
        default: true,
      },
      showAdd: {
        type: Boolean,
        default: true,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        currentIndexImage: 0,
        images: [],
        isDragover: false,
        showLightbox: false,
        arrLightBox: [],
        uploading_images: [],
      };
    },
    components: {
      Popper,
      VueImageLightbox,
    },
    computed: {
      imagePreview() {
        const index = findIndex(this.images, {highlight: 1});
        if (index > -1) {
          return this.images[index].postar
            ? this.images[index].postar
            : this.images[index].path;
        }
        return this.images.length
          ? this.images[0].postar
            ? this.images[0].postar
            : this.images[0].path
          : '';
      },
      imageDefault() {
        if (this.images[this.currentIndexImage]) {
          return this.images[this.currentIndexImage].default;
        }
        return 0;
      },
    },
    methods: {
      preventEvent(e) {
        e.preventDefault();
        e.stopPropagation();
      },
      async onDrop(e) {
        this.isDragover = false;
        e.stopPropagation();
        e.preventDefault();
        const files = e.dataTransfer.files;
        if (!files.length) {
          return false;
        }
        if (!this.isValidNumberOfImages(files.length)) {
          return false;
        }
        let tmp_files = [];
        forEach(files, (value, index) => {
          tmp_files.push(value);
        });

        this.uploading_images = [];
        // Multiple upload?
        tmp_files = tmp_files.filter((x, i) => {
          if (!this.multiple && i > 0) {
            return false;
          }
          return true;
        });
        // Sort by name
        tmp_files = tmp_files.sort((a, b) => {
          // If video then move to bottom
          const a_type =
            a?.name && a.name.indexOf('.mp4') !== -1 ? 'video' : 'image';
          const b_type =
            b?.name && b.name.indexOf('.mp4') !== -1 ? 'video' : 'image';
          if (a_type === 'video' && b_type !== 'video') {
            return 1;
          }
          if (b_type === 'video' && a_type !== 'video') {
            return -1;
          }
          return a.name > b.name ? 1 : -1;
        });
        await Promise.all(
          tmp_files.map((value, index) => {
            return this.createImage(value, this.uploading_images);
          })
        ).then(results => {
          console.log('onDrop results: ', results);
          if (document.getElementById(this.idUpload)) {
            document.getElementById(this.idUpload).value = [];
          }

          // Sort by name
          this.uploading_images = this.uploading_images.sort((a, b) => {
            // If video then move to bottom
            if (a.type === 'video' && b.type !== 'video') {
              return 1;
            }
            if (b.type === 'video' && a.type !== 'video') {
              return -1;
            }
            return a.name > b.name ? 1 : -1;
          });
          console.log('uploading_images: ', this.uploading_images);
          this.uploading_images.map(img => {
            this.images.push(img);
          });
          this.$emit(
            'upload-success',
            tmp_files,
            this.images.length - 1,
            this.images
          );
          this.uploading_images = [];
        });
        return true;
      },
      onDragover() {
        this.isDragover = true;
      },
      createImage(file, uploading_images) {
        console.log('createImage');
        if (this.disabled) {
          return Promise.resolve();
        }
        const formData = new FormData();
        formData.append('file', file);

        // 動画表示用
        return Promise.resolve()
          .then(() => {
            if (file.name.indexOf('.mp4') === -1) {
              return Promise.resolve(file);
            }
            return this.getVideoCover(file);
          })
          .then(fileCover => {
            return new Promise((resolve1, reject1) => {
              const reader = new FileReader();
              reader.onload = e => {
                const dataURI = e.target.result;
                if (dataURI) {
                  if (!uploading_images.length) {
                    if (file.name.indexOf('.mp4') === -1) {
                      uploading_images.push({
                        type: 'image',
                        name: file.name,
                        path: dataURI,
                        postar: null,
                        highlight: 1,
                        default: 1,
                      });
                    } else {
                      uploading_images.push({
                        type: 'video',
                        name: file.name,
                        path: URL.createObjectURL(file),
                        postar: dataURI,
                        highlight: 1,
                        default: 1,
                      });
                    }
                    this.currentIndexImage = 0;
                  } else if (file.name.indexOf('.mp4') === -1) {
                    uploading_images.push({
                      type: 'image',
                      name: file.name,
                      path: dataURI,
                      postar: null,
                      highlight: 0,
                      default: 0,
                    });
                  } else {
                    uploading_images.push({
                      type: 'video',
                      name: file.name,
                      path: URL.createObjectURL(file),
                      postar: dataURI,
                      highlight: 0,
                      default: 0,
                    });
                  }
                  return resolve1();
                }
                return resolve1();
              };
              reader.readAsDataURL(fileCover);
            });
          })
          .catch(error => {
            console.log('error: ', error);
            return Promise.resolve();
          });
      },
      editImage(file) {
        console.log('editImage');
        if (this.disabled) {
          return;
        }
        const formData = new FormData();
        formData.append('file', file);

        // 動画表示用
        Promise.resolve()
          .then(() => {
            if (file.name.indexOf('.mp4') === -1) {
              return Promise.resolve(file);
            }
            return this.getVideoCover(file);
          })
          .then(fileCover => {
            const reader = new FileReader();
            reader.onload = e => {
              const dataURI = e.target.result;
              if (dataURI) {
                if (this.images.length && this.images[this.currentIndexImage]) {
                  if (file.name.indexOf('.mp4') === -1) {
                    this.images[this.currentIndexImage].type = 'image';
                    this.images[this.currentIndexImage].postar = null;
                    this.images[this.currentIndexImage].path = dataURI;
                  } else {
                    this.images[this.currentIndexImage].type = 'video';
                    this.images[this.currentIndexImage].postar = dataURI;
                    this.images[this.currentIndexImage].path =
                      URL.createObjectURL(file);
                  }
                  this.images[this.currentIndexImage].name = file.name;
                }
                this.$emit(
                  'edit-image',
                  formData,
                  this.currentIndexImage,
                  this.images
                );
              }
            };
            reader.readAsDataURL(fileCover);
          })
          .catch(error => {
            console.log('error: ', error);
          });
      },
      async uploadFieldChange(e) {
        const files = e.target.files || e.dataTransfer.files;
        if (!files.length) {
          return false;
        }
        if (!this.isValidNumberOfImages(files.length)) {
          return false;
        }
        let tmp_files = [];
        forEach(files, (value, index) => {
          tmp_files.push(value);
        });
        // Sort by name
        tmp_files = tmp_files.sort((a, b) => {
          // If video then move to bottom
          const a_type =
            a?.name && a.name.indexOf('.mp4') !== -1 ? 'video' : 'image';
          const b_type =
            b?.name && b.name.indexOf('.mp4') !== -1 ? 'video' : 'image';
          if (a_type === 'video' && b_type !== 'video') {
            return 1;
          }
          if (b_type === 'video' && a_type !== 'video') {
            return -1;
          }
          return a.name > b.name ? 1 : -1;
        });
        this.uploading_images = [];
        await Promise.all(
          tmp_files.map((value, index) => {
            return this.createImage(value, this.uploading_images);
          })
        ).then(results => {
          console.log('uploadFieldChange results: ', results);
          if (document.getElementById(this.idUpload)) {
            document.getElementById(this.idUpload).value = [];
          }

          // Sort by name
          this.uploading_images = this.uploading_images.sort((a, b) => {
            // If video then move to bottom
            if (a.type === 'video' && b.type !== 'video') {
              return 1;
            }
            if (b.type === 'video' && a.type !== 'video') {
              return -1;
            }
            return a.name > b.name ? 1 : -1;
          });
          console.log('uploading_images: ', this.uploading_images);
          this.uploading_images.map(img => {
            this.images.push(img);
          });
          this.$emit(
            'upload-success',
            tmp_files,
            this.images.length - 1,
            this.images
          );
          this.uploading_images = null;
        });
        return true;
      },
      editFieldChange(e) {
        const files = e.target.files || e.dataTransfer.files;
        if (!files.length) {
          return false;
        }
        if (!this.isValidNumberOfImages(files.length)) {
          return false;
        }
        forEach(files, (value, index) => {
          this.editImage(value);
        });
        if (document.getElementById(this.idEdit)) {
          document.getElementById(this.idEdit).value = '';
        }
        return true;
      },
      changeHighlight(currentIndex) {
        this.currentIndexImage = currentIndex;
        const arr = this.images;
        this.images = [];
        arr.map((item, index) => {
          if (currentIndex === index) {
            item.highlight = 1;
          } else {
            item.highlight = 0;
          }
          return item;
        });
        this.images = arr;
      },
      markIsPrimary(currentIndex) {
        this.images.map((item, index) => {
          if (currentIndex === index) {
            item.highlight = 1;
            item.default = 1;
          } else {
            item.highlight = 0;
            item.default = 0;
          }
          return item;
        });
        this.currentIndexImage = 0;
        this.images = orderBy(this.images, 'default', 'desc');
        this.$emit('mark-is-primary', currentIndex, this.images);
      },
      deleteImage(currentIndex) {
        this.$emit(
          'before-remove',
          currentIndex,
          () => {
            if (this.images[currentIndex].default === 1) {
              this.images[0].default = 1;
            }
            this.images.splice(currentIndex, 1);
            this.currentIndexImage = 0;
            if (this.images.length) {
              this.images[0].highlight = 1;
            }
          },
          this.images
        );
      },
      openGallery(index) {
        this.showLightbox = true;
        this.$refs.lightbox.showImage(index);
      },
      onOpenedLightBox(value) {
        if (value) {
          this.showLightbox = true;
        } else {
          this.showLightbox = false;
        }
      },
      isValidNumberOfImages(amount) {
        if (amount > this.maxImage) {
          this.$emit('limit-exceeded', amount);
          return false;
        }
        return true;
      },
      getVideoCover(file, seekTo = 0.0) {
        return new Promise((resolve, reject) => {
          // Load the file to a video player
          const videoPlayer = document.createElement('video');
          videoPlayer.setAttribute('src', URL.createObjectURL(file));
          videoPlayer.load();
          videoPlayer.addEventListener('error', ex => {
            reject('error when loading video file', ex);
          });
          // Load metadata of the video to get video duration and dimensions
          videoPlayer.addEventListener('loadedmetadata', () => {
            // Seek to user defined timestamp (in seconds) if possible
            if (videoPlayer.duration < seekTo) {
              reject('video is too short.');
              return;
            }
            // Delay seeking or else 'seeked' event won't fire on Safari
            setTimeout(() => {
              videoPlayer.currentTime = seekTo;
            }, 200);
            // Extract video thumbnail once seeking is complete
            videoPlayer.addEventListener('seeked', () => {
              // Define a canvas to have the same dimension as the video
              const canvas = document.createElement('canvas');
              canvas.width = videoPlayer.videoWidth;
              canvas.height = videoPlayer.videoHeight;
              // Draw the video frame to canvas
              const ctx = canvas.getContext('2d');
              ctx.drawImage(videoPlayer, 0, 0, canvas.width, canvas.height);
              // Return the canvas image as a blob
              ctx.canvas.toBlob(
                blob => {
                  resolve(blob);
                },
                'image/jpeg',
                0.75 /* Quality */
              );
            });
          });
        });
      },
    },
    watch: {
      dataImages: {
        handler(newVal) {
          this.images = cloneDeep(newVal);
        },
        deep: true,
      },
    },
    mounted() {
      document.body.addEventListener('dragleave', event => {
        event.stopPropagation();
        event.preventDefault();
        this.isDragover = false;
      });
    },
    created() {
      this.images = [];
      this.images = cloneDeep(this.dataImages);
    },
  };
</script>

<style lang="css" scoped>
  .text-small {
    font-size: 0.875rem;
  }

  .position-relative {
    position: relative;
  }

  .position-absolute {
    position: absolute;
  }

  .text-center {
    text-align: center;
  }

  .text-primary {
    color: #2fa3e6;
  }

  .display-flex {
    display: flex;
  }

  .flex-column {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .justify-content-center {
    justify-content: center;
  }

  .justify-content-between {
    justify-content: space-between;
  }

  .justify-content-end {
    justify-content: flex-end;
  }

  .align-items-center {
    align-items: center;
  }

  .full-width {
    width: 100%;
  }

  .full-height {
    height: 100%;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .centered {
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    position: absolute;
    display: block;
  }

  .image-container {
    width: 100%;
    height: var(--custom-upload-box-height);
    border: 1px dashed #d6d6d6;
    border-radius: 4px;
    background-color: #fff;
  }

  .image-center {
    width: 100%;
    height: 100%;
  }

  .image-icon-drag {
    fill: #c9c8c8;
    height: 50px;
    width: 50px;
  }

  .drag-text {
    padding-top: 5px;
    color: #777;
    font-weight: 400;
    line-height: 1.5;
  }

  .browse-text {
    font-size: 86%;
    color: #206ec5;
    text-decoration: none;
  }

  .image-input {
    overflow: hidden;
    opacity: 0;
    top: 0;
    left: 0;
    bottom: 0;
  }

  .image-input label {
    display: block;
  }

  .drag-upload-cover {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fcfeff;
    opacity: 0.9;
    z-index: 1;
    margin: 5px;
    border: 2px dashed #268ddd;
  }

  .drag-upload-cover {
    font-weight: 400;
    font-size: 20px;
  }

  .icon-drag-drop {
    height: 50px;
    width: 50px;
    fill: #2fa3e6;
  }

  .drop-text-here {
    margin: 0;
    line-height: 1.5;
  }

  .display-none {
    display: none;
  }

  /* list images*/
  .image-list {
    border: 1px solid #d6d6d6;
  }

  .preview-image {
    height: calc(var(--custom-upload-box-height) - 40px);
    padding: 5px;
    border-radius: 15px;
    box-sizing: border-box;
  }

  .image-overlay {
    background: rgba(0, 0, 0, 0.7);
    z-index: 10;
    border-radius: 5px;
    opacity: 0;
    transition: all 0.3s ease-in-out 0s;
  }

  .image-overlay-details {
    position: absolute;
    z-index: 11;
    opacity: 0;
    transform: translate(0, -50%);
    top: 50%;
  }

  .icon-overlay {
    width: 40px;
    height: 40px;
    fill: #fff;
  }

  .preview-image:hover .image-overlay,
  .preview-image:hover .image-overlay-details {
    opacity: 1;
  }

  .img-responsive {
    display: block;
    max-width: 100%;
    height: auto;
  }

  .show-img {
    max-height: calc(var(--custom-upload-box-height) - 40px);
    max-width: 100%;
    display: block;
    vertical-align: middle;
  }

  /*image bottom*/
  .image-bottom {
    bottom: 0;
    left: 0;
    height: 40px;
    padding: 5px 10px;
    box-sizing: border-box;
  }

  .image-primary {
    border-radius: 4px;
    background-color: #e3edf7;
    padding: 3px 7px;
    font-size: 11px;
    margin-right: 5px;
  }

  .image-icon-primary {
    width: 10px;
    height: 10px;
    margin-right: 2px;
  }

  .image-icon-delete {
    height: 14px;
    width: 14px;
    fill: #222;
  }

  .image-edit {
    margin-right: 10px;
  }

  .image-icon-edit {
    height: 14px;
    width: 14px;
    fill: #222;
  }

  .image-list-container {
    /* max-width: 240px; */
    min-height: 50px;
    margin-top: 10px;
  }

  .image-list-container .image-list-item {
    height: 32px;
    width: 32px;
    border-radius: 4px;
    border: 1px solid #d6d6d6;
  }

  .image-list-container .image-list-item:not(:last-child) {
    margin-right: 5px;
    margin-bottom: 5px;
  }

  .image-list-container .image-list-item .show-img {
    max-width: 25px;
    max-height: 17px;
  }

  .image-list-container .image-highlight {
    border: 1px solid #2fa3e7;
  }

  .add-image-svg {
    width: 12px;
    height: 12px;
    fill: #222;
  }

  .input-add-image {
    overflow: hidden;
    opacity: 0;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 11;
  }

  .input-add-image label {
    display: block;
  }

  .image-icon-info {
    width: 14px;
    height: 14px;
    fill: #222;
  }

  .mark-text-primary {
    color: #034694;
  }

  .popper-custom {
    background: #000;
    padding: 10px;
    border: none;
    box-shadow: none;
    color: white;
    text-align: left;
    font-size: 12px;
  }
</style>
<style lang="css">
  .popper-custom .popper__arrow {
    border-color: #000 transparent transparent transparent !important;
  }
</style>
