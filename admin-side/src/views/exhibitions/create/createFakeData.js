const getRandomInt = (min, max) =>
  Math.floor(Math.random() * (max - min + 1)) + min

const generateUniqueId = () => getRandomInt(10, 9999)

const brands = [
  {jp: 'ルイヴィトン', en: 'LOUIS VUITTON'},
  {jp: 'グッチ', en: 'GUCCI'},
  {jp: 'エルメス', en: 'HERMÈS'},
  {jp: 'シャネル', en: 'CHANEL'},
  {jp: 'ロレックス', en: 'ROLEX'},
  {jp: 'オメガ', en: 'OMEGA'},
  {jp: 'ベンツ', en: 'Mercedes-Benz'},
  {jp: 'BMW', en: 'BMW'},
  {jp: 'アウディ', en: 'AUDI'},
  {jp: 'ポルシェ', en: 'PORSCHE'},
]

const productTypes = [
  'ショルダー バッグ',
  'トートバッグ',
  'チェーンウォレット',
  'クロスボディ バッグ',
  '腕時計',
  'クロノグラフ 時計',
  'セダン 車',
  'スポーツカー',
  'SUV 車',
  'ハッチバック',
]

const adjectives = [
  'ダミエ アズール',
  'モノグラム キャンバス',
  'マトラッセ',
  'スモール レザーグッズ',
  'シルバー フレーム',
  'ゴールド仕上げ',
  'アイボリー ホワイト',
  'ブラック レザー',
  'レッド スウェード',
  'ブルー メタリック',
]

const statusTags = [
  '♪',
  '正規品',
  '新品同様',
  '限定モデル',
  '高級仕様',
  '希少品',
  '国内正規',
  '美品',
]

export const createFakeData = (exhibition_no, num, auctionType) => {
  return Array.from({length: num}, () => {
    const randomId = generateUniqueId()
    const brand = brands[getRandomInt(0, brands.length - 1)]
    const adjective = adjectives[getRandomInt(0, adjectives.length - 1)]
    const type = productTypes[getRandomInt(0, productTypes.length - 1)]
    const tag1 = statusTags[getRandomInt(0, statusTags.length - 1)]
    const tag2 = statusTags[getRandomInt(0, statusTags.length - 1)]

    const productName = `${brand.jp} ${tag1} ${adjective} ${type} ${tag2} ${brand.en} ${type}`

    return {
      validate: false,
      data: {
        exhibition_no: exhibition_no,
        manage_no: `ID000${randomId}`,
        quantity: 1,
        lowest_bid_price: getRandomInt(4, 9),
        lowest_bid_accept_price: auctionType === 1 ? 15 : 20,
        lowest_bid_quantity: 1,
        lowest_bid_accept_quantity: auctionType === 1 ? 1 : 20,
        free_field: {
          category: '',
          maker: brand.en,
          model: '',
          product_name: productName,
          capacity: '',
          rank: ['A', 'B', 'C'][getRandomInt(0, 2)],
          sim: '',
          sku_id: String(getRandomInt(1000, 9999)),
          note1: `${productName} - 高級感と実用性を兼ね備えた人気商品です。`,
          note2: '正規品・安心の品質・迅速発送可能。',
          note1_en: `${productName} - Combines luxury and practicality.`,
          note2_en: 'Authentic product, guaranteed quality, ready to ship.',
          color: ['黒', '白', '赤', 'ゴールド', 'シルバー'][getRandomInt(0, 4)],
        },
        picturePath: [],
      },
    }
  })
}
