<template>
  <div
    id="my-strictly-unique-vue-upload-multiple-image"
    style="display: flex; justify-content: center"
  >
    <VueUploadMultipleImage
      @upload-success="uploadImageSuccess"
      @before-remove="beforeRemove"
      @edit-image="editImage"
      :data-images="origImages"
      :max-image="200"
      dropText=""
      browseText=""
      dragText=""
      :showAdd="true"
      :showEdit="false"
      :showDelete="true"
      idUpload="myIdUpload"
      idEdit="myIdEdit"
    ></VueUploadMultipleImage>
  </div>
</template>

<script>
  import UploadFile from '@/api/uploadFileToS3';
  import Base from '@/common/base';
  import cloneDeep from 'lodash-es/cloneDeep';
  import VueUploadMultipleImage from './VueUploadMultipleImage.vue';

  export default {
    name: 'ImageUpload',
    props: {
      origImages: {
        type: Array,
        default() {
          return [];
        },
      },
    },
    data() {
      return {
        images: [],
      };
    },
    components: {
      VueUploadMultipleImage,
    },
    created() {
      this.images = [];
      this.images = cloneDeep(this.origImages);
    },
    methods: {
      uploadImageSuccess(files, index, fileList) {
        this.onUploadStart();

        // Upload image api
        let s3urls = [];
        Promise.all(
          files.map((file, i) => {
            return new Promise((resolve, reject) => {
              UploadFile.upload('item-ancillary', file, data => {
                if (data.status === 200) {
                  let ftype = 'image';
                  if (data.message.indexOf('.mp4') !== -1) {
                    ftype = 'video';
                  }
                  s3urls = s3urls.concat([
                    {
                      index: i,
                      type: ftype,
                      name: Base.getFileName(data.message),
                      path: data.message,
                      postar: '',
                    },
                  ]);
                }
                return resolve();
              });
            });
          })
        ).then(results => {
          // Get uploaded file urls
          if (s3urls) {
            s3urls = s3urls.sort((a, b) => a.index - b.index);
            s3urls = s3urls.map(x => {
              delete x.index;
              return x;
            });
          }
          if (index < 0 || index >= this.images.length) {
            this.images = this.images.concat(s3urls);
          }
          this.onUploadFinish();
          this.$emit('onChange', this.images);
        });
      },
      beforeRemove(index, done, fileList) {
        const r = confirm('画像を削除しますか？');
        if (r === true) {
          done();
          // Do the same on images
          this.images.splice(index, 1);
          this.$emit('onChange', this.images);
        }
      },
      editImage(formData, index, fileList) {
        // Upload image api
        UploadFile.upload('item-ancillary', formData.get('file'), data => {
          if (data.status === 200) {
            if (this.images.length && this.images[index]) {
              this.images[index].path = data.message;
              this.images[index].name = Base.getFileName(data.message);
            }
          }
          this.$emit('onChange', this.images);
        });
      },
      onUploadStart() {
        this.$emit('onUploadStart', this.fileInfos);
      },
      onUploadFinish() {
        this.$emit('onUploadFinish', this.fileInfos);
      },
    },
    watch: {
      origImages: {
        handler(newVal) {
          this.images = cloneDeep(newVal);
        },
        deep: true,
      },
    },
  };
</script>
<style>
  :root {
    --custom-upload-box-height: 350px;
  }
</style>
<style lang="scss" scoped>
  #my-strictly-unique-vue-upload-multiple-image {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
    margin-top: 0;
  }

  h1,
  h2 {
    font-weight: normal;
  }

  ul {
    list-style-type: none;
    padding: 0;
  }

  li {
    display: inline-block;
    margin: 0 10px;
  }

  a {
    color: #42b983;
  }
</style>
