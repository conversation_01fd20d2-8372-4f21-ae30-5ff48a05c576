<!-- /#/exhibitions?page= -->
<template>
  <div class="mb-3">
    <CCard>
      <CCardHeader class="d-flex">
        <strong>検索条件</strong>
      </CCardHeader>
      <CCardBody>
        <CForm onsubmit="return false;">
          <CRow class="mb-3">
            <CFormLabel for="colFormLabelSm" class="col-sm-1">
              入札会
            </CFormLabel>
            <CCol :sm="4">
              <CFormSelect
                name="exhibition"
                :options="optionsExhibition"
                v-model="searchCondition.selectExhibition"
                :disabled="exhibitionLoading"
                @change="onChangeExhibition"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CFormLabel for="colFormLabelSm" class="col-sm-1">
              商品名
            </CFormLabel>
            <CCol :sm="4">
              <CFormInput v-model="searchCondition.productName" />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CRow>
          <CCol sm="5"></CCol>
          <CCol sm="2">
            <div class="d-grid gap-2">
              <CButton
                color="info"
                class="exhibitor-list btn-sm"
                @click="getLots()"
                block
                >検索
              </CButton>
            </div>
          </CCol>
          <CCol sm="5"></CCol>
        </CRow>
      </CCardFooter>
    </CCard>
    <CRow class="mt-3">
      <CCol sm="12">
        <CTableWrapper
          ref="lotListTable"
          name="lotList"
          :items="lotList"
          :exhibitionEndFlag="exhibitionEndFlag"
          :totalCount="totalCount"
          :currentCount="currentCount"
          :exhibitionInfo="exhibitionInfo"
          hover
          striped
          border
          small
          fixed
          :loading="tableLoading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          :exhibitionStartFlag="exhibitionStartFlag"
          caption="出展一覧"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
          @lot-delete="openLotDelete($event, 2)"
          @lot-cancel="openLotDelete($event, 1)"
          @lot-republish="openLotDelete($event, 3)"
        />
      </CCol>
    </CRow>

    <CModal backdrop="static" :keyboard="false" :visible="cancelConfirmModal">
      <CModalHeader>
        <CModalTitle>{{ cancelModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="upsertLoading" class="my-3 text-center">
          <CSpinner />
        </div>
        <div v-else>
          <div v-if="errMsgArray?.length > 0">
            <p v-for="item in errMsgArray" :key="item">{{ item }}</p>
          </div>
          <div v-else>
            {{ cancelModalMessage }}
          </div>
        </div>
      </CModalBody>
      <CModalFooter>
        <template v-if="errMsgArray?.length > 0">
          <CButton
            @click="
              cancelConfirmModal = false;
              errMsgArray = [];
            "
            color="primary"
          >
            OK
          </CButton>
        </template>
        <template v-else>
          <CButton
            @click="
              cancelConfirmModal = false;
              deleteTargetLotNo = 0;
            "
            color="dark"
          >
            キャンセル
          </CButton>
          <CButton @click="lotDelete()" color="primary"> OK </CButton>
        </template>
      </CModalFooter>
    </CModal>
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="completeModal"
      @close="
        () => {
          completeModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <p>完了しました。</p>
      </CModalBody>
      <CModalFooter>
        <CButton @click="completeModal = false" color="primary" class="m-1">
          OK
        </CButton>
      </CModalFooter>
    </CModal>
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModalShow"
      @close="
        () => {
          errorModalShow = false;
          errMsgArray = [];
          errorStatus = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>エラー確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-for="text in errMsgArray" :key="text">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            (errorModalShow = false), (errMsgArray = []), (errorStatus = false)
          "
          color="dark"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '@/store/common';
  import {onBeforeMount, onMounted, ref, watch} from 'vue';
  import {useRoute, useRouter} from 'vue-router';
  import {useLotStore} from '../../store/lot';
  import CTableWrapper from './ExhibitionTable.vue';
  import {useAuthStore} from '@/store/auth';

  const authStore = useAuthStore();
  const router = useRouter();
  const route = useRoute();
  const store = useCommonStore();
  const {selectedExhibition, setSelectedExhibition} = useLotStore();

  const tableLoading = ref(false);
  const upsertLoading = ref(false);
  const optionsExhibition = ref([]);
  const completeModal = ref(false);
  const exhibitionStartFlag = ref(false);
  const exhibitionEndFlag = ref(false);
  const exhibitionLoading = ref(false);
  const cancelConfirmModal = ref(false);
  const cancelModalTitle = ref('');
  const cancelModalMessage = ref('');
  const deleteTargetLotNo = ref(0);
  const deleteMode = ref(0);
  // 検索項目
  const searchCondition = ref({
    selectExhibition: null,
    productName: null,
  });
  // Screen params
  const constantList = ref([]);
  const lotList = ref([]);
  const activePage = ref(1);
  const itemsPerPage = ref(10);
  const pages = ref(1);
  const itemsSorter = ref({asc: true, column: 'manage_no'});
  const errMsgArray = ref([]);
  // Counting
  const currentCount = ref('0');
  const totalCount = ref('0');

  const optionsCategory = ref([]);
  // 時間不整合モーダル
  const errorModalShow = ref(false);
  const prevRoute = ref(null);
  // Exhibition Info
  const exhibitionInfo = ref({});

  onBeforeMount(() => {
    prevRoute.value = router.options.history?.state?.back;
    // 入札会、商品情報からの遷移時のみ検索条件を保持する
    if (['/auctions', '/item'].some(x => prevRoute.value.includes(x))) {
      searchCondition.value = {
        ...searchCondition.value,
        selectExhibition: String(selectedExhibition),
      };
    } else {
      // それ以外は初期化
      store.set(['activePage', 1]);
      store.set(['itemsPerPage', 10]);
      store.set(['itemsSorter', {asc: true, column: 'manage_no'}]);
    }
  });

  watch(
    () => route.query?.page,
    page => {
      if (page) {
        activePage.value = Number(page);
      }
    }
  );

  watch(
    () => itemsPerPage.value,
    newVal => {
      if (lotList.value.length > newVal) {
        pages.value =
          Number.parseInt(lotList.value.length / newVal, 10) +
          (lotList.value.length % newVal > 0 ? 1 : 0);
      } else {
        pages.value = 1;
      }
    }
  );

  const getConstants = () => {
    return Methods.apiExecute('get-constants-by-keys', {
      key_strings: ['AUCTION_CLASSIFICATION', 'SEARCH_DATETIME_FROM'],
    }).then(response => {
      if (response.status === 200) {
        constantList.value = response.data || [];

        optionsCategory.value = ['全てのカテゴリ'];
        for (const data of constantList.value.filter(
          constantItem => constantItem.key_string === 'PRODUCT_CATEGORY'
        )) {
          optionsCategory.value.push({
            label: data.value2,
            value: data.value1,
          });
        }
      }
    });
  };

  const onChangeExhibition = val => {
    setSelectedExhibition(val);
  };

  const getSelectedExhibitionInfo = () => {
    const exh = optionsExhibition.value.find(
      x => String(x.value) === String(searchCondition.value.selectExhibition)
    );
    const bidItem = exh?.exhibition;
    if (bidItem) {
      const languageCode = authStore.user.language_code;
      const exhName = bidItem.localized_json_array.find(
        x => x.f1 === languageCode
      );
      const methodItem = constantList.value.find(
        x =>
          x.key_string === 'AUCTION_CLASSIFICATION' &&
          x.value1 ===
            String(
              bidItem.exhibition_classification_info.auctionClassification
            ) &&
          x.language_code === languageCode
      );
      const method = methodItem ? methodItem.value2 : '';
      // 入札時間
      const startDatetime = bidItem.start_datetime
        ? Methods.getFormatDateTime(bidItem.start_datetime)
        : Methods.getFormatDateTime(bidItem.preview_start_datetime);
      const endDatetime = bidItem.end_datetime
        ? Methods.getFormatDateTime(bidItem.end_datetime)
        : Methods.getFormatDateTime(bidItem.preview_end_datetime);
      const bidDateTime = `${startDatetime} ~ ${endDatetime}`;
      // 終了判定
      const isEnded = endDatetime
        ? Date.now() - Date.parse(endDatetime) >= 0
        : true;

      exhibitionStartFlag.value =
        Methods.getFormatDateTime(new Date()) >=
        Methods.getFormatDateTime(bidItem.start_datetime);
      exhibitionEndFlag.value =
        Methods.getFormatDateTime(new Date()) >=
        Methods.getFormatDateTime(bidItem.end_datetime);
      const finalExhibitionName = exhName ? exhName.f2 : '';
      exhibitionInfo.value = {
        lotNo: bidItem.lot_no,
        exhibitionNo: bidItem.exhibition_no,
        exhibitionName: finalExhibitionName,
        method,
        status: bidItem.status,
        bidDateTime,
        startDatetime: bidItem.start_datetime,
        isEnded,
      };
    } else {
      exhibitionInfo.value = {
        lotNo: null,
        exhibitionNo: null,
        exhibitionName: '',
        method: '',
        status: '',
        bidDateTime: '',
        startDatetime: '',
        isEnded: null,
      };
    }
  };

  const getExhibitions = () => {
    console.log('getExhibitions');
    exhibitionLoading.value = true;

    const params = {
      category: null,
      preview_start_datetime_from: null,
      preview_start_datetime_to: null,
      start_datetime_from: null,
      start_datetime_to: null,
      previewFromDateFlag: true,
      previewToDateFlag: true,
      bidFromDateFlag: true,
      bidToDateFlag: true,
    };
    return Methods.apiExecute('get-exhibitions', params)
      .then(response => {
        exhibitionLoading.value = false;
        if (response.status === 200) {
          optionsExhibition.value = (response.data || []).map(x => {
            const exhName =
              x.localized_json_array.find(
                y => y.f1 === authStore.user.language_code
              )?.f2 || '未設定';
            return {
              value: x.exhibition_no,
              label: exhName,
              start_datetime: x.start_datetime,
              end_datetime: x.end_datetime,
              exhibition: {
                ...x,
                exhibition_name: exhName,
              },
            };
          });
          if (selectedExhibition) {
            // 入札会が見つからない場合は空で設定する
            const fExh = optionsExhibition.value.find(
              x => x.value === selectedExhibition
            );
            searchCondition.value.selectExhibition = String(fExh?.value || '');
          } else {
            searchCondition.value.selectExhibition = String(
              optionsExhibition.value[0]?.value || ''
            );
          }
        }
        return Promise.resolve();
      })
      .catch(error => {
        console.log(error);
        exhibitionLoading.value = false;
        errorModalShow.value = true;
        Methods.parseHtmlResponseError(router, error);
        return Promise.reject(error);
      });
  };

  const getLots = async () => {
    console.log('getLots');
    tableLoading.value = true;
    lotList.value = [];

    // Get selected exhibition info
    getSelectedExhibitionInfo();

    // Get lot items
    const request = {
      exhibitionNo: searchCondition.value.selectExhibition,
      productName: searchCondition.value.productName,
      category: null,
    };
    // Request to server
    await Methods.apiExecute('get-lots', request)
      .then(response => {
        if (response.status === 200) {
          // Preparing data for displaying
          totalCount.value = Base.number2string(response.data.total_count || 0);
          currentCount.value = Base.number2string(
            response.data.current_count || 0
          );

          // 出展一覧の検索結果
          const lot_data = response.data.data;

          for (const row of lot_data) {
            row.productName = '';
            row.exhibition_name = '未設定';

            if (
              row.localized_json_array &&
              row.localized_json_array.length > 0
            ) {
              // F1 is language_code, f2 is included productName, brand, category
              const matchingEntry = row.localized_json_array.find(
                entry => entry.f1 === authStore.user.language_code
              );

              if (matchingEntry?.f2) {
                // If there's a matching entry, update the row values
                row.productName = matchingEntry.f2.productName || '';
                // Maker
                row.maker = matchingEntry.f2.maker || '';
                // Only update exhibition_name if there's a matching localized entry
                row.exhibition_name =
                  matchingEntry.f2.exhibition_name || row.exhibition_name;
              }
            }
            row.lowest_bid_price =
              row.lowest_bid_price > 0 ? row.lowest_bid_price : 0;
            row.lowest_bid_accept_price =
              row.lowest_bid_accept_price > 0 ? row.lowest_bid_accept_price : 0;
          }

          // 出展一覧に更新
          lotList.value = lot_data;
        }

        // Console.log('★★:',prevRoute.value.name)
        if (prevRoute.value === '商品情報') {
          itemsPerPage.value = store.itemsPerPage;
          itemsSorter.value = store.itemsSorter;
        }
        pages.value =
          Number.parseInt(lotList.value.length / itemsPerPage.value, 10) +
          (lotList.value.length % itemsPerPage.value > 0 ? 1 : 0);
        activePage.value =
          store?.activePage > pages.value
            ? Number(pages.value)
            : store?.activePage;
        router?.push({query: {page: activePage.value}}).catch(() => {});

        tableLoading.value = false;
      })
      .catch(error => {
        console.log(error);
        tableLoading.value = false;
        errorModalShow.value = true;
        errMsgArray.value = Methods.parseHtmlResponseError(router, error);
        Methods.parseHtmlResponseError(router, error);
        errMsgArray.value = Methods.parseHtmlResponseError(router, error);
      });
  };

  const pageChange = val => {
    store.set(['activePage', val]);
    router?.push({query: {page: val}}).catch(() => {});
  };

  const paginationChange = val => {
    itemsPerPage.value = val;
    store.set(['itemsPerPage', val]);
  };

  const sorterChange = val => {
    itemsSorter.value = val;
    store.set(['itemsSorter', val]);
    pageChange(1);
  };

  const getLotId = (prefix, lotId) => {
    console.log('getLotId:', prefix, lotId);
    if (prefix && lotId) {
      return prefix + lotId;
    }
    return null;
  };

  const getProductName = lotNo => {
    const lot = lotList.value.find(x => x.lot_no === lotNo);
    return lot ? lot.productName : '';
  };

  // Mode: 1 cancel, 2 delete, 3 republish
  const openLotDelete = (lotNo, mode) => {
    console.log('lotNo: ', lotNo, 'Mode: ', mode);
    deleteTargetLotNo.value = lotNo;
    deleteMode.value = mode;

    cancelModalTitle.value = '';
    cancelModalMessage.value = '';
    switch (deleteMode.value) {
      case 1:
        cancelModalTitle.value = '出品停止確認';
        cancelModalMessage.value = `商品名:${getProductName(deleteTargetLotNo.value)}を出品停止にしてもよろしいですか？`;
        break;
      case 2:
        cancelModalTitle.value = '出品削除確認';
        cancelModalMessage.value = `商品名:${getProductName(deleteTargetLotNo.value)}を出品削除にしてもよろしいですか？`;
        break;
      case 3:
        cancelModalTitle.value = '出品停止取消確認';
        cancelModalMessage.value = `商品名:${getProductName(deleteTargetLotNo.value)}を出品に戻してもよろしいですか？`;
        break;
      default:
        break;
    }
    cancelConfirmModal.value = true;
  };

  const lotDelete = () => {
    upsertLoading.value = true;
    errMsgArray.value = [];
    const request = {
      exhibition_no: searchCondition.value.selectExhibition,
      lot_no: deleteTargetLotNo.value,
    };
    console.log('request: ', request);

    let api_name = '';
    switch (deleteMode.value) {
      case 1:
        api_name = 'cancel-exhibition-item';
        break;
      case 2:
        api_name = 'delete-exhibition-item';
        break;
      case 3:
        api_name = 'republish-exhibition-item';
        break;
      default:
        break;
    }

    Methods.apiExecute(api_name, request)
      .then(async () => {
        upsertLoading.value = false;
        cancelConfirmModal.value = false;
        completeModal.value = true;
        tableLoading.value = false;
        await getLots();
      })
      .catch(error => {
        upsertLoading.value = false;
        console.log(error.response);
        errMsgArray.value = Methods.parseHtmlResponseError(router, error);
      });
  };

  onMounted(() => {
    getConstants()
      .then(() => {
        return getExhibitions();
      })
      .then(() => {
        if (searchCondition.value.selectExhibition) {
          getLots();
        }
      });
  });
</script>
