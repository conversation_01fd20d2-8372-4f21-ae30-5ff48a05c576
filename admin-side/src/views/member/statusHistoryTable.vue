<template>
  <div>
    <CDataTable
      :loading="loading"
      :hover="hover"
      :striped="striped"
      :border="border"
      :small="small"
      :fixed="fixed"
      :items="items"
      :fields="fields"
      :items-per-page="small ? 10 : 5"
      :dark="dark"
      :sorter="true"
      :sorterValue="itemsSorter"
      :pagination="{doubleArrows: false, align: 'center'}"
      @update:sorter-value="sorterChange"
    />
  </div>
</template>

<script>
  import CDataTable from '@/components/Table/CDataTable.vue';

  export default {
    name: 'statusHistoryTable',
    components: {
      CDataTable,
    },
    props: {
      items: Array,
      fields: {
        type: Array,
        default() {
          return [
            {
              key: 'create_datetime',
              label: '変更日時',
              _classes: 'text-center',
            },
            {
              key: 'user_name',
              label: 'ステータス設定者',
              _style: 'text-align: center',
            },
            {
              key: 'before_status_name',
              label: '変更前ステータス',
              _style: 'text-align: center',
            },
            {
              key: 'after_status_name',
              label: '変更後ステータス',
              _style: 'text-align: center',
            },
          ];
        },
      },
      hover: Boolean,
      striped: Boolean,
      border: Boolean,
      small: Boolean,
      fixed: Boolean,
      dark: Boolean,
      loading: Boolean,
    },
    data() {
      return {
        itemsSorter: {asc: false, column: 'create_datetime'},
      };
    },
    methods: {
      sorterChange(val) {
        this.itemsSorter = val;
      },
    },
  };
</script>
