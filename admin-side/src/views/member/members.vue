<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>検索条件</strong>
      </CCardHeader>
      <CCardBody>
        <CForm class="members-card-list" onsubmit="return false;">
          <CRow class="mb-3">
            <CCol sm="2"> 会社名 </CCol>
            <CCol sm="4">
              <CFormInput
                name="companyName"
                v-model="search_condition.companyName"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 会員ID </CCol>
            <CCol sm="4">
              <CFormInput name="memberId" v-model="search_condition.memberId" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 取引先コード </CCol>
            <CCol sm="4">
              <CFormInput
                name="customerCode"
                v-model="search_condition.customerCode"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2">
              <label>ステータス</label>
            </CCol>
            <CCol sm="9">
              <CFormCheck
                v-for="option in options_status"
                :key="option.value"
                :id="option.value"
                :value="option.value"
                :label="option.label"
                v-model="search_condition.memberStatus"
                inline
              />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CRow class="align-items-center">
          <CCol md="5" class="mb-3 mb-xl-0 text-right"></CCol>
          <CCol md="2" class="mb-3 mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="handleSearch" block>
              検索
            </CButton>
          </CCol>
          <CCol md="1" />
          <CCol
            md="2"
            class="mb-3 mb-xl-0 text-right d-grid"
            style="padding-left: 0px"
          >
            <CButton
              size="sm"
              class="btn-bounce-email"
              @click="csvDownload"
              block
            >
              <span class="mx-2">エクセル</span>
              <CIcon name="download" />
            </CButton>
          </CCol>
          <CCol
            md="2"
            class="mb-3 mb-xl-0 text-right d-grid"
            style="padding-left: 0px"
          >
            <label
              v-if="!isReadOnly"
              class="btn btn-success btn-sm btn-block"
              style="margin-bottom: 0"
            >
              <span class="mx-2">エクセル</span>
              <CIcon name="upload" />
              <input
                type="file"
                accept=".xlsx"
                ref="fileImport"
                @change="csvUpload"
                hidden
              />
            </label>
          </CCol>
        </CRow>
      </CCardFooter>
    </CCard>
    <CRow class="mt-3 mb-3">
      <CCol sm="12">
        <MemberTable
          name="memberList"
          :items="memberList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          caption="会員一覧"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
          @changeStatus="onChangeStatus"
          @onViewStatusHistory="onViewStatusHistory"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="statusChangeModal"
      color="warning"
      @close="
        () => {
          statusChangeModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>
          {{ statusChangeModalTitle }}
        </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loadingChangeStatus && validateResult.length === 0">
          ステータスを変更してもよろしいですか？
        </div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loadingChangeStatus"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          v-if="validateResult.length === 0"
          @click="closeStatusChangeModal"
          color="dark"
          :disabled="loadingChangeStatus"
          >キャンセル</CButton
        >
        <CButton
          @click="changeStatus"
          color="primary"
          :disabled="loadingChangeStatus"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="statusHistoryModal"
      size="lg"
      @close="
        () => {
          statusHistoryModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> ステータス変更履歴 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div class="mb-9">
          <CHistoryTableWrapper
            :items="statusHistories"
            :loading="loadingStatusHistory"
            hover
            striped
            border
            small
            fixed
          />
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="closeStatusHistoryModal" color="dark">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="csvModal"
      @close="
        () => {
          csvModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> 確認 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loadingCsv"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton v-if="!loadingCsv" @click="closeCsvModal" color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import UploadFile from '@/api/uploadFileToS3';
  import Base from '@/common/base';
  import ScaleLoader from '@/components/Table/ScaleLoader.vue';
  import {useCommonStore} from '@/store/common';
  import useFileDownload from '../../common/useFileDownload';
  import MemberTable from '../../components/member/list/memberTable.vue';
  import CHistoryTableWrapper from './statusHistoryTable.vue';
  import {useAuthStore} from '@/store/auth';

  export default {
    name: 'members',
    components: {
      MemberTable,
      CHistoryTableWrapper,
      ScaleLoader,
    },
    setup() {
      const store = useCommonStore();
      const {download} = useFileDownload();
      const {isReadOnly} = useAuthStore();
      return {store, download, isReadOnly};
    },
    data() {
      return {
        // 検索条件
        loading: true,
        options_status: [],
        options_status_name: {},

        // ステータス変更用変数
        loadingChangeStatus: false,
        statusChangeModal: false,
        statusChangeModalTitle: '確認',

        // ステータス履歴履歴
        statusHistoryModal: false,
        loadingStatusHistory: false,
        statusHistories: [],

        // CSV用
        loadingCsv: false,
        csvModal: false,

        // Screen params
        memberList: [],
        display_code: 1,
        search_condition: {
          companyName: '',
          memberId: '',
          customerCode: '',
          memberStatus: [],
        },
        activePage: 1,
        itemsPerPage: 10,
        pages: 1,
        itemsSorter: {asc: false, column: 'create_datetime'},

        // Counting
        current_count: 0,
        total_count: 0,

        validateResult: [],
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;
        // 登録・編集画面からの遷移時のみ検索条件を保持する
        if (vm.prevRoute.name === '会員編集') {
          vm.search_condition = vm.store.membersSearchCondition || {
            companyName: '',
            memberId: '',
            customerCode: '',
            memberStatus: [],
          };
        } else {
          // それ以外は初期化
          vm.store.set([
            'membersSearchCondition',
            {
              companyName: '',
              memberId: '',
              customerCode: '',
              memberStatus: [],
            },
          ]);
          vm.store.set(['activePage', 1]);
          vm.store.set(['itemsPerPage', 10]);
          vm.store.set([
            'itemsSorter',
            {asc: false, column: 'create_datetime'},
          ]);
        }
      });
    },
    mounted() {
      this.getConstantsData()
        .then(() => {
          this.getMemberRequestData();
        })
        .catch(error => {
          console.log(JSON.stringify(error));
          this.loading = false;
          Methods.parseHtmlResponseError(this.$router, error);
        });
    },
    watch: {
      $route: {
        immediate: true,
        handler(route) {
          if (route.query?.page) {
            this.activePage = Number(route.query.page);
          }
        },
      },
      search_condition: {
        handler(newVal) {
          this.store.set(['membersSearchCondition', newVal]);
        },
        deep: true,
        immediate: false,
      },
      itemsPerPage(newVal) {
        if (this.memberList.length > newVal) {
          this.pages =
            Number.parseInt(this.memberList.length / newVal, 10) +
            (this.memberList.length % newVal > 0 ? 1 : 0);
        } else {
          this.pages = 1;
        }
      },
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
    },
    methods: {
      getConstantsData() {
        const params = {
          key_strings: ['MEMBER_STATUS_CODE'],
        };
        // Request to server
        return Methods.apiExecute('get-constants-by-keys', params).then(
          response => {
            if (response.status === 200) {
              const constants = response.data;
              this.options_status = [];
              this.options_status_name = {};

              for (let i = 0; i < constants.length; i++) {
                if (constants[i].key_string === 'MEMBER_STATUS_CODE') {
                  this.options_status.push({
                    value: constants[i].value1,
                    label: constants[i].value2,
                  });
                  this.options_status_name[constants[i].value1] =
                    constants[i].value2;
                }
              }
            }
            return Promise.resolve();
          }
        );
      },
      getMemberRequestData() {
        this.getMembersFromServer(this.search_condition)
          .then(members => {
            this.memberList = members;
            // 登録・編集画面からの遷移時は閲覧してたページに戻す
            if (this.prevRoute && this.prevRoute.name === '会員編集') {
              this.itemsPerPage = this.store.itemsPerPage;
              this.itemsSorter = this.store.itemsSorter;
            }
            this.pages =
              Number.parseInt(this.memberList.length / this.itemsPerPage, 10) +
              (this.memberList.length % this.itemsPerPage > 0 ? 1 : 0);
            this.activePage =
              this.store.activePage > this.pages
                ? Number(this.pages)
                : this.store.activePage;
            this.$router.push({query: {page: this.activePage}}).catch(() => {});
            this.loading = false;
          })
          .catch(error => {
            this.loading = false;
            Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      handleSearch() {
        this.getMembersFromServer(this.search_condition)
          .then(members => {
            this.memberList = members;

            // 登録・編集画面からの遷移時は閲覧してたページに戻す
            if (this.prevRoute && this.prevRoute.name === '会員編集') {
              this.itemsPerPage = this.store.itemsPerPage;
              this.itemsSorter = this.store.itemsSorter;
            }
            this.pages =
              Number.parseInt(this.memberList.length / this.itemsPerPage, 10) +
              (this.memberList.length % this.itemsPerPage > 0 ? 1 : 0);
            this.activePage =
              this.store.activePage > this.pages
                ? Number(this.pages)
                : this.store.activePage;
            this.$router.push({query: {page: this.activePage}}).catch(() => {});
          })
          .catch(error => {
            this.loading = false;
            Methods.parseHtmlResponseError(this.$router, error);
          });
      },

      getMembersFromServer(params) {
        this.total_count = 0;
        this.current_count = 0;

        // Request to server
        return Methods.apiExecute('get-members', params).then(response => {
          this.loading = false;
          const memberList = (response.data.data || []).map(x => {
            return {
              ...x,
              ...x.free_field,
              status_name: this.options_status_name[x.status],
            };
          });
          this.total_count = response.data ? response.data.total_count || 0 : 0;
          this.current_count = response.data
            ? response.data.current_count || 0
            : 0;
          console.log('memberList = ', memberList);
          return Promise.resolve(memberList ?? []);
        });
      },
      changeStatus() {
        console.log('changeStatus');

        if (this.validateResult.length > 0) {
          this.loadingChangeStatus = false;
          this.statusChangeModal = false;
          return;
        }
        this.loadingChangeStatus = true;

        if (this.statusChangeData === null) {
          this.closeStatusChangeModal();
          return;
        }

        const item = this.statusChangeData.item;
        const params = {
          member_no: item.member_no,
          member_request_no: item.member_request_no,
          status: this.statusChangeData.status,
          tanto: '',
        };
        console.log(`params = ${JSON.stringify(params)}`);

        Methods.apiExecute('update-member-status', params)
          .then(response => {
            if (response.status === 200) {
              this.loadingChangeStatus = false;
              this.closeStatusChangeModal();
              const result = response.data;
              console.log(`result = ${JSON.stringify(result)}`);
              this.handleSearch();
            }
          })
          .catch(error => {
            this.loadingChangeStatus = false;
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            );
            if (this.validateResult.length === 0) {
              this.closeStatusChangeModal();
            } else {
              this.statusChangeModalTitle = '入力エラー';
            }
          });
      },
      onChangeStatus(data) {
        console.log(`onChangeStatus: ${JSON.stringify(data)}`);
        this.statusChangeData = data;
        this.statusChangeModal = true;
        this.validateResult = [];
        this.statusChangeModalTitle = '確認';
      },
      closeStatusChangeModal() {
        console.log('closeStatusChangeModal');
        this.statusChangeModal = false;
        this.statusChangeData = null;
        this.loadingChangeStatus = false;
      },
      onViewStatusHistory(data) {
        console.log('onViewStatusHistory');
        console.log(`data = ${JSON.stringify(data)}`);

        this.statusHistoryModal = true;
        this.loadingStatusHistory = true;

        // Request to server
        this.getStatusHistoryFromServer(data.member_request_no)
          .then(() => {
            this.loadingStatusHistory = false;
          })
          .catch(error => {
            Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      closeStatusHistoryModal() {
        console.log('closeStatusHistoryModal');
        this.statusHistoryModal = false;
        this.statusHistories = [];
      },
      getStatusHistoryFromServer(member_request_no) {
        const params = {
          member_request_no,
        };
        return Methods.apiExecute('get-member-status-history', params).then(
          response => {
            if (response.status === 200) {
              const result = response.data;
              this.statusHistories = result.map(item => {
                return {
                  create_datetime: item.create_datetime,
                  user_name: item.user_name,
                  before_status: item.before_status,
                  after_status: item.after_status,
                  before_status_name: item.before_status
                    ? this.options_status_name[item.before_status]
                    : '',
                  after_status_name: item.after_status
                    ? this.options_status_name[item.after_status]
                    : '',
                };
              });
              console.log(
                `statusHistories = ${JSON.stringify(this.statusHistories)}`
              );
            }
            return Promise.resolve();
          }
        );
      },
      pageChange(val) {
        this.store.set(['activePage', val]);
        this.$router.push({query: {page: val}}).catch(() => {});
      },
      paginationChange(val) {
        this.itemsPerPage = val;
        this.store.set(['itemsPerPage', val]);
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
        this.pageChange(1);
      },
      csvDownload() {
        console.log('csvDownload');

        this.csvModal = true;
        this.loadingCsv = true;
        this.validateResult = [];

        this.search_condition.sorter = this.itemsSorter;

        // Request to server
        Methods.apiExecute('export-member-csv-file', this.search_condition)
          .then(response => {
            if (response.status === 200) {
              const csv = response.data;
              console.log(`csv = ${JSON.stringify(csv)}`);
              this.download(csv.url);
            }
            this.loadingCsv = false;
            this.csvModal = false;
          })
          .catch(error => {
            this.loadingCsv = false;
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            );
          });
      },
      csvUpload(event) {
        console.log('csvUpload');

        const fileInfo =
          event.target.files.length > 0 ? event.target.files[0] : null;
        event.target.value = null;

        if (typeof fileInfo === 'undefined' || fileInfo === null) {
          return;
        }

        // Show modal
        this.csvModal = true;
        this.loadingCsv = true;
        this.validateResult = [];

        // Upload to s3
        const api_type = 'csv-upload';
        UploadFile.upload(api_type, fileInfo, data => {
          console.log(`data. ${JSON.stringify(data)}`);
          if (data.status === 200) {
            const s3url = data.message;
            // Request to import member api
            Methods.apiExecute('import-member-csv-file', {url: s3url})
              .then(response => {
                console.log(
                  `import-member-csv-file = ${JSON.stringify(response)}`
                );
                this.loadingCsv = false;
                this.validateResult = ['処理が完了しました。'];
              })
              .catch(error => {
                this.loadingCsv = false;
                console.log(JSON.stringify(error));
                this.validateResult = Methods.parseHtmlResponseError(
                  this.$router,
                  error
                );
              });
          }
        });
      },
      closeCsvModal() {
        console.log('closeCsvModal');
        this.csvModal = false;
        this.handleSearch();
      },
    },
  };
</script>
