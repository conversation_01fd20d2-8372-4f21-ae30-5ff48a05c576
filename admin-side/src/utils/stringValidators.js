/**
 * Check if the string has uppercase characters
 * @param {string} value - The string to check
 * @returns {boolean} - True if the string has uppercase characters, false otherwise
 */
export const hasUppercase = value => {
  return /[A-Z]/.test(value);
};

/**
 * Check if the string has lowercase characters
 * @param {string} value - The string to check
 * @returns {boolean} - True if the string has lowercase characters, false otherwise
 */
export const hasLowercase = value => {
  return /[a-z]/.test(value);
};

/**
 * Check if the string has digits
 * @param {string} value - The string to check
 * @returns {boolean} - True if the string has digits, false otherwise
 */
export const hasDigit = value => {
  return /\d/.test(value);
};
