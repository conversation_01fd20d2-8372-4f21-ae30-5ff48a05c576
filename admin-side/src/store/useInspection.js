import Base from '@/common/base';
import router from '@/router/index';
import {subComma} from '@/views/common/commonFilters';
import cloneDeep from 'lodash-es/cloneDeep';
import moment from 'moment';
import {defineStore} from 'pinia';
import Methods from '../api/methods';

const initItemFreeField = {
  estimatePrice: null,
  authLetterFilePath: [],
  identityFilePath: [],
  documentFilePath: [],
  identity_file: null,
  document_file: null,
  maker: null,
  bike_type: null,
  bike_type_other: null,
  first_inspection: null,
  first_inspection_other: null,
  inspec_expire: null,
  inspec_expire_other: null,
  fuel_type: null,
  meter_speed: null,
  meter_speed_other: null,
  meter_taco: null,
  meter_taco_other: null,
  horn: null,
  horn_other: null,
  headlight_hi: null,
  headlight_hi_other: null,
  headlight_lo: null,
  headlight_lo_other: null,
  winker_fl: null,
  winker_fl_other: null,
  winker_fr: null,
  winker_fr_other: null,
  winker_rl: null,
  winker_rl_other: null,
  winker_rr: null,
  winker_rr_other: null,
  tail_light_small: null,
  tail_light_small_other: null,
  tail_light_break: null,
  tail_light_break_other: null,
  battery_health: null,
  battery_health_other: null,
  radiator_leak: null,
  radiator_leak_other: null,
  engine_white_smoke: null,
  engine_white_smoke_other: null,
  engine_noise: null,
  engine_noise_other: null,
  front_fork_strain: null,
  front_fork_strain_other: null,
  front_fork_rusty: null,
  front_fork_rusty_other: null,
  front_fork_oil_leak: null,
  front_fork_oil_leak_other: null,
  handle: null,
  handle_other: null,
  front_break: null,
  front_break_other: null,
  rear_break: null,
  rear_break_other: null,
  engine_condition: null,
  engine_condition_other: null,
  tire_condition: null,
  tire_condition_other: null,
  modification: null,
  modification_other: null,
  scratch: null,
  scratch_other: null,
  other_memo: null,
  other_memo_other: null,
  model: null,
  model_other: null,
  frame_no: null,
  frame_no_other: null,
  engine_type: null,
  engine_type_other: null,
  mileage: null,
  mileage_other: null,
  color: null,
  color_other: null,
  cubic_capacity: null,
  cubic_capacity_other: null,
  engine_serial: null,
  engine_serial_other: null,
};

const useInspection = defineStore('useInspection', {
  state: () => {
    return {
      loading: false,
      errors: [],

      // Screen params
      requestDetailData: {
        item_free_field: cloneDeep(initItemFreeField),
        member_free_field: {},
      },
      itemImages: [],
      optionalImages: [],
      documentFiles: [],
      detailRequestNo: 0,
      bodyTypes: {},

      approvalModal: false,
      rejectedModal: false,
      updateModal: false,
      changeFlag: false,
      errMsgArray: [],
      modalLoading: false,
      requestResultData: {
        assessment_amount: null,
        comment: null,
        assessment_expired: null,
        method_selection_expired: null,
        agency_expired: null,
      },
      cancelModal: false,
      constants: {},

      // Data for change checking
      origRequestData: null,
    };
  },
  actions: {
    reset() {
      this.loading = false;
      this.errors = [];

      // Screen params
      this.requestDetailData = {
        item_free_field: cloneDeep(initItemFreeField),
        member_free_field: {},
      };
      this.itemImages = [];
      this.optionalImages = [];
      this.detailRequestNo = 0;
      this.bodyTypes = {};

      this.approvalModal = false;
      this.rejectedModal = false;
      this.updateModal = false;
      this.changeFlag = false;
      this.errMsgArray = [];
      this.modalLoading = false;
      this.requestResultData = cloneDeep({
        assessment_amount: null,
        comment: null,
        assessment_expired: null,
        method_selection_expired: null,
        agency_expired: null,
      });
      this.cancelModal = false;
      this.constants = {};
    },
    getConstants() {
      this.constants = {};

      // Item request images
      this.constants.itemImages = [
        {
          id: 1,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '左',
        },
        {
          id: 2,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '右',
        },
        {
          id: 3,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '前',
        },
        {
          id: 4,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '後',
        },
        {
          id: 5,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'メーター',
        },
        {
          id: 6,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'タンク中',
        },
        {
          id: 7,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'エンジン左',
        },
        {
          id: 8,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'エンジン右',
        },
        {
          id: 9,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'エンジン前',
        },
        {
          id: 10,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'エンジン番号',
        },
        {
          id: 11,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'シート',
        },
        {
          id: 12,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '前方ホイール左',
        },
        {
          id: 13,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '前方ホイール右',
        },
        {
          id: 14,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '前方タイヤ',
        },
        {
          id: 16,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '後方ホイール左',
        },
        {
          id: 17,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '後方ホイール右',
        },
        {
          id: 15,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: '後方タイヤ',
        },
        {
          id: 19,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'リアサスペンション左',
        },
        {
          id: 18,
          title: '',
          fileName: null,
          key: null,
          preview: null,
          label: 'リアサスペンション右',
        },
      ];

      // Item optional images
      this.constants.optionalImages = [
        {id: 1, title: '', fileName: null, key: null, preview: null},
        {id: 2, title: '', fileName: null, key: null, preview: null},
        {id: 3, title: '', fileName: null, key: null, preview: null},
        {id: 4, title: '', fileName: null, key: null, preview: null},
        {id: 5, title: '', fileName: null, key: null, preview: null},
        {id: 6, title: '', fileName: null, key: null, preview: null},
        {id: 7, title: '', fileName: null, key: null, preview: null},
        {id: 8, title: '', fileName: null, key: null, preview: null},
        {id: 9, title: '', fileName: null, key: null, preview: null},
        {id: 10, title: '', fileName: null, key: null, preview: null},
      ];

      const params = {
        key_strings: [
          'ASSESSMENT_EXPIRED',
          'METHOD_SELECTION_EXPIRED',
          'AGENCY_EXPIRED',
          'DOCUMENT_FILES',
          'SELECT_YES_NO',
          'SELECT_FUEL_TYPE',
          'SELECT_BATTERY_HEALTH',
          'SELECT_RADIATOR_LEAK',
          'SELECT_ARI_NASI',
          'SELECT_TEXT_AREA',
          'SELECT_BODY_TYPE',
          'SELECT_MAKER',
          'SELECT_IDENTITY_FILE',
          'SELECT_INPUT_OPTION',
          'SELECT_FUMEI_NASI',
          'SELECT_YES_NO_TACO',
        ],
      };
      // Request to server
      return Methods.apiExecute('get-constants-by-keys', params).then(
        response => {
          if (response.status === 200) {
            for (const row of response.data) {
              const now = new Date();
              switch (row.key_string) {
                case 'ASSESSMENT_EXPIRED':
                  now.setDate(now.getDate() + Number(row.value1));
                  this.requestResultData.assessmentExpired =
                    moment(now).format('YYYY-MM-DD');
                  break;
                case 'METHOD_SELECTION_EXPIRED':
                  now.setDate(now.getDate() + Number(row.value1));
                  this.requestResultData.methodSelectionExpired =
                    moment(now).format('YYYY-MM-DD');
                  break;
                case 'AGENCY_EXPIRED':
                  now.setDate(now.getDate() + Number(row.value1));
                  this.requestResultData.agencyExpired =
                    moment(now).format('YYYY-MM-DD');
                  break;
                case 'DOCUMENT_FILES':
                case 'SELECT_YES_NO':
                case 'SELECT_FUEL_TYPE':
                case 'SELECT_BATTERY_HEALTH':
                case 'SELECT_RADIATOR_LEAK':
                case 'SELECT_ARI_NASI':
                case 'SELECT_TEXT_AREA':
                case 'SELECT_BODY_TYPE':
                case 'SELECT_MAKER':
                case 'SELECT_IDENTITY_FILE':
                case 'SELECT_INPUT_OPTION':
                case 'SELECT_FUMEI_NASI':
                case 'SELECT_YES_NO_TACO':
                  if (!this.constants[row.key_string]) {
                    this.constants[row.key_string] = [];
                  }
                  this.constants[row.key_string].push({
                    value: row.value1,
                    label: row.value2,
                    isTextInput: String(row.value3) === '1',
                  });
                  break;
                default:
                  break;
              }
            }

            // Body types
            if (this.constants.SELECT_BODY_TYPE) {
              this.bodyTypes = JSON.parse(
                `{${this.constants.SELECT_BODY_TYPE.map(x => `"${x.value}":false`).join(',')}}`
              );
            }
          }
          return Promise.resolve();
        }
      );
    },
    getRequestData(id) {
      this.loading = true;
      this.detailRequestNo = id;
      const search_condition = {
        detail_request_no: this.detailRequestNo,
      };
      console.log('search_condition:', search_condition);
      // Request to server
      return Methods.apiExecute('get-request-detail', search_condition).then(
        response => {
          if (response.status === 200) {
            const requestData = response.data;

            if (typeof requestData === 'undefined' || requestData === null) {
              return Promise.resolve(null);
            } else {
              return Promise.resolve(requestData);
            }
          }
          return Promise.resolve(null);
        }
      );
    },
    register(btnStatus) {
      this.modalLoading = true;
      this.errMsgArray = [];

      const params = {
        request_no: this.requestDetailData.request_no,
        btnStatus,
        detail_request_no: this.detailRequestNo,
        item_free_field: Object.assign(
          {},
          this.requestDetailData.item_free_field,
          {
            body_types: Object.keys(this.bodyTypes).filter(
              x => this.bodyTypes[x]
            ),
            maker: this.requestDetailData.item_free_field.maker
              ? Number(this.requestDetailData.item_free_field.maker)
              : null,
            fuel_type: this.requestDetailData.item_free_field.fuel_type
              ? Number(this.requestDetailData.item_free_field.fuel_type)
              : null,
          }
        ),
        request_category_id: this.requestDetailData.request_category_id,
        assessment_amount: subComma(this.requestResultData.assessment_amount),
        comment: this.requestResultData.comment,
        assessmentExpired:
          this.requestDetailData.request_category_id === '1' && btnStatus === 1
            ? this.requestResultData.assessmentExpired
            : this.requestDetailData.request_category_id !== '5' &&
                btnStatus === 2
              ? this.requestDetailData.assessment_expired
              : null,
        methodSelectionExpired:
          this.requestDetailData.request_category_id === '1' && btnStatus === 1
            ? this.requestResultData.methodSelectionExpired
            : null,
        agencyExpired:
          this.requestDetailData.request_category_id === '3' && btnStatus === 1
            ? this.requestResultData.agencyExpired
            : null,
        exhibition_item_no:
          this.requestDetailData.request_category_id === '5'
            ? this.requestDetailData.exhibition_item_no
            : null,
        ship_address:
          this.requestDetailData.request_category_id === '5' && btnStatus === 1
            ? this.requestDetailData.member_free_field.ship_address
            : null,
      };
      console.log('params:', params);
      Methods.apiExecute('regist-request-result', params)
        .then(response => {
          // Validation error results
          if (response.status === 400) {
            this.errMsgArray.push(response.message);
            this.modalLoading = false;
          } else {
            this.modalLoading = false;
            this.approvalModal = false;

            // Update original data for change-checking
            this.updateOrigData();

            router.push({path: '/requests'});
          }
        })
        .catch(error => {
          this.modalLoading = false;
          console.log(JSON.stringify(error));
          this.errMsgArray = Methods.parseHtmlResponseError(router, error)[0];
        });
    },
    updateExpired() {
      this.modalLoading = true;
      this.errMsgArray = [];

      const params = {
        detail_request_no: this.detailRequestNo,
        assessmentExpired:
          this.requestDetailData.request_category_id === '1'
            ? this.requestDetailData.assessment_expired
            : null,
        methodSelectionExpired:
          this.requestDetailData.request_category_id === '1'
            ? this.requestDetailData.method_selection_expired
            : null,
        agencyExpired:
          this.requestDetailData.request_category_id === '1'
            ? this.requestDetailData.agency_expired
            : null,
      };
      console.log('params:', params);
      Methods.apiExecute('update-request-expired', params)
        .then(response => {
          // Validation error results
          if (response.status === 400) {
            this.errMsgArray.push(response.message);
            this.modalLoading = false;
          } else {
            this.modalLoading = false;
            this.updateModal = false;

            // Update original data for change-checking
            this.updateOrigData();

            router.push({path: '/requests'});
          }
        })
        .catch(error => {
          this.modalLoading = false;
          console.log(JSON.stringify(error));
          this.errMsgArray = Methods.parseHtmlResponseError(router, error)[0];
        });
    },
    onChangeModelValue({val, isTextInput}, column) {
      console.log('val, isTextInput: ', val, isTextInput);
      this.errors[column] = null;
      this.requestDetailData.item_free_field[column] = Number(val);
      if (!isTextInput) {
        this.requestDetailData.item_free_field[`${column}_other`] = null;
      }
    },
    onMounted(itemRequestId) {
      this.reset();
      this.getConstants()
        .then(() => {
          this.getRequestData(itemRequestId).then(request => {
            this.requestDetailData = Object.assign({}, request, {
              item_free_field: Object.assign(
                {},
                initItemFreeField,
                request.item_free_field
              ),
            });

            // Get item request images
            const dbImages = request.itemRequestFiles;
            if (dbImages) {
              this.itemImages = this.constants.itemImages.map(x => {
                const img =
                  dbImages.find(y => {
                    return (
                      String(y.division) === '1' &&
                      String(y.physical_name) === String(x.id)
                    );
                  }) || {};
                return {
                  ...x,
                  title: img.file_title,
                  key: img.file_path,
                  file_path: img.filePath
                    ? this.$options.filters.getFullImagePath(img.filePath)
                    : null,
                };
              });
              // Saved optional images
              this.optionalImages = this.constants.optionalImages.map(x => {
                const img =
                  dbImages.find(y => {
                    return (
                      String(y.division) === '2' &&
                      String(y.physical_name) === String(x.id)
                    );
                  }) || {};
                return {
                  ...x,
                  title: img.file_title,
                  key: img.file_path,
                  file_path: img.filePath
                    ? this.$options.filters.getFullImagePath(img.filePath)
                    : null,
                };
              });
            }
            console.log('itemImages: ', this.itemImages);

            // Body types
            if (this.requestDetailData.item_free_field.body_types) {
              const tmpBdTyp = this.constants.SELECT_BODY_TYPE.map(x => {
                const typ =
                  this.requestDetailData.item_free_field.body_types.find(
                    y => String(y) === String(x.value)
                  );
                if (typ) {
                  return `"${x.value}":true`;
                }
                return `"${x.value}":false`;
              });
              this.bodyTypes = JSON.parse(`{${tmpBdTyp.join(',')}}`);
            }

            // Maker
            if (this.requestDetailData.item_free_field.maker) {
              this.requestDetailData.item_free_field.maker = String(
                this.requestDetailData.item_free_field.maker
              );
            }

            // Fuel type
            if (this.requestDetailData.item_free_field.fuel_type) {
              this.requestDetailData.item_free_field.fuel_type = String(
                this.requestDetailData.item_free_field.fuel_type
              );
            }

            // Document files
            if (this.requestDetailData.item_free_field.documentFilePath) {
              this.documentFiles = this.constants.DOCUMENT_FILES.map(x => {
                const doc =
                  this.requestDetailData.item_free_field.documentFilePath.find(
                    y => String(y.id) === String(x.value)
                  ) || {};
                return {
                  id: doc.id,
                  label: x.label,
                  key: doc.key,
                };
              });
            }

            // Update original data for change-checking
            this.updateOrigData();

            this.loading = false;
          });
        })
        .catch(error => {
          console.log(error);
          this.loading = false;
          Methods.parseHtmlResponseError(this.$router, error);
        });
    },
    updateOrigData() {
      this.origRequestData = cloneDeep({
        item_free_field: this.requestDetailData.item_free_field,
        assessment_amount: this.requestResultData.assessment_amount,
        comment: this.requestResultData.comment,
        assessment_expired: this.requestResultData.assessment_expired,
        method_selection_expired:
          this.requestResultData.method_selection_expired,
        agency_expired: this.requestResultData.agency_expired,
      });
    },
    isDataChanged() {
      const current = cloneDeep({
        item_free_field: this.requestDetailData.item_free_field,
        assessment_amount: this.requestResultData.assessment_amount,
        comment: this.requestResultData.comment,
        assessment_expired: this.requestResultData.assessment_expired,
        method_selection_expired:
          this.requestResultData.method_selection_expired,
        agency_expired: this.requestResultData.agency_expired,
      });
      if (Base.objectsAreIdentical(this.origRequestData, current)) {
        return false;
      } else {
        return true;
      }
    },
  },
  getters: {},
});

export default useInspection;
