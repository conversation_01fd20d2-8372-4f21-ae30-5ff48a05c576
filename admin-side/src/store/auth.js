import {defineStore} from 'pinia';
import {ref, computed} from 'vue';
import {useRouter} from 'vue-router';
import {
  signIn,
  signOut,
  confirmSignIn,
  fetchAuthSession as amplifyFetchAuthSession,
} from '@aws-amplify/auth';
import {Hub} from 'aws-amplify/utils';
import {jwtDecode} from 'jwt-decode';

// Translates Cognito error objects into ja messages.
const errorToMessage = error => {
  if (!error) return '不明なエラーが発生しました。';
  // Check message content first for specific cases
  if (error.message && error.message.includes('User is disabled.')) {
    return 'このアカウントは無効化されています。';
  }

  // Then check error name
  switch (error.name) {
    case 'UserNotConfirmedException':
      return 'このアカウントは有効性が検証されていません。';
    case 'UserNotFoundException':
      return 'メールアドレス、またはパスワードが間違っています。';
    case 'NotAuthorizedException':
      return 'メールアドレス、またはパスワードが間違っています。';
    case 'UserAlreadyAuthenticatedException':
      return '既にログインしています。';
    case 'LimitExceededException':
      return '試行回数が制限を超えました。しばらくしてから再度お試しください。';
    case 'InvalidPasswordException':
      return 'パスワードは8文字以上で、大文字、小文字、数字をそれぞれ1文字以上含める必要があります。';
    default:
      return 'エラーが発生しました。しばらくしてから再度お試しください。';
  }
};

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter();
  const user = ref(null);
  const idToken = ref(null);
  const accessToken = ref(null);

  const isAuthenticated = computed(() => !!user.value);

  // 一般の管理者('30')は Read-only access
  const isReadOnly = computed(() => user.value?.role_id === '30');

  function _setUserFromToken(token) {
    if (!token) {
      user.value = null;
      idToken.value = null;
      accessToken.value = null;
      return;
    }

    const decodedToken = jwtDecode(token);
    user.value = {
      email: decodedToken.email,
      admin_no: decodedToken['custom:admin_no'],
      member_no: decodedToken['custom:member_no'],
      language_code: decodedToken['custom:admin_language_code'],
      role_id: decodedToken['custom:role_id'],
      admin_name: decodedToken['custom:admin_name'],
      tenant_id: decodedToken['tenant-id'],
    };
    idToken.value = token;
  }

  async function fetchAuthSession(forceRefresh = false) {
    try {
      const session = await amplifyFetchAuthSession({forceRefresh});
      const token = session.tokens?.idToken?.toString();
      if (!token) {
        throw new Error('No token found in session');
      }
      _setUserFromToken(token);
      accessToken.value = session.tokens?.accessToken?.toString();
      return true;
    } catch (e) {
      user.value = null;
      idToken.value = null;
      accessToken.value = null;
      return false;
    }
  }

  async function login(username, password) {
    try {
      const signInResponse = await signIn({
        username,
        password,
        options: {
          authFlowType: 'USER_PASSWORD_AUTH',
        },
      });

      if (signInResponse.isSignedIn) {
        await fetchAuthSession();
        return {type: 'SUCCESS'};
      }

      const {signInStep} = signInResponse.nextStep;
      switch (signInStep) {
        case 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED':
          return {
            type: 'NEW_PASSWORD_REQUIRED',
            message: '初回ログイン時はパスワードの変更が必要です。',
          };
        case 'CONFIRM_SIGN_UP':
          console.log('CONFIRM_SIGN_UP : TODO ');
          break;
        case 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE':
          console.log('CONFIRM_SIGN_IN_WITH_EMAIL_CODE : TODO');
          break;
        default:
          return {
            type: 'ERROR',
            message: `未対応の認証ステップです: ${signInStep}`,
          };
      }
    } catch (error) {
      console.error('Error signing in', error);
      throw new Error(errorToMessage(error));
    }
  }

  async function completeNewPassword(newPassword) {
    try {
      await confirmSignIn({challengeResponse: newPassword});
      await fetchAuthSession();
    } catch (error) {
      console.error('Error completing new password:', error);
      throw new Error(errorToMessage(error));
    }
  }

  async function logout() {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out: ', error);
    } finally {
      user.value = null;
      idToken.value = null;
      accessToken.value = null;
      router.push('/pages/login');
    }
  }

  Hub.listen('auth', ({payload: {event}}) => {
    switch (event) {
      case 'signedIn':
      case 'cognitoHostedUI':
      case 'signedOut':
        user.value = null;
        idToken.value = null;
        accessToken.value = null;
        console.log('Successfully signed out');
        break;
      case 'session_fail':
      case 'tokenRefresh_failure':
        logout();
        break;
    }
  });

  return {
    user,
    idToken,
    accessToken,
    isAuthenticated,
    isReadOnly,
    fetchAuthSession,
    login,
    completeNewPassword,
    logout,
  };
});
