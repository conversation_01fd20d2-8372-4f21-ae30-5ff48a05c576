import UploadFile from '@/api/uploadFileToS3';
import Base from '@/common/base';
import router from '@/router/index';
import {getFullImagePath} from '@/views/common/commonFilters';
import cloneDeep from 'lodash-es/cloneDeep';
import omit from 'lodash-es/omit';
import {defineStore} from 'pinia';
import Methods from '../api/methods';

const initItemFreeField = {
  estimatePrice: null,
  authLetterFilePath: [],
  identityFilePath: [],
  documentFilePath: [],
  identity_file: null,
  document_file: null,
  maker: null,
  bike_type: null,
  bike_type_other: null,
  first_inspection: null,
  first_inspection_other: null,
  inspec_expire: null,
  inspec_expire_other: null,
  fuel_type: null,
  meter_speed: null,
  meter_speed_other: null,
  meter_taco: null,
  meter_taco_other: null,
  horn: null,
  horn_other: null,
  headlight_hi: null,
  headlight_hi_other: null,
  headlight_lo: null,
  headlight_lo_other: null,
  winker_fl: null,
  winker_fl_other: null,
  winker_fr: null,
  winker_fr_other: null,
  winker_rl: null,
  winker_rl_other: null,
  winker_rr: null,
  winker_rr_other: null,
  tail_light_small: null,
  tail_light_small_other: null,
  tail_light_break: null,
  tail_light_break_other: null,
  battery_health: null,
  battery_health_other: null,
  radiator_leak: null,
  radiator_leak_other: null,
  engine_white_smoke: null,
  engine_white_smoke_other: null,
  engine_noise: null,
  engine_noise_other: null,
  front_fork_strain: null,
  front_fork_strain_other: null,
  front_fork_rusty: null,
  front_fork_rusty_other: null,
  front_fork_oil_leak: null,
  front_fork_oil_leak_other: null,
  handle: null,
  handle_other: null,
  front_break: null,
  front_break_other: null,
  rear_break: null,
  rear_break_other: null,
  engine_condition: null,
  engine_condition_other: null,
  tire_condition: null,
  tire_condition_other: null,
  modification: null,
  modification_other: null,
  scratch: null,
  scratch_other: null,
  other_memo: null,
  other_memo_other: null,
  model: null,
  model_other: null,
  frame_no: null,
  frame_no_other: null,
  engine_type: null,
  engine_type_other: null,
  mileage: null,
  mileage_other: null,
  color: null,
  color_other: null,
  cubic_capacity: null,
  cubic_capacity_other: null,
  engine_serial: null,
  engine_serial_other: null,
};

const useItem = defineStore('useItem', {
  state: () => {
    return {
      loading: false,
      errors: [],

      // Screen params
      itemDetailData: {
        item_free_field: cloneDeep(initItemFreeField),
      },
      itemImages: [],
      optionalImages: [],
      documentFiles: [],
      bodyTypes: {},

      approvalModal: false,
      rejectedModal: false,
      updateModal: false,
      changeFlag: false,
      errMsgArray: [],
      modalLoading: false,
      cancelModal: false,
      constants: {},

      // Data for change checking
      origItemData: null,
    };
  },
  actions: {
    reset() {
      this.loading = false;
      this.errors = [];

      // Screen params
      this.itemDetailData = {
        item_free_field: cloneDeep(initItemFreeField),
      };
      this.itemImages = [];
      this.optionalImages = [];
      this.bodyTypes = {};

      this.approvalModal = false;
      this.rejectedModal = false;
      this.updateModal = false;
      this.changeFlag = false;
      this.errMsgArray = [];
      this.modalLoading = false;
      this.cancelModal = false;
      this.constants = {};
      this.origItemData = null;
    },
    getConstants() {
      this.constants = {};

      // Required item images
      this.constants.itemImages = [
        {
          id: 1,
          title: '左',
          fileName: null,
          key: null,
          preview: null,
          label: '左',
        },
        {
          id: 2,
          title: '右',
          fileName: null,
          key: null,
          preview: null,
          label: '右',
        },
        {
          id: 3,
          title: '前',
          fileName: null,
          key: null,
          preview: null,
          label: '前',
        },
        {
          id: 4,
          title: '後',
          fileName: null,
          key: null,
          preview: null,
          label: '後',
        },
        {
          id: 5,
          title: 'メーター',
          fileName: null,
          key: null,
          preview: null,
          label: 'メーター',
        },
        {
          id: 6,
          title: 'タンク中',
          fileName: null,
          key: null,
          preview: null,
          label: 'タンク中',
        },
        {
          id: 7,
          title: 'エンジン左',
          fileName: null,
          key: null,
          preview: null,
          label: 'エンジン左',
        },
        {
          id: 8,
          title: 'エンジン右',
          fileName: null,
          key: null,
          preview: null,
          label: 'エンジン右',
        },
        {
          id: 9,
          title: 'エンジン前',
          fileName: null,
          key: null,
          preview: null,
          label: 'エンジン前',
        },
        {
          id: 10,
          title: 'エンジン番号',
          fileName: null,
          key: null,
          preview: null,
          label: 'エンジン番号',
        },
        {
          id: 11,
          title: 'シート',
          fileName: null,
          key: null,
          preview: null,
          label: 'シート',
        },
        {
          id: 12,
          title: '前方ホイール左',
          fileName: null,
          key: null,
          preview: null,
          label: '前方ホイール左',
        },
        {
          id: 13,
          title: '前方ホイール右',
          fileName: null,
          key: null,
          preview: null,
          label: '前方ホイール右',
        },
        {
          id: 14,
          title: '前方タイヤ',
          fileName: null,
          key: null,
          preview: null,
          label: '前方タイヤ',
        },
        {
          id: 16,
          title: '後方ホイール左',
          fileName: null,
          key: null,
          preview: null,
          label: '後方ホイール左',
        },
        {
          id: 17,
          title: '後方ホイール右',
          fileName: null,
          key: null,
          preview: null,
          label: '後方ホイール右',
        },
        {
          id: 15,
          title: '後方タイヤ',
          fileName: null,
          key: null,
          preview: null,
          label: '後方タイヤ',
        },
        {
          id: 19,
          title: 'リアサスペンション左',
          fileName: null,
          key: null,
          preview: null,
          label: 'リアサスペンション左',
        },
        {
          id: 18,
          title: 'リアサスペンション右',
          fileName: null,
          key: null,
          preview: null,
          label: 'リアサスペンション右',
        },
      ];

      // Optional item images
      this.constants.optionalImages = [
        {id: 1, title: '', fileName: null, key: null, preview: null},
        {id: 2, title: '', fileName: null, key: null, preview: null},
        {id: 3, title: '', fileName: null, key: null, preview: null},
        {id: 4, title: '', fileName: null, key: null, preview: null},
        {id: 5, title: '', fileName: null, key: null, preview: null},
        {id: 6, title: '', fileName: null, key: null, preview: null},
        {id: 7, title: '', fileName: null, key: null, preview: null},
        {id: 8, title: '', fileName: null, key: null, preview: null},
        {id: 9, title: '', fileName: null, key: null, preview: null},
        {id: 10, title: '', fileName: null, key: null, preview: null},
      ];

      const params = {
        key_strings: [
          'DOCUMENT_FILES',
          'SELECT_YES_NO',
          'SELECT_FUEL_TYPE',
          'SELECT_BATTERY_HEALTH',
          'SELECT_RADIATOR_LEAK',
          'SELECT_ARI_NASI',
          'SELECT_TEXT_AREA',
          'SELECT_BODY_TYPE',
          'SELECT_MAKER',
          'SELECT_IDENTITY_FILE',
          'SELECT_INPUT_OPTION',
          'SELECT_FUMEI_NASI',
          'SELECT_YES_NO_TACO',
        ],
      };
      // Request to server
      return Methods.apiExecute('get-constants-by-keys', params).then(
        response => {
          if (response.status === 200) {
            for (const row of response.data) {
              const now = new Date();
              switch (row.key_string) {
                case 'DOCUMENT_FILES':
                case 'SELECT_YES_NO':
                case 'SELECT_FUEL_TYPE':
                case 'SELECT_BATTERY_HEALTH':
                case 'SELECT_RADIATOR_LEAK':
                case 'SELECT_ARI_NASI':
                case 'SELECT_TEXT_AREA':
                case 'SELECT_BODY_TYPE':
                case 'SELECT_MAKER':
                case 'SELECT_IDENTITY_FILE':
                case 'SELECT_INPUT_OPTION':
                case 'SELECT_FUMEI_NASI':
                case 'SELECT_YES_NO_TACO':
                  if (!this.constants[row.key_string]) {
                    this.constants[row.key_string] = [];
                  }
                  this.constants[row.key_string].push({
                    value: row.value1,
                    label: row.value2,
                    isTextInput: String(row.value3) === '1',
                  });
                  break;
                default:
                  break;
              }
            }

            // Body types
            if (this.constants.SELECT_BODY_TYPE) {
              this.bodyTypes = JSON.parse(
                `{${this.constants.SELECT_BODY_TYPE.map(x => `"${x.value}":false`).join(',')}}`
              );
            }
          }
          return Promise.resolve();
        }
      );
    },
    getItemData(itemNo) {
      this.loading = true;
      const params = {
        item_no: itemNo,
      };
      // Request to server
      return Methods.apiExecute('get-item-detail-new', params).then(
        response => {
          if (response.status === 200) {
            const itemData = response.data;
            if (itemData && itemData.length > 0) {
              return Promise.resolve(itemData[0]);
            } else {
              return Promise.resolve(null);
            }
          }
          return Promise.resolve(null);
        }
      );
    },
    updateItem() {
      this.modalLoading = true;
      this.errMsgArray = [];

      const params = {
        validation_mode: false,
        item: {
          item_no: this.itemDetailData.item_no,
          manage_no: this.itemDetailData.manage_no,
          free_field: Object.assign({}, this.itemDetailData.item_free_field, {
            body_types: Object.keys(this.bodyTypes).filter(
              x => this.bodyTypes[x]
            ),
            maker: this.itemDetailData.item_free_field.maker
              ? Number(this.itemDetailData.item_free_field.maker)
              : null,
            fuel_type: this.itemDetailData.item_free_field.fuel_type
              ? Number(this.itemDetailData.item_free_field.fuel_type)
              : null,
          }),
          itemImages: this.itemImages
            .filter(y => y.key)
            .map(x => omit(x, 'file', 'preview')),
          optionalImages: this.optionalImages
            .filter(y => y.key)
            .map(x => omit(x, 'file', 'preview')),
          documentFiles: this.documentFiles
            .filter(y => y.key)
            .map(x => omit(x, 'file', 'preview', 'fileName')),
        },
      };
      console.log('params:', params);
      Methods.apiExecute('update-item', params)
        .then(response => {
          // Validation error results
          if (response.status === 400) {
            this.errMsgArray.push(response.message);
            this.modalLoading = false;
          } else {
            this.modalLoading = false;
            this.approvalModal = false;

            // Update original data for change-checking
            this.updateOrigData();

            router.push({path: '/items'});
          }
        })
        .catch(error => {
          this.modalLoading = false;
          console.log('error: ', error);
          this.errMsgArray = Methods.parseHtmlResponseError(router, error);
        });
    },
    onChangeModelValue({val, isTextInput}, column) {
      console.log('val, isTextInput: ', val, isTextInput);
      this.errors[column] = null;
      this.itemDetailData.item_free_field[column] = Number(val);
      if (!isTextInput) {
        this.itemDetailData.item_free_field[`${column}_other`] = null;
      }
    },
    onMounted(itemNo) {
      this.reset();
      this.getConstants()
        .then(() => {
          this.getItemData(itemNo).then(request => {
            this.itemDetailData = Object.assign({}, request, {
              item_free_field: Object.assign(
                {},
                initItemFreeField,
                request.item_free_field
              ),
            });

            // Get item request images
            const dbImages = request.ancillary_json_array;
            this.itemImages = this.constants.itemImages.map(x => {
              if (dbImages) {
                const img =
                  dbImages.find(y => {
                    return (
                      String(y.division) === '3' &&
                      String(y.physical_name) === String(x.id)
                    );
                  }) || {};
                return {
                  ...x,
                  title: img.file_title,
                  key: img.file_path,
                  file_path: img.filePath
                    ? this.$options.filters.getFullImagePath(img.filePath)
                    : null,
                };
              }
              return x;
            });
            // Saved optional images
            this.optionalImages = this.constants.optionalImages.map(x => {
              if (dbImages) {
                const img =
                  dbImages.find(y => {
                    return (
                      String(y.division) === '4' &&
                      String(y.physical_name) === String(x.id)
                    );
                  }) || {};
                return {
                  ...x,
                  title: img.file_title,
                  key: img.file_path,
                  file_path: img.filePath
                    ? this.$options.filters.getFullImagePath(img.filePath)
                    : null,
                };
              }
              return x;
            });
            console.log('itemImages: ', this.itemImages);

            // Body types
            if (this.itemDetailData.item_free_field.body_types) {
              const tmpBdTyp = this.constants.SELECT_BODY_TYPE.map(x => {
                const typ = this.itemDetailData.item_free_field.body_types.find(
                  y => String(y) === String(x.value)
                );
                if (typ) {
                  return `"${x.value}":true`;
                }
                return `"${x.value}":false`;
              });
              this.bodyTypes = JSON.parse(`{${tmpBdTyp.join(',')}}`);
            }

            // Maker
            if (this.itemDetailData.item_free_field.maker) {
              this.itemDetailData.item_free_field.maker = String(
                this.itemDetailData.item_free_field.maker
              );
            }

            // Fuel type
            if (this.itemDetailData.item_free_field.fuel_type) {
              this.itemDetailData.item_free_field.fuel_type = String(
                this.itemDetailData.item_free_field.fuel_type
              );
            }

            // Document files
            if (this.itemDetailData.item_free_field.documentFilePath) {
              this.documentFiles = this.constants.DOCUMENT_FILES.map(x => {
                const doc =
                  this.itemDetailData.item_free_field.documentFilePath.find(
                    y => String(y.id) === String(x.value)
                  ) || {};
                return {
                  id: doc.id,
                  label: x.label,
                  key: doc.key,
                };
              });
            }

            // Update original data for change-checking
            this.updateOrigData();

            this.loading = false;
          });
        })
        .catch(error => {
          console.log(error);
          this.loading = false;
          Methods.parseHtmlResponseError(this.$router, error);
        });
    },
    updateOrigData() {
      this.origItemData = cloneDeep({
        item_free_field: this.itemDetailData.item_free_field,
      });
    },
    isDataChanged() {
      const current = cloneDeep({
        item_free_field: this.itemDetailData.item_free_field,
      });
      if (Base.objectsAreIdentical(this.origItemData, current)) {
        return false;
      } else {
        return true;
      }
    },
    // Type: 1 itemImages, 2 optionalImages
    clearImage(index, type, tempImage) {
      const image =
        type === 1 ? this.itemImages[index] : this.optionalImages[index];
      if (!image) {
        return Promise.resolve();
      }
      if (image.key || image.file) {
        image.key = null;
        image.file = null;
        image.fileName = null;
        image.preview = null;
        if (type === 2) {
          image.title = '';
        }
      }
      return Promise.resolve();
    },
    // Type: 1 itemImages, 2 optionalImages
    uploadItemImage(index, type, tempImage) {
      const image =
        type === 1 ? this.itemImages[index] : this.optionalImages[index];
      if (!image) {
        return Promise.resolve();
      }
      // Update title only
      if (tempImage.key) {
        Object.assign(image, {
          title: tempImage.title,
        });
        return Promise.resolve();
      }
      // If no file selected then exit
      if (!tempImage.file) {
        return Promise.resolve();
      }
      return Promise.resolve()
        .then(() => {
          return UploadFile.getUploadCredentials('item-ancillary');
        })
        .then(credentials => {
          return UploadFile.uploadPromise(credentials, tempImage.file).then(
            result => {
              Object.assign(image, {
                key: result.Key,
                fileName: tempImage.fileName,
                title: tempImage.title,
                preview: tempImage.preview,
              });
              return Promise.resolve(result.Key);
            }
          );
        });
    },
    getPreviewData(type, index) {
      console.log('getPreviewData: ', type, index);
      const image =
        type === 1 ? this.itemImages[index] : this.optionalImages[index];
      if (!image || !image.key || image.preview) {
        return null;
      }
      return getFullImagePath(image.key);
    },
  },
  getters: {},
});

export default useItem;
