import {defineStore} from 'pinia';

export const useCommonStore = defineStore('common', {
  state: () => ({
    sidebarVisible: '',
    sidebarUnfoldable: false,
    theme: 'light',
    stocksSearchCondition: {
      status: [],
      new_flag: false,
      manage_no_from: '',
      manage_no_to: '',
      category: '',
      area_id: '',
      maker: '',
    },
    exhibitionsSearchCondition: {
      area_id: null,
      preview_start_datetime_from: null,
      preview_start_datetime_to: null,
      start_datetime_from: null,
      start_datetime_to: null,
    },
    position: 0,
    noticesSearchCondition: {
      display_code: ['1', '3'],
      startDate: '',
      endDate: '',
    },
    constantSearchCondition: {
      selectedConstantKey: '',
    },
    batchResultSearchCondition: {
      execution_datetime_date_from: null,
      execution_datetime_time_from: null,
      execution_datetime_date_to: null,
      execution_datetime_time_to: null,
      error_exists: '1',
    },
    activePage: 1,
    itemsPerPage: 10,
    itemsSorter: {asc: true, column: ''},

    // Exhibition page
    stockProps: {
      prop_item_no: null,
      prop_lot_no: null,
      prop_start_price: null,
      prop_item_lowest_bid_accept_price: null,
      prop_start_date: null,
      prop_end_date: null,
      prop_searched: null,
    },
  }),
  actions: {
    toggleSidebar() {
      this.sidebarVisible = !this.sidebarVisible;
    },
    toggleUnfoldable() {
      this.sidebarUnfoldable = !this.sidebarUnfoldable;
    },
    updateSidebarVisible(payload) {
      this.sidebarVisible = payload.value;
    },
    set([variable, value]) {
      this[variable] = value;
    },
  },
});
