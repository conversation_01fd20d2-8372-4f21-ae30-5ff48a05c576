import {useAuthStore} from '@/store/auth';
import {format} from 'date-fns';
import Api from './index';

export default {
  // ロケーションプロトコルがhttpsかを返す
  boolHttpsProtocol() {
    return location.protocol === 'https:';
  },
  /**
   * Execute API calls with proper authentication
   * @param {string} path - API endpoint path
   * @param {object} item - Request payload
   * @param {object} config - Additional config options
   * @param {boolean} isPublic - Whether this is a public API that doesn't require authentication
   */
  async apiExecute(path, item, config = {}, isPublic = false) {
    if (path === 'login' || path === 'register' || isPublic) {
      // Public API calls don't need authentication
      return Api()
        .post(path, item, config)
        .then(data => {
          console.log(`⚙️Public API: ${path}, レスポンス:`, data);
          return Promise.resolve(data);
        });
    }

    try {
      // Protected API calls require Cognito authentication
      const authStore = useAuthStore();
      const token = authStore.idToken;

      if (!token) {
        // This will trigger a logout and redirect via the router guard
        await authStore.logout();
        throw new Error('No Cognito token found, logging out.');
      }

      const apiConfig = {
        ...config,
        headers: {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        },
      };

      return Api()
        .post(path, item, apiConfig)
        .then(data => {
          console.log(`⚙️Protected API: ${path}, レスポンス:`, data);
          return Promise.resolve(data);
        });
    } catch (error) {
      console.error('Authentication error:', error);
      throw error;
    }
  },
  parseHtmlResponseError(router, error) {
    if (!error.response || error.code === 'ECONNABORTED') {
      return null;
    }
    const errorStatus = error.response.status;
    const responseData = error.response.data;
    const ClientErrorStatus = [400, 406, 409];
    if (error.response && ClientErrorStatus.includes(errorStatus)) {
      if (responseData.message) {
        if (errorStatus === 406) {
          // If [validation error], return the error message
          const errMsg = [];
          Object.values(responseData.message).forEach(err => {
            errMsg.push(err);
          });
          return errMsg;
        }
        // If not [server-validate-error], return the error message
        return [responseData.message];
      } else {
        const errMsg = [];
        Object.values(responseData.errors).forEach(err => {
          errMsg.push(err);
        });

        return errMsg;
      }
    } else if (error.response && (errorStatus === 401 || errorStatus === 403)) {
      const authStore = useAuthStore();
      authStore.logout();
    } else if (error.response) {
      console.error(`Server error: ${errorStatus}`);
      if (errorStatus >= 500) {
        const authStore = useAuthStore();
        authStore.logout();
        router.push({name: 'Page500'}).catch(() => {});
      }
    }
    return null;
  },
  // Timestamp with time zoneをYYYY-MM-DDの形で返す
  getFormatDate(date, fmt = 'yyyy-MM-dd') {
    return date ? format(date, fmt) : '';
  },

  // Timestamp with time zoneをHH-mmの形で返す
  getFormatTime(date, fmt = 'HH:mm') {
    return date ? format(date, fmt) : '';
  },
  // Timestamp with time zoneをyyyy/MM/DD HH:mmの形で返す
  getFormatDateTime(date) {
    return date ? format(date, 'yyyy/MM/dd HH:mm') : '';
  },
};
