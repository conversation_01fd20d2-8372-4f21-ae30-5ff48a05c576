<template>
  <CDropdown placement="bottom-end" variant="nav-item">
    <CDropdownToggle class="py-0 pe-0 d-flex align-items-center" :caret="false">
      {{ authStore.user?.admin_name || '' }}　様
      <CIcon :height="30" icon="cil-user" />
    </CDropdownToggle>
    <CDropdownMenu class="pt-0">
      <CDropdownItem @click="authStore.logout">
        <CIcon icon="cil-lock-locked" /> ログアウト
      </CDropdownItem>
    </CDropdownMenu>
  </CDropdown>
</template>

<script setup>
  import {useAuthStore} from '@/store/auth';
  const authStore = useAuthStore();
</script>

<style scoped>
  .c-icon {
    margin-right: 0.3rem;
  }
</style>
