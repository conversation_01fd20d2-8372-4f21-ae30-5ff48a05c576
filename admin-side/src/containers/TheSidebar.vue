<template>
  <CSidebar
    class="border-end"
    position="fixed"
    :unfoldable="sidebarUnfoldable"
    :visible="sidebarVisible"
    @visible-change="
      event =>
        store.updateSidebarVisible({
          value: event,
        })
    "
  >
    <CSidebarHeader class="border-bottom">
      <RouterLink custom to="/exhibitions" v-slot="{href, navigate}">
        <CSidebarBrand v-bind="$attrs" as="a" :href="href" @click="navigate">
          <a class="logo-title h5">SaaS（モック）</a>
        </CSidebarBrand>
      </RouterLink>
      <CCloseButton class="d-lg-none" dark @click="store.toggleSidebar()" />
    </CSidebarHeader>
    <AppSidebarNav />
  </CSidebar>
</template>

<script setup>
  import {useCommonStore} from '@/store/common';
  import {computed} from 'vue';
  import {RouterLink} from 'vue-router';
  import {AppSidebarNav} from './AppSidebarNav';

  const store = useCommonStore();
  const sidebarUnfoldable = computed(() => store.sidebarUnfoldable);
  const sidebarVisible = computed(() => store.sidebarVisible);
</script>

<style>
  .simplebar-content {
    background-color: #3c4b64;
  }

  .sidebar-nav .nav-title {
    background-color: #fff;
    color: #3c4b64;
    margin-top: 0;
  }

  .nav-item {
    cursor: pointer;
    margin-top: 0 !important;
  }

  .sidebar-nav .nav-link {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    border-radius: 0;
  }

  .sidebar-nav .nav-link.active {
    background-color: #ffffff0d;
    color: rgba(255, 255, 255, 0.8);
  }

  .sidebar-header {
    background-color: #ffffff !important;
    display: inline;
    padding: 20px;
    text-align: center;
  }

  .logo-title img {
    width: 80%;
  }

  .sidebar-nav .nav-link:hover {
    background-color: #321fdb;
    color: #fff;
  }

  .c-sidebar a:hover {
    text-decoration: none !important;
  }

  .c-sidebar .logo-title:focus {
    text-decoration: none !important;
    cursor: pointer;
  }

  .c-sidebar .logo-title:hover {
    text-decoration: none !important;
    cursor: pointer;
  }
</style>
