<template>
  <div>
    <TheSidebar />
    <div class="wrapper d-flex flex-column min-vh-100">
      <TheHeader />
      <div class="body flex-grow-1">
        <CContainer class="px-4" fluid>
          <router-view />
        </CContainer>
      </div>
      <TheFooter />
    </div>
  </div>
</template>

<script>
  import {CContainer} from '@coreui/vue';
  import TheFooter from './TheFooter.vue';
  import TheHeader from './TheHeader.vue';
  import TheSidebar from './TheSidebar.vue';

  export default {
    name: 'TheContainer',
    components: {
      TheSidebar,
      TheHeader,
      TheFooter,
      CContainer,
    },
  };
</script>

<style scoped>
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s;
  }
  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }
</style>
