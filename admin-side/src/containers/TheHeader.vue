<template>
  <CHeader position="sticky" :class="headerClassNames">
    <CContainer class="border-bottom px-4" fluid>
      <CHeaderToggler
        @click="store.toggleSidebar()"
        style="margin-inline-start: -14px"
      >
        <CIcon icon="cil-menu" size="lg" />
      </CHeaderToggler>
      <CHeaderNav class="d-none d-md-flex">
        <CBreadcrumb class="my-0">
          <CBreadcrumbItem
            v-for="item in breadcrumbs"
            :key="item"
            :href="item.active ? '' : item.path"
            :active="item.active"
          >
            {{ item.name }}
          </CBreadcrumbItem>
        </CBreadcrumb>
      </CHeaderNav>
      <CHeaderNav class="ms-auto">
        <TheHeaderDropdownAccnt />
      </CHeaderNav>
    </CContainer>
  </CHeader>
</template>

<script setup>
  import {useCommonStore} from '@/store/common';
  import {useColorModes} from '@coreui/vue';
  import {onMounted, ref} from 'vue';
  import {useRouter} from 'vue-router';
  import TheHeaderDropdownAccnt from './TheHeaderDropdownAccnt';

  const store = useCommonStore();

  const headerClassNames = ref('mb-4 p-0');
  const {colorMode, setColorMode} = useColorModes(
    'coreui-free-vue-admin-template-theme'
  );
  const breadcrumbs = ref();
  const router = useRouter();
  const getBreadcrumbs = () => {
    const routes = router.currentRoute.value.matched.filter(route => {
      return route.name || route.meta?.label;
    });
    const lastItem = routes ? routes[routes.length - 1] : null;
    return routes.map(route => {
      return {
        active: route.name === lastItem.name,
        name: route.name,
        path: `${router.options.history.base}${route.path}`,
      };
    });
  };

  router.afterEach(() => {
    breadcrumbs.value = getBreadcrumbs();
  });

  onMounted(() => {
    breadcrumbs.value = getBreadcrumbs();
    document.addEventListener('scroll', () => {
      if (document.documentElement.scrollTop > 0) {
        headerClassNames.value = 'mb-4 p-0 shadow-sm';
      } else {
        headerClassNames.value = 'mb-4 p-0';
      }
    });
  });
</script>
