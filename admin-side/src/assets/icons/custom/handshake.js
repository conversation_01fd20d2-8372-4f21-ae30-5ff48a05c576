/*
 * Example of SVG converted to js array, so it can be used with CIcon.
 * The first argument is two last values of svg viewBox,
 * The second argument is the SVG content stripped of SVG tags
 */
export const handshake = [
  '512 512',
  `<g>
    <path d="M505.899,211.567L368.7,74.369c-7.876-7.902-21.649-7.902-29.525,0l-62.925,62.899c-8.064,8.124-8.064,21.35,0.034,29.525
      l17.05,16.853c-11.042-0.879-26.547,4.838-27.989,4.745l-39.083-12.109l9.455-9.455c3.951-3.951,6.135-9.199,6.135-14.771
      c0-5.598-2.185-10.846-6.127-14.771L172.834,74.36c-7.91-7.876-21.615-7.876-29.525,0.026L6.135,211.542
      C2.185,215.493,0,220.749,0,226.347c0,5.572,2.185,10.829,6.127,14.754l62.874,62.899c3.934,3.951,9.19,6.127,14.763,6.127
      s10.812-2.176,14.771-6.127l4.318-4.318c11.136,23.322,48.546,70.741,74.633,96.845l34.304,34.304
      c10.513,10.496,20.471,12.723,26.991,12.723c8.303,0,16.102-3.422,22.554-9.873c6.315-6.323,8.098-12.476,8.499-16.922
      c7.561,1.775,18.91,0.947,25.796-5.948c6.315-6.298,8.098-12.476,8.499-16.922c7.561,1.775,18.91,0.973,25.796-5.922
      c6.315-6.323,8.098-12.476,8.491-16.922c7.612,1.672,18.927,0.922,25.796-5.948c5.837-5.845,12.194-16.29,7.578-30.839
      c9.813-9.156,26.377-19.831,42.735-29.559c3.823,3.388,8.567,5.419,13.713,5.419c5.598,0,10.846-2.176,14.763-6.127l62.891-62.874
      C514.031,232.943,514.031,219.717,505.899,211.567z M83.772,282.573l-56.209-56.226L158.071,95.796l56.209,56.226L83.772,282.573z
       M347.034,346.539c-0.913,0-2.662-0.427-4.215-0.802c-2.628-0.623-5.598-1.348-8.909-1.348c-6.673,0-11.025,2.953-13.508,5.427
      c-5.803,5.803-6.426,12.023-6.801,15.753c-0.265,2.628-0.29,2.773-0.836,3.849c-0.913,0-2.662-0.427-4.215-0.802
      c-2.628-0.648-5.598-1.348-8.926-1.348c-6.699,0-11.059,2.953-13.525,5.427c-5.803,5.803-6.426,12.023-6.801,15.727
      c-0.265,2.628-0.29,2.773-0.853,3.849c-0.913,0-2.671-0.427-4.224-0.802c-2.611-0.623-5.572-1.323-8.9-1.323
      c-6.699,0-11.059,2.953-13.5,5.402c-5.828,5.803-6.451,12.023-6.827,15.753c-0.265,2.628-0.29,2.773-1.775,4.275
      c-2.364,2.372-3.934,2.372-4.446,2.372c-2.338,0-5.572-1.903-8.892-5.222l-34.304-34.304
      c-29.261-29.278-62.515-75.947-75.349-96.128l85.709-85.709l18.91,5.333c-15.812,12.075-28.365,29.372-38.502,41.933
      c-3.362,4.173-6.025,7.526-7.902,9.404c-8.004,8.004-10.453,16.375-7.279,24.926c2.338,6.349,7.441,11.452,13.901,17.903
      c5.751,5.751,12.996,8.798,20.966,8.798c16.316,0,31.94-12.373,47.053-24.354c3.311-2.62,13.261-9.353,19.507-13.542
      c24.55,22.349,48.23,44.245,64.93,60.937C351.352,341.718,347.674,345.396,347.034,346.539z M357.914,312.44
      c-0.828-0.905-1.374-1.715-2.278-2.62c-21.896-21.897-50.466-45.338-68.036-62.174c-5.393-5.171-15.121-3.618-22.69-1.289
      c-8.841,1.502-17.118,5.7-27.708,14.089c-10.027,7.953-23.748,18.825-31.164,18.825c-0.998,0-6.801-4.122-7.902-5.222
      c-2.125-2.125,4.113-9.122,8.141-14.123c14.046-17.399,32.725-37.001,57.19-46.404c20.804-8.004,35.26-4.557,54.4-4.267
      l77.781,76.902C381.628,294.665,368.145,303.693,357.914,312.44z M428.245,282.514l-130.534-130.5l56.226-56.201l130.526,130.5
      L428.245,282.514z"/>
  </g>`,
];
