<!DOCTYPE html>
<!--
* CoreUI Free Vue.js Admin Template
* @version v5.0.0
* @link https://coreui.io/product/free-vue-admin-template/
* Copyright (c) 2024 creativeLabs <PERSON>
* Licensed under MIT (https://github.com/coreui/coreui-free-vue-admin-template/blob/main/LICENSE)
-->
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,shrink-to-fit=no">
    <meta name="description" content="CoreUI Vue.js Admin Template">
    <meta name="author" content="creativeLabs <PERSON>">
    <title>DEMO 　管理サイト</title>
    <!-- favicons for all devices -->
    <link rel="apple-touch-icon" sizes="57x57" href="./src/assets/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="60x60" href="./src/assets/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="72x72" href="./src/assets/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="76x76" href="./src/assets/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="114x114" href="./src/assets/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="120x120" href="./src/assets/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="144x144" href="./src/assets/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="152x152" href="./src/assets/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="./src/assets/favicon/favicon.ico">
    <link rel="icon" type="image/vnd.microsoft.icon" sizes="192x192" href="./src/assets/favicon/favicon.ico">
    <link rel="icon" type="image/vnd.microsoft.icon" sizes="32x32" href="./src/assets/favicon/favicon.ico">
    <link rel="icon" type="image/vnd.microsoft.icon" sizes="96x96" href="./src/assets/favicon/favicon.ico">
    <link rel="icon" type="image/vnd.microsoft.icon" sizes="16x16" href="./src/assets/favicon/favicon.ico">
    <link rel="manifest" href="/manifest.json">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="./src/assets/favicon/favicon.ico">
    <meta name="theme-color" content="#ffffff">
    <script>
      const userMode = localStorage.getItem('coreui-free-vue-admin-template-theme');
	    const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.dataset.coreuiTheme = 'dark';
			}
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but this app doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
    <!-- built files will be auto injected -->
  </body>
</html>
